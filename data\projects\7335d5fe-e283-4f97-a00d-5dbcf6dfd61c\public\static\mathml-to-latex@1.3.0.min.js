!function(e, t) {
    "object" == typeof exports && "object" == typeof module ? module.exports = t() : "function" == typeof define && define.amd ? define([], t) : "object" == typeof exports ? exports.MathMLToLaTeX = t() : e.MathMLToLaTeX = t()
}(this, (()=>(()=>{
    var e = {
        2167: (e,t)=>{
            "use strict";
            function r(e, t) {
                return void 0 === t && (t = Object),
                t && "function" == typeof t.freeze ? t.freeze(e) : e
            }
            var a = r({
                HTML: "text/html",
                isHTML: function(e) {
                    return e === a.HTML
                },
                XML_APPLICATION: "application/xml",
                XML_TEXT: "text/xml",
                XML_XHTML_APPLICATION: "application/xhtml+xml",
                XML_SVG_IMAGE: "image/svg+xml"
            })
              , n = r({
                HTML: "http://www.w3.org/1999/xhtml",
                isHTML: function(e) {
                    return e === n.HTML
                },
                SVG: "http://www.w3.org/2000/svg",
                XML: "http://www.w3.org/XML/1998/namespace",
                XMLNS: "http://www.w3.org/2000/xmlns/"
            });
            t.assign = function(e, t) {
                if (null === e || "object" != typeof e)
                    throw new TypeError("target is not an object");
                for (var r in t)
                    Object.prototype.hasOwnProperty.call(t, r) && (e[r] = t[r]);
                return e
            }
            ,
            t.find = function(e, t, r) {
                if (void 0 === r && (r = Array.prototype),
                e && "function" == typeof r.find)
                    return r.find.call(e, t);
                for (var a = 0; a < e.length; a++)
                    if (Object.prototype.hasOwnProperty.call(e, a)) {
                        var n = e[a];
                        if (t.call(void 0, n, a, e))
                            return n
                    }
            }
            ,
            t.freeze = r,
            t.MIME_TYPE = a,
            t.NAMESPACE = n
        }
        ,
        6129: (e,t,r)=>{
            var a = r(2167)
              , n = r(1146)
              , o = r(1045)
              , i = r(6925)
              , s = n.DOMImplementation
              , c = a.NAMESPACE
              , l = i.ParseError
              , u = i.XMLReader;
            function h(e) {
                return e.replace(/\r[\n\u0085]/g, "\n").replace(/[\r\u0085\u2028]/g, "\n")
            }
            function d(e) {
                this.options = e || {
                    locator: {}
                }
            }
            function m() {
                this.cdata = !1
            }
            function p(e, t) {
                t.lineNumber = e.lineNumber,
                t.columnNumber = e.columnNumber
            }
            function f(e) {
                if (e)
                    return "\n@" + (e.systemId || "") + "#[line:" + e.lineNumber + ",col:" + e.columnNumber + "]"
            }
            function x(e, t, r) {
                return "string" == typeof e ? e.substr(t, r) : e.length >= t + r || t ? new java.lang.String(e,t,r) + "" : e
            }
            function g(e, t) {
                e.currentElement ? e.currentElement.appendChild(t) : e.doc.appendChild(t)
            }
            d.prototype.parseFromString = function(e, t) {
                var r = this.options
                  , a = new u
                  , n = r.domBuilder || new m
                  , i = r.errorHandler
                  , s = r.locator
                  , l = r.xmlns || {}
                  , d = /\/x?html?$/.test(t)
                  , p = d ? o.HTML_ENTITIES : o.XML_ENTITIES;
                s && n.setDocumentLocator(s),
                a.errorHandler = function(e, t, r) {
                    if (!e) {
                        if (t instanceof m)
                            return t;
                        e = t
                    }
                    var a = {}
                      , n = e instanceof Function;
                    function o(t) {
                        var o = e[t];
                        !o && n && (o = 2 == e.length ? function(r) {
                            e(t, r)
                        }
                        : e),
                        a[t] = o && function(e) {
                            o("[xmldom " + t + "]\t" + e + f(r))
                        }
                        || function() {}
                    }
                    return r = r || {},
                    o("warning"),
                    o("error"),
                    o("fatalError"),
                    a
                }(i, n, s),
                a.domBuilder = r.domBuilder || n,
                d && (l[""] = c.HTML),
                l.xml = l.xml || c.XML;
                var x = r.normalizeLineEndings || h;
                return e && "string" == typeof e ? a.parse(x(e), l, p) : a.errorHandler.error("invalid doc source"),
                n.doc
            }
            ,
            m.prototype = {
                startDocument: function() {
                    this.doc = (new s).createDocument(null, null, null),
                    this.locator && (this.doc.documentURI = this.locator.systemId)
                },
                startElement: function(e, t, r, a) {
                    var n = this.doc
                      , o = n.createElementNS(e, r || t)
                      , i = a.length;
                    g(this, o),
                    this.currentElement = o,
                    this.locator && p(this.locator, o);
                    for (var s = 0; s < i; s++) {
                        e = a.getURI(s);
                        var c = a.getValue(s)
                          , l = (r = a.getQName(s),
                        n.createAttributeNS(e, r));
                        this.locator && p(a.getLocator(s), l),
                        l.value = l.nodeValue = c,
                        o.setAttributeNode(l)
                    }
                },
                endElement: function(e, t, r) {
                    var a = this.currentElement;
                    a.tagName,
                    this.currentElement = a.parentNode
                },
                startPrefixMapping: function(e, t) {},
                endPrefixMapping: function(e) {},
                processingInstruction: function(e, t) {
                    var r = this.doc.createProcessingInstruction(e, t);
                    this.locator && p(this.locator, r),
                    g(this, r)
                },
                ignorableWhitespace: function(e, t, r) {},
                characters: function(e, t, r) {
                    if (e = x.apply(this, arguments)) {
                        if (this.cdata)
                            var a = this.doc.createCDATASection(e);
                        else
                            a = this.doc.createTextNode(e);
                        this.currentElement ? this.currentElement.appendChild(a) : /^\s*$/.test(e) && this.doc.appendChild(a),
                        this.locator && p(this.locator, a)
                    }
                },
                skippedEntity: function(e) {},
                endDocument: function() {
                    this.doc.normalize()
                },
                setDocumentLocator: function(e) {
                    (this.locator = e) && (e.lineNumber = 0)
                },
                comment: function(e, t, r) {
                    e = x.apply(this, arguments);
                    var a = this.doc.createComment(e);
                    this.locator && p(this.locator, a),
                    g(this, a)
                },
                startCDATA: function() {
                    this.cdata = !0
                },
                endCDATA: function() {
                    this.cdata = !1
                },
                startDTD: function(e, t, r) {
                    var a = this.doc.implementation;
                    if (a && a.createDocumentType) {
                        var n = a.createDocumentType(e, t, r);
                        this.locator && p(this.locator, n),
                        g(this, n),
                        this.doc.doctype = n
                    }
                },
                warning: function(e) {
                    console.warn("[xmldom warning]\t" + e, f(this.locator))
                },
                error: function(e) {
                    console.error("[xmldom error]\t" + e, f(this.locator))
                },
                fatalError: function(e) {
                    throw new l(e,this.locator)
                }
            },
            "endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl".replace(/\w+/g, (function(e) {
                m.prototype[e] = function() {
                    return null
                }
            }
            )),
            t.DOMParser = d
        }
        ,
        1146: (e,t,r)=>{
            var a = r(2167)
              , n = a.find
              , o = a.NAMESPACE;
            function i(e) {
                return "" !== e
            }
            function s(e, t) {
                return e.hasOwnProperty(t) || (e[t] = !0),
                e
            }
            function c(e) {
                if (!e)
                    return [];
                var t = function(e) {
                    return e ? e.split(/[\t\n\f\r ]+/).filter(i) : []
                }(e);
                return Object.keys(t.reduce(s, {}))
            }
            function l(e, t) {
                for (var r in e)
                    Object.prototype.hasOwnProperty.call(e, r) && (t[r] = e[r])
            }
            function u(e, t) {
                var r = e.prototype;
                if (!(r instanceof t)) {
                    function a() {}
                    a.prototype = t.prototype,
                    l(r, a = new a),
                    e.prototype = r = a
                }
                r.constructor != e && ("function" != typeof e && console.error("unknown Class:" + e),
                r.constructor = e)
            }
            var h = {}
              , d = h.ELEMENT_NODE = 1
              , m = h.ATTRIBUTE_NODE = 2
              , p = h.TEXT_NODE = 3
              , f = h.CDATA_SECTION_NODE = 4
              , x = h.ENTITY_REFERENCE_NODE = 5
              , g = h.ENTITY_NODE = 6
              , w = h.PROCESSING_INSTRUCTION_NODE = 7
              , b = h.COMMENT_NODE = 8
              , v = h.DOCUMENT_NODE = 9
              , C = h.DOCUMENT_TYPE_NODE = 10
              , A = h.DOCUMENT_FRAGMENT_NODE = 11
              , E = h.NOTATION_NODE = 12
              , y = {}
              , _ = {}
              , q = (y.INDEX_SIZE_ERR = (_[1] = "Index size error",
            1),
            y.DOMSTRING_SIZE_ERR = (_[2] = "DOMString size error",
            2),
            y.HIERARCHY_REQUEST_ERR = (_[3] = "Hierarchy request error",
            3))
              , D = (y.WRONG_DOCUMENT_ERR = (_[4] = "Wrong document",
            4),
            y.INVALID_CHARACTER_ERR = (_[5] = "Invalid character",
            5),
            y.NO_DATA_ALLOWED_ERR = (_[6] = "No data allowed",
            6),
            y.NO_MODIFICATION_ALLOWED_ERR = (_[7] = "No modification allowed",
            7),
            y.NOT_FOUND_ERR = (_[8] = "Not found",
            8))
              , T = (y.NOT_SUPPORTED_ERR = (_[9] = "Not supported",
            9),
            y.INUSE_ATTRIBUTE_ERR = (_[10] = "Attribute in use",
            10));
            function N(e, t) {
                if (t instanceof Error)
                    var r = t;
                else
                    r = this,
                    Error.call(this, _[e]),
                    this.message = _[e],
                    Error.captureStackTrace && Error.captureStackTrace(this, N);
                return r.code = e,
                t && (this.message = this.message + ": " + t),
                r
            }
            function M() {}
            function O(e, t) {
                this._node = e,
                this._refresh = t,
                L(this)
            }
            function L(e) {
                var t = e._node._inc || e._node.ownerDocument._inc;
                if (e._inc !== t) {
                    var r = e._refresh(e._node);
                    if (we(e, "length", r.length),
                    !e.$$length || r.length < e.$$length)
                        for (var a = r.length; a in e; a++)
                            Object.prototype.hasOwnProperty.call(e, a) && delete e[a];
                    l(r, e),
                    e._inc = t
                }
            }
            function B() {}
            function S(e, t) {
                for (var r = e.length; r--; )
                    if (e[r] === t)
                        return r
            }
            function F(e, t, r, a) {
                if (a ? t[S(t, a)] = r : t[t.length++] = r,
                e) {
                    r.ownerElement = e;
                    var n = e.ownerDocument;
                    n && (a && G(n, e, a),
                    function(e, t, r) {
                        e && e._inc++,
                        r.namespaceURI === o.XMLNS && (t._nsMap[r.prefix ? r.localName : ""] = r.value)
                    }(n, e, r))
                }
            }
            function k(e, t, r) {
                var a = S(t, r);
                if (!(a >= 0))
                    throw new N(D,new Error(e.tagName + "@" + r));
                for (var n = t.length - 1; a < n; )
                    t[a] = t[++a];
                if (t.length = n,
                e) {
                    var o = e.ownerDocument;
                    o && (G(o, e, r),
                    r.ownerElement = null)
                }
            }
            function P() {}
            function R() {}
            function I(e) {
                return ("<" == e ? "&lt;" : ">" == e && "&gt;") || "&" == e && "&amp;" || '"' == e && "&quot;" || "&#" + e.charCodeAt() + ";"
            }
            function U(e, t) {
                if (t(e))
                    return !0;
                if (e = e.firstChild)
                    do {
                        if (U(e, t))
                            return !0
                    } while (e = e.nextSibling)
            }
            function j() {
                this.ownerDocument = this
            }
            function G(e, t, r, a) {
                e && e._inc++,
                r.namespaceURI === o.XMLNS && delete t._nsMap[r.prefix ? r.localName : ""]
            }
            function V(e, t, r) {
                if (e && e._inc) {
                    e._inc++;
                    var a = t.childNodes;
                    if (r)
                        a[a.length++] = r;
                    else {
                        for (var n = t.firstChild, o = 0; n; )
                            a[o++] = n,
                            n = n.nextSibling;
                        a.length = o,
                        delete a[a.length]
                    }
                }
            }
            function $(e, t) {
                var r = t.previousSibling
                  , a = t.nextSibling;
                return r ? r.nextSibling = a : e.firstChild = a,
                a ? a.previousSibling = r : e.lastChild = r,
                t.parentNode = null,
                t.previousSibling = null,
                t.nextSibling = null,
                V(e.ownerDocument, e),
                t
            }
            function X(e) {
                return e && e.nodeType === R.DOCUMENT_TYPE_NODE
            }
            function H(e) {
                return e && e.nodeType === R.ELEMENT_NODE
            }
            function W(e) {
                return e && e.nodeType === R.TEXT_NODE
            }
            function z(e, t) {
                var r = e.childNodes || [];
                if (n(r, H) || X(t))
                    return !1;
                var a = n(r, X);
                return !(t && a && r.indexOf(a) > r.indexOf(t))
            }
            function Y(e, t) {
                var r = e.childNodes || [];
                if (n(r, (function(e) {
                    return H(e) && e !== t
                }
                )))
                    return !1;
                var a = n(r, X);
                return !(t && a && r.indexOf(a) > r.indexOf(t))
            }
            function J(e, t, r) {
                var a = e.childNodes || []
                  , o = t.childNodes || [];
                if (t.nodeType === R.DOCUMENT_FRAGMENT_NODE) {
                    var i = o.filter(H);
                    if (i.length > 1 || n(o, W))
                        throw new N(q,"More than one element or text in fragment");
                    if (1 === i.length && !z(e, r))
                        throw new N(q,"Element in fragment can not be inserted before doctype")
                }
                if (H(t) && !z(e, r))
                    throw new N(q,"Only one element can be added and only after doctype");
                if (X(t)) {
                    if (n(a, X))
                        throw new N(q,"Only one doctype is allowed");
                    var s = n(a, H);
                    if (r && a.indexOf(s) < a.indexOf(r))
                        throw new N(q,"Doctype can only be inserted before an element");
                    if (!r && s)
                        throw new N(q,"Doctype can not be appended since element is present")
                }
            }
            function Z(e, t, r) {
                var a = e.childNodes || []
                  , o = t.childNodes || [];
                if (t.nodeType === R.DOCUMENT_FRAGMENT_NODE) {
                    var i = o.filter(H);
                    if (i.length > 1 || n(o, W))
                        throw new N(q,"More than one element or text in fragment");
                    if (1 === i.length && !Y(e, r))
                        throw new N(q,"Element in fragment can not be inserted before doctype")
                }
                if (H(t) && !Y(e, r))
                    throw new N(q,"Only one element can be added and only after doctype");
                if (X(t)) {
                    if (n(a, (function(e) {
                        return X(e) && e !== r
                    }
                    )))
                        throw new N(q,"Only one doctype is allowed");
                    var s = n(a, H);
                    if (r && a.indexOf(s) < a.indexOf(r))
                        throw new N(q,"Doctype can only be inserted before an element")
                }
            }
            function Q(e, t, r, a) {
                (function(e, t, r) {
                    if (!function(e) {
                        return e && (e.nodeType === R.DOCUMENT_NODE || e.nodeType === R.DOCUMENT_FRAGMENT_NODE || e.nodeType === R.ELEMENT_NODE)
                    }(e))
                        throw new N(q,"Unexpected parent node type " + e.nodeType);
                    if (r && r.parentNode !== e)
                        throw new N(D,"child not in parent");
                    if (!function(e) {
                        return e && (H(e) || W(e) || X(e) || e.nodeType === R.DOCUMENT_FRAGMENT_NODE || e.nodeType === R.COMMENT_NODE || e.nodeType === R.PROCESSING_INSTRUCTION_NODE)
                    }(t) || X(t) && e.nodeType !== R.DOCUMENT_NODE)
                        throw new N(q,"Unexpected node type " + t.nodeType + " for parent node type " + e.nodeType)
                }
                )(e, t, r),
                e.nodeType === R.DOCUMENT_NODE && (a || J)(e, t, r);
                var n = t.parentNode;
                if (n && n.removeChild(t),
                t.nodeType === A) {
                    var o = t.firstChild;
                    if (null == o)
                        return t;
                    var i = t.lastChild
                } else
                    o = i = t;
                var s = r ? r.previousSibling : e.lastChild;
                o.previousSibling = s,
                i.nextSibling = r,
                s ? s.nextSibling = o : e.firstChild = o,
                null == r ? e.lastChild = i : r.previousSibling = i;
                do {
                    o.parentNode = e
                } while (o !== i && (o = o.nextSibling));
                return V(e.ownerDocument || e, e),
                t.nodeType == A && (t.firstChild = t.lastChild = null),
                t
            }
            function K() {
                this._nsMap = {}
            }
            function ee() {}
            function te() {}
            function re() {}
            function ae() {}
            function ne() {}
            function oe() {}
            function ie() {}
            function se() {}
            function ce() {}
            function le() {}
            function ue() {}
            function he() {}
            function de(e, t) {
                var r = []
                  , a = 9 == this.nodeType && this.documentElement || this
                  , n = a.prefix
                  , o = a.namespaceURI;
                if (o && null == n && null == (n = a.lookupPrefix(o)))
                    var i = [{
                        namespace: o,
                        prefix: null
                    }];
                return fe(this, r, e, t, i),
                r.join("")
            }
            function me(e, t, r) {
                var a = e.prefix || ""
                  , n = e.namespaceURI;
                if (!n)
                    return !1;
                if ("xml" === a && n === o.XML || n === o.XMLNS)
                    return !1;
                for (var i = r.length; i--; ) {
                    var s = r[i];
                    if (s.prefix === a)
                        return s.namespace !== n
                }
                return !0
            }
            function pe(e, t, r) {
                e.push(" ", t, '="', r.replace(/[<>&"\t\n\r]/g, I), '"')
            }
            function fe(e, t, r, a, n) {
                if (n || (n = []),
                a) {
                    if (!(e = a(e)))
                        return;
                    if ("string" == typeof e)
                        return void t.push(e)
                }
                switch (e.nodeType) {
                case d:
                    var i = e.attributes
                      , s = i.length
                      , c = e.firstChild
                      , l = e.tagName
                      , u = l;
                    if (!(r = o.isHTML(e.namespaceURI) || r) && !e.prefix && e.namespaceURI) {
                        for (var h, g = 0; g < i.length; g++)
                            if ("xmlns" === i.item(g).name) {
                                h = i.item(g).value;
                                break
                            }
                        if (!h)
                            for (var E = n.length - 1; E >= 0; E--)
                                if ("" === (y = n[E]).prefix && y.namespace === e.namespaceURI) {
                                    h = y.namespace;
                                    break
                                }
                        if (h !== e.namespaceURI)
                            for (E = n.length - 1; E >= 0; E--) {
                                var y;
                                if ((y = n[E]).namespace === e.namespaceURI) {
                                    y.prefix && (u = y.prefix + ":" + l);
                                    break
                                }
                            }
                    }
                    t.push("<", u);
                    for (var _ = 0; _ < s; _++)
                        "xmlns" == (q = i.item(_)).prefix ? n.push({
                            prefix: q.localName,
                            namespace: q.value
                        }) : "xmlns" == q.nodeName && n.push({
                            prefix: "",
                            namespace: q.value
                        });
                    for (_ = 0; _ < s; _++) {
                        var q, D, T;
                        me(q = i.item(_), 0, n) && (pe(t, (D = q.prefix || "") ? "xmlns:" + D : "xmlns", T = q.namespaceURI),
                        n.push({
                            prefix: D,
                            namespace: T
                        })),
                        fe(q, t, r, a, n)
                    }
                    if (l === u && me(e, 0, n) && (pe(t, (D = e.prefix || "") ? "xmlns:" + D : "xmlns", T = e.namespaceURI),
                    n.push({
                        prefix: D,
                        namespace: T
                    })),
                    c || r && !/^(?:meta|link|img|br|hr|input)$/i.test(l)) {
                        if (t.push(">"),
                        r && /^script$/i.test(l))
                            for (; c; )
                                c.data ? t.push(c.data) : fe(c, t, r, a, n.slice()),
                                c = c.nextSibling;
                        else
                            for (; c; )
                                fe(c, t, r, a, n.slice()),
                                c = c.nextSibling;
                        t.push("</", u, ">")
                    } else
                        t.push("/>");
                    return;
                case v:
                case A:
                    for (c = e.firstChild; c; )
                        fe(c, t, r, a, n.slice()),
                        c = c.nextSibling;
                    return;
                case m:
                    return pe(t, e.name, e.value);
                case p:
                    return t.push(e.data.replace(/[<&>]/g, I));
                case f:
                    return t.push("<![CDATA[", e.data, "]]>");
                case b:
                    return t.push("\x3c!--", e.data, "--\x3e");
                case C:
                    var N = e.publicId
                      , M = e.systemId;
                    if (t.push("<!DOCTYPE ", e.name),
                    N)
                        t.push(" PUBLIC ", N),
                        M && "." != M && t.push(" ", M),
                        t.push(">");
                    else if (M && "." != M)
                        t.push(" SYSTEM ", M, ">");
                    else {
                        var O = e.internalSubset;
                        O && t.push(" [", O, "]"),
                        t.push(">")
                    }
                    return;
                case w:
                    return t.push("<?", e.target, " ", e.data, "?>");
                case x:
                    return t.push("&", e.nodeName, ";");
                default:
                    t.push("??", e.nodeName)
                }
            }
            function xe(e, t, r) {
                var a;
                switch (t.nodeType) {
                case d:
                    (a = t.cloneNode(!1)).ownerDocument = e;
                case A:
                    break;
                case m:
                    r = !0
                }
                if (a || (a = t.cloneNode(!1)),
                a.ownerDocument = e,
                a.parentNode = null,
                r)
                    for (var n = t.firstChild; n; )
                        a.appendChild(xe(e, n, r)),
                        n = n.nextSibling;
                return a
            }
            function ge(e, t, r) {
                var a = new t.constructor;
                for (var n in t)
                    if (Object.prototype.hasOwnProperty.call(t, n)) {
                        var o = t[n];
                        "object" != typeof o && o != a[n] && (a[n] = o)
                    }
                switch (t.childNodes && (a.childNodes = new M),
                a.ownerDocument = e,
                a.nodeType) {
                case d:
                    var i = t.attributes
                      , s = a.attributes = new B
                      , c = i.length;
                    s._ownerElement = a;
                    for (var l = 0; l < c; l++)
                        a.setAttributeNode(ge(e, i.item(l), !0));
                    break;
                case m:
                    r = !0
                }
                if (r)
                    for (var u = t.firstChild; u; )
                        a.appendChild(ge(e, u, r)),
                        u = u.nextSibling;
                return a
            }
            function we(e, t, r) {
                e[t] = r
            }
            y.INVALID_STATE_ERR = (_[11] = "Invalid state",
            11),
            y.SYNTAX_ERR = (_[12] = "Syntax error",
            12),
            y.INVALID_MODIFICATION_ERR = (_[13] = "Invalid modification",
            13),
            y.NAMESPACE_ERR = (_[14] = "Invalid namespace",
            14),
            y.INVALID_ACCESS_ERR = (_[15] = "Invalid access",
            15),
            N.prototype = Error.prototype,
            l(y, N),
            M.prototype = {
                length: 0,
                item: function(e) {
                    return e >= 0 && e < this.length ? this[e] : null
                },
                toString: function(e, t) {
                    for (var r = [], a = 0; a < this.length; a++)
                        fe(this[a], r, e, t);
                    return r.join("")
                },
                filter: function(e) {
                    return Array.prototype.filter.call(this, e)
                },
                indexOf: function(e) {
                    return Array.prototype.indexOf.call(this, e)
                }
            },
            O.prototype.item = function(e) {
                return L(this),
                this[e] || null
            }
            ,
            u(O, M),
            B.prototype = {
                length: 0,
                item: M.prototype.item,
                getNamedItem: function(e) {
                    for (var t = this.length; t--; ) {
                        var r = this[t];
                        if (r.nodeName == e)
                            return r
                    }
                },
                setNamedItem: function(e) {
                    var t = e.ownerElement;
                    if (t && t != this._ownerElement)
                        throw new N(T);
                    var r = this.getNamedItem(e.nodeName);
                    return F(this._ownerElement, this, e, r),
                    r
                },
                setNamedItemNS: function(e) {
                    var t, r = e.ownerElement;
                    if (r && r != this._ownerElement)
                        throw new N(T);
                    return t = this.getNamedItemNS(e.namespaceURI, e.localName),
                    F(this._ownerElement, this, e, t),
                    t
                },
                removeNamedItem: function(e) {
                    var t = this.getNamedItem(e);
                    return k(this._ownerElement, this, t),
                    t
                },
                removeNamedItemNS: function(e, t) {
                    var r = this.getNamedItemNS(e, t);
                    return k(this._ownerElement, this, r),
                    r
                },
                getNamedItemNS: function(e, t) {
                    for (var r = this.length; r--; ) {
                        var a = this[r];
                        if (a.localName == t && a.namespaceURI == e)
                            return a
                    }
                    return null
                }
            },
            P.prototype = {
                hasFeature: function(e, t) {
                    return !0
                },
                createDocument: function(e, t, r) {
                    var a = new j;
                    if (a.implementation = this,
                    a.childNodes = new M,
                    a.doctype = r || null,
                    r && a.appendChild(r),
                    t) {
                        var n = a.createElementNS(e, t);
                        a.appendChild(n)
                    }
                    return a
                },
                createDocumentType: function(e, t, r) {
                    var a = new oe;
                    return a.name = e,
                    a.nodeName = e,
                    a.publicId = t || "",
                    a.systemId = r || "",
                    a
                }
            },
            R.prototype = {
                firstChild: null,
                lastChild: null,
                previousSibling: null,
                nextSibling: null,
                attributes: null,
                parentNode: null,
                childNodes: null,
                ownerDocument: null,
                nodeValue: null,
                namespaceURI: null,
                prefix: null,
                localName: null,
                insertBefore: function(e, t) {
                    return Q(this, e, t)
                },
                replaceChild: function(e, t) {
                    Q(this, e, t, Z),
                    t && this.removeChild(t)
                },
                removeChild: function(e) {
                    return $(this, e)
                },
                appendChild: function(e) {
                    return this.insertBefore(e, null)
                },
                hasChildNodes: function() {
                    return null != this.firstChild
                },
                cloneNode: function(e) {
                    return ge(this.ownerDocument || this, this, e)
                },
                normalize: function() {
                    for (var e = this.firstChild; e; ) {
                        var t = e.nextSibling;
                        t && t.nodeType == p && e.nodeType == p ? (this.removeChild(t),
                        e.appendData(t.data)) : (e.normalize(),
                        e = t)
                    }
                },
                isSupported: function(e, t) {
                    return this.ownerDocument.implementation.hasFeature(e, t)
                },
                hasAttributes: function() {
                    return this.attributes.length > 0
                },
                lookupPrefix: function(e) {
                    for (var t = this; t; ) {
                        var r = t._nsMap;
                        if (r)
                            for (var a in r)
                                if (Object.prototype.hasOwnProperty.call(r, a) && r[a] === e)
                                    return a;
                        t = t.nodeType == m ? t.ownerDocument : t.parentNode
                    }
                    return null
                },
                lookupNamespaceURI: function(e) {
                    for (var t = this; t; ) {
                        var r = t._nsMap;
                        if (r && Object.prototype.hasOwnProperty.call(r, e))
                            return r[e];
                        t = t.nodeType == m ? t.ownerDocument : t.parentNode
                    }
                    return null
                },
                isDefaultNamespace: function(e) {
                    return null == this.lookupPrefix(e)
                }
            },
            l(h, R),
            l(h, R.prototype),
            j.prototype = {
                nodeName: "#document",
                nodeType: v,
                doctype: null,
                documentElement: null,
                _inc: 1,
                insertBefore: function(e, t) {
                    if (e.nodeType == A) {
                        for (var r = e.firstChild; r; ) {
                            var a = r.nextSibling;
                            this.insertBefore(r, t),
                            r = a
                        }
                        return e
                    }
                    return Q(this, e, t),
                    e.ownerDocument = this,
                    null === this.documentElement && e.nodeType === d && (this.documentElement = e),
                    e
                },
                removeChild: function(e) {
                    return this.documentElement == e && (this.documentElement = null),
                    $(this, e)
                },
                replaceChild: function(e, t) {
                    Q(this, e, t, Z),
                    e.ownerDocument = this,
                    t && this.removeChild(t),
                    H(e) && (this.documentElement = e)
                },
                importNode: function(e, t) {
                    return xe(this, e, t)
                },
                getElementById: function(e) {
                    var t = null;
                    return U(this.documentElement, (function(r) {
                        if (r.nodeType == d && r.getAttribute("id") == e)
                            return t = r,
                            !0
                    }
                    )),
                    t
                },
                getElementsByClassName: function(e) {
                    var t = c(e);
                    return new O(this,(function(r) {
                        var a = [];
                        return t.length > 0 && U(r.documentElement, (function(n) {
                            if (n !== r && n.nodeType === d) {
                                var o = n.getAttribute("class");
                                if (o) {
                                    var i = e === o;
                                    if (!i) {
                                        var s = c(o);
                                        i = t.every((l = s,
                                        function(e) {
                                            return l && -1 !== l.indexOf(e)
                                        }
                                        ))
                                    }
                                    i && a.push(n)
                                }
                            }
                            var l
                        }
                        )),
                        a
                    }
                    ))
                },
                createElement: function(e) {
                    var t = new K;
                    return t.ownerDocument = this,
                    t.nodeName = e,
                    t.tagName = e,
                    t.localName = e,
                    t.childNodes = new M,
                    (t.attributes = new B)._ownerElement = t,
                    t
                },
                createDocumentFragment: function() {
                    var e = new le;
                    return e.ownerDocument = this,
                    e.childNodes = new M,
                    e
                },
                createTextNode: function(e) {
                    var t = new re;
                    return t.ownerDocument = this,
                    t.appendData(e),
                    t
                },
                createComment: function(e) {
                    var t = new ae;
                    return t.ownerDocument = this,
                    t.appendData(e),
                    t
                },
                createCDATASection: function(e) {
                    var t = new ne;
                    return t.ownerDocument = this,
                    t.appendData(e),
                    t
                },
                createProcessingInstruction: function(e, t) {
                    var r = new ue;
                    return r.ownerDocument = this,
                    r.tagName = r.nodeName = r.target = e,
                    r.nodeValue = r.data = t,
                    r
                },
                createAttribute: function(e) {
                    var t = new ee;
                    return t.ownerDocument = this,
                    t.name = e,
                    t.nodeName = e,
                    t.localName = e,
                    t.specified = !0,
                    t
                },
                createEntityReference: function(e) {
                    var t = new ce;
                    return t.ownerDocument = this,
                    t.nodeName = e,
                    t
                },
                createElementNS: function(e, t) {
                    var r = new K
                      , a = t.split(":")
                      , n = r.attributes = new B;
                    return r.childNodes = new M,
                    r.ownerDocument = this,
                    r.nodeName = t,
                    r.tagName = t,
                    r.namespaceURI = e,
                    2 == a.length ? (r.prefix = a[0],
                    r.localName = a[1]) : r.localName = t,
                    n._ownerElement = r,
                    r
                },
                createAttributeNS: function(e, t) {
                    var r = new ee
                      , a = t.split(":");
                    return r.ownerDocument = this,
                    r.nodeName = t,
                    r.name = t,
                    r.namespaceURI = e,
                    r.specified = !0,
                    2 == a.length ? (r.prefix = a[0],
                    r.localName = a[1]) : r.localName = t,
                    r
                }
            },
            u(j, R),
            K.prototype = {
                nodeType: d,
                hasAttribute: function(e) {
                    return null != this.getAttributeNode(e)
                },
                getAttribute: function(e) {
                    var t = this.getAttributeNode(e);
                    return t && t.value || ""
                },
                getAttributeNode: function(e) {
                    return this.attributes.getNamedItem(e)
                },
                setAttribute: function(e, t) {
                    var r = this.ownerDocument.createAttribute(e);
                    r.value = r.nodeValue = "" + t,
                    this.setAttributeNode(r)
                },
                removeAttribute: function(e) {
                    var t = this.getAttributeNode(e);
                    t && this.removeAttributeNode(t)
                },
                appendChild: function(e) {
                    return e.nodeType === A ? this.insertBefore(e, null) : function(e, t) {
                        return t.parentNode && t.parentNode.removeChild(t),
                        t.parentNode = e,
                        t.previousSibling = e.lastChild,
                        t.nextSibling = null,
                        t.previousSibling ? t.previousSibling.nextSibling = t : e.firstChild = t,
                        e.lastChild = t,
                        V(e.ownerDocument, e, t),
                        t
                    }(this, e)
                },
                setAttributeNode: function(e) {
                    return this.attributes.setNamedItem(e)
                },
                setAttributeNodeNS: function(e) {
                    return this.attributes.setNamedItemNS(e)
                },
                removeAttributeNode: function(e) {
                    return this.attributes.removeNamedItem(e.nodeName)
                },
                removeAttributeNS: function(e, t) {
                    var r = this.getAttributeNodeNS(e, t);
                    r && this.removeAttributeNode(r)
                },
                hasAttributeNS: function(e, t) {
                    return null != this.getAttributeNodeNS(e, t)
                },
                getAttributeNS: function(e, t) {
                    var r = this.getAttributeNodeNS(e, t);
                    return r && r.value || ""
                },
                setAttributeNS: function(e, t, r) {
                    var a = this.ownerDocument.createAttributeNS(e, t);
                    a.value = a.nodeValue = "" + r,
                    this.setAttributeNode(a)
                },
                getAttributeNodeNS: function(e, t) {
                    return this.attributes.getNamedItemNS(e, t)
                },
                getElementsByTagName: function(e) {
                    return new O(this,(function(t) {
                        var r = [];
                        return U(t, (function(a) {
                            a === t || a.nodeType != d || "*" !== e && a.tagName != e || r.push(a)
                        }
                        )),
                        r
                    }
                    ))
                },
                getElementsByTagNameNS: function(e, t) {
                    return new O(this,(function(r) {
                        var a = [];
                        return U(r, (function(n) {
                            n === r || n.nodeType !== d || "*" !== e && n.namespaceURI !== e || "*" !== t && n.localName != t || a.push(n)
                        }
                        )),
                        a
                    }
                    ))
                }
            },
            j.prototype.getElementsByTagName = K.prototype.getElementsByTagName,
            j.prototype.getElementsByTagNameNS = K.prototype.getElementsByTagNameNS,
            u(K, R),
            ee.prototype.nodeType = m,
            u(ee, R),
            te.prototype = {
                data: "",
                substringData: function(e, t) {
                    return this.data.substring(e, e + t)
                },
                appendData: function(e) {
                    e = this.data + e,
                    this.nodeValue = this.data = e,
                    this.length = e.length
                },
                insertData: function(e, t) {
                    this.replaceData(e, 0, t)
                },
                appendChild: function(e) {
                    throw new Error(_[q])
                },
                deleteData: function(e, t) {
                    this.replaceData(e, t, "")
                },
                replaceData: function(e, t, r) {
                    r = this.data.substring(0, e) + r + this.data.substring(e + t),
                    this.nodeValue = this.data = r,
                    this.length = r.length
                }
            },
            u(te, R),
            re.prototype = {
                nodeName: "#text",
                nodeType: p,
                splitText: function(e) {
                    var t = this.data
                      , r = t.substring(e);
                    t = t.substring(0, e),
                    this.data = this.nodeValue = t,
                    this.length = t.length;
                    var a = this.ownerDocument.createTextNode(r);
                    return this.parentNode && this.parentNode.insertBefore(a, this.nextSibling),
                    a
                }
            },
            u(re, te),
            ae.prototype = {
                nodeName: "#comment",
                nodeType: b
            },
            u(ae, te),
            ne.prototype = {
                nodeName: "#cdata-section",
                nodeType: f
            },
            u(ne, te),
            oe.prototype.nodeType = C,
            u(oe, R),
            ie.prototype.nodeType = E,
            u(ie, R),
            se.prototype.nodeType = g,
            u(se, R),
            ce.prototype.nodeType = x,
            u(ce, R),
            le.prototype.nodeName = "#document-fragment",
            le.prototype.nodeType = A,
            u(le, R),
            ue.prototype.nodeType = w,
            u(ue, R),
            he.prototype.serializeToString = function(e, t, r) {
                return de.call(e, t, r)
            }
            ,
            R.prototype.toString = de;
            try {
                if (Object.defineProperty) {
                    function be(e) {
                        switch (e.nodeType) {
                        case d:
                        case A:
                            var t = [];
                            for (e = e.firstChild; e; )
                                7 !== e.nodeType && 8 !== e.nodeType && t.push(be(e)),
                                e = e.nextSibling;
                            return t.join("");
                        default:
                            return e.nodeValue
                        }
                    }
                    Object.defineProperty(O.prototype, "length", {
                        get: function() {
                            return L(this),
                            this.$$length
                        }
                    }),
                    Object.defineProperty(R.prototype, "textContent", {
                        get: function() {
                            return be(this)
                        },
                        set: function(e) {
                            switch (this.nodeType) {
                            case d:
                            case A:
                                for (; this.firstChild; )
                                    this.removeChild(this.firstChild);
                                (e || String(e)) && this.appendChild(this.ownerDocument.createTextNode(e));
                                break;
                            default:
                                this.data = e,
                                this.value = e,
                                this.nodeValue = e
                            }
                        }
                    }),
                    we = function(e, t, r) {
                        e["$$" + t] = r
                    }
                }
            } catch (ve) {}
            t.DocumentType = oe,
            t.DOMException = N,
            t.DOMImplementation = P,
            t.Element = K,
            t.Node = R,
            t.NodeList = M,
            t.XMLSerializer = he
        }
        ,
        1045: (e,t,r)=>{
            "use strict";
            var a = r(2167).freeze;
            t.XML_ENTITIES = a({
                amp: "&",
                apos: "'",
                gt: ">",
                lt: "<",
                quot: '"'
            }),
            t.HTML_ENTITIES = a({
                Aacute: "Á",
                aacute: "á",
                Abreve: "Ă",
                abreve: "ă",
                ac: "∾",
                acd: "∿",
                acE: "∾̳",
                Acirc: "Â",
                acirc: "â",
                acute: "´",
                Acy: "А",
                acy: "а",
                AElig: "Æ",
                aelig: "æ",
                af: "⁡",
                Afr: "𝔄",
                afr: "𝔞",
                Agrave: "À",
                agrave: "à",
                alefsym: "ℵ",
                aleph: "ℵ",
                Alpha: "Α",
                alpha: "α",
                Amacr: "Ā",
                amacr: "ā",
                amalg: "⨿",
                AMP: "&",
                amp: "&",
                And: "⩓",
                and: "∧",
                andand: "⩕",
                andd: "⩜",
                andslope: "⩘",
                andv: "⩚",
                ang: "∠",
                ange: "⦤",
                angle: "∠",
                angmsd: "∡",
                angmsdaa: "⦨",
                angmsdab: "⦩",
                angmsdac: "⦪",
                angmsdad: "⦫",
                angmsdae: "⦬",
                angmsdaf: "⦭",
                angmsdag: "⦮",
                angmsdah: "⦯",
                angrt: "∟",
                angrtvb: "⊾",
                angrtvbd: "⦝",
                angsph: "∢",
                angst: "Å",
                angzarr: "⍼",
                Aogon: "Ą",
                aogon: "ą",
                Aopf: "𝔸",
                aopf: "𝕒",
                ap: "≈",
                apacir: "⩯",
                apE: "⩰",
                ape: "≊",
                apid: "≋",
                apos: "'",
                ApplyFunction: "⁡",
                approx: "≈",
                approxeq: "≊",
                Aring: "Å",
                aring: "å",
                Ascr: "𝒜",
                ascr: "𝒶",
                Assign: "≔",
                ast: "*",
                asymp: "≈",
                asympeq: "≍",
                Atilde: "Ã",
                atilde: "ã",
                Auml: "Ä",
                auml: "ä",
                awconint: "∳",
                awint: "⨑",
                backcong: "≌",
                backepsilon: "϶",
                backprime: "‵",
                backsim: "∽",
                backsimeq: "⋍",
                Backslash: "∖",
                Barv: "⫧",
                barvee: "⊽",
                Barwed: "⌆",
                barwed: "⌅",
                barwedge: "⌅",
                bbrk: "⎵",
                bbrktbrk: "⎶",
                bcong: "≌",
                Bcy: "Б",
                bcy: "б",
                bdquo: "„",
                becaus: "∵",
                Because: "∵",
                because: "∵",
                bemptyv: "⦰",
                bepsi: "϶",
                bernou: "ℬ",
                Bernoullis: "ℬ",
                Beta: "Β",
                beta: "β",
                beth: "ℶ",
                between: "≬",
                Bfr: "𝔅",
                bfr: "𝔟",
                bigcap: "⋂",
                bigcirc: "◯",
                bigcup: "⋃",
                bigodot: "⨀",
                bigoplus: "⨁",
                bigotimes: "⨂",
                bigsqcup: "⨆",
                bigstar: "★",
                bigtriangledown: "▽",
                bigtriangleup: "△",
                biguplus: "⨄",
                bigvee: "⋁",
                bigwedge: "⋀",
                bkarow: "⤍",
                blacklozenge: "⧫",
                blacksquare: "▪",
                blacktriangle: "▴",
                blacktriangledown: "▾",
                blacktriangleleft: "◂",
                blacktriangleright: "▸",
                blank: "␣",
                blk12: "▒",
                blk14: "░",
                blk34: "▓",
                block: "█",
                bne: "=⃥",
                bnequiv: "≡⃥",
                bNot: "⫭",
                bnot: "⌐",
                Bopf: "𝔹",
                bopf: "𝕓",
                bot: "⊥",
                bottom: "⊥",
                bowtie: "⋈",
                boxbox: "⧉",
                boxDL: "╗",
                boxDl: "╖",
                boxdL: "╕",
                boxdl: "┐",
                boxDR: "╔",
                boxDr: "╓",
                boxdR: "╒",
                boxdr: "┌",
                boxH: "═",
                boxh: "─",
                boxHD: "╦",
                boxHd: "╤",
                boxhD: "╥",
                boxhd: "┬",
                boxHU: "╩",
                boxHu: "╧",
                boxhU: "╨",
                boxhu: "┴",
                boxminus: "⊟",
                boxplus: "⊞",
                boxtimes: "⊠",
                boxUL: "╝",
                boxUl: "╜",
                boxuL: "╛",
                boxul: "┘",
                boxUR: "╚",
                boxUr: "╙",
                boxuR: "╘",
                boxur: "└",
                boxV: "║",
                boxv: "│",
                boxVH: "╬",
                boxVh: "╫",
                boxvH: "╪",
                boxvh: "┼",
                boxVL: "╣",
                boxVl: "╢",
                boxvL: "╡",
                boxvl: "┤",
                boxVR: "╠",
                boxVr: "╟",
                boxvR: "╞",
                boxvr: "├",
                bprime: "‵",
                Breve: "˘",
                breve: "˘",
                brvbar: "¦",
                Bscr: "ℬ",
                bscr: "𝒷",
                bsemi: "⁏",
                bsim: "∽",
                bsime: "⋍",
                bsol: "\\",
                bsolb: "⧅",
                bsolhsub: "⟈",
                bull: "•",
                bullet: "•",
                bump: "≎",
                bumpE: "⪮",
                bumpe: "≏",
                Bumpeq: "≎",
                bumpeq: "≏",
                Cacute: "Ć",
                cacute: "ć",
                Cap: "⋒",
                cap: "∩",
                capand: "⩄",
                capbrcup: "⩉",
                capcap: "⩋",
                capcup: "⩇",
                capdot: "⩀",
                CapitalDifferentialD: "ⅅ",
                caps: "∩︀",
                caret: "⁁",
                caron: "ˇ",
                Cayleys: "ℭ",
                ccaps: "⩍",
                Ccaron: "Č",
                ccaron: "č",
                Ccedil: "Ç",
                ccedil: "ç",
                Ccirc: "Ĉ",
                ccirc: "ĉ",
                Cconint: "∰",
                ccups: "⩌",
                ccupssm: "⩐",
                Cdot: "Ċ",
                cdot: "ċ",
                cedil: "¸",
                Cedilla: "¸",
                cemptyv: "⦲",
                cent: "¢",
                CenterDot: "·",
                centerdot: "·",
                Cfr: "ℭ",
                cfr: "𝔠",
                CHcy: "Ч",
                chcy: "ч",
                check: "✓",
                checkmark: "✓",
                Chi: "Χ",
                chi: "χ",
                cir: "○",
                circ: "ˆ",
                circeq: "≗",
                circlearrowleft: "↺",
                circlearrowright: "↻",
                circledast: "⊛",
                circledcirc: "⊚",
                circleddash: "⊝",
                CircleDot: "⊙",
                circledR: "®",
                circledS: "Ⓢ",
                CircleMinus: "⊖",
                CirclePlus: "⊕",
                CircleTimes: "⊗",
                cirE: "⧃",
                cire: "≗",
                cirfnint: "⨐",
                cirmid: "⫯",
                cirscir: "⧂",
                ClockwiseContourIntegral: "∲",
                CloseCurlyDoubleQuote: "”",
                CloseCurlyQuote: "’",
                clubs: "♣",
                clubsuit: "♣",
                Colon: "∷",
                colon: ":",
                Colone: "⩴",
                colone: "≔",
                coloneq: "≔",
                comma: ",",
                commat: "@",
                comp: "∁",
                compfn: "∘",
                complement: "∁",
                complexes: "ℂ",
                cong: "≅",
                congdot: "⩭",
                Congruent: "≡",
                Conint: "∯",
                conint: "∮",
                ContourIntegral: "∮",
                Copf: "ℂ",
                copf: "𝕔",
                coprod: "∐",
                Coproduct: "∐",
                COPY: "©",
                copy: "©",
                copysr: "℗",
                CounterClockwiseContourIntegral: "∳",
                crarr: "↵",
                Cross: "⨯",
                cross: "✗",
                Cscr: "𝒞",
                cscr: "𝒸",
                csub: "⫏",
                csube: "⫑",
                csup: "⫐",
                csupe: "⫒",
                ctdot: "⋯",
                cudarrl: "⤸",
                cudarrr: "⤵",
                cuepr: "⋞",
                cuesc: "⋟",
                cularr: "↶",
                cularrp: "⤽",
                Cup: "⋓",
                cup: "∪",
                cupbrcap: "⩈",
                CupCap: "≍",
                cupcap: "⩆",
                cupcup: "⩊",
                cupdot: "⊍",
                cupor: "⩅",
                cups: "∪︀",
                curarr: "↷",
                curarrm: "⤼",
                curlyeqprec: "⋞",
                curlyeqsucc: "⋟",
                curlyvee: "⋎",
                curlywedge: "⋏",
                curren: "¤",
                curvearrowleft: "↶",
                curvearrowright: "↷",
                cuvee: "⋎",
                cuwed: "⋏",
                cwconint: "∲",
                cwint: "∱",
                cylcty: "⌭",
                Dagger: "‡",
                dagger: "†",
                daleth: "ℸ",
                Darr: "↡",
                dArr: "⇓",
                darr: "↓",
                dash: "‐",
                Dashv: "⫤",
                dashv: "⊣",
                dbkarow: "⤏",
                dblac: "˝",
                Dcaron: "Ď",
                dcaron: "ď",
                Dcy: "Д",
                dcy: "д",
                DD: "ⅅ",
                dd: "ⅆ",
                ddagger: "‡",
                ddarr: "⇊",
                DDotrahd: "⤑",
                ddotseq: "⩷",
                deg: "°",
                Del: "∇",
                Delta: "Δ",
                delta: "δ",
                demptyv: "⦱",
                dfisht: "⥿",
                Dfr: "𝔇",
                dfr: "𝔡",
                dHar: "⥥",
                dharl: "⇃",
                dharr: "⇂",
                DiacriticalAcute: "´",
                DiacriticalDot: "˙",
                DiacriticalDoubleAcute: "˝",
                DiacriticalGrave: "`",
                DiacriticalTilde: "˜",
                diam: "⋄",
                Diamond: "⋄",
                diamond: "⋄",
                diamondsuit: "♦",
                diams: "♦",
                die: "¨",
                DifferentialD: "ⅆ",
                digamma: "ϝ",
                disin: "⋲",
                div: "÷",
                divide: "÷",
                divideontimes: "⋇",
                divonx: "⋇",
                DJcy: "Ђ",
                djcy: "ђ",
                dlcorn: "⌞",
                dlcrop: "⌍",
                dollar: "$",
                Dopf: "𝔻",
                dopf: "𝕕",
                Dot: "¨",
                dot: "˙",
                DotDot: "⃜",
                doteq: "≐",
                doteqdot: "≑",
                DotEqual: "≐",
                dotminus: "∸",
                dotplus: "∔",
                dotsquare: "⊡",
                doublebarwedge: "⌆",
                DoubleContourIntegral: "∯",
                DoubleDot: "¨",
                DoubleDownArrow: "⇓",
                DoubleLeftArrow: "⇐",
                DoubleLeftRightArrow: "⇔",
                DoubleLeftTee: "⫤",
                DoubleLongLeftArrow: "⟸",
                DoubleLongLeftRightArrow: "⟺",
                DoubleLongRightArrow: "⟹",
                DoubleRightArrow: "⇒",
                DoubleRightTee: "⊨",
                DoubleUpArrow: "⇑",
                DoubleUpDownArrow: "⇕",
                DoubleVerticalBar: "∥",
                DownArrow: "↓",
                Downarrow: "⇓",
                downarrow: "↓",
                DownArrowBar: "⤓",
                DownArrowUpArrow: "⇵",
                DownBreve: "̑",
                downdownarrows: "⇊",
                downharpoonleft: "⇃",
                downharpoonright: "⇂",
                DownLeftRightVector: "⥐",
                DownLeftTeeVector: "⥞",
                DownLeftVector: "↽",
                DownLeftVectorBar: "⥖",
                DownRightTeeVector: "⥟",
                DownRightVector: "⇁",
                DownRightVectorBar: "⥗",
                DownTee: "⊤",
                DownTeeArrow: "↧",
                drbkarow: "⤐",
                drcorn: "⌟",
                drcrop: "⌌",
                Dscr: "𝒟",
                dscr: "𝒹",
                DScy: "Ѕ",
                dscy: "ѕ",
                dsol: "⧶",
                Dstrok: "Đ",
                dstrok: "đ",
                dtdot: "⋱",
                dtri: "▿",
                dtrif: "▾",
                duarr: "⇵",
                duhar: "⥯",
                dwangle: "⦦",
                DZcy: "Џ",
                dzcy: "џ",
                dzigrarr: "⟿",
                Eacute: "É",
                eacute: "é",
                easter: "⩮",
                Ecaron: "Ě",
                ecaron: "ě",
                ecir: "≖",
                Ecirc: "Ê",
                ecirc: "ê",
                ecolon: "≕",
                Ecy: "Э",
                ecy: "э",
                eDDot: "⩷",
                Edot: "Ė",
                eDot: "≑",
                edot: "ė",
                ee: "ⅇ",
                efDot: "≒",
                Efr: "𝔈",
                efr: "𝔢",
                eg: "⪚",
                Egrave: "È",
                egrave: "è",
                egs: "⪖",
                egsdot: "⪘",
                el: "⪙",
                Element: "∈",
                elinters: "⏧",
                ell: "ℓ",
                els: "⪕",
                elsdot: "⪗",
                Emacr: "Ē",
                emacr: "ē",
                empty: "∅",
                emptyset: "∅",
                EmptySmallSquare: "◻",
                emptyv: "∅",
                EmptyVerySmallSquare: "▫",
                emsp: " ",
                emsp13: " ",
                emsp14: " ",
                ENG: "Ŋ",
                eng: "ŋ",
                ensp: " ",
                Eogon: "Ę",
                eogon: "ę",
                Eopf: "𝔼",
                eopf: "𝕖",
                epar: "⋕",
                eparsl: "⧣",
                eplus: "⩱",
                epsi: "ε",
                Epsilon: "Ε",
                epsilon: "ε",
                epsiv: "ϵ",
                eqcirc: "≖",
                eqcolon: "≕",
                eqsim: "≂",
                eqslantgtr: "⪖",
                eqslantless: "⪕",
                Equal: "⩵",
                equals: "=",
                EqualTilde: "≂",
                equest: "≟",
                Equilibrium: "⇌",
                equiv: "≡",
                equivDD: "⩸",
                eqvparsl: "⧥",
                erarr: "⥱",
                erDot: "≓",
                Escr: "ℰ",
                escr: "ℯ",
                esdot: "≐",
                Esim: "⩳",
                esim: "≂",
                Eta: "Η",
                eta: "η",
                ETH: "Ð",
                eth: "ð",
                Euml: "Ë",
                euml: "ë",
                euro: "€",
                excl: "!",
                exist: "∃",
                Exists: "∃",
                expectation: "ℰ",
                ExponentialE: "ⅇ",
                exponentiale: "ⅇ",
                fallingdotseq: "≒",
                Fcy: "Ф",
                fcy: "ф",
                female: "♀",
                ffilig: "ﬃ",
                fflig: "ﬀ",
                ffllig: "ﬄ",
                Ffr: "𝔉",
                ffr: "𝔣",
                filig: "ﬁ",
                FilledSmallSquare: "◼",
                FilledVerySmallSquare: "▪",
                fjlig: "fj",
                flat: "♭",
                fllig: "ﬂ",
                fltns: "▱",
                fnof: "ƒ",
                Fopf: "𝔽",
                fopf: "𝕗",
                ForAll: "∀",
                forall: "∀",
                fork: "⋔",
                forkv: "⫙",
                Fouriertrf: "ℱ",
                fpartint: "⨍",
                frac12: "½",
                frac13: "⅓",
                frac14: "¼",
                frac15: "⅕",
                frac16: "⅙",
                frac18: "⅛",
                frac23: "⅔",
                frac25: "⅖",
                frac34: "¾",
                frac35: "⅗",
                frac38: "⅜",
                frac45: "⅘",
                frac56: "⅚",
                frac58: "⅝",
                frac78: "⅞",
                frasl: "⁄",
                frown: "⌢",
                Fscr: "ℱ",
                fscr: "𝒻",
                gacute: "ǵ",
                Gamma: "Γ",
                gamma: "γ",
                Gammad: "Ϝ",
                gammad: "ϝ",
                gap: "⪆",
                Gbreve: "Ğ",
                gbreve: "ğ",
                Gcedil: "Ģ",
                Gcirc: "Ĝ",
                gcirc: "ĝ",
                Gcy: "Г",
                gcy: "г",
                Gdot: "Ġ",
                gdot: "ġ",
                gE: "≧",
                ge: "≥",
                gEl: "⪌",
                gel: "⋛",
                geq: "≥",
                geqq: "≧",
                geqslant: "⩾",
                ges: "⩾",
                gescc: "⪩",
                gesdot: "⪀",
                gesdoto: "⪂",
                gesdotol: "⪄",
                gesl: "⋛︀",
                gesles: "⪔",
                Gfr: "𝔊",
                gfr: "𝔤",
                Gg: "⋙",
                gg: "≫",
                ggg: "⋙",
                gimel: "ℷ",
                GJcy: "Ѓ",
                gjcy: "ѓ",
                gl: "≷",
                gla: "⪥",
                glE: "⪒",
                glj: "⪤",
                gnap: "⪊",
                gnapprox: "⪊",
                gnE: "≩",
                gne: "⪈",
                gneq: "⪈",
                gneqq: "≩",
                gnsim: "⋧",
                Gopf: "𝔾",
                gopf: "𝕘",
                grave: "`",
                GreaterEqual: "≥",
                GreaterEqualLess: "⋛",
                GreaterFullEqual: "≧",
                GreaterGreater: "⪢",
                GreaterLess: "≷",
                GreaterSlantEqual: "⩾",
                GreaterTilde: "≳",
                Gscr: "𝒢",
                gscr: "ℊ",
                gsim: "≳",
                gsime: "⪎",
                gsiml: "⪐",
                Gt: "≫",
                GT: ">",
                gt: ">",
                gtcc: "⪧",
                gtcir: "⩺",
                gtdot: "⋗",
                gtlPar: "⦕",
                gtquest: "⩼",
                gtrapprox: "⪆",
                gtrarr: "⥸",
                gtrdot: "⋗",
                gtreqless: "⋛",
                gtreqqless: "⪌",
                gtrless: "≷",
                gtrsim: "≳",
                gvertneqq: "≩︀",
                gvnE: "≩︀",
                Hacek: "ˇ",
                hairsp: " ",
                half: "½",
                hamilt: "ℋ",
                HARDcy: "Ъ",
                hardcy: "ъ",
                hArr: "⇔",
                harr: "↔",
                harrcir: "⥈",
                harrw: "↭",
                Hat: "^",
                hbar: "ℏ",
                Hcirc: "Ĥ",
                hcirc: "ĥ",
                hearts: "♥",
                heartsuit: "♥",
                hellip: "…",
                hercon: "⊹",
                Hfr: "ℌ",
                hfr: "𝔥",
                HilbertSpace: "ℋ",
                hksearow: "⤥",
                hkswarow: "⤦",
                hoarr: "⇿",
                homtht: "∻",
                hookleftarrow: "↩",
                hookrightarrow: "↪",
                Hopf: "ℍ",
                hopf: "𝕙",
                horbar: "―",
                HorizontalLine: "─",
                Hscr: "ℋ",
                hscr: "𝒽",
                hslash: "ℏ",
                Hstrok: "Ħ",
                hstrok: "ħ",
                HumpDownHump: "≎",
                HumpEqual: "≏",
                hybull: "⁃",
                hyphen: "‐",
                Iacute: "Í",
                iacute: "í",
                ic: "⁣",
                Icirc: "Î",
                icirc: "î",
                Icy: "И",
                icy: "и",
                Idot: "İ",
                IEcy: "Е",
                iecy: "е",
                iexcl: "¡",
                iff: "⇔",
                Ifr: "ℑ",
                ifr: "𝔦",
                Igrave: "Ì",
                igrave: "ì",
                ii: "ⅈ",
                iiiint: "⨌",
                iiint: "∭",
                iinfin: "⧜",
                iiota: "℩",
                IJlig: "Ĳ",
                ijlig: "ĳ",
                Im: "ℑ",
                Imacr: "Ī",
                imacr: "ī",
                image: "ℑ",
                ImaginaryI: "ⅈ",
                imagline: "ℐ",
                imagpart: "ℑ",
                imath: "ı",
                imof: "⊷",
                imped: "Ƶ",
                Implies: "⇒",
                in: "∈",
                incare: "℅",
                infin: "∞",
                infintie: "⧝",
                inodot: "ı",
                Int: "∬",
                int: "∫",
                intcal: "⊺",
                integers: "ℤ",
                Integral: "∫",
                intercal: "⊺",
                Intersection: "⋂",
                intlarhk: "⨗",
                intprod: "⨼",
                InvisibleComma: "⁣",
                InvisibleTimes: "⁢",
                IOcy: "Ё",
                iocy: "ё",
                Iogon: "Į",
                iogon: "į",
                Iopf: "𝕀",
                iopf: "𝕚",
                Iota: "Ι",
                iota: "ι",
                iprod: "⨼",
                iquest: "¿",
                Iscr: "ℐ",
                iscr: "𝒾",
                isin: "∈",
                isindot: "⋵",
                isinE: "⋹",
                isins: "⋴",
                isinsv: "⋳",
                isinv: "∈",
                it: "⁢",
                Itilde: "Ĩ",
                itilde: "ĩ",
                Iukcy: "І",
                iukcy: "і",
                Iuml: "Ï",
                iuml: "ï",
                Jcirc: "Ĵ",
                jcirc: "ĵ",
                Jcy: "Й",
                jcy: "й",
                Jfr: "𝔍",
                jfr: "𝔧",
                jmath: "ȷ",
                Jopf: "𝕁",
                jopf: "𝕛",
                Jscr: "𝒥",
                jscr: "𝒿",
                Jsercy: "Ј",
                jsercy: "ј",
                Jukcy: "Є",
                jukcy: "є",
                Kappa: "Κ",
                kappa: "κ",
                kappav: "ϰ",
                Kcedil: "Ķ",
                kcedil: "ķ",
                Kcy: "К",
                kcy: "к",
                Kfr: "𝔎",
                kfr: "𝔨",
                kgreen: "ĸ",
                KHcy: "Х",
                khcy: "х",
                KJcy: "Ќ",
                kjcy: "ќ",
                Kopf: "𝕂",
                kopf: "𝕜",
                Kscr: "𝒦",
                kscr: "𝓀",
                lAarr: "⇚",
                Lacute: "Ĺ",
                lacute: "ĺ",
                laemptyv: "⦴",
                lagran: "ℒ",
                Lambda: "Λ",
                lambda: "λ",
                Lang: "⟪",
                lang: "⟨",
                langd: "⦑",
                langle: "⟨",
                lap: "⪅",
                Laplacetrf: "ℒ",
                laquo: "«",
                Larr: "↞",
                lArr: "⇐",
                larr: "←",
                larrb: "⇤",
                larrbfs: "⤟",
                larrfs: "⤝",
                larrhk: "↩",
                larrlp: "↫",
                larrpl: "⤹",
                larrsim: "⥳",
                larrtl: "↢",
                lat: "⪫",
                lAtail: "⤛",
                latail: "⤙",
                late: "⪭",
                lates: "⪭︀",
                lBarr: "⤎",
                lbarr: "⤌",
                lbbrk: "❲",
                lbrace: "{",
                lbrack: "[",
                lbrke: "⦋",
                lbrksld: "⦏",
                lbrkslu: "⦍",
                Lcaron: "Ľ",
                lcaron: "ľ",
                Lcedil: "Ļ",
                lcedil: "ļ",
                lceil: "⌈",
                lcub: "{",
                Lcy: "Л",
                lcy: "л",
                ldca: "⤶",
                ldquo: "“",
                ldquor: "„",
                ldrdhar: "⥧",
                ldrushar: "⥋",
                ldsh: "↲",
                lE: "≦",
                le: "≤",
                LeftAngleBracket: "⟨",
                LeftArrow: "←",
                Leftarrow: "⇐",
                leftarrow: "←",
                LeftArrowBar: "⇤",
                LeftArrowRightArrow: "⇆",
                leftarrowtail: "↢",
                LeftCeiling: "⌈",
                LeftDoubleBracket: "⟦",
                LeftDownTeeVector: "⥡",
                LeftDownVector: "⇃",
                LeftDownVectorBar: "⥙",
                LeftFloor: "⌊",
                leftharpoondown: "↽",
                leftharpoonup: "↼",
                leftleftarrows: "⇇",
                LeftRightArrow: "↔",
                Leftrightarrow: "⇔",
                leftrightarrow: "↔",
                leftrightarrows: "⇆",
                leftrightharpoons: "⇋",
                leftrightsquigarrow: "↭",
                LeftRightVector: "⥎",
                LeftTee: "⊣",
                LeftTeeArrow: "↤",
                LeftTeeVector: "⥚",
                leftthreetimes: "⋋",
                LeftTriangle: "⊲",
                LeftTriangleBar: "⧏",
                LeftTriangleEqual: "⊴",
                LeftUpDownVector: "⥑",
                LeftUpTeeVector: "⥠",
                LeftUpVector: "↿",
                LeftUpVectorBar: "⥘",
                LeftVector: "↼",
                LeftVectorBar: "⥒",
                lEg: "⪋",
                leg: "⋚",
                leq: "≤",
                leqq: "≦",
                leqslant: "⩽",
                les: "⩽",
                lescc: "⪨",
                lesdot: "⩿",
                lesdoto: "⪁",
                lesdotor: "⪃",
                lesg: "⋚︀",
                lesges: "⪓",
                lessapprox: "⪅",
                lessdot: "⋖",
                lesseqgtr: "⋚",
                lesseqqgtr: "⪋",
                LessEqualGreater: "⋚",
                LessFullEqual: "≦",
                LessGreater: "≶",
                lessgtr: "≶",
                LessLess: "⪡",
                lesssim: "≲",
                LessSlantEqual: "⩽",
                LessTilde: "≲",
                lfisht: "⥼",
                lfloor: "⌊",
                Lfr: "𝔏",
                lfr: "𝔩",
                lg: "≶",
                lgE: "⪑",
                lHar: "⥢",
                lhard: "↽",
                lharu: "↼",
                lharul: "⥪",
                lhblk: "▄",
                LJcy: "Љ",
                ljcy: "љ",
                Ll: "⋘",
                ll: "≪",
                llarr: "⇇",
                llcorner: "⌞",
                Lleftarrow: "⇚",
                llhard: "⥫",
                lltri: "◺",
                Lmidot: "Ŀ",
                lmidot: "ŀ",
                lmoust: "⎰",
                lmoustache: "⎰",
                lnap: "⪉",
                lnapprox: "⪉",
                lnE: "≨",
                lne: "⪇",
                lneq: "⪇",
                lneqq: "≨",
                lnsim: "⋦",
                loang: "⟬",
                loarr: "⇽",
                lobrk: "⟦",
                LongLeftArrow: "⟵",
                Longleftarrow: "⟸",
                longleftarrow: "⟵",
                LongLeftRightArrow: "⟷",
                Longleftrightarrow: "⟺",
                longleftrightarrow: "⟷",
                longmapsto: "⟼",
                LongRightArrow: "⟶",
                Longrightarrow: "⟹",
                longrightarrow: "⟶",
                looparrowleft: "↫",
                looparrowright: "↬",
                lopar: "⦅",
                Lopf: "𝕃",
                lopf: "𝕝",
                loplus: "⨭",
                lotimes: "⨴",
                lowast: "∗",
                lowbar: "_",
                LowerLeftArrow: "↙",
                LowerRightArrow: "↘",
                loz: "◊",
                lozenge: "◊",
                lozf: "⧫",
                lpar: "(",
                lparlt: "⦓",
                lrarr: "⇆",
                lrcorner: "⌟",
                lrhar: "⇋",
                lrhard: "⥭",
                lrm: "‎",
                lrtri: "⊿",
                lsaquo: "‹",
                Lscr: "ℒ",
                lscr: "𝓁",
                Lsh: "↰",
                lsh: "↰",
                lsim: "≲",
                lsime: "⪍",
                lsimg: "⪏",
                lsqb: "[",
                lsquo: "‘",
                lsquor: "‚",
                Lstrok: "Ł",
                lstrok: "ł",
                Lt: "≪",
                LT: "<",
                lt: "<",
                ltcc: "⪦",
                ltcir: "⩹",
                ltdot: "⋖",
                lthree: "⋋",
                ltimes: "⋉",
                ltlarr: "⥶",
                ltquest: "⩻",
                ltri: "◃",
                ltrie: "⊴",
                ltrif: "◂",
                ltrPar: "⦖",
                lurdshar: "⥊",
                luruhar: "⥦",
                lvertneqq: "≨︀",
                lvnE: "≨︀",
                macr: "¯",
                male: "♂",
                malt: "✠",
                maltese: "✠",
                Map: "⤅",
                map: "↦",
                mapsto: "↦",
                mapstodown: "↧",
                mapstoleft: "↤",
                mapstoup: "↥",
                marker: "▮",
                mcomma: "⨩",
                Mcy: "М",
                mcy: "м",
                mdash: "—",
                mDDot: "∺",
                measuredangle: "∡",
                MediumSpace: " ",
                Mellintrf: "ℳ",
                Mfr: "𝔐",
                mfr: "𝔪",
                mho: "℧",
                micro: "µ",
                mid: "∣",
                midast: "*",
                midcir: "⫰",
                middot: "·",
                minus: "−",
                minusb: "⊟",
                minusd: "∸",
                minusdu: "⨪",
                MinusPlus: "∓",
                mlcp: "⫛",
                mldr: "…",
                mnplus: "∓",
                models: "⊧",
                Mopf: "𝕄",
                mopf: "𝕞",
                mp: "∓",
                Mscr: "ℳ",
                mscr: "𝓂",
                mstpos: "∾",
                Mu: "Μ",
                mu: "μ",
                multimap: "⊸",
                mumap: "⊸",
                nabla: "∇",
                Nacute: "Ń",
                nacute: "ń",
                nang: "∠⃒",
                nap: "≉",
                napE: "⩰̸",
                napid: "≋̸",
                napos: "ŉ",
                napprox: "≉",
                natur: "♮",
                natural: "♮",
                naturals: "ℕ",
                nbsp: " ",
                nbump: "≎̸",
                nbumpe: "≏̸",
                ncap: "⩃",
                Ncaron: "Ň",
                ncaron: "ň",
                Ncedil: "Ņ",
                ncedil: "ņ",
                ncong: "≇",
                ncongdot: "⩭̸",
                ncup: "⩂",
                Ncy: "Н",
                ncy: "н",
                ndash: "–",
                ne: "≠",
                nearhk: "⤤",
                neArr: "⇗",
                nearr: "↗",
                nearrow: "↗",
                nedot: "≐̸",
                NegativeMediumSpace: "​",
                NegativeThickSpace: "​",
                NegativeThinSpace: "​",
                NegativeVeryThinSpace: "​",
                nequiv: "≢",
                nesear: "⤨",
                nesim: "≂̸",
                NestedGreaterGreater: "≫",
                NestedLessLess: "≪",
                NewLine: "\n",
                nexist: "∄",
                nexists: "∄",
                Nfr: "𝔑",
                nfr: "𝔫",
                ngE: "≧̸",
                nge: "≱",
                ngeq: "≱",
                ngeqq: "≧̸",
                ngeqslant: "⩾̸",
                nges: "⩾̸",
                nGg: "⋙̸",
                ngsim: "≵",
                nGt: "≫⃒",
                ngt: "≯",
                ngtr: "≯",
                nGtv: "≫̸",
                nhArr: "⇎",
                nharr: "↮",
                nhpar: "⫲",
                ni: "∋",
                nis: "⋼",
                nisd: "⋺",
                niv: "∋",
                NJcy: "Њ",
                njcy: "њ",
                nlArr: "⇍",
                nlarr: "↚",
                nldr: "‥",
                nlE: "≦̸",
                nle: "≰",
                nLeftarrow: "⇍",
                nleftarrow: "↚",
                nLeftrightarrow: "⇎",
                nleftrightarrow: "↮",
                nleq: "≰",
                nleqq: "≦̸",
                nleqslant: "⩽̸",
                nles: "⩽̸",
                nless: "≮",
                nLl: "⋘̸",
                nlsim: "≴",
                nLt: "≪⃒",
                nlt: "≮",
                nltri: "⋪",
                nltrie: "⋬",
                nLtv: "≪̸",
                nmid: "∤",
                NoBreak: "⁠",
                NonBreakingSpace: " ",
                Nopf: "ℕ",
                nopf: "𝕟",
                Not: "⫬",
                not: "¬",
                NotCongruent: "≢",
                NotCupCap: "≭",
                NotDoubleVerticalBar: "∦",
                NotElement: "∉",
                NotEqual: "≠",
                NotEqualTilde: "≂̸",
                NotExists: "∄",
                NotGreater: "≯",
                NotGreaterEqual: "≱",
                NotGreaterFullEqual: "≧̸",
                NotGreaterGreater: "≫̸",
                NotGreaterLess: "≹",
                NotGreaterSlantEqual: "⩾̸",
                NotGreaterTilde: "≵",
                NotHumpDownHump: "≎̸",
                NotHumpEqual: "≏̸",
                notin: "∉",
                notindot: "⋵̸",
                notinE: "⋹̸",
                notinva: "∉",
                notinvb: "⋷",
                notinvc: "⋶",
                NotLeftTriangle: "⋪",
                NotLeftTriangleBar: "⧏̸",
                NotLeftTriangleEqual: "⋬",
                NotLess: "≮",
                NotLessEqual: "≰",
                NotLessGreater: "≸",
                NotLessLess: "≪̸",
                NotLessSlantEqual: "⩽̸",
                NotLessTilde: "≴",
                NotNestedGreaterGreater: "⪢̸",
                NotNestedLessLess: "⪡̸",
                notni: "∌",
                notniva: "∌",
                notnivb: "⋾",
                notnivc: "⋽",
                NotPrecedes: "⊀",
                NotPrecedesEqual: "⪯̸",
                NotPrecedesSlantEqual: "⋠",
                NotReverseElement: "∌",
                NotRightTriangle: "⋫",
                NotRightTriangleBar: "⧐̸",
                NotRightTriangleEqual: "⋭",
                NotSquareSubset: "⊏̸",
                NotSquareSubsetEqual: "⋢",
                NotSquareSuperset: "⊐̸",
                NotSquareSupersetEqual: "⋣",
                NotSubset: "⊂⃒",
                NotSubsetEqual: "⊈",
                NotSucceeds: "⊁",
                NotSucceedsEqual: "⪰̸",
                NotSucceedsSlantEqual: "⋡",
                NotSucceedsTilde: "≿̸",
                NotSuperset: "⊃⃒",
                NotSupersetEqual: "⊉",
                NotTilde: "≁",
                NotTildeEqual: "≄",
                NotTildeFullEqual: "≇",
                NotTildeTilde: "≉",
                NotVerticalBar: "∤",
                npar: "∦",
                nparallel: "∦",
                nparsl: "⫽⃥",
                npart: "∂̸",
                npolint: "⨔",
                npr: "⊀",
                nprcue: "⋠",
                npre: "⪯̸",
                nprec: "⊀",
                npreceq: "⪯̸",
                nrArr: "⇏",
                nrarr: "↛",
                nrarrc: "⤳̸",
                nrarrw: "↝̸",
                nRightarrow: "⇏",
                nrightarrow: "↛",
                nrtri: "⋫",
                nrtrie: "⋭",
                nsc: "⊁",
                nsccue: "⋡",
                nsce: "⪰̸",
                Nscr: "𝒩",
                nscr: "𝓃",
                nshortmid: "∤",
                nshortparallel: "∦",
                nsim: "≁",
                nsime: "≄",
                nsimeq: "≄",
                nsmid: "∤",
                nspar: "∦",
                nsqsube: "⋢",
                nsqsupe: "⋣",
                nsub: "⊄",
                nsubE: "⫅̸",
                nsube: "⊈",
                nsubset: "⊂⃒",
                nsubseteq: "⊈",
                nsubseteqq: "⫅̸",
                nsucc: "⊁",
                nsucceq: "⪰̸",
                nsup: "⊅",
                nsupE: "⫆̸",
                nsupe: "⊉",
                nsupset: "⊃⃒",
                nsupseteq: "⊉",
                nsupseteqq: "⫆̸",
                ntgl: "≹",
                Ntilde: "Ñ",
                ntilde: "ñ",
                ntlg: "≸",
                ntriangleleft: "⋪",
                ntrianglelefteq: "⋬",
                ntriangleright: "⋫",
                ntrianglerighteq: "⋭",
                Nu: "Ν",
                nu: "ν",
                num: "#",
                numero: "№",
                numsp: " ",
                nvap: "≍⃒",
                nVDash: "⊯",
                nVdash: "⊮",
                nvDash: "⊭",
                nvdash: "⊬",
                nvge: "≥⃒",
                nvgt: ">⃒",
                nvHarr: "⤄",
                nvinfin: "⧞",
                nvlArr: "⤂",
                nvle: "≤⃒",
                nvlt: "<⃒",
                nvltrie: "⊴⃒",
                nvrArr: "⤃",
                nvrtrie: "⊵⃒",
                nvsim: "∼⃒",
                nwarhk: "⤣",
                nwArr: "⇖",
                nwarr: "↖",
                nwarrow: "↖",
                nwnear: "⤧",
                Oacute: "Ó",
                oacute: "ó",
                oast: "⊛",
                ocir: "⊚",
                Ocirc: "Ô",
                ocirc: "ô",
                Ocy: "О",
                ocy: "о",
                odash: "⊝",
                Odblac: "Ő",
                odblac: "ő",
                odiv: "⨸",
                odot: "⊙",
                odsold: "⦼",
                OElig: "Œ",
                oelig: "œ",
                ofcir: "⦿",
                Ofr: "𝔒",
                ofr: "𝔬",
                ogon: "˛",
                Ograve: "Ò",
                ograve: "ò",
                ogt: "⧁",
                ohbar: "⦵",
                ohm: "Ω",
                oint: "∮",
                olarr: "↺",
                olcir: "⦾",
                olcross: "⦻",
                oline: "‾",
                olt: "⧀",
                Omacr: "Ō",
                omacr: "ō",
                Omega: "Ω",
                omega: "ω",
                Omicron: "Ο",
                omicron: "ο",
                omid: "⦶",
                ominus: "⊖",
                Oopf: "𝕆",
                oopf: "𝕠",
                opar: "⦷",
                OpenCurlyDoubleQuote: "“",
                OpenCurlyQuote: "‘",
                operp: "⦹",
                oplus: "⊕",
                Or: "⩔",
                or: "∨",
                orarr: "↻",
                ord: "⩝",
                order: "ℴ",
                orderof: "ℴ",
                ordf: "ª",
                ordm: "º",
                origof: "⊶",
                oror: "⩖",
                orslope: "⩗",
                orv: "⩛",
                oS: "Ⓢ",
                Oscr: "𝒪",
                oscr: "ℴ",
                Oslash: "Ø",
                oslash: "ø",
                osol: "⊘",
                Otilde: "Õ",
                otilde: "õ",
                Otimes: "⨷",
                otimes: "⊗",
                otimesas: "⨶",
                Ouml: "Ö",
                ouml: "ö",
                ovbar: "⌽",
                OverBar: "‾",
                OverBrace: "⏞",
                OverBracket: "⎴",
                OverParenthesis: "⏜",
                par: "∥",
                para: "¶",
                parallel: "∥",
                parsim: "⫳",
                parsl: "⫽",
                part: "∂",
                PartialD: "∂",
                Pcy: "П",
                pcy: "п",
                percnt: "%",
                period: ".",
                permil: "‰",
                perp: "⊥",
                pertenk: "‱",
                Pfr: "𝔓",
                pfr: "𝔭",
                Phi: "Φ",
                phi: "φ",
                phiv: "ϕ",
                phmmat: "ℳ",
                phone: "☎",
                Pi: "Π",
                pi: "π",
                pitchfork: "⋔",
                piv: "ϖ",
                planck: "ℏ",
                planckh: "ℎ",
                plankv: "ℏ",
                plus: "+",
                plusacir: "⨣",
                plusb: "⊞",
                pluscir: "⨢",
                plusdo: "∔",
                plusdu: "⨥",
                pluse: "⩲",
                PlusMinus: "±",
                plusmn: "±",
                plussim: "⨦",
                plustwo: "⨧",
                pm: "±",
                Poincareplane: "ℌ",
                pointint: "⨕",
                Popf: "ℙ",
                popf: "𝕡",
                pound: "£",
                Pr: "⪻",
                pr: "≺",
                prap: "⪷",
                prcue: "≼",
                prE: "⪳",
                pre: "⪯",
                prec: "≺",
                precapprox: "⪷",
                preccurlyeq: "≼",
                Precedes: "≺",
                PrecedesEqual: "⪯",
                PrecedesSlantEqual: "≼",
                PrecedesTilde: "≾",
                preceq: "⪯",
                precnapprox: "⪹",
                precneqq: "⪵",
                precnsim: "⋨",
                precsim: "≾",
                Prime: "″",
                prime: "′",
                primes: "ℙ",
                prnap: "⪹",
                prnE: "⪵",
                prnsim: "⋨",
                prod: "∏",
                Product: "∏",
                profalar: "⌮",
                profline: "⌒",
                profsurf: "⌓",
                prop: "∝",
                Proportion: "∷",
                Proportional: "∝",
                propto: "∝",
                prsim: "≾",
                prurel: "⊰",
                Pscr: "𝒫",
                pscr: "𝓅",
                Psi: "Ψ",
                psi: "ψ",
                puncsp: " ",
                Qfr: "𝔔",
                qfr: "𝔮",
                qint: "⨌",
                Qopf: "ℚ",
                qopf: "𝕢",
                qprime: "⁗",
                Qscr: "𝒬",
                qscr: "𝓆",
                quaternions: "ℍ",
                quatint: "⨖",
                quest: "?",
                questeq: "≟",
                QUOT: '"',
                quot: '"',
                rAarr: "⇛",
                race: "∽̱",
                Racute: "Ŕ",
                racute: "ŕ",
                radic: "√",
                raemptyv: "⦳",
                Rang: "⟫",
                rang: "⟩",
                rangd: "⦒",
                range: "⦥",
                rangle: "⟩",
                raquo: "»",
                Rarr: "↠",
                rArr: "⇒",
                rarr: "→",
                rarrap: "⥵",
                rarrb: "⇥",
                rarrbfs: "⤠",
                rarrc: "⤳",
                rarrfs: "⤞",
                rarrhk: "↪",
                rarrlp: "↬",
                rarrpl: "⥅",
                rarrsim: "⥴",
                Rarrtl: "⤖",
                rarrtl: "↣",
                rarrw: "↝",
                rAtail: "⤜",
                ratail: "⤚",
                ratio: "∶",
                rationals: "ℚ",
                RBarr: "⤐",
                rBarr: "⤏",
                rbarr: "⤍",
                rbbrk: "❳",
                rbrace: "}",
                rbrack: "]",
                rbrke: "⦌",
                rbrksld: "⦎",
                rbrkslu: "⦐",
                Rcaron: "Ř",
                rcaron: "ř",
                Rcedil: "Ŗ",
                rcedil: "ŗ",
                rceil: "⌉",
                rcub: "}",
                Rcy: "Р",
                rcy: "р",
                rdca: "⤷",
                rdldhar: "⥩",
                rdquo: "”",
                rdquor: "”",
                rdsh: "↳",
                Re: "ℜ",
                real: "ℜ",
                realine: "ℛ",
                realpart: "ℜ",
                reals: "ℝ",
                rect: "▭",
                REG: "®",
                reg: "®",
                ReverseElement: "∋",
                ReverseEquilibrium: "⇋",
                ReverseUpEquilibrium: "⥯",
                rfisht: "⥽",
                rfloor: "⌋",
                Rfr: "ℜ",
                rfr: "𝔯",
                rHar: "⥤",
                rhard: "⇁",
                rharu: "⇀",
                rharul: "⥬",
                Rho: "Ρ",
                rho: "ρ",
                rhov: "ϱ",
                RightAngleBracket: "⟩",
                RightArrow: "→",
                Rightarrow: "⇒",
                rightarrow: "→",
                RightArrowBar: "⇥",
                RightArrowLeftArrow: "⇄",
                rightarrowtail: "↣",
                RightCeiling: "⌉",
                RightDoubleBracket: "⟧",
                RightDownTeeVector: "⥝",
                RightDownVector: "⇂",
                RightDownVectorBar: "⥕",
                RightFloor: "⌋",
                rightharpoondown: "⇁",
                rightharpoonup: "⇀",
                rightleftarrows: "⇄",
                rightleftharpoons: "⇌",
                rightrightarrows: "⇉",
                rightsquigarrow: "↝",
                RightTee: "⊢",
                RightTeeArrow: "↦",
                RightTeeVector: "⥛",
                rightthreetimes: "⋌",
                RightTriangle: "⊳",
                RightTriangleBar: "⧐",
                RightTriangleEqual: "⊵",
                RightUpDownVector: "⥏",
                RightUpTeeVector: "⥜",
                RightUpVector: "↾",
                RightUpVectorBar: "⥔",
                RightVector: "⇀",
                RightVectorBar: "⥓",
                ring: "˚",
                risingdotseq: "≓",
                rlarr: "⇄",
                rlhar: "⇌",
                rlm: "‏",
                rmoust: "⎱",
                rmoustache: "⎱",
                rnmid: "⫮",
                roang: "⟭",
                roarr: "⇾",
                robrk: "⟧",
                ropar: "⦆",
                Ropf: "ℝ",
                ropf: "𝕣",
                roplus: "⨮",
                rotimes: "⨵",
                RoundImplies: "⥰",
                rpar: ")",
                rpargt: "⦔",
                rppolint: "⨒",
                rrarr: "⇉",
                Rrightarrow: "⇛",
                rsaquo: "›",
                Rscr: "ℛ",
                rscr: "𝓇",
                Rsh: "↱",
                rsh: "↱",
                rsqb: "]",
                rsquo: "’",
                rsquor: "’",
                rthree: "⋌",
                rtimes: "⋊",
                rtri: "▹",
                rtrie: "⊵",
                rtrif: "▸",
                rtriltri: "⧎",
                RuleDelayed: "⧴",
                ruluhar: "⥨",
                rx: "℞",
                Sacute: "Ś",
                sacute: "ś",
                sbquo: "‚",
                Sc: "⪼",
                sc: "≻",
                scap: "⪸",
                Scaron: "Š",
                scaron: "š",
                sccue: "≽",
                scE: "⪴",
                sce: "⪰",
                Scedil: "Ş",
                scedil: "ş",
                Scirc: "Ŝ",
                scirc: "ŝ",
                scnap: "⪺",
                scnE: "⪶",
                scnsim: "⋩",
                scpolint: "⨓",
                scsim: "≿",
                Scy: "С",
                scy: "с",
                sdot: "⋅",
                sdotb: "⊡",
                sdote: "⩦",
                searhk: "⤥",
                seArr: "⇘",
                searr: "↘",
                searrow: "↘",
                sect: "§",
                semi: ";",
                seswar: "⤩",
                setminus: "∖",
                setmn: "∖",
                sext: "✶",
                Sfr: "𝔖",
                sfr: "𝔰",
                sfrown: "⌢",
                sharp: "♯",
                SHCHcy: "Щ",
                shchcy: "щ",
                SHcy: "Ш",
                shcy: "ш",
                ShortDownArrow: "↓",
                ShortLeftArrow: "←",
                shortmid: "∣",
                shortparallel: "∥",
                ShortRightArrow: "→",
                ShortUpArrow: "↑",
                shy: "­",
                Sigma: "Σ",
                sigma: "σ",
                sigmaf: "ς",
                sigmav: "ς",
                sim: "∼",
                simdot: "⩪",
                sime: "≃",
                simeq: "≃",
                simg: "⪞",
                simgE: "⪠",
                siml: "⪝",
                simlE: "⪟",
                simne: "≆",
                simplus: "⨤",
                simrarr: "⥲",
                slarr: "←",
                SmallCircle: "∘",
                smallsetminus: "∖",
                smashp: "⨳",
                smeparsl: "⧤",
                smid: "∣",
                smile: "⌣",
                smt: "⪪",
                smte: "⪬",
                smtes: "⪬︀",
                SOFTcy: "Ь",
                softcy: "ь",
                sol: "/",
                solb: "⧄",
                solbar: "⌿",
                Sopf: "𝕊",
                sopf: "𝕤",
                spades: "♠",
                spadesuit: "♠",
                spar: "∥",
                sqcap: "⊓",
                sqcaps: "⊓︀",
                sqcup: "⊔",
                sqcups: "⊔︀",
                Sqrt: "√",
                sqsub: "⊏",
                sqsube: "⊑",
                sqsubset: "⊏",
                sqsubseteq: "⊑",
                sqsup: "⊐",
                sqsupe: "⊒",
                sqsupset: "⊐",
                sqsupseteq: "⊒",
                squ: "□",
                Square: "□",
                square: "□",
                SquareIntersection: "⊓",
                SquareSubset: "⊏",
                SquareSubsetEqual: "⊑",
                SquareSuperset: "⊐",
                SquareSupersetEqual: "⊒",
                SquareUnion: "⊔",
                squarf: "▪",
                squf: "▪",
                srarr: "→",
                Sscr: "𝒮",
                sscr: "𝓈",
                ssetmn: "∖",
                ssmile: "⌣",
                sstarf: "⋆",
                Star: "⋆",
                star: "☆",
                starf: "★",
                straightepsilon: "ϵ",
                straightphi: "ϕ",
                strns: "¯",
                Sub: "⋐",
                sub: "⊂",
                subdot: "⪽",
                subE: "⫅",
                sube: "⊆",
                subedot: "⫃",
                submult: "⫁",
                subnE: "⫋",
                subne: "⊊",
                subplus: "⪿",
                subrarr: "⥹",
                Subset: "⋐",
                subset: "⊂",
                subseteq: "⊆",
                subseteqq: "⫅",
                SubsetEqual: "⊆",
                subsetneq: "⊊",
                subsetneqq: "⫋",
                subsim: "⫇",
                subsub: "⫕",
                subsup: "⫓",
                succ: "≻",
                succapprox: "⪸",
                succcurlyeq: "≽",
                Succeeds: "≻",
                SucceedsEqual: "⪰",
                SucceedsSlantEqual: "≽",
                SucceedsTilde: "≿",
                succeq: "⪰",
                succnapprox: "⪺",
                succneqq: "⪶",
                succnsim: "⋩",
                succsim: "≿",
                SuchThat: "∋",
                Sum: "∑",
                sum: "∑",
                sung: "♪",
                Sup: "⋑",
                sup: "⊃",
                sup1: "¹",
                sup2: "²",
                sup3: "³",
                supdot: "⪾",
                supdsub: "⫘",
                supE: "⫆",
                supe: "⊇",
                supedot: "⫄",
                Superset: "⊃",
                SupersetEqual: "⊇",
                suphsol: "⟉",
                suphsub: "⫗",
                suplarr: "⥻",
                supmult: "⫂",
                supnE: "⫌",
                supne: "⊋",
                supplus: "⫀",
                Supset: "⋑",
                supset: "⊃",
                supseteq: "⊇",
                supseteqq: "⫆",
                supsetneq: "⊋",
                supsetneqq: "⫌",
                supsim: "⫈",
                supsub: "⫔",
                supsup: "⫖",
                swarhk: "⤦",
                swArr: "⇙",
                swarr: "↙",
                swarrow: "↙",
                swnwar: "⤪",
                szlig: "ß",
                Tab: "\t",
                target: "⌖",
                Tau: "Τ",
                tau: "τ",
                tbrk: "⎴",
                Tcaron: "Ť",
                tcaron: "ť",
                Tcedil: "Ţ",
                tcedil: "ţ",
                Tcy: "Т",
                tcy: "т",
                tdot: "⃛",
                telrec: "⌕",
                Tfr: "𝔗",
                tfr: "𝔱",
                there4: "∴",
                Therefore: "∴",
                therefore: "∴",
                Theta: "Θ",
                theta: "θ",
                thetasym: "ϑ",
                thetav: "ϑ",
                thickapprox: "≈",
                thicksim: "∼",
                ThickSpace: "  ",
                thinsp: " ",
                ThinSpace: " ",
                thkap: "≈",
                thksim: "∼",
                THORN: "Þ",
                thorn: "þ",
                Tilde: "∼",
                tilde: "˜",
                TildeEqual: "≃",
                TildeFullEqual: "≅",
                TildeTilde: "≈",
                times: "×",
                timesb: "⊠",
                timesbar: "⨱",
                timesd: "⨰",
                tint: "∭",
                toea: "⤨",
                top: "⊤",
                topbot: "⌶",
                topcir: "⫱",
                Topf: "𝕋",
                topf: "𝕥",
                topfork: "⫚",
                tosa: "⤩",
                tprime: "‴",
                TRADE: "™",
                trade: "™",
                triangle: "▵",
                triangledown: "▿",
                triangleleft: "◃",
                trianglelefteq: "⊴",
                triangleq: "≜",
                triangleright: "▹",
                trianglerighteq: "⊵",
                tridot: "◬",
                trie: "≜",
                triminus: "⨺",
                TripleDot: "⃛",
                triplus: "⨹",
                trisb: "⧍",
                tritime: "⨻",
                trpezium: "⏢",
                Tscr: "𝒯",
                tscr: "𝓉",
                TScy: "Ц",
                tscy: "ц",
                TSHcy: "Ћ",
                tshcy: "ћ",
                Tstrok: "Ŧ",
                tstrok: "ŧ",
                twixt: "≬",
                twoheadleftarrow: "↞",
                twoheadrightarrow: "↠",
                Uacute: "Ú",
                uacute: "ú",
                Uarr: "↟",
                uArr: "⇑",
                uarr: "↑",
                Uarrocir: "⥉",
                Ubrcy: "Ў",
                ubrcy: "ў",
                Ubreve: "Ŭ",
                ubreve: "ŭ",
                Ucirc: "Û",
                ucirc: "û",
                Ucy: "У",
                ucy: "у",
                udarr: "⇅",
                Udblac: "Ű",
                udblac: "ű",
                udhar: "⥮",
                ufisht: "⥾",
                Ufr: "𝔘",
                ufr: "𝔲",
                Ugrave: "Ù",
                ugrave: "ù",
                uHar: "⥣",
                uharl: "↿",
                uharr: "↾",
                uhblk: "▀",
                ulcorn: "⌜",
                ulcorner: "⌜",
                ulcrop: "⌏",
                ultri: "◸",
                Umacr: "Ū",
                umacr: "ū",
                uml: "¨",
                UnderBar: "_",
                UnderBrace: "⏟",
                UnderBracket: "⎵",
                UnderParenthesis: "⏝",
                Union: "⋃",
                UnionPlus: "⊎",
                Uogon: "Ų",
                uogon: "ų",
                Uopf: "𝕌",
                uopf: "𝕦",
                UpArrow: "↑",
                Uparrow: "⇑",
                uparrow: "↑",
                UpArrowBar: "⤒",
                UpArrowDownArrow: "⇅",
                UpDownArrow: "↕",
                Updownarrow: "⇕",
                updownarrow: "↕",
                UpEquilibrium: "⥮",
                upharpoonleft: "↿",
                upharpoonright: "↾",
                uplus: "⊎",
                UpperLeftArrow: "↖",
                UpperRightArrow: "↗",
                Upsi: "ϒ",
                upsi: "υ",
                upsih: "ϒ",
                Upsilon: "Υ",
                upsilon: "υ",
                UpTee: "⊥",
                UpTeeArrow: "↥",
                upuparrows: "⇈",
                urcorn: "⌝",
                urcorner: "⌝",
                urcrop: "⌎",
                Uring: "Ů",
                uring: "ů",
                urtri: "◹",
                Uscr: "𝒰",
                uscr: "𝓊",
                utdot: "⋰",
                Utilde: "Ũ",
                utilde: "ũ",
                utri: "▵",
                utrif: "▴",
                uuarr: "⇈",
                Uuml: "Ü",
                uuml: "ü",
                uwangle: "⦧",
                vangrt: "⦜",
                varepsilon: "ϵ",
                varkappa: "ϰ",
                varnothing: "∅",
                varphi: "ϕ",
                varpi: "ϖ",
                varpropto: "∝",
                vArr: "⇕",
                varr: "↕",
                varrho: "ϱ",
                varsigma: "ς",
                varsubsetneq: "⊊︀",
                varsubsetneqq: "⫋︀",
                varsupsetneq: "⊋︀",
                varsupsetneqq: "⫌︀",
                vartheta: "ϑ",
                vartriangleleft: "⊲",
                vartriangleright: "⊳",
                Vbar: "⫫",
                vBar: "⫨",
                vBarv: "⫩",
                Vcy: "В",
                vcy: "в",
                VDash: "⊫",
                Vdash: "⊩",
                vDash: "⊨",
                vdash: "⊢",
                Vdashl: "⫦",
                Vee: "⋁",
                vee: "∨",
                veebar: "⊻",
                veeeq: "≚",
                vellip: "⋮",
                Verbar: "‖",
                verbar: "|",
                Vert: "‖",
                vert: "|",
                VerticalBar: "∣",
                VerticalLine: "|",
                VerticalSeparator: "❘",
                VerticalTilde: "≀",
                VeryThinSpace: " ",
                Vfr: "𝔙",
                vfr: "𝔳",
                vltri: "⊲",
                vnsub: "⊂⃒",
                vnsup: "⊃⃒",
                Vopf: "𝕍",
                vopf: "𝕧",
                vprop: "∝",
                vrtri: "⊳",
                Vscr: "𝒱",
                vscr: "𝓋",
                vsubnE: "⫋︀",
                vsubne: "⊊︀",
                vsupnE: "⫌︀",
                vsupne: "⊋︀",
                Vvdash: "⊪",
                vzigzag: "⦚",
                Wcirc: "Ŵ",
                wcirc: "ŵ",
                wedbar: "⩟",
                Wedge: "⋀",
                wedge: "∧",
                wedgeq: "≙",
                weierp: "℘",
                Wfr: "𝔚",
                wfr: "𝔴",
                Wopf: "𝕎",
                wopf: "𝕨",
                wp: "℘",
                wr: "≀",
                wreath: "≀",
                Wscr: "𝒲",
                wscr: "𝓌",
                xcap: "⋂",
                xcirc: "◯",
                xcup: "⋃",
                xdtri: "▽",
                Xfr: "𝔛",
                xfr: "𝔵",
                xhArr: "⟺",
                xharr: "⟷",
                Xi: "Ξ",
                xi: "ξ",
                xlArr: "⟸",
                xlarr: "⟵",
                xmap: "⟼",
                xnis: "⋻",
                xodot: "⨀",
                Xopf: "𝕏",
                xopf: "𝕩",
                xoplus: "⨁",
                xotime: "⨂",
                xrArr: "⟹",
                xrarr: "⟶",
                Xscr: "𝒳",
                xscr: "𝓍",
                xsqcup: "⨆",
                xuplus: "⨄",
                xutri: "△",
                xvee: "⋁",
                xwedge: "⋀",
                Yacute: "Ý",
                yacute: "ý",
                YAcy: "Я",
                yacy: "я",
                Ycirc: "Ŷ",
                ycirc: "ŷ",
                Ycy: "Ы",
                ycy: "ы",
                yen: "¥",
                Yfr: "𝔜",
                yfr: "𝔶",
                YIcy: "Ї",
                yicy: "ї",
                Yopf: "𝕐",
                yopf: "𝕪",
                Yscr: "𝒴",
                yscr: "𝓎",
                YUcy: "Ю",
                yucy: "ю",
                Yuml: "Ÿ",
                yuml: "ÿ",
                Zacute: "Ź",
                zacute: "ź",
                Zcaron: "Ž",
                zcaron: "ž",
                Zcy: "З",
                zcy: "з",
                Zdot: "Ż",
                zdot: "ż",
                zeetrf: "ℨ",
                ZeroWidthSpace: "​",
                Zeta: "Ζ",
                zeta: "ζ",
                Zfr: "ℨ",
                zfr: "𝔷",
                ZHcy: "Ж",
                zhcy: "ж",
                zigrarr: "⇝",
                Zopf: "ℤ",
                zopf: "𝕫",
                Zscr: "𝒵",
                zscr: "𝓏",
                zwj: "‍",
                zwnj: "‌"
            }),
            t.entityMap = t.HTML_ENTITIES
        }
        ,
        3969: (e,t,r)=>{
            var a = r(1146);
            t.DOMImplementation = a.DOMImplementation,
            t.XMLSerializer = a.XMLSerializer,
            t.DOMParser = r(6129).DOMParser
        }
        ,
        6925: (e,t,r)=>{
            var a = r(2167).NAMESPACE
              , n = /[A-Z_a-z\xC0-\xD6\xD8-\xF6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/
              , o = new RegExp("[\\-\\.0-9" + n.source.slice(1, -1) + "\\u00B7\\u0300-\\u036F\\u203F-\\u2040]")
              , i = new RegExp("^" + n.source + o.source + "*(?::" + n.source + o.source + "*)?$");
            function s(e, t) {
                this.message = e,
                this.locator = t,
                Error.captureStackTrace && Error.captureStackTrace(this, s)
            }
            function c() {}
            function l(e, t) {
                return t.lineNumber = e.lineNumber,
                t.columnNumber = e.columnNumber,
                t
            }
            function u(e, t, r, n, o, i) {
                function s(e, t, a) {
                    r.attributeNames.hasOwnProperty(e) && i.fatalError("Attribute " + e + " redefined"),
                    r.addValue(e, t.replace(/[\t\n\r]/g, " ").replace(/&#?\w+;/g, o), a)
                }
                for (var c, l = ++t, u = 0; ; ) {
                    var h = e.charAt(l);
                    switch (h) {
                    case "=":
                        if (1 === u)
                            c = e.slice(t, l),
                            u = 3;
                        else {
                            if (2 !== u)
                                throw new Error("attribute equal must after attrName");
                            u = 3
                        }
                        break;
                    case "'":
                    case '"':
                        if (3 === u || 1 === u) {
                            if (1 === u && (i.warning('attribute value must after "="'),
                            c = e.slice(t, l)),
                            t = l + 1,
                            !((l = e.indexOf(h, t)) > 0))
                                throw new Error("attribute value no end '" + h + "' match");
                            s(c, d = e.slice(t, l), t - 1),
                            u = 5
                        } else {
                            if (4 != u)
                                throw new Error('attribute value must after "="');
                            s(c, d = e.slice(t, l), t),
                            i.warning('attribute "' + c + '" missed start quot(' + h + ")!!"),
                            t = l + 1,
                            u = 5
                        }
                        break;
                    case "/":
                        switch (u) {
                        case 0:
                            r.setTagName(e.slice(t, l));
                        case 5:
                        case 6:
                        case 7:
                            u = 7,
                            r.closed = !0;
                        case 4:
                        case 1:
                            break;
                        case 2:
                            r.closed = !0;
                            break;
                        default:
                            throw new Error("attribute invalid close char('/')")
                        }
                        break;
                    case "":
                        return i.error("unexpected end of input"),
                        0 == u && r.setTagName(e.slice(t, l)),
                        l;
                    case ">":
                        switch (u) {
                        case 0:
                            r.setTagName(e.slice(t, l));
                        case 5:
                        case 6:
                        case 7:
                            break;
                        case 4:
                        case 1:
                            "/" === (d = e.slice(t, l)).slice(-1) && (r.closed = !0,
                            d = d.slice(0, -1));
                        case 2:
                            2 === u && (d = c),
                            4 == u ? (i.warning('attribute "' + d + '" missed quot(")!'),
                            s(c, d, t)) : (a.isHTML(n[""]) && d.match(/^(?:disabled|checked|selected)$/i) || i.warning('attribute "' + d + '" missed value!! "' + d + '" instead!!'),
                            s(d, d, t));
                            break;
                        case 3:
                            throw new Error("attribute value missed!!")
                        }
                        return l;
                    case "":
                        h = " ";
                    default:
                        if (h <= " ")
                            switch (u) {
                            case 0:
                                r.setTagName(e.slice(t, l)),
                                u = 6;
                                break;
                            case 1:
                                c = e.slice(t, l),
                                u = 2;
                                break;
                            case 4:
                                var d = e.slice(t, l);
                                i.warning('attribute "' + d + '" missed quot(")!!'),
                                s(c, d, t);
                            case 5:
                                u = 6
                            }
                        else
                            switch (u) {
                            case 2:
                                r.tagName,
                                a.isHTML(n[""]) && c.match(/^(?:disabled|checked|selected)$/i) || i.warning('attribute "' + c + '" missed value!! "' + c + '" instead2!!'),
                                s(c, c, t),
                                t = l,
                                u = 1;
                                break;
                            case 5:
                                i.warning('attribute space is required"' + c + '"!!');
                            case 6:
                                u = 1,
                                t = l;
                                break;
                            case 3:
                                u = 4,
                                t = l;
                                break;
                            case 7:
                                throw new Error("elements closed character '/' and '>' must be connected to")
                            }
                    }
                    l++
                }
            }
            function h(e, t, r) {
                for (var n = e.tagName, o = null, i = e.length; i--; ) {
                    var s = e[i]
                      , c = s.qName
                      , l = s.value;
                    if ((m = c.indexOf(":")) > 0)
                        var u = s.prefix = c.slice(0, m)
                          , h = c.slice(m + 1)
                          , d = "xmlns" === u && h;
                    else
                        h = c,
                        u = null,
                        d = "xmlns" === c && "";
                    s.localName = h,
                    !1 !== d && (null == o && (o = {},
                    p(r, r = {})),
                    r[d] = o[d] = l,
                    s.uri = a.XMLNS,
                    t.startPrefixMapping(d, l))
                }
                for (i = e.length; i--; )
                    (u = (s = e[i]).prefix) && ("xml" === u && (s.uri = a.XML),
                    "xmlns" !== u && (s.uri = r[u || ""]));
                var m;
                (m = n.indexOf(":")) > 0 ? (u = e.prefix = n.slice(0, m),
                h = e.localName = n.slice(m + 1)) : (u = null,
                h = e.localName = n);
                var f = e.uri = r[u || ""];
                if (t.startElement(f, h, n, e),
                !e.closed)
                    return e.currentNSMap = r,
                    e.localNSMap = o,
                    !0;
                if (t.endElement(f, h, n),
                o)
                    for (u in o)
                        Object.prototype.hasOwnProperty.call(o, u) && t.endPrefixMapping(u)
            }
            function d(e, t, r, a, n) {
                if (/^(?:script|textarea)$/i.test(r)) {
                    var o = e.indexOf("</" + r + ">", t)
                      , i = e.substring(t + 1, o);
                    if (/[&<]/.test(i))
                        return /^script$/i.test(r) ? (n.characters(i, 0, i.length),
                        o) : (i = i.replace(/&#?\w+;/g, a),
                        n.characters(i, 0, i.length),
                        o)
                }
                return t + 1
            }
            function m(e, t, r, a) {
                var n = a[r];
                return null == n && ((n = e.lastIndexOf("</" + r + ">")) < t && (n = e.lastIndexOf("</" + r)),
                a[r] = n),
                n < t
            }
            function p(e, t) {
                for (var r in e)
                    Object.prototype.hasOwnProperty.call(e, r) && (t[r] = e[r])
            }
            function f(e, t, r, a) {
                if ("-" === e.charAt(t + 2))
                    return "-" === e.charAt(t + 3) ? (n = e.indexOf("--\x3e", t + 4)) > t ? (r.comment(e, t + 4, n - t - 4),
                    n + 3) : (a.error("Unclosed comment"),
                    -1) : -1;
                if ("CDATA[" == e.substr(t + 3, 6)) {
                    var n = e.indexOf("]]>", t + 9);
                    return r.startCDATA(),
                    r.characters(e, t + 9, n - t - 9),
                    r.endCDATA(),
                    n + 3
                }
                var o = function(e, t) {
                    var r, a = [], n = /'[^']+'|"[^"]+"|[^\s<>\/=]+=?|(\/?\s*>|<)/g;
                    for (n.lastIndex = t,
                    n.exec(e); r = n.exec(e); )
                        if (a.push(r),
                        r[1])
                            return a
                }(e, t)
                  , i = o.length;
                if (i > 1 && /!doctype/i.test(o[0][0])) {
                    var s = o[1][0]
                      , c = !1
                      , l = !1;
                    i > 3 && (/^public$/i.test(o[2][0]) ? (c = o[3][0],
                    l = i > 4 && o[4][0]) : /^system$/i.test(o[2][0]) && (l = o[3][0]));
                    var u = o[i - 1];
                    return r.startDTD(s, c, l),
                    r.endDTD(),
                    u.index + u[0].length
                }
                return -1
            }
            function x(e, t, r) {
                var a = e.indexOf("?>", t);
                if (a) {
                    var n = e.substring(t, a).match(/^<\?(\S*)\s*([\s\S]*?)\s*$/);
                    return n ? (n[0].length,
                    r.processingInstruction(n[1], n[2]),
                    a + 2) : -1
                }
                return -1
            }
            function g() {
                this.attributeNames = {}
            }
            s.prototype = new Error,
            s.prototype.name = s.name,
            c.prototype = {
                parse: function(e, t, r) {
                    var n = this.domBuilder;
                    n.startDocument(),
                    p(t, t = {}),
                    function(e, t, r, n, o) {
                        function i(e) {
                            var t = e.slice(1, -1);
                            return Object.hasOwnProperty.call(r, t) ? r[t] : "#" === t.charAt(0) ? function(e) {
                                if (e > 65535) {
                                    var t = 55296 + ((e -= 65536) >> 10)
                                      , r = 56320 + (1023 & e);
                                    return String.fromCharCode(t, r)
                                }
                                return String.fromCharCode(e)
                            }(parseInt(t.substr(1).replace("x", "0x"))) : (o.error("entity not found:" + e),
                            e)
                        }
                        function c(t) {
                            if (t > y) {
                                var r = e.substring(y, t).replace(/&#?\w+;/g, i);
                                C && p(y),
                                n.characters(r, 0, t - y),
                                y = t
                            }
                        }
                        function p(t, r) {
                            for (; t >= b && (r = v.exec(e)); )
                                w = r.index,
                                b = w + r[0].length,
                                C.lineNumber++;
                            C.columnNumber = t - w + 1
                        }
                        for (var w = 0, b = 0, v = /.*(?:\r\n?|\n)|.*$/g, C = n.locator, A = [{
                            currentNSMap: t
                        }], E = {}, y = 0; ; ) {
                            try {
                                var _ = e.indexOf("<", y);
                                if (_ < 0) {
                                    if (!e.substr(y).match(/^\s*$/)) {
                                        var q = n.doc
                                          , D = q.createTextNode(e.substr(y));
                                        q.appendChild(D),
                                        n.currentElement = D
                                    }
                                    return
                                }
                                switch (_ > y && c(_),
                                e.charAt(_ + 1)) {
                                case "/":
                                    var T = e.indexOf(">", _ + 3)
                                      , N = e.substring(_ + 2, T).replace(/[ \t\n\r]+$/g, "")
                                      , M = A.pop();
                                    T < 0 ? (N = e.substring(_ + 2).replace(/[\s<].*/, ""),
                                    o.error("end tag name: " + N + " is not complete:" + M.tagName),
                                    T = _ + 1 + N.length) : N.match(/\s</) && (N = N.replace(/[\s<].*/, ""),
                                    o.error("end tag name: " + N + " maybe not complete"),
                                    T = _ + 1 + N.length);
                                    var O = M.localNSMap
                                      , L = M.tagName == N;
                                    if (L || M.tagName && M.tagName.toLowerCase() == N.toLowerCase()) {
                                        if (n.endElement(M.uri, M.localName, N),
                                        O)
                                            for (var B in O)
                                                Object.prototype.hasOwnProperty.call(O, B) && n.endPrefixMapping(B);
                                        L || o.fatalError("end tag name: " + N + " is not match the current start tagName:" + M.tagName)
                                    } else
                                        A.push(M);
                                    T++;
                                    break;
                                case "?":
                                    C && p(_),
                                    T = x(e, _, n);
                                    break;
                                case "!":
                                    C && p(_),
                                    T = f(e, _, n, o);
                                    break;
                                default:
                                    C && p(_);
                                    var S = new g
                                      , F = A[A.length - 1].currentNSMap
                                      , k = (T = u(e, _, S, F, i, o),
                                    S.length);
                                    if (!S.closed && m(e, T, S.tagName, E) && (S.closed = !0,
                                    r.nbsp || o.warning("unclosed xml attribute")),
                                    C && k) {
                                        for (var P = l(C, {}), R = 0; R < k; R++) {
                                            var I = S[R];
                                            p(I.offset),
                                            I.locator = l(C, {})
                                        }
                                        n.locator = P,
                                        h(S, n, F) && A.push(S),
                                        n.locator = C
                                    } else
                                        h(S, n, F) && A.push(S);
                                    a.isHTML(S.uri) && !S.closed ? T = d(e, T, S.tagName, i, n) : T++
                                }
                            } catch (e) {
                                if (e instanceof s)
                                    throw e;
                                o.error("element parse error: " + e),
                                T = -1
                            }
                            T > y ? y = T : c(Math.max(_, y) + 1)
                        }
                    }(e, t, r, n, this.errorHandler),
                    n.endDocument()
                }
            },
            g.prototype = {
                setTagName: function(e) {
                    if (!i.test(e))
                        throw new Error("invalid tagName:" + e);
                    this.tagName = e
                },
                addValue: function(e, t, r) {
                    if (!i.test(e))
                        throw new Error("invalid attribute:" + e);
                    this.attributeNames[e] = this.length,
                    this[this.length++] = {
                        qName: e,
                        value: t,
                        offset: r
                    }
                },
                length: 0,
                getLocalName: function(e) {
                    return this[e].localName
                },
                getLocator: function(e) {
                    return this[e].locator
                },
                getQName: function(e) {
                    return this[e].qName
                },
                getURI: function(e) {
                    return this[e].uri
                },
                getValue: function(e) {
                    return this[e].value
                }
            },
            t.XMLReader = c,
            t.ParseError = s
        }
        ,
        6036: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.InvalidNumberOfChildrenError = void 0;
            var a = r(6187);
            Object.defineProperty(t, "InvalidNumberOfChildrenError", {
                enumerable: !0,
                get: function() {
                    return a.InvalidNumberOfChildrenError
                }
            })
        }
        ,
        6187: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.InvalidNumberOfChildrenError = void 0;
            class r extends Error {
                constructor(e, t, r, a="exactly") {
                    super(`${e} tag must have ${a} ${t} children. It's actually ${r}`),
                    this.name = "InvalidNumberOfChildrenError"
                }
            }
            t.InvalidNumberOfChildrenError = r
        }
        ,
        8398: function(e, t, r) {
            "use strict";
            var a = this && this.__createBinding || (Object.create ? function(e, t, r, a) {
                void 0 === a && (a = r);
                var n = Object.getOwnPropertyDescriptor(t, r);
                n && !("get"in n ? !t.__esModule : n.writable || n.configurable) || (n = {
                    enumerable: !0,
                    get: function() {
                        return t[r]
                    }
                }),
                Object.defineProperty(e, a, n)
            }
            : function(e, t, r, a) {
                void 0 === a && (a = r),
                e[a] = t[r]
            }
            )
              , n = this && this.__exportStar || function(e, t) {
                for (var r in e)
                    "default" === r || Object.prototype.hasOwnProperty.call(t, r) || a(t, e, r)
            }
            ;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            n(r(8208), t),
            n(r(7011), t),
            n(r(3487), t),
            n(r(5379), t)
        },
        7011: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.JoinWithManySeparators = void 0;
            class r {
                constructor(e) {
                    this._separators = e
                }
                static join(e, t) {
                    return new r(t)._join(e)
                }
                _join(e) {
                    return e.reduce(((e,t,r,a)=>e + t + (r === a.length - 1 ? "" : this._get(r))), "")
                }
                _get(e) {
                    return this._separators[e] ? this._separators[e] : this._separators.length > 0 ? this._separators[this._separators.length - 1] : ","
                }
            }
            t.JoinWithManySeparators = r
        }
        ,
        3487: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.mathMLElementToLaTeXConverter = void 0;
            const a = r(6483);
            t.mathMLElementToLaTeXConverter = e=>new a.MathMLElementToLatexConverterAdapter(e).toLatexConverter()
        }
        ,
        5379: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.normalizeWhiteSpaces = void 0,
            t.normalizeWhiteSpaces = e=>e.replace(/\s+/g, " ")
        }
        ,
        7274: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.BracketWrapper = void 0;
            const a = r(836);
            t.BracketWrapper = class {
                constructor() {
                    this._open = "{",
                    this._close = "}"
                }
                wrap(e) {
                    return new a.Wrapper(this._open,this._close).wrap(e)
                }
            }
        }
        ,
        5667: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.GenericWrapper = void 0;
            const a = r(836);
            t.GenericWrapper = class {
                constructor(e, t) {
                    this._open = "\\left" + e,
                    this._close = "\\right" + t
                }
                wrap(e) {
                    return new a.Wrapper(this._open,this._close).wrap(e)
                }
            }
        }
        ,
        8208: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.GenericWrapper = t.ParenthesisWrapper = t.BracketWrapper = void 0;
            var a = r(7274);
            Object.defineProperty(t, "BracketWrapper", {
                enumerable: !0,
                get: function() {
                    return a.BracketWrapper
                }
            });
            var n = r(3246);
            Object.defineProperty(t, "ParenthesisWrapper", {
                enumerable: !0,
                get: function() {
                    return n.ParenthesisWrapper
                }
            });
            var o = r(5667);
            Object.defineProperty(t, "GenericWrapper", {
                enumerable: !0,
                get: function() {
                    return o.GenericWrapper
                }
            })
        }
        ,
        3246: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.ParenthesisWrapper = void 0;
            const a = r(836);
            t.ParenthesisWrapper = class {
                constructor() {
                    this._open = "\\left(",
                    this._close = "\\right)"
                }
                wrap(e) {
                    return new a.Wrapper(this._open,this._close).wrap(e)
                }
                wrapIfMoreThanOneChar(e) {
                    return e.length <= 1 ? e : this.wrap(e)
                }
            }
        }
        ,
        836: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.Wrapper = void 0,
            t.Wrapper = class {
                constructor(e, t) {
                    this._open = e,
                    this._close = t
                }
                wrap(e) {
                    return this._open + e + this._close
                }
            }
        }
        ,
        7510: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.GenericSpacingWrapper = void 0;
            const a = r(8398);
            t.GenericSpacingWrapper = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    return this._mathmlElement.children.map((e=>(0,
                    a.mathMLElementToLaTeXConverter)(e))).map((e=>e.convert())).join(" ")
                }
            }
        }
        ,
        3589: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.GenericUnderOver = void 0;
            const a = r(3487)
              , n = r(6036)
              , o = r(6626);
            t.GenericUnderOver = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const {name: e, children: t} = this._mathmlElement
                      , r = t.length;
                    if (2 !== r)
                        throw new n.InvalidNumberOfChildrenError(e,2,r);
                    const o = (0,
                    a.mathMLElementToLaTeXConverter)(t[0]).convert()
                      , i = (0,
                    a.mathMLElementToLaTeXConverter)(t[1]).convert();
                    return this._applyCommand(o, i)
                }
                _applyCommand(e, t) {
                    const r = this._mathmlElement.name.match(/under/) ? s.Under : s.Over;
                    return new i(r).apply(e, t)
                }
            }
            ;
            class i {
                constructor(e) {
                    this._type = e
                }
                apply(e, t) {
                    return o.latexAccents.includes(t) ? `${t}{${e}}` : `${this._defaultCommand}{${t}}{${e}}`
                }
                get _defaultCommand() {
                    return this._type === s.Under ? "\\underset" : "\\overset"
                }
            }
            var s;
            !function(e) {
                e[e.Under = 0] = "Under",
                e[e.Over = 1] = "Over"
            }(s || (s = {}))
        }
        ,
        1733: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.GenericUnderOver = t.GenericSpacingWrapper = t.MTr = t.MTable = t.MUnderover = t.MText = t.MMultiscripts = t.MSubsup = t.MSub = t.MSup = t.MPhantom = t.MError = t.MEnclose = t.MAction = t.MRoot = t.MFrac = t.MFenced = t.MSqrt = t.MN = t.MO = t.MI = t.Math = void 0;
            var a = r(2283);
            Object.defineProperty(t, "Math", {
                enumerable: !0,
                get: function() {
                    return a.Math
                }
            });
            var n = r(7523);
            Object.defineProperty(t, "MI", {
                enumerable: !0,
                get: function() {
                    return n.MI
                }
            });
            var o = r(5258);
            Object.defineProperty(t, "MO", {
                enumerable: !0,
                get: function() {
                    return o.MO
                }
            });
            var i = r(411);
            Object.defineProperty(t, "MN", {
                enumerable: !0,
                get: function() {
                    return i.MN
                }
            });
            var s = r(8301);
            Object.defineProperty(t, "MSqrt", {
                enumerable: !0,
                get: function() {
                    return s.MSqrt
                }
            });
            var c = r(5237);
            Object.defineProperty(t, "MFenced", {
                enumerable: !0,
                get: function() {
                    return c.MFenced
                }
            });
            var l = r(4060);
            Object.defineProperty(t, "MFrac", {
                enumerable: !0,
                get: function() {
                    return l.MFrac
                }
            });
            var u = r(5851);
            Object.defineProperty(t, "MRoot", {
                enumerable: !0,
                get: function() {
                    return u.MRoot
                }
            });
            var h = r(916);
            Object.defineProperty(t, "MAction", {
                enumerable: !0,
                get: function() {
                    return h.MAction
                }
            });
            var d = r(7018);
            Object.defineProperty(t, "MEnclose", {
                enumerable: !0,
                get: function() {
                    return d.MEnclose
                }
            });
            var m = r(6752);
            Object.defineProperty(t, "MError", {
                enumerable: !0,
                get: function() {
                    return m.MError
                }
            });
            var p = r(9897);
            Object.defineProperty(t, "MPhantom", {
                enumerable: !0,
                get: function() {
                    return p.MPhantom
                }
            });
            var f = r(5806);
            Object.defineProperty(t, "MSup", {
                enumerable: !0,
                get: function() {
                    return f.MSup
                }
            });
            var x = r(9);
            Object.defineProperty(t, "MSub", {
                enumerable: !0,
                get: function() {
                    return x.MSub
                }
            });
            var g = r(2740);
            Object.defineProperty(t, "MSubsup", {
                enumerable: !0,
                get: function() {
                    return g.MSubsup
                }
            });
            var w = r(4596);
            Object.defineProperty(t, "MMultiscripts", {
                enumerable: !0,
                get: function() {
                    return w.MMultiscripts
                }
            });
            var b = r(3808);
            Object.defineProperty(t, "MText", {
                enumerable: !0,
                get: function() {
                    return b.MText
                }
            });
            var v = r(2710);
            Object.defineProperty(t, "MUnderover", {
                enumerable: !0,
                get: function() {
                    return v.MUnderover
                }
            });
            var C = r(4134);
            Object.defineProperty(t, "MTable", {
                enumerable: !0,
                get: function() {
                    return C.MTable
                }
            });
            var A = r(7188);
            Object.defineProperty(t, "MTr", {
                enumerable: !0,
                get: function() {
                    return A.MTr
                }
            });
            var E = r(7510);
            Object.defineProperty(t, "GenericSpacingWrapper", {
                enumerable: !0,
                get: function() {
                    return E.GenericSpacingWrapper
                }
            });
            var y = r(3589);
            Object.defineProperty(t, "GenericUnderOver", {
                enumerable: !0,
                get: function() {
                    return y.GenericUnderOver
                }
            })
        }
        ,
        916: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MAction = void 0;
            const a = r(3487);
            t.MAction = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const {children: e} = this._mathmlElement;
                    return this._isToggle() ? e.map((e=>(0,
                    a.mathMLElementToLaTeXConverter)(e))).map((e=>e.convert())).join(" \\Longrightarrow ") : (0,
                    a.mathMLElementToLaTeXConverter)(e[0]).convert()
                }
                _isToggle() {
                    const {actiontype: e} = this._mathmlElement.attributes;
                    return "toggle" === e || !e
                }
            }
        }
        ,
        2283: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.Math = void 0;
            const a = r(3487)
              , n = r(5379);
            t.Math = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const e = this._mathmlElement.children.map((e=>(0,
                    a.mathMLElementToLaTeXConverter)(e))).map((e=>e.convert())).join(" ");
                    return (0,
                    n.normalizeWhiteSpaces)(e)
                }
            }
        }
        ,
        7018: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MEnclose = void 0;
            const a = r(3487);
            t.MEnclose = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const e = this._mathmlElement.children.map((e=>(0,
                    a.mathMLElementToLaTeXConverter)(e))).map((e=>e.convert())).join(" ");
                    return "actuarial" === this._notation ? `\\overline{\\left.${e}\\right|}` : "radical" === this._notation ? `\\sqrt{${e}}` : ["box", "roundedbox", "circle"].includes(this._notation) ? `\\boxed{${e}}` : "left" === this._notation ? `\\left|${e}` : "right" === this._notation ? `${e}\\right|` : "top" === this._notation ? `\\overline{${e}}` : "bottom" === this._notation ? `\\underline{${e}}` : "updiagonalstrike" === this._notation ? `\\cancel{${e}}` : "downdiagonalstrike" === this._notation ? `\\bcancel{${e}}` : "updiagonalarrow" === this._notation ? `\\cancelto{}{${e}}` : ["verticalstrike", "horizontalstrike"].includes(this._notation) ? `\\hcancel{${e}}` : "madruwb" === this._notation ? `\\underline{${e}\\right|}` : "phasorangle" === this._notation ? `{\\angle \\underline{${e}}}` : `\\overline{\\left.\\right)${e}}`
                }
                get _notation() {
                    return this._mathmlElement.attributes.notation || "longdiv"
                }
            }
        }
        ,
        6752: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MError = void 0;
            const a = r(3487);
            t.MError = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    return `\\color{red}{${this._mathmlElement.children.map((e=>(0,
                    a.mathMLElementToLaTeXConverter)(e))).map((e=>e.convert())).join(" ")}}`
                }
            }
        }
        ,
        5237: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MFenced = void 0;
            const a = r(3487)
              , n = r(8398);
            t.MFenced = class {
                constructor(e) {
                    this._mathmlElement = e,
                    this._open = this._mathmlElement.attributes.open || "",
                    this._close = this._mathmlElement.attributes.close || "",
                    this._separators = Array.from(this._mathmlElement.attributes.separators || "")
                }
                convert() {
                    const e = this._mathmlElement.children.map((e=>(0,
                    a.mathMLElementToLaTeXConverter)(e))).map((e=>e.convert()));
                    return this._isThereRelativeOfName(this._mathmlElement.children, "mtable") ? new i(this._open,this._close).apply(e) : new o(this._open,this._close,this._separators).apply(e)
                }
                _isThereRelativeOfName(e, t) {
                    return e.some((e=>e.name === t || this._isThereRelativeOfName(e.children, t)))
                }
            }
            ;
            class o {
                constructor(e, t, r) {
                    this._open = e || "(",
                    this._close = t || ")",
                    this._separators = r
                }
                apply(e) {
                    const t = n.JoinWithManySeparators.join(e, this._separators);
                    return new n.GenericWrapper(this._open,this._close).wrap(t)
                }
            }
            class i {
                constructor(e, t) {
                    this._genericCommand = "matrix",
                    this._separators = new s(e,t)
                }
                apply(e) {
                    const t = this._command
                      , r = `\\begin{${t}}\n ${e.join("")}\n\\end{${t}}`;
                    return t === this._genericCommand ? this._separators.wrap(r) : r
                }
                get _command() {
                    return this._separators.areParentheses() ? "pmatrix" : this._separators.areSquareBrackets() ? "bmatrix" : this._separators.areBrackets() ? "Bmatrix" : this._separators.areDivides() ? "vmatrix" : this._separators.areParallels() ? "Vmatrix" : this._separators.areNotEqual() ? this._genericCommand : "bmatrix"
                }
            }
            class s {
                constructor(e, t) {
                    this._open = e,
                    this._close = t
                }
                wrap(e) {
                    return new n.GenericWrapper(this._open,this._close).wrap(e)
                }
                areParentheses() {
                    return this._compare("(", ")")
                }
                areSquareBrackets() {
                    return this._compare("[", "]")
                }
                areBrackets() {
                    return this._compare("{", "}")
                }
                areDivides() {
                    return this._compare("|", "|")
                }
                areParallels() {
                    return this._compare("||", "||")
                }
                areNotEqual() {
                    return this._open !== this._close
                }
                _compare(e, t) {
                    return this._open === e && this._close === t
                }
            }
        }
        ,
        4060: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MFrac = void 0;
            const a = r(6036)
              , n = r(8398);
            t.MFrac = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const {children: e, name: t} = this._mathmlElement
                      , r = e.length;
                    if (2 !== r)
                        throw new a.InvalidNumberOfChildrenError(t,2,r);
                    const o = (0,
                    n.mathMLElementToLaTeXConverter)(e[0]).convert()
                      , i = (0,
                    n.mathMLElementToLaTeXConverter)(e[1]).convert();
                    // return this._isBevelled() ? `${this._wrapIfMoreThanOneChar(o)}/${this._wrapIfMoreThanOneChar(i)}` : `\\frac{${o}}{${i}}`
                    return this._isBevelled() ? `${o}/${i}` : `\\frac{${o}}{${i}}`
                }
                _wrapIfMoreThanOneChar(e) {
                    return (new n.ParenthesisWrapper).wrapIfMoreThanOneChar(e)
                }
                _isBevelled() {
                    return !!this._mathmlElement.attributes.bevelled
                }
            }
        }
        ,
        7523: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MI = void 0;
            const a = r(8398)
              , n = r(2408)
              , o = r(4317);
            t.MI = class {
                constructor(e) {
                    this.utf8Converter = new o.HashUTF8ToLtXConverter,
                    this._mathmlElement = e
                }
                convert() {
                    const e = (0,
                    a.normalizeWhiteSpaces)(this._mathmlElement.value);
                    if (" " === e)
                        return i.apply(e);
                    const t = e.trim()
                      , r = i.apply(t)
                      , n = this.utf8Converter.convert(r);
                    return n !== r ? n : this.wrapInMathVariant(r, this.getMathVariant(this._mathmlElement.attributes))
                }
                getMathVariant(e) {
                    if (e && e.mathvariant)
                        return e.mathvariant
                }
                wrapInMathVariant(e, t) {
                    switch (t) {
                    case "bold":
                        return `\\mathbf{${e}}`;
                    case "italic":
                        return `\\mathit{${e}}`;
                    case "bold-italic":
                        return `\\mathbf{\\mathit{${e}}}`;
                    case "double-struck":
                        return `\\mathbb{${e}}`;
                    case "bold-fraktur":
                        return `\\mathbf{\\mathfrak{${e}}}`;
                    case "script":
                        return `\\mathcal{${e}}`;
                    case "bold-script":
                        return `\\mathbf{\\mathcal{${e}}}`;
                    case "fraktur":
                        return `\\mathfrak{${e}}`;
                    case "sans-serif":
                        return `\\mathsf{${e}}`;
                    case "bold-sans-serif":
                        return `\\mathbf{\\mathsf{${e}}}`;
                    case "sans-serif-italic":
                        return `\\mathsf{\\mathit{${e}}}`;
                    case "sans-serif-bold-italic":
                        return `\\mathbf{\\mathsf{\\mathit{${e}}}}`;
                    case "monospace":
                        return `\\mathtt{${e}}`;
                    default:
                        return e
                    }
                }
            }
            ;
            class i {
                constructor(e) {
                    this._value = e
                }
                static apply(e) {
                    return new i(e)._apply()
                }
                _apply() {
                    return this._findByCharacter() || this._findByGlyph() || this._findByNumber() || (new o.HashUTF8ToLtXConverter).convert(this._value)
                }
                _findByCharacter() {
                    return n.allMathSymbolsByChar[this._value]
                }
                _findByGlyph() {
                    return n.allMathSymbolsByGlyph[this._value]
                }
                _findByNumber() {
                    return n.mathNumberByGlyph[this._value]
                }
            }
        }
        ,
        4596: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MMultiscripts = void 0;
            const a = r(8398)
              , n = r(6036);
            t.MMultiscripts = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const {name: e, children: t} = this._mathmlElement
                      , r = t.length;
                    if (r < 3)
                        throw new n.InvalidNumberOfChildrenError(e,3,r,"at least");
                    const o = (0,
                    a.mathMLElementToLaTeXConverter)(t[0]).convert();
                    return this._prescriptLatex() + this._wrapInParenthesisIfThereIsSpace(o) + this._postscriptLatex()
                }
                _prescriptLatex() {
                    const {children: e} = this._mathmlElement;
                    let t, r;
                    if (this._isPrescripts(e[1]))
                        t = e[2],
                        r = e[3];
                    else {
                        if (!this._isPrescripts(e[3]))
                            return "";
                        t = e[4],
                        r = e[5]
                    }
                    return `\\_{${(0,
                    a.mathMLElementToLaTeXConverter)(t).convert()}}^{${(0,
                    a.mathMLElementToLaTeXConverter)(r).convert()}}`
                }
                _postscriptLatex() {
                    const {children: e} = this._mathmlElement;
                    if (this._isPrescripts(e[1]))
                        return "";
                    const t = e[1]
                      , r = e[2];
                    return `_{${(0,
                    a.mathMLElementToLaTeXConverter)(t).convert()}}^{${(0,
                    a.mathMLElementToLaTeXConverter)(r).convert()}}`
                }
                _wrapInParenthesisIfThereIsSpace(e) {
                    return e.match(/\s+/g) ? (new a.ParenthesisWrapper).wrap(e) : e
                }
                _isPrescripts(e) {
                    return "mprescripts" === (null == e ? void 0 : e.name)
                }
            }
        }
        ,
        411: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MN = void 0;
            const a = r(8398)
              , n = r(2408);
            t.MN = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const e = (0,
                    a.normalizeWhiteSpaces)(this._mathmlElement.value).trim();
                    return n.mathNumberByGlyph[e] || e
                }
            }
        }
        ,
        5258: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MO = void 0;
            const a = r(8398)
              , n = r(2408);
            t.MO = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const e = (0,
                    a.normalizeWhiteSpaces)(this._mathmlElement.value).trim();
                    return o.operate(e)
                }
            }
            ;
            class o {
                constructor(e) {
                    this._value = e
                }
                static operate(e) {
                    return new o(e)._operate()
                }
                _operate() {
                    return this._findByCharacter() || this._findByGlyph() || this._findByNumber() || (new n.HashUTF8ToLtXConverter).convert(this._value)
                }
                _findByCharacter() {
                    return n.allMathOperatorsByChar[this._value]
                }
                _findByGlyph() {
                    return n.allMathOperatorsByGlyph[this._value]
                }
                _findByNumber() {
                    return n.mathNumberByGlyph[this._value]
                }
            }
        }
        ,
        9897: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MPhantom = void 0,
            t.MPhantom = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    return ""
                }
            }
        }
        ,
        5851: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MRoot = void 0;
            const a = r(8398)
              , n = r(6036);
            t.MRoot = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const {name: e, children: t} = this._mathmlElement
                      , r = t.length;
                    if (2 !== r)
                        throw new n.InvalidNumberOfChildrenError(e,2,r);
                    const o = (0,
                    a.mathMLElementToLaTeXConverter)(t[0]).convert();
                    return `\\sqrt[${(0,
                    a.mathMLElementToLaTeXConverter)(t[1]).convert()}]{${o}}`
                }
            }
        }
        ,
        8301: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MSqrt = void 0;
            const a = r(8398);
            t.MSqrt = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    return `\\sqrt{${this._mathmlElement.children.map((e=>(0,
                    a.mathMLElementToLaTeXConverter)(e))).map((e=>e.convert())).join(" ")}}`
                }
            }
        }
        ,
        9: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MSub = void 0;
            const a = r(8398)
              , n = r(6036);
            t.MSub = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const {name: e, children: t} = this._mathmlElement
                      , r = t.length;
                    if (2 !== r)
                        throw new n.InvalidNumberOfChildrenError(e,2,r);
                    const o = (0,
                    a.mathMLElementToLaTeXConverter)(t[0]).convert()
                      , i = (0,
                    a.mathMLElementToLaTeXConverter)(t[1]).convert();
                    // return `${(new a.ParenthesisWrapper).wrapIfMoreThanOneChar(o)}_ ${(new a.BracketWrapper).wrap(i)}`
                    return `${o}_ ${(new a.BracketWrapper).wrap(i)}`
                }
            }
        }
        ,
        2740: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MSubsup = void 0;
            const a = r(8398)
              , n = r(6036);
            t.MSubsup = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const {name: e, children: t} = this._mathmlElement
                      , r = t.length;
                    if (3 !== r)
                        throw new n.InvalidNumberOfChildrenError(e,3,r);
                    const o = (0,
                    a.mathMLElementToLaTeXConverter)(t[0]).convert()
                      , i = (0,
                    a.mathMLElementToLaTeXConverter)(t[1]).convert()
                      , s = (0,
                    a.mathMLElementToLaTeXConverter)(t[2]).convert()
                      , c = (new a.BracketWrapper).wrap(i)
                      , l = (new a.BracketWrapper).wrap(s);
                    return `${this._wrapInParenthesisIfThereIsSpace(o)}_ ${c}^${l}`
                }
                _wrapInParenthesisIfThereIsSpace(e) {
                    return e.match(/\s+/g) ? (new a.ParenthesisWrapper).wrap(e) : e
                }
            }
        }
        ,
        5806: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MSup = void 0;
            const a = r(8398)
              , n = r(6036);
            t.MSup = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const {name: e, children: t} = this._mathmlElement
                      , r = t.length;
                    if (2 !== r)
                        throw new n.InvalidNumberOfChildrenError(e,2,r);
                    const o = (0,
                    a.mathMLElementToLaTeXConverter)(t[0]).convert()
                      , i = (0,
                    a.mathMLElementToLaTeXConverter)(t[1]).convert();
                    // return `${(new a.ParenthesisWrapper).wrapIfMoreThanOneChar(o)}^${(new a.BracketWrapper).wrap(i)}`
                    return `${o}^${(new a.BracketWrapper).wrap(i)}`
                }
            }
        }
        ,
        4134: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MTable = void 0;
            const a = r(8398);
            t.MTable = class {
                constructor(e) {
                    this._mathmlElement = e,
                    this._addFlagRecursiveIfName(this._mathmlElement.children, "mtable", "innerTable")
                }
                convert() {
                    const e = this._mathmlElement.children.map((e=>(0,
                    a.mathMLElementToLaTeXConverter)(e))).map((e=>e.convert())).join(" \\\\\n");
                    return this._hasFlag("innerTable") ? this._wrap(e) : e
                }
                _wrap(e) {
                    return `\\begin{matrix}${e}\\end{matrix}`
                }
                _addFlagRecursiveIfName(e, t, r) {
                    e.forEach((e=>{
                        e.name === t && (e.attributes[r] = r),
                        this._addFlagRecursiveIfName(e.children, t, r)
                    }
                    ))
                }
                _hasFlag(e) {
                    return !!this._mathmlElement.attributes[e]
                }
            }
        }
        ,
        3808: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MText = void 0;
            const a = r(7523);
            t.MText = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const {attributes: e, value: t} = this._mathmlElement;
                    return [...t].map((e=>/^[a-zA-Z0-9]$/.test(e) || " " === e ? {
                        value: e,
                        isAlphanumeric: !0
                    } : {
                        value: e,
                        isAlphanumeric: !1
                    })).reduce(((e,t)=>{
                        if (t.isAlphanumeric) {
                            const r = e[e.length - 1];
                            if (r && r.isAlphanumeric)
                                return r.value += t.value,
                                e
                        }
                        return [...e, t]
                    }
                    ), []).map((t=>t.isAlphanumeric ? new n(e.mathvariant).apply(t.value) : new a.MI({
                        name: "mi",
                        attributes: {},
                        children: [],
                        value: t.value
                    }).convert())).join("")
                }
            }
            ;
            class n {
                constructor(e) {
                    this._mathvariant = e || "normal"
                }
                apply(e) {
                    return this._commands.reduce(((t,r,a)=>0 === a ? `${r}{${e}}` : `${r}{${t}}`), "")
                }
                get _commands() {
                    switch (this._mathvariant) {
                    case "bold":
                        return ["\\textbf"];
                    case "italic":
                        return ["\\textit"];
                    case "bold-italic":
                        return ["\\textit", "\\textbf"];
                    case "double-struck":
                        return ["\\mathbb"];
                    case "monospace":
                        return ["\\mathtt"];
                    case "bold-fraktur":
                    case "fraktur":
                        return ["\\mathfrak"];
                    default:
                        return ["\\text"]
                    }
                }
            }
        }
        ,
        7188: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MTr = void 0;
            const a = r(8398);
            t.MTr = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    return this._mathmlElement.children.map((e=>(0,
                    a.mathMLElementToLaTeXConverter)(e))).map((e=>e.convert())).join(" & ")
                }
            }
        }
        ,
        2710: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MUnderover = void 0;
            const a = r(8398)
              , n = r(6036);
            t.MUnderover = class {
                constructor(e) {
                    this._mathmlElement = e
                }
                convert() {
                    const {name: e, children: t} = this._mathmlElement
                      , r = t.length;
                    if (3 !== r)
                        throw new n.InvalidNumberOfChildrenError(e,3,r);
                    return `${(0,
                    a.mathMLElementToLaTeXConverter)(t[0]).convert()}_{${(0,
                    a.mathMLElementToLaTeXConverter)(t[1]).convert()}}^{${(0,
                    a.mathMLElementToLaTeXConverter)(t[2]).convert()}}`
                }
            }
        }
        ,
        6483: function(e, t, r) {
            "use strict";
            var a = this && this.__createBinding || (Object.create ? function(e, t, r, a) {
                void 0 === a && (a = r);
                var n = Object.getOwnPropertyDescriptor(t, r);
                n && !("get"in n ? !t.__esModule : n.writable || n.configurable) || (n = {
                    enumerable: !0,
                    get: function() {
                        return t[r]
                    }
                }),
                Object.defineProperty(e, a, n)
            }
            : function(e, t, r, a) {
                void 0 === a && (a = r),
                e[a] = t[r]
            }
            )
              , n = this && this.__setModuleDefault || (Object.create ? function(e, t) {
                Object.defineProperty(e, "default", {
                    enumerable: !0,
                    value: t
                })
            }
            : function(e, t) {
                e.default = t
            }
            )
              , o = this && this.__importStar || function(e) {
                if (e && e.__esModule)
                    return e;
                var t = {};
                if (null != e)
                    for (var r in e)
                        "default" !== r && Object.prototype.hasOwnProperty.call(e, r) && a(t, e, r);
                return n(t, e),
                t
            }
            ;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MathMLElementToLatexConverterAdapter = void 0;
            const i = o(r(1733));
            t.MathMLElementToLatexConverterAdapter = class {
                constructor(e) {
                    this._mathMLElement = e
                }
                toLatexConverter() {
                    const {name: e} = this._mathMLElement;
                    return new (s[e] || i.GenericSpacingWrapper)(this._mathMLElement)
                }
            }
            ;
            const s = {
                math: i.Math,
                mi: i.MI,
                mo: i.MO,
                mn: i.MN,
                msqrt: i.MSqrt,
                mfenced: i.MFenced,
                mfrac: i.MFrac,
                mroot: i.MRoot,
                maction: i.MAction,
                menclose: i.MEnclose,
                merror: i.MError,
                mphantom: i.MPhantom,
                msup: i.MSup,
                msub: i.MSub,
                msubsup: i.MSubsup,
                mmultiscripts: i.MMultiscripts,
                mtext: i.MText,
                munderover: i.MUnderover,
                mtable: i.MTable,
                mtr: i.MTr,
                mover: i.GenericUnderOver,
                munder: i.GenericUnderOver,
                mrow: i.GenericSpacingWrapper,
                mpadded: i.GenericSpacingWrapper
            }
        },
        965: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.ErrorHandler = void 0,
            t.ErrorHandler = class {
                constructor() {
                    this._errors = [],
                    this.errorLocator = {}
                }
                fixError(e, t) {
                    return this._isMissingAttributeValueError(t) ? (this._errors.push(t),
                    this._fixMissingAttribute(t, e)) : e
                }
                isThereAnyErrors() {
                    return this._errors.length > 0
                }
                cleanErrors() {
                    this._errors = []
                }
                _fixMissingAttribute(e, t) {
                    const r = e.split('"')[1];
                    if (r)
                        return t.replace(this._matchMissingValueForAttribute(r), "");
                    for (; this._mathGenericMissingValue().exec(t); )
                        t = t.replace(this._mathGenericMissingValue(), "$1$3");
                    return t
                }
                _matchMissingValueForAttribute(e) {
                    return new RegExp(`(${e}=(?!("|')))|(${e}(?!("|')))`,"gm")
                }
                _mathGenericMissingValue() {
                    return /(\<.* )(\w+=(?!\"|\'))(.*\>)/gm
                }
                _isMissingAttributeValueError(e) {
                    return !!e.includes("attribute") && !!e.includes("missed") || e.includes("attribute value missed")
                }
            }
        }
        ,
        6177: function(e, t, r) {
            "use strict";
            var a = this && this.__createBinding || (Object.create ? function(e, t, r, a) {
                void 0 === a && (a = r);
                var n = Object.getOwnPropertyDescriptor(t, r);
                n && !("get"in n ? !t.__esModule : n.writable || n.configurable) || (n = {
                    enumerable: !0,
                    get: function() {
                        return t[r]
                    }
                }),
                Object.defineProperty(e, a, n)
            }
            : function(e, t, r, a) {
                void 0 === a && (a = r),
                e[a] = t[r]
            }
            )
              , n = this && this.__exportStar || function(e, t) {
                for (var r in e)
                    "default" === r || Object.prototype.hasOwnProperty.call(t, r) || a(t, e, r)
            }
            ;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            n(r(5242), t),
            n(r(965), t),
            n(r(1877), t)
        },
        1877: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.ElementsToMathMLAdapter = void 0,
            t.ElementsToMathMLAdapter = class {
                convert(e) {
                    return e.filter((e=>void 0 !== e.tagName)).map((e=>this._convertElement(e)))
                }
                _convertElement(e) {
                    return {
                        name: e.tagName,
                        attributes: this._convertElementAttributes(e.attributes),
                        value: this._hasElementChild(e) ? "" : e.textContent || "",
                        children: this._hasElementChild(e) ? this.convert(Array.from(e.childNodes)) : []
                    }
                }
                _convertElementAttributes(e) {
                    return Array.from(e).reduce(((e,t)=>Object.assign({
                        [t.nodeName]: t.nodeValue === t.nodeName ? "" : t.nodeValue
                    }, e)), {})
                }
                _hasElementChild(e) {
                    const t = e.childNodes;
                    return !!t && 0 !== t.length && this._isThereAnyNoTextNode(t)
                }
                _isThereAnyNoTextNode(e) {
                    return Array.from(e).some((e=>"#text" !== e.nodeName))
                }
            }
        }
        ,
        5242: function(e, t, r) {
            "use strict";
            var a = this && this.__importDefault || function(e) {
                return e && e.__esModule ? e : {
                    default: e
                }
            }
            ;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.XmlToMathMLAdapter = void 0;
            const n = a(r(3969));
            t.XmlToMathMLAdapter = class {
                constructor(e, t) {
                    this._xml = "",
                    this._elementsConvertor = e,
                    this._errorHandler = t,
                    this._xmlDOM = new n.default.DOMParser({
                        locator: this._errorHandler.errorLocator,
                        errorHandler: this._fixError.bind(this)
                    })
                }
                convert(e) {
                    return this._xml = this._removeLineBreaks(e),
                    this._elementsConvertor.convert(this._mathMLElements)
                }
                _fixError(e) {
                    this._xml = this._errorHandler.fixError(this._xml, e)
                }
                _removeLineBreaks(e) {
                    return e.replace(/\n|\r\n|\r/g, "")
                }
                get _mathMLElements() {
                    let e = this._xmlDOM.parseFromString(this._xml).getElementsByTagName("math");
                    return this._errorHandler.isThereAnyErrors() && (this._errorHandler.cleanErrors(),
                    e = this._xmlDOM.parseFromString(this._xml).getElementsByTagName("math")),
                    Array.from(e)
                }
            }
        },
        4150: function(e, t, r) {
            "use strict";
            var a = this && this.__createBinding || (Object.create ? function(e, t, r, a) {
                void 0 === a && (a = r);
                var n = Object.getOwnPropertyDescriptor(t, r);
                n && !("get"in n ? !t.__esModule : n.writable || n.configurable) || (n = {
                    enumerable: !0,
                    get: function() {
                        return t[r]
                    }
                }),
                Object.defineProperty(e, a, n)
            }
            : function(e, t, r, a) {
                void 0 === a && (a = r),
                e[a] = t[r]
            }
            )
              , n = this && this.__exportStar || function(e, t) {
                for (var r in e)
                    "default" === r || Object.prototype.hasOwnProperty.call(t, r) || a(t, e, r)
            }
            ;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            n(r(488), t)
        },
        488: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.makeToMathElementsConverter = void 0;
            const a = r(6177);
            t.makeToMathElementsConverter = ()=>{
                const e = new a.ElementsToMathMLAdapter
                  , t = new a.ErrorHandler;
                return new a.XmlToMathMLAdapter(e,t)
            }
        }
        ,
        8863: function(e, t, r) {
            "use strict";
            var a = this && this.__createBinding || (Object.create ? function(e, t, r, a) {
                void 0 === a && (a = r);
                var n = Object.getOwnPropertyDescriptor(t, r);
                n && !("get"in n ? !t.__esModule : n.writable || n.configurable) || (n = {
                    enumerable: !0,
                    get: function() {
                        return t[r]
                    }
                }),
                Object.defineProperty(e, a, n)
            }
            : function(e, t, r, a) {
                void 0 === a && (a = r),
                e[a] = t[r]
            }
            )
              , n = this && this.__exportStar || function(e, t) {
                for (var r in e)
                    "default" === r || Object.prototype.hasOwnProperty.call(t, r) || a(t, e, r)
            }
            ;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            n(r(862), t)
        },
        862: (e,t,r)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.MathMLToLaTeX = void 0;
            const a = r(6483)
              , n = r(4150);
            t.MathMLToLaTeX = class {
                static convert(e) {
                    return (0,
                    n.makeToMathElementsConverter)().convert(e).map((e=>new a.MathMLElementToLatexConverterAdapter(e).toLatexConverter())).map((e=>e.convert())).join("").trim()
                }
            }
        }
        ,
        1868: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.allMathOperatorsByChar = void 0,
            t.allMathOperatorsByChar = {
                _: "\\underline",
                "&#x23E1;": "\\underbrace",
                "&#x23E0;": "\\overbrace",
                "&#x23DF;": "\\underbrace",
                "&#x23DE;": "\\overbrace",
                "&#x23DD;": "\\underbrace",
                "&#x23DC;": "\\overbrace",
                "&#x23B5;": "\\underbrace",
                "&#x23B4;": "\\overbrace",
                "&#x20DC;": "\\square",
                "&#x20DB;": "\\square",
                "&#x2064;": "",
                "&#x2057;": "''''",
                "&#x203E;": "\\bar",
                "&#x2037;": "```",
                "&#x2036;": "``",
                "&#x2035;": "`",
                "&#x2034;": "'''",
                "&#x2033;": "''",
                "&#x201F;": "``",
                "&#x201E;": ",,",
                "&#x201B;": "`",
                "&#x201A;": ",",
                "&#x302;": "\\hat",
                "&#x2F7;": "\\sim",
                "&#x2DD;": "\\sim",
                "&#x2DC;": "\\sim",
                "&#x2DA;": "\\circ",
                "&#x2D9;": "\\cdot",
                "&#x2D8;": "",
                "&#x2CD;": "\\_",
                "&#x2CB;": "ˋ",
                "&#x2CA;": "ˊ",
                "&#x2C9;": "ˉ",
                "&#x2C7;": "",
                "&#x2C6;": "\\hat",
                "&#xBA;": "o",
                "&#xB9;": "1",
                "&#xB8;": "¸",
                "&#xB4;": "´",
                "&#xB3;": "3",
                "&#xB2;": "2",
                "&#xB0;": "\\circ",
                "&#xAF;": "\\bar",
                "&#xAA;": "a",
                "&#xA8;": "\\cdot\\cdot",
                "~": "\\sim",
                "`": "`",
                "^": "\\hat",
                "--": "--",
                "++": "++",
                "&amp;": "\\&",
                "&#x2061;": "",
                "&#x221C;": "\\sqrt[4]{}",
                "&#x221B;": "\\sqrt[3]{}",
                "&#x221A;": "\\sqrt{}",
                "&#x2146;": "d",
                "&#x2145;": "\\mathbb{D}",
                "?": "?",
                "@": "@",
                "//": "//",
                "!!": "!!",
                "!": "!",
                "&#x266F;": "\\#",
                "&#x266E;": "",
                "&#x266D;": "",
                "&#x2032;": "'",
                "&lt;>": "<>",
                "**": "\\star\\star",
                "&#x2207;": "\\nabla",
                "&#x2202;": "\\partial",
                "&#x2299;": "\\bigodot",
                "&#xAC;": "\\neg",
                "&#x2222;": "\\measuredangle",
                "&#x2221;": "\\measuredangle",
                "&#x2220;": "\\angle",
                "&#xF7;": "\\div",
                "/": "/",
                "&#x2216;": "\\backslash",
                "\\": "\\backslash",
                "%": "\\%",
                "&#x2297;": "\\bigotimes",
                "&#xB7;": "\\cdot",
                "&#x2A3F;": "\\coprod",
                "&#x2A2F;": "\\times",
                "&#x22C5;": "\\cdot",
                "&#x22A1;": "\\boxdot",
                "&#x22A0;": "\\boxtimes",
                "&#x2062;": "",
                "&#x2043;": "-",
                "&#x2022;": "\\cdot",
                "&#xD7;": "\\times",
                ".": ".",
                "*": "\\star",
                "&#x222A;": "\\cup",
                "&#x2229;": "\\cap",
                "&#x2210;": "\\coprod",
                "&#x220F;": "\\prod",
                "&#x2240;": "",
                "&#x2AFF;": "",
                "&#x2AFC;": "\\mid\\mid\\mid",
                "&#x2A09;": "\\times",
                "&#x2A08;": "",
                "&#x2A07;": "",
                "&#x2A06;": "\\sqcup",
                "&#x2A05;": "\\sqcap",
                "&#x2A02;": "\\otimes",
                "&#x2A00;": "\\odot",
                "&#x22C2;": "\\cap",
                "&#x22C1;": "\\vee",
                "&#x22C0;": "\\wedge",
                "&#x2A04;": "\\uplus",
                "&#x2A03;": "\\cup",
                "&#x22C3;": "\\cup",
                "&#x2A1C;": "\\underline{\\int}",
                "&#x2A1B;": "\\overline{\\int}",
                "&#x2A1A;": "\\int",
                "&#x2A19;": "\\int",
                "&#x2A18;": "\\int",
                "&#x2A17;": "\\int",
                "&#x2A16;": "\\oint",
                "&#x2A15;": "\\oint",
                "&#x2A14;": "\\int",
                "&#x2A13;": "\\int",
                "&#x2A12;": "\\int",
                "&#x2A11;": "\\int",
                "&#x2A10;": "\\int",
                "&#x2A0F;": "\\bcancel{\\int}",
                "&#x2A0E;": "",
                "&#x2A0D;": "\\hcancel{\\int}",
                "&#x2A0C;": "\\iiiint",
                "&#x2233;": "\\oint",
                "&#x2232;": "\\oint",
                "&#x2231;": "\\int",
                "&#x2230;": "\\oiint",
                "&#x222F;": "\\oiint",
                "&#x222E;": "\\oint",
                "&#x222B;": "\\int",
                "&#x2A01;": "\\oplus",
                "&#x2298;": "\\oslash",
                "&#x2296;": "\\ominus",
                "&#x2295;": "\\oplus",
                "&#x222D;": "\\iiint",
                "&#x222C;": "\\iint",
                "&#x2A0B;": "",
                "&#x2A0A;": "",
                "&#x2211;": "\\sum",
                "&#x229F;": "\\boxminus",
                "&#x229E;": "\\boxplus",
                "&#x2214;": "\\dot{+}",
                "&#x2213;": "+-",
                "&#x2212;": "-",
                "&#xB1;": "\\pm",
                "-": "-",
                "+": "+",
                "&#x2B46;": "\\Rrightarrow",
                "&#x2B45;": "\\Lleftarrow",
                "&#x29F4;": ":\\rightarrow",
                "&#x29EF;": "",
                "&#x29DF;": "\\bullet-\\bullet",
                "&#x299F;": "\\angle",
                "&#x299E;": "\\measuredangle",
                "&#x299D;": "\\measuredangle",
                "&#x299C;": "\\perp",
                "&#x299B;": "\\measuredangle",
                "&#x299A;": "",
                "&#x2999;": "\\vdots",
                "&#x297F;": "",
                "&#x297E;": "",
                "&#x297D;": "\\prec",
                "&#x297C;": "\\succ",
                "&#x297B;": "\\underset{\\rightarrow}{\\supset}",
                "&#x297A;": "",
                "&#x2979;": "\\underset{\\rightarrow}{\\subset}",
                "&#x2978;": "\\underset{\\rightarrow}{>}",
                "&#x2977;": "",
                "&#x2976;": "\\underset{\\leftarrow}{<}",
                "&#x2975;": "\\underset{\\approx}{\\rightarrow}",
                "&#x2974;": "\\underset{\\sim}{\\rightarrow}",
                "&#x2973;": "\\underset{\\sim}{\\leftarrow}",
                "&#x2972;": "\\overset{\\sim}{\\rightarrow}",
                "&#x2971;": "\\overset{=}{\\rightarrow}",
                "&#x2970;": "",
                "&#x296F;": "",
                "&#x296E;": "",
                "&#x296D;": "\\overline{\\rightharpoondown}",
                "&#x296C;": "\\underline{\\rightharpoonup}",
                "&#x296B;": "\\overline{\\leftharpoondown}",
                "&#x296A;": "\\underline{\\leftharpoonup}",
                "&#x2969;": "\\rightleftharpoons",
                "&#x2968;": "\\rightleftharpoons",
                "&#x2967;": "\\rightleftharpoons",
                "&#x2966;": "\\rightleftharpoons",
                "&#x2965;": "\\Downarrow",
                "&#x2964;": "\\Rightarrow",
                "&#x2963;": "\\Uparrow",
                "&#x2962;": "\\Leftarrow",
                "&#x2961;": "\\downarrow",
                "&#x2960;": "\\uparrow",
                "&#x295F;": "\\rightarrow",
                "&#x295E;": "\\leftarrow",
                "&#x295D;": "\\downarrow",
                "&#x295C;": "\\uparrow",
                "&#x295B;": "\\rightarrow",
                "&#x295A;": "\\leftarrow",
                "&#x2959;": "\\downarrow",
                "&#x2958;": "\\uparrow",
                "&#x2957;": "\\rightarrow",
                "&#x2956;": "\\leftarrow",
                "&#x2955;": "\\downarrow",
                "&#x2954;": "\\uparrow",
                "&#x2953;": "\\rightarrow",
                "&#x2952;": "\\leftarrow",
                "&#x2951;": "\\updownarrow",
                "&#x2950;": "\\leftrightarrow",
                "&#x294F;": "\\updownarrow",
                "&#x294E;": "\\leftrightarrow",
                "&#x294D;": "\\updownarrow",
                "&#x294C;": "\\updownarrow",
                "&#x294B;": "\\leftrightarrow",
                "&#x294A;": "\\leftrightarrow",
                "&#x2949;": "",
                "&#x2948;": "\\leftrightarrow",
                "&#x2947;": "\\nrightarrow",
                "&#x2946;": "",
                "&#x2945;": "",
                "&#x2944;": "\\rightleftarrows",
                "&#x2943;": "\\leftrightarrows",
                "&#x2942;": "\\rightleftarrows",
                "&#x2941;": "\\circlearrowright",
                "&#x2940;": "\\circlearrowleft",
                "&#x293F;": "\\rightarrow",
                "&#x293E;": "\\leftarrow",
                "&#x293D;": "",
                "&#x293C;": "",
                "&#x293B;": "",
                "&#x293A;": "",
                "&#x2939;": "",
                "&#x2938;": "",
                "&#x2937;": "\\Rsh",
                "&#x2936;": "\\Lsh",
                "&#x2935;": "\\downarrow",
                "&#x2934;": "\\uparrow",
                "&#x2933;": "\\leadsto",
                "&#x2932;": "",
                "&#x2931;": "",
                "&#x2930;": "",
                "&#x292F;": "",
                "&#x292E;": "",
                "&#x292D;": "",
                "&#x292C;": "\\times",
                "&#x292B;": "\\times",
                "&#x292A;": "",
                "&#x2929;": "",
                "&#x2928;": "",
                "&#x2927;": "",
                "&#x2926;": "",
                "&#x2925;": "",
                "&#x2924;": "",
                "&#x2923;": "",
                "&#x2922;": "",
                "&#x2921;": "",
                "&#x2920;": "\\mapsto\\cdot",
                "&#x291F;": "\\cdot\\leftarrow",
                "&#x291E;": "\\rightarrow\\cdot",
                "&#x291D;": "\\leftarrow",
                "&#x291C;": "\\rightarrow",
                "&#x291B;": "\\leftarrow",
                "&#x291A;": "\\rightarrow",
                "&#x2919;": "\\leftarrow",
                "&#x2918;": "\\rightarrow",
                "&#x2917;": "\\rightarrow",
                "&#x2916;": "\\rightarrow",
                "&#x2915;": "\\rightarrow",
                "&#x2914;": "\\rightarrow",
                "&#x2913;": "\\downarrow",
                "&#x2912;": "\\uparrow",
                "&#x2911;": "\\rightarrow",
                "&#x2910;": "\\rightarrow",
                "&#x290F;": "\\rightarrow",
                "&#x290E;": "\\leftarrow",
                "&#x290D;": "\\rightarrow",
                "&#x290C;": "\\leftarrow",
                "&#x290B;": "\\Downarrow",
                "&#x290A;": "\\Uparrow",
                "&#x2909;": "\\uparrow",
                "&#x2908;": "\\downarrow",
                "&#x2907;": "\\Rightarrow",
                "&#x2906;": "\\Leftarrow",
                "&#x2905;": "\\mapsto",
                "&#x2904;": "\\nLeftrightarrow",
                "&#x2903;": "\\nRightarrow",
                "&#x2902;": "\\nLeftarrow",
                "&#x2901;": "\\rightsquigarrow",
                "&#x2900;": "\\rightsquigarrow",
                "&#x27FF;": "\\rightsquigarrow",
                "&#x27FE;": "\\Rightarrow",
                "&#x27FD;": "\\Leftarrow",
                "&#x27FC;": "\\mapsto",
                "&#x27FB;": "\\leftarrow",
                "&#x27FA;": "\\Longleftrightarrow",
                "&#x27F9;": "\\Longrightarrow",
                "&#x27F8;": "\\Longleftarrow",
                "&#x27F7;": "\\leftrightarrow",
                "&#x27F6;": "\\rightarrow",
                "&#x27F5;": "\\leftarrow",
                "&#x27F1;": "\\Downarrow",
                "&#x27F0;": "\\Uparrow",
                "&#x22B8;": "\\rightarrow",
                "&#x21FF;": "\\leftrightarrow",
                "&#x21FE;": "\\rightarrow",
                "&#x21FD;": "\\leftarrow",
                "&#x21FC;": "\\nleftrightarrow",
                "&#x21FB;": "\\nrightarrow",
                "&#x21FA;": "\\nleftarrow",
                "&#x21F9;": "\\nleftrightarrow",
                "&#x21F8;": "\\nrightarrow",
                "&#x21F7;": "\\nleftarrow",
                "&#x21F6;": "\\Rrightarrow",
                "&#x21F5;": "",
                "&#x21F4;": "\\rightarrow",
                "&#x21F3;": "\\Updownarrow",
                "&#x21F2;": "\\searrow",
                "&#x21F1;": "\\nwarrow",
                "&#x21F0;": "\\Leftarrow",
                "&#x21EF;": "\\Uparrow",
                "&#x21EE;": "\\Uparrow",
                "&#x21ED;": "\\Uparrow",
                "&#x21EC;": "\\Uparrow",
                "&#x21EB;": "\\Uparrow",
                "&#x21EA;": "\\Uparrow",
                "&#x21E9;": "\\Downarrow",
                "&#x21E8;": "\\Rightarrow",
                "&#x21E7;": "\\Uparrow",
                "&#x21E6;": "\\Leftarrow",
                "&#x21E5;": "\\rightarrow",
                "&#x21E4;": "\\leftarrow",
                "&#x21E3;": "\\downarrow",
                "&#x21E2;": "\\rightarrow",
                "&#x21E1;": "\\uparrow",
                "&#x21E0;": "\\leftarrow",
                "&#x21DF;": "\\downarrow",
                "&#x21DE;": "\\uparrow",
                "&#x21DD;": "\\rightsquigarrow",
                "&#x21DC;": "\\leftarrow",
                "&#x21DB;": "\\Rrightarrow",
                "&#x21DA;": "\\Lleftarrow",
                "&#x21D9;": "\\swarrow",
                "&#x21D8;": "\\searrow",
                "&#x21D7;": "\\nearrow",
                "&#x21D6;": "\\nwarrow",
                "&#x21D5;": "\\Updownarrow",
                "&#x21D4;": "\\Leftrightarrow",
                "&#x21D3;": "\\Downarrow",
                "&#x21D2;": "\\Rightarrow",
                "&#x21D1;": "\\Uparrow",
                "&#x21D0;": "\\Leftarrow",
                "&#x21CF;": "\\nRightarrow",
                "&#x21CE;": "\\nLeftrightarrow",
                "&#x21CD;": "\\nLeftarrow",
                "&#x21CC;": "\\rightleftharpoons",
                "&#x21CB;": "\\leftrightharpoons",
                "&#x21CA;": "\\downdownarrows",
                "&#x21C9;": "\\rightrightarrows",
                "&#x21C8;": "\\upuparrows",
                "&#x21C7;": "\\leftleftarrows",
                "&#x21C6;": "\\leftrightarrows",
                "&#x21C5;": "",
                "&#x21C4;": "\\rightleftarrows",
                "&#x21C3;": "\\downharpoonleft",
                "&#x21C2;": "\\downharpoonright",
                "&#x21C1;": "\\rightharpoondown",
                "&#x21C0;": "\\rightharpoonup",
                "&#x21BF;": "\\upharpoonleft",
                "&#x21BE;": "\\upharpoonright",
                "&#x21BD;": "\\leftharpoondown",
                "&#x21BC;": "\\leftharpoonup",
                "&#x21BB;": "\\circlearrowright",
                "&#x21BA;": "\\circlearrowleft",
                "&#x21B9;": "\\leftrightarrows",
                "&#x21B8;": "\\overline{\\nwarrow}",
                "&#x21B7;": "\\curvearrowright",
                "&#x21B6;": "\\curvearrowleft",
                "&#x21B5;": "\\swarrow",
                "&#x21B4;": "\\searrow",
                "&#x21B3;": "\\Rsh",
                "&#x21B2;": "\\Lsh",
                "&#x21B1;": "\\Rsh",
                "&#x21B0;": "\\Lsh",
                "&#x21AF;": "\\swarrow",
                "&#x21AE;": "",
                "&#x21AD;": "\\leftrightsquigarrow",
                "&#x21AC;": "\\looparrowright",
                "&#x21AB;": "\\looparrowleft",
                "&#x21AA;": "\\hookrightarrow",
                "&#x21A9;": "\\hookleftarrow",
                "&#x21A8;": "\\underline{\\updownarrow}",
                "&#x21A7;": "\\downarrow",
                "&#x21A6;": "\\rightarrowtail",
                "&#x21A5;": "\\uparrow",
                "&#x21A4;": "\\leftarrowtail",
                "&#x21A3;": "\\rightarrowtail",
                "&#x21A2;": "\\leftarrowtail",
                "&#x21A1;": "\\downarrow",
                "&#x21A0;": "\\twoheadrightarrow",
                "&#x219F;": "\\uparrow",
                "&#x219E;": "\\twoheadleftarrow",
                "&#x219D;": "\\nearrow",
                "&#x219C;": "\\nwarrow",
                "&#x219B;": "",
                "&#x219A;": "",
                "&#x2199;": "\\swarrow",
                "&#x2198;": "\\searrow",
                "&#x2197;": "\\nearrow",
                "&#x2196;": "\\nwarrow",
                "&#x2195;": "\\updownarrow",
                "&#x2194;": "\\leftrightarrow",
                "&#x2193;": "\\downarrow",
                "&#x2192;": "\\rightarrow",
                "&#x2191;": "\\uparrow",
                "&#x2190;": "\\leftarrow",
                "|||": "\\left|||\\right.",
                "||": "\\left||\\right.",
                "|": "\\left|\\right.",
                "&#x2AFE;": "",
                "&#x2AFD;": "//",
                "&#x2AFB;": "///",
                "&#x2AFA;": "",
                "&#x2AF9;": "",
                "&#x2AF8;": "",
                "&#x2AF7;": "",
                "&#x2AF6;": "\\vdots",
                "&#x2AF5;": "",
                "&#x2AF4;": "",
                "&#x2AF3;": "",
                "&#x2AF2;": "\\nparallel",
                "&#x2AF1;": "",
                "&#x2AF0;": "",
                "&#x2AEF;": "",
                "&#x2AEE;": "\\bcancel{\\mid}",
                "&#x2AED;": "",
                "&#x2AEC;": "",
                "&#x2AEB;": "",
                "&#x2AEA;": "",
                "&#x2AE9;": "",
                "&#x2AE8;": "\\underline{\\perp}",
                "&#x2AE7;": "\\overline{\\top}",
                "&#x2AE6;": "",
                "&#x2AE5;": "",
                "&#x2AE4;": "",
                "&#x2AE3;": "",
                "&#x2AE2;": "",
                "&#x2AE1;": "",
                "&#x2AE0;": "\\perp",
                "&#x2ADF;": "\\top",
                "&#x2ADE;": "\\dashv",
                "&#x2ADD;&#x338;": "",
                "&#x2ADD;": "",
                "&#x2ADB;": "\\pitchfork",
                "&#x2ADA;": "",
                "&#x2AD9;": "",
                "&#x2AD8;": "",
                "&#x2AD7;": "",
                "&#x2AD6;": "",
                "&#x2AD5;": "",
                "&#x2AD4;": "",
                "&#x2AD3;": "",
                "&#x2AD2;": "",
                "&#x2AD1;": "",
                "&#x2AD0;": "",
                "&#x2ACF;": "",
                "&#x2ACE;": "",
                "&#x2ACD;": "",
                "&#x2ACC;": "\\underset{\\neq}{\\supset}",
                "&#x2ACB;": "\\underset{\\neq}{\\subset}",
                "&#x2ACA;": "\\underset{\\approx}{\\supset}",
                "&#x2AC9;": "\\underset{\\approx}{\\subset}",
                "&#x2AC8;": "\\underset{\\sim}{\\supset}",
                "&#x2AC7;": "\\underset{\\sim}{\\subset}",
                "&#x2AC6;": "\\supseteqq",
                "&#x2AC5;": "\\subseteqq",
                "&#x2AC4;": "\\dot{\\supseteq}",
                "&#x2AC3;": "\\dot{\\subseteq}",
                "&#x2AC2;": "\\underset{\\times}{\\supset}",
                "&#x2AC1;": "\\underset{\\times}{\\subset}",
                "&#x2AC0;": "\\underset{+}{\\supset}",
                "&#x2ABF;": "\\underset{+}{\\subset}",
                "&#x2ABE;": "",
                "&#x2ABD;": "",
                "&#x2ABC;": "\\gg ",
                "&#x2ABB;": "\\ll",
                "&#x2ABA;": "\\underset{\\cancel{\\approx}}{\\succ}",
                "&#x2AB9;": "\\underset{\\cancel{\\approx}}{\\prec}",
                "&#x2AB8;": "\\underset{\\approx}{\\succ}",
                "&#x2AB7;": "\\underset{\\approx}{\\prec}",
                "&#x2AB6;": "\\underset{\\cancel{=}}{\\succ}",
                "&#x2AB5;": "\\underset{\\cancel{=}}{\\prec}",
                "&#x2AB4;": "\\underset{=}{\\succ}",
                "&#x2AB3;": "\\underset{=}{\\prec}",
                "&#x2AB2;": "",
                "&#x2AB1;": "",
                "&#x2AAE;": "",
                "&#x2AAD;": "\\underline{\\hcancel{>}}",
                "&#x2AAC;": "\\underline{\\hcancel{>}}",
                "&#x2AAB;": "\\hcancel{>}",
                "&#x2AAA;": "\\hcancel{<}",
                "&#x2AA9;": "",
                "&#x2AA8;": "",
                "&#x2AA7;": "\\vartriangleright",
                "&#x2AA6;": "\\vartriangleleft",
                "&#x2AA5;": "><",
                "&#x2AA4;": "><",
                "&#x2AA3;": "\\underline{\\ll}",
                "&#x2AA2;&#x338;": "\\cancel{\\gg}",
                "&#x2AA2;": "\\gg",
                "&#x2AA1;&#x338;": "\\cancel{\\ll}",
                "&#x2AA1;": "\\ll",
                "&#x2AA0;": "\\overset{\\sim}{\\geqq}",
                "&#x2A9F;": "\\overset{\\sim}{\\leqq}",
                "&#x2A9E;": "\\overset{\\sim}{>}",
                "&#x2A9D;": "\\overset{\\sim}{<}",
                "&#x2A9C;": "",
                "&#x2A9B;": "",
                "&#x2A9A;": "\\overset{=}{>}",
                "&#x2A99;": "\\overset{=}{<}",
                "&#x2A98;": "",
                "&#x2A97;": "",
                "&#x2A96;": "",
                "&#x2A95;": "",
                "&#x2A94;": "",
                "&#x2A93;": "",
                "&#x2A92;": "\\underset{=}{\\gtrless}",
                "&#x2A91;": "\\underset{=}{\\lessgtr}",
                "&#x2A90;": "\\underset{<}{\\gtrsim}",
                "&#x2A8F;": "\\underset{>}{\\lesssim}",
                "&#x2A8E;": "\\underset{\\simeq}{>}",
                "&#x2A8D;": "\\underset{\\simeq}{<}",
                "&#x2A8C;": "\\gtreqqless",
                "&#x2A8B;": "\\lesseqqgtr",
                "&#x2A8A;": "\\underset{\\cancel{\\approx}}{>}",
                "&#x2A89;": "\\underset{\\approx}{<}",
                "&#x2A86;": "\\underset{\\approx}{>}",
                "&#x2A85;": "\\underset{\\approx}{<}",
                "&#x2A84;": "",
                "&#x2A83;": "",
                "&#x2A82;": "",
                "&#x2A81;": "",
                "&#x2A80;": "",
                "&#x2A7F;": "",
                "&#x2A7E;&#x338;": "\\bcancel{\\geq}",
                "&#x2A7E;": "\\geq",
                "&#x2A7D;&#x338;": "\\bcancel{\\leq}",
                "&#x2A7D;": "\\leq",
                "&#x2A7C;": "",
                "&#x2A7B;": "",
                "&#x2A7A;": "",
                "&#x2A79;": "",
                "&#x2A78;": "\\overset{\\dots}{\\equiv}",
                "&#x2A77;": "",
                "&#x2A76;": "===",
                "&#x2A75;": "==",
                "&#x2A74;": "::=",
                "&#x2A73;": "",
                "&#x2A72;": "\\underset{=}{+}",
                "&#x2A71;": "\\overset{=}{+}",
                "&#x2A70;": "\\overset{\\approx}{=}",
                "&#x2A6F;": "\\overset{\\wedge}{=}",
                "&#x2A6E;": "\\overset{*}{=}",
                "&#x2A6D;": "\\dot{\\approx}",
                "&#x2A6C;": "",
                "&#x2A6B;": "",
                "&#x2A6A;": "\\dot{\\sim}",
                "&#x2A69;": "",
                "&#x2A68;": "",
                "&#x2A67;": "\\dot{\\equiv}",
                "&#x2A66;": "\\underset{\\cdot}{=}",
                "&#x2A65;": "",
                "&#x2A64;": "",
                "&#x2A63;": "\\underset{=}{\\vee}",
                "&#x2A62;": "\\overset{=}{\\vee}",
                "&#x2A61;": "ul(vv)",
                "&#x2A60;": "\\underset{=}{\\wedge}",
                "&#x2A5F;": "\\underline{\\wedge}",
                "&#x2A5E;": "\\overset{=}{\\wedge}",
                "&#x2A5D;": "\\hcancel{\\vee}",
                "&#x2A5C;": "\\hcancel{\\wedge}",
                "&#x2A5B;": "",
                "&#x2A5A;": "",
                "&#x2A59;": "",
                "&#x2A58;": "\\vee",
                "&#x2A57;": "\\wedge",
                "&#x2A56;": "",
                "&#x2A55;": "",
                "&#x2A54;": "",
                "&#x2A53;": "",
                "&#x2A52;": "\\dot{\\vee}",
                "&#x2A51;": "\\dot{\\wedge}",
                "&#x2A50;": "",
                "&#x2A4F;": "",
                "&#x2A4E;": "",
                "&#x2A4D;": "\\overline{\\cap}",
                "&#x2A4C;": "\\overline{\\cup}",
                "&#x2A4B;": "",
                "&#x2A4A;": "",
                "&#x2A49;": "",
                "&#x2A48;": "",
                "&#x2A47;": "",
                "&#x2A46;": "",
                "&#x2A45;": "",
                "&#x2A44;": "",
                "&#x2A43;": "\\overline{\\cap}",
                "&#x2A42;": "\\overline{\\cup}",
                "&#x2A41;": "",
                "&#x2A40;": "",
                "&#x2A3E;": "",
                "&#x2A3D;": "\\llcorner",
                "&#x2A3C;": "\\lrcorner",
                "&#x2A3B;": "",
                "&#x2A3A;": "",
                "&#x2A39;": "",
                "&#x2A38;": "",
                "&#x2A37;": "",
                "&#x2A36;": "\\hat{\\otimes}",
                "&#x2A35;": "",
                "&#x2A34;": "",
                "&#x2A33;": "",
                "&#x2A32;": "\\underline{\\times}",
                "&#x2A31;": "\\underline{\\times}",
                "&#x2A30;": "\\dot{\\times}",
                "&#x2A2E;": "",
                "&#x2A2D;": "",
                "&#x2A2C;": "",
                "&#x2A2B;": "",
                "&#x2A2A;": "",
                "&#x2A29;": "",
                "&#x2A28;": "",
                "&#x2A27;": "",
                "&#x2A26;": "\\underset{\\sim}{+}",
                "&#x2A25;": "\\underset{\\circ}{+}",
                "&#x2A24;": "\\overset{\\sim}{+}",
                "&#x2A23;": "\\hat{+}",
                "&#x2A22;": "\\dot{+}",
                "&#x2A21;": "\\upharpoonright",
                "&#x2A20;": ">>",
                "&#x2A1F;": "",
                "&#x2A1E;": "\\triangleleft",
                "&#x2A1D;": "\\bowtie",
                "&#x29FF;": "",
                "&#x29FE;": "+",
                "&#x29FB;": "\\hcancel{|||}",
                "&#x29FA;": "\\hcancel{||}",
                "&#x29F9;": "\\backslash",
                "&#x29F8;": "/",
                "&#x29F7;": "hcancel{\backslash}",
                "&#x29F6;": "",
                "&#x29F5;": "\\backslash",
                "&#x29F2;": "\\Phi",
                "&#x29F1;": "",
                "&#x29F0;": "",
                "&#x29EE;": "",
                "&#x29ED;": "",
                "&#x29EC;": "",
                "&#x29EB;": "\\lozenge",
                "&#x29EA;": "",
                "&#x29E9;": "",
                "&#x29E8;": "",
                "&#x29E7;": "\\ddagger",
                "&#x29E2;": "\\sqcup\\sqcup",
                "&#x29E1;": "",
                "&#x29E0;": "\\square",
                "&#x29DE;": "",
                "&#x29DD;": "",
                "&#x29DC;": "",
                "&#x29DB;": "\\{\\{",
                "&#x29D9;": "\\{",
                "&#x29D8;": "\\}",
                "&#x29D7;": "",
                "&#x29D6;": "",
                "&#x29D5;": "\\bowtie",
                "&#x29D4;": "\\bowtie",
                "&#x29D3;": "\\bowtie",
                "&#x29D2;": "\\bowtie",
                "&#x29D1;": "\\bowtie",
                "&#x29D0;&#x338;": "| \\not\\triangleright",
                "&#x29D0;": "| \\triangleright",
                "&#x29CF;&#x338;": "\\not\\triangleleft |",
                "&#x29CF;": "\\triangleleft |",
                "&#x29CE;": "",
                "&#x29CD;": "\\triangle",
                "&#x29CC;": "",
                "&#x29CB;": "\\underline{\\triangle}",
                "&#x29CA;": "\\dot{\\triangle}",
                "&#x29C9;": "",
                "&#x29C8;": "\\boxed{\\circ}",
                "&#x29C7;": "\\boxed{\\circ}",
                "&#x29C6;": "\\boxed{\\rightarrow}",
                "&#x29C5;": "\\bcancel{\\square}",
                "&#x29C4;": "\\cancel{\\square}",
                "&#x29C3;": "\\odot",
                "&#x29C2;": "\\odot",
                "&#x29BF;": "\\odot",
                "&#x29BE;": "\\odot",
                "&#x29BD;": "\\varnothing",
                "&#x29BC;": "\\oplus",
                "&#x29BB;": "\\otimes",
                "&#x29BA;": "",
                "&#x29B9;": "\\varnothing",
                "&#x29B8;": "\\varnothing",
                "&#x29B7;": "\\ominus",
                "&#x29B6;": "\\ominus",
                "&#x29B5;": "\\ominus",
                "&#x29B4;": "\\vec{\\varnothing}",
                "&#x29B3;": "\\vec{\\varnothing}",
                "&#x29B2;": "\\dot{\\varnothing}",
                "&#x29B1;": "\\overline{\\varnothing}",
                "&#x29B0;": "\\varnothing",
                "&#x29AF;": "",
                "&#x29AE;": "",
                "&#x29AD;": "",
                "&#x29AC;": "",
                "&#x29AB;": "",
                "&#x29AA;": "",
                "&#x29A9;": "",
                "&#x29A8;": "",
                "&#x29A7;": "",
                "&#x29A6;": "",
                "&#x29A5;": "",
                "&#x29A4;": "",
                "&#x29A3;": "",
                "&#x29A2;": "",
                "&#x29A1;": "\\not\\lor",
                "&#x29A0;": "\\bcancel{>}",
                "&#x2982;": ":",
                "&#x2981;": "\\circ",
                "&#x2758;": "|",
                "&#x25B2;": "\\bigtriangleup",
                "&#x22FF;": "\\Epsilon",
                "&#x22FE;": "\\overline{\\ni}",
                "&#x22FD;": "\\overline{\\ni}",
                "&#x22FC;": "\\in",
                "&#x22FB;": "\\in",
                "&#x22FA;": "\\in",
                "&#x22F9;": "\\underline{\\in}",
                "&#x22F8;": "\\underline{\\in}",
                "&#x22F7;": "\\overline{\\in}",
                "&#x22F6;": "\\overline{\\in}",
                "&#x22F5;": "\\dot{\\in}",
                "&#x22F4;": "\\in",
                "&#x22F3;": "\\in",
                "&#x22F2;": "\\in",
                "&#x22F0;": "\\ddots",
                "&#x22E9;": "\\underset{\\sim}{\\succ}",
                "&#x22E8;": "\\underset{\\sim}{\\prec}",
                "&#x22E7;": "\\underset{\\not\\sim}{>}",
                "&#x22E6;": "\\underset{\\not\\sim}{<}",
                "&#x22E5;": "\\not\\sqsupseteq",
                "&#x22E4;": "\\not\\sqsubseteq",
                "&#x22E3;": "\\not\\sqsupseteq",
                "&#x22E2;": "\\not\\sqsubseteq",
                "&#x22E1;": "\\nsucc",
                "&#x22E0;": "\\nprec",
                "&#x22DF;": "\\succ",
                "&#x22DE;": "\\prec",
                "&#x22DD;": "\\overline{>}",
                "&#x22DC;": "\\overline{<}",
                "&#x22DB;": "\\underset{>}{\\leq}",
                "&#x22DA;": "\\underset{<}{\\geq}",
                "&#x22D5;": "\\#",
                "&#x22D3;": "\\cup",
                "&#x22D2;": "\\cap",
                "&#x22D1;": "\\supset",
                "&#x22D0;": "\\subset",
                "&#x22CF;": "\\wedge",
                "&#x22CE;": "\\vee",
                "&#x22CD;": "\\simeq",
                "&#x22C8;": "\\bowtie",
                "&#x22C7;": "\\ast",
                "&#x22C6;": "\\star",
                "&#x22C4;": "\\diamond",
                "&#x22BF;": "\\triangle",
                "&#x22BE;": "\\measuredangle",
                "&#x22BD;": "\\overline{\\lor}",
                "&#x22BC;": "\\overline{\\land}",
                "&#x22BB;": "\\underline{\\lor}",
                "&#x22BA;": "\\top",
                "&#x22B9;": "",
                "&#x22B7;": "\\circ\\multimap",
                "&#x22B6;": "\\circ\\multimap",
                "&#x22B3;": "\\triangleright",
                "&#x22B2;": "\\triangleleft",
                "&#x22B1;": "\\succ",
                "&#x22B0;": "\\prec",
                "&#x22AB;": "|\\models",
                "&#x22AA;": "|\\models",
                "&#x22A7;": "\\models",
                "&#x22A6;": "\\vdash",
                "&#x229D;": "\\ominus",
                "&#x229C;": "\\ominus",
                "&#x229B;": "\\odot",
                "&#x229A;": "\\odot",
                "&#x2294;": "\\sqcup",
                "&#x2293;": "\\sqcap",
                "&#x2292;": "\\sqsupseteq",
                "&#x2291;": "\\sqsubseteq",
                "&#x2290;&#x338;": "\\not\\sqsupset",
                "&#x2290;": "\\sqsupset",
                "&#x228F;&#x338;": "\\not\\sqsubset",
                "&#x228F;": "\\sqsubset",
                "&#x228E;": "\\cup",
                "&#x228D;": "\\cup",
                "&#x228C;": "\\cup",
                "&#x227F;&#x338;": "\\not\\succsim",
                "&#x227F;": "\\succsim",
                "&#x227E;": "\\precsim",
                "&#x2279;": "\\not\\overset{>}{<}",
                "&#x2278;": "\\not\\overset{>}{<}",
                "&#x2277;": "\\overset{>}{<}",
                "&#x2276;": "\\overset{<}{>}",
                "&#x2275;": "\\not\\geg",
                "&#x2274;": "\\not\\leq",
                "&#x2273;": "\\geg",
                "&#x2272;": "\\leq",
                "&#x226C;": "",
                "&#x2267;": "\\geg",
                "&#x2266;&#x338;": "\\not\\leq",
                "&#x2266;": "\\leq",
                "&#x2263;": "\\overset{=}{=} ",
                "&#x225E;": "\\overset{m}{=} ",
                "&#x225D;": "\\overset{def}{=}",
                "&#x2258;": "=",
                "&#x2256;": "=",
                "&#x2255;": "=:",
                "&#x2253;": "\\doteq",
                "&#x2252;": "\\doteq",
                "&#x2251;": "\\doteq",
                "&#x2250;": "\\doteq",
                "&#x224F;&#x338;": "",
                "&#x224F;": "",
                "&#x224E;&#x338;": "",
                "&#x224E;": "",
                "&#x224C;": "\\approx",
                "&#x224B;": "\\approx",
                "&#x224A;": "\\approx",
                "&#x2242;&#x338;": "\\neq",
                "&#x2242;": "=",
                "&#x223F;": "\\sim",
                "&#x223E;": "\\infty",
                "&#x223D;&#x331;": "\\sim",
                "&#x223D;": "\\sim",
                "&#x223B;": "\\sim",
                "&#x223A;": ":-:",
                "&#x2239;": "-:",
                "&#x2238;": "\\bot",
                "&#x2237;": "::",
                "&#x2236;": ":",
                "&#x2223;": "|",
                "&#x221F;": "\\llcorner",
                "&#x2219;": "\\cdot",
                "&#x2218;": "\\circ",
                "&#x2217;": "*",
                "&#x2215;": "/",
                "&#x220E;": "\\square",
                "&#x220D;": "\\ni",
                "&#x220A;": "\\in",
                "&#x2206;": "\\Delta",
                "&#x2044;": "/",
                "&#x2AB0;&#x338;": "\\nsucceq",
                "&#x2AB0;": "\\succeq",
                "&#x2AAF;&#x338;": "\\npreceq",
                "&#x2AAF;": "\\preceq",
                "&#x2A88;": "\\ngeqslant",
                "&#x2A87;": "\\nleqslant",
                "&#x29F3;": "\\Phi",
                "&#x29E6;": "\\models",
                "&#x29E5;": "\\not\\equiv",
                "&#x29E4;": "\\approx\\neq",
                "&#x29E3;": "\\neq",
                "&#x29C1;": "\\circle",
                "&#x29C0;": "\\circle",
                "&#x25E6;": "\\circle",
                "&#x25D7;": "\\circle",
                "&#x25D6;": "\\circle",
                "&#x25CF;": "\\circle",
                "&#x25CE;": "\\circledcirc",
                "&#x25CD;": "\\circledcirc",
                "&#x25CC;": "\\circledcirc",
                "&#x25C9;": "\\circledcirc",
                "&#x25C8;": "\\diamond",
                "&#x25C7;": "\\diamond",
                "&#x25C6;": "\\diamond",
                "&#x25C5;": "\\triangleleft",
                "&#x25C4;": "\\triangleleft",
                "&#x25C3;": "\\triangleleft",
                "&#x25C2;": "\\triangleleft",
                "&#x25C1;": "\\triangleleft",
                "&#x25C0;": "\\triangleleft",
                "&#x25BF;": "\\triangledown",
                "&#x25BE;": "\\triangledown",
                "&#x25BD;": "\\triangledown",
                "&#x25BC;": "\\triangledown",
                "&#x25B9;": "\\triangleright",
                "&#x25B8;": "\\triangleright",
                "&#x25B7;": "\\triangleright",
                "&#x25B6;": "\\triangleright",
                "&#x25B5;": "\\triangle",
                "&#x25B4;": "\\triangle",
                "&#x25B3;": "\\triangle",
                "&#x25B1;": "\\square",
                "&#x25B0;": "\\square",
                "&#x25AF;": "\\square",
                "&#x25AE;": "\\square",
                "&#x25AD;": "\\square",
                "&#x25AB;": "\\square",
                "&#x25AA;": "\\square",
                "&#x25A1;": "\\square",
                "&#x25A0;": "\\square",
                "&#x22ED;": "\\not\\triangleright",
                "&#x22EC;": "\\not\\triangleleft",
                "&#x22EB;": "\\not\\triangleright",
                "&#x22EA;": "\\not\\triangleleft",
                "&#x22D9;": "\\ggg",
                "&#x22D8;": "\\lll",
                "&#x22D7;": "*>",
                "&#x22D6;": "<*",
                "&#x22D4;": "\\pitchfork",
                "&#x22CC;": "",
                "&#x22CB;": "",
                "&#x22CA;": "\\rtimes",
                "&#x22C9;": "\\ltimes",
                "&#x22B5;": "\\triangleright",
                "&#x22B4;": "",
                "&#x22A5;": "\\bot",
                "&#x2281;": "\\nsucc",
                "&#x2280;": "\\preceq",
                "&#x227D;": "\\succeq",
                "&#x227C;": "\\preceq",
                "&#x227B;": "\\succ",
                "&#x227A;": "\\prec",
                "&#x2271;": "\\geq/",
                "&#x2270;": "\\leq/",
                "&#x226D;": "\\neq",
                "&#x226B;&#x338;": "\\not\\gg",
                "&#x226B;": "\\gg",
                "&#x226A;&#x338;": "\\not\\ll",
                "&#x226A;": "\\ll",
                "&#x2269;": "\\ngeqslant",
                "&#x2268;": "\\nleqslant",
                "&#x2261;": "\\equiv",
                "&#x225F;": "\\doteq",
                "&#x225C;": "\\triangleq",
                "&#x225B;": "\\doteq",
                "&#x225A;": "\\triangleq",
                "&#x2259;": "\\triangleq",
                "&#x2257;": "\\doteq",
                "&#x2254;": ":=",
                "&#x224D;": "\\asymp",
                "&#x2247;": "\\ncong",
                "&#x2246;": "\\ncong",
                "&#x2245;": "\\cong",
                "&#x2244;": "\\not\\simeq",
                "&#x2243;": "\\simeq",
                "&#x2241;": "\\not\\sim",
                "&#x2226;": "\\not\\parallel",
                "&#x2225;": "\\parallel",
                "&#x2224;": "\\not|",
                "&#x221D;": "\\propto",
                "==": "==",
                "=": "=",
                ":=": ":=",
                "/=": "=",
                "-=": "-=",
                "+=": "+=",
                "*=": "*=",
                "!=": "!=",
                "&#x2260;": "\\neq",
                "&#x2262;": "\\equiv /",
                "&#x2249;": "\\approx /",
                "&#x223C;": "sim",
                "&#x2248;": "\\approx",
                "&#x226E;": "</",
                "&lt;": "<",
                "&#x226F;": ">/",
                ">=": ">=",
                ">": ">",
                "&#x2265;": "\\geq",
                "&#x2264;": "\\leq",
                "&lt;=": "<=",
                "&#x228B;": "\\supsetneq",
                "&#x228A;": "\\subsetneq",
                "&#x2289;": "\\nsupseteq",
                "&#x2288;": "\\nsubseteq",
                "&#x2287;": "\\supseteq",
                "&#x2286;": "\\subseteq",
                "&#x2285;": "\\not\\supset",
                "&#x2284;": "\\not\\subset",
                "&#x2283;&#x20D2;": "\\supset |",
                "&#x2283;": "\\supset",
                "&#x2282;&#x20D2;": "\\subset |",
                "&#x2282;": "\\subset",
                "&#x220C;": "\\not\\in",
                "&#x2209;": "\\notin",
                "&#x2208;": "\\in",
                "&#x2201;": "C",
                "&#x2204;": "\\nexists",
                "&#x2203;": "\\exists",
                "&#x2200;": "\\forall",
                "&#x2227;": "\\land",
                "&amp;&amp;": "\\&\\&",
                "&#x2228;": "\\lor",
                "&#x22AF;": "\\cancel{\\vDash}",
                "&#x22AE;": "\\cancel{\\Vdash}",
                "&#x22AD;": "\\nvDash",
                "&#x22AC;": "\\nvDash",
                "&#x22A9;": "\\Vdash",
                "&#x22A8;": "\\vDash",
                "&#x22A4;": "\\top",
                "&#x22A3;": "\\dashv",
                "&#x22A2;": "\\vdash",
                "&#x220B;": "\\ni",
                "&#x22F1;": "\\ddots",
                "&#x22EF;": "\\hdots",
                "&#x22EE;": "\\vdots",
                "&#x2026;": "\\hdots",
                "&#x3F6;": "\\ni",
                ":": ":",
                "...": "\\cdots",
                "..": "..",
                "->": "->",
                "&#x2235;": "\\because",
                "&#x2234;": "\\therefore ",
                "&#x2063;": "",
                ",": ",",
                ";": ";",
                "&#x29FD;": "\\}",
                "&#x29FC;": "\\{",
                "&#x2998;": "\\]",
                "&#x2997;": "\\[",
                "&#x2996;": "\\ll",
                "&#x2995;": "\\gg",
                "&#x2994;": "\\gg",
                "&#x2993;": "\\ll",
                "&#x2992;": "\\gg",
                "&#x2991;": "\\ll",
                "&#x2990;": "\\]",
                "&#x298F;": "\\]",
                "&#x298E;": "\\]",
                "&#x298D;": "\\[",
                "&#x298C;": "\\[",
                "&#x298B;": "\\]",
                "&#x298A;": "\\triangleright",
                "&#x2989;": "\\triangleleft",
                "&#x2988;": "|\\)",
                "&#x2987;": "\\(|",
                "&#x2986;": "|\\)",
                "&#x2985;": "\\(\\(",
                "&#x2984;": "|\\}",
                "&#x2983;": "\\{|",
                "&#x2980;": "\\||",
                "&#x27EF;": "\\left. \\right]",
                "&#x27EE;": "\\left[ \\right.",
                "&#x27ED;": "\\left. \\right]]",
                "&#x27EC;": "\\left[[ \\right.",
                "&#x27EB;": "\\gg",
                "&#x27EA;": "\\ll",
                "&#x27E9;": "\\rangle",
                "&#x27E8;": "\\langle",
                "&#x27E7;": "\\left. \\right]]",
                "&#x27E6;": "\\left[[ \\right.",
                "&#x2773;": "\\left.\\right)",
                "&#x2772;": "\\left(\\right.",
                "&#x232A;": "\\rangle",
                "&#x2329;": "\\langle",
                "&#x230B;": "\\rfloor",
                "&#x230A;": "\\lfloor",
                "&#x2309;": "\\rceil",
                "&#x2308;": "\\lceil",
                "&#x2016;": "\\parallel",
                "}": "\\left.\\right\\}",
                "{": "\\left\\{\\right.",
                "]": "\\left]\\right.",
                "[": "\\left[\\right.",
                ")": "\\left.\\right)",
                "(": "\\left(\\right.",
                "&#x201D;": '"',
                "&#x201C;": "``",
                "&#x2019;": "'",
                "&#x2018;": "`",
                "%CE%B1": "\\alpha",
                "%CE%B2": "\\beta",
                "%CE%B3": "\\gamma",
                "%CE%93": "\\Gamma",
                "%CE%B4": "\\delta",
                "%CE%94": "\\Delta",
                "%CF%B5": "\\epsilon",
                "%CE%B6": "\\zeta",
                "%CE%B7": "\\eta",
                "%CE%B8": "\\theta",
                "%CE%98": "\\Theta",
                "%CE%B9": "\\iota",
                "%CE%BA": "\\kappa",
                "%CE%BB": "\\lambda",
                "%CE%BC": "\\mu",
                "%CE%BD": "\\nu",
                "%CE%BF": "\\omicron",
                "%CF%80": "\\pi",
                "%CE%A0": "\\Pi",
                "%CF%81": "\\pho",
                "%CF%83": "\\sigma",
                "%CE%A3": "\\Sigma",
                "%CF%84": "\\tau",
                "%CF%85": "\\upsilon",
                "%CE%A5": "\\Upsilon",
                "%CF%95": "\\phi",
                "%CE%A6": "\\Phi",
                "%CF%87": "\\chi",
                "%CF%88": "\\psi",
                "%CE%A8": "\\Psi",
                "%CF%89": "\\omega",
                "%CE%A9": "\\Omega"
            }
        }
        ,
        598: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.allMathOperatorsByGlyph = void 0,
            t.allMathOperatorsByGlyph = {
                _: "\\underline",
                "⏡": "\\underbrace",
                "⏠": "\\overbrace",
                "⏟": "\\underbrace",
                "⏞": "\\overbrace",
                "⏝": "\\underbrace",
                "⏜": "\\overbrace",
                "⎵": "\\underbrace",
                "⎴": "\\overbrace",
                "⃜": "\\square",
                "⃛": "\\square",
                "⁤": "",
                "⁗": "''''",
                "‾": "\\overline",
                "‷": "```",
                "‶": "``",
                "‵": "`",
                "‴": "'''",
                "″": "''",
                "‟": "``",
                "„": ",,",
                "‛": "`",
                "‚": ",",
                "^": "\\hat",
                "˷": "\\sim",
                "˝": "\\sim",
                "˜": "\\sim",
                "˚": "\\circ",
                "˙": "\\cdot",
                "˘": " ",
                ˍ: "\\_",
                ˋ: "ˋ",
                ˊ: "ˊ",
                ˉ: "ˉ",
                ˇ: "",
                ˆ: "\\hat",
                º: "o",
                "¹": "1",
                "¸": ",",
                "´": "´",
                "³": "3",
                "²": "2",
                "°": "\\circ",
                "¯": "\\bar",
                ª: "a",
                "↛": "\\nrightarrow",
                "¨": "\\cdot\\cdot",
                "~": "\\sim",
                "`": "`",
                "--": "--",
                "++": "++",
                "&": "\\&",
                "∜": "\\sqrt[4]{}",
                "∛": "\\sqrt[3]{}",
                "√": "\\sqrt{}",
                ⅆ: "d",
                ⅅ: "\\mathbb{D}",
                "?": "?",
                "@": "@",
                "//": "//",
                "!!": "!!",
                "!": "!",
                "♯": "\\#",
                "♮": "",
                "♭": "",
                "′": "'",
                "<>": "<>",
                "**": "\\star\\star",
                "∇": "\\nabla",
                "∂": "\\partial",
                "⊙": "\\bigodot",
                "¬": "\\neg",
                "∢": "\\measuredangle",
                "∡": "\\measuredangle",
                "∠": "\\angle",
                "÷": "\\div",
                "/": "/",
                "∖": "\\backslash",
                "\\": "\\backslash",
                "%": "\\%",
                "⊗": "\\bigotimes",
                "·": "\\cdot",
                "⨿": "\\coprod",
                "⨯": "\\times",
                "⋅": "\\cdot",
                "⊡": "\\boxdot",
                "⊠": "\\boxtimes",
                "⁢": "",
                "⁃": "-",
                "•": "\\cdot",
                ".": ".",
                "*": "\\star",
                "∪": "\\cup",
                "∩": "\\cap",
                "∐": "\\coprod",
                "∏": "\\prod",
                "≀": "",
                "⫿": "",
                "⫼": "\\mid\\mid\\mid",
                "⨉": "\\times",
                "⨈": "",
                "⨇": "",
                "⨆": "\\sqcup",
                "⨅": "\\sqcap",
                "⨂": "\\otimes",
                "⨀": "\\odot",
                "⋂": "\\cap",
                "⋁": "\\vee",
                "⋀": "\\wedge",
                "⨄": "\\uplus",
                "⨃": "\\cup",
                "⋃": "\\cup",
                "⨜": "\\underline{\\int}",
                "⨛": "\\overline{\\int}",
                "⨚": "\\int",
                "⨙": "\\int",
                "⨘": "\\int",
                "⨗": "\\int",
                "⨖": "\\oint",
                "⨕": "\\oint",
                "⨔": "\\int",
                "⨓": "\\int",
                "⨒": "\\int",
                "⨑": "\\int",
                "⨐": "\\int",
                "⨏": "\\bcancel{\\int}",
                "⨎": "",
                "⨍": "\\hcancel{\\int}",
                "⨌": "\\iiiint",
                "∳": "\\oint",
                "∲": "\\oint",
                "∱": "\\int",
                "∰": "\\oiint",
                "∯": "\\oiint",
                "∮": "\\oint",
                "∫": "\\int",
                "⨁": "\\oplus",
                "⊘": "\\oslash",
                "⊖": "\\ominus",
                "⊕": "\\oplus",
                "∭": "\\iiint",
                "∬": "\\iint",
                "⨋": "",
                "⨊": "",
                "∑": "\\sum",
                "⊟": "\\boxminus",
                "⊞": "\\boxplus",
                "∔": "\\dot{+}",
                "∓": "+-",
                "−": "-",
                "±": "\\pm",
                "-": "-",
                "+": "+",
                "⭆": "\\Rrightarrow",
                "⭅": "\\Lleftarrow",
                "⧴": ":\\rightarrow",
                "⧯": "",
                "⧟": "\\bullet-\\bullet",
                "⦟": "\\angle",
                "⦞": "\\measuredangle",
                "⦝": "\\measuredangle",
                "⦜": "\\perp",
                "⦛": "\\measuredangle",
                "⦚": "",
                "⦙": "\\vdots",
                "⥿": "",
                "⥾": "",
                "⥽": "\\prec",
                "⥼": "\\succ",
                "⥻": "\\underset{\\rightarrow}{\\supset}",
                "⥺": "",
                "⥹": "\\underset{\\rightarrow}{\\subset}",
                "⥸": "\\underset{\\rightarrow}{>}",
                "⥷": "",
                "⥶": "\\underset{\\leftarrow}{<}",
                "⥵": "\\underset{\\approx}{\\rightarrow}",
                "⥴": "\\underset{\\sim}{\\rightarrow}",
                "⥳": "\\underset{\\sim}{\\leftarrow}",
                "⥲": "\\overset{\\sim}{\\rightarrow}",
                "⥱": "\\overset{=}{\\rightarrow}",
                "⥰": "",
                "⥯": "",
                "⥮": "",
                "⥭": "\\overline{\\rightharpoondown}",
                "⥬": "\\underline{\\rightharpoonup}",
                "⥫": "\\overline{\\leftharpoondown}",
                "⥪": "\\underline{\\leftharpoonup}",
                "⥩": "\\rightleftharpoons",
                "⥨": "\\rightleftharpoons",
                "⥧": "\\rightleftharpoons",
                "⥦": "\\rightleftharpoons",
                "⥥": "\\Downarrow",
                "⥤": "\\Rightarrow",
                "⥣": "\\Uparrow",
                "⥢": "\\Leftarrow",
                "⥡": "\\downarrow",
                "⥠": "\\uparrow",
                "⥟": "\\rightarrow",
                "⥞": "\\leftarrow",
                "⥝": "\\downarrow",
                "⥜": "\\uparrow",
                "⥛": "\\rightarrow",
                "⥚": "\\leftarrow",
                "⥙": "\\downarrow",
                "⥘": "\\uparrow",
                "⥗": "\\rightarrow",
                "⥖": "\\leftarrow",
                "⥕": "\\downarrow",
                "⥔": "\\uparrow",
                "⥓": "\\rightarrow",
                "⥒": "\\leftarrow",
                "⥑": "\\updownarrow",
                "⥐": "\\leftrightarrow",
                "⥏": "\\updownarrow",
                "⥎": "\\leftrightarrow",
                "⥍": "\\updownarrow",
                "⥌": "\\updownarrow",
                "⥋": "\\leftrightarrow",
                "⥊": "\\leftrightarrow",
                "⥉": "",
                "⥈": "\\leftrightarrow",
                "⥇": "\\nrightarrow",
                "⥆": "",
                "⥅": "",
                "⥄": "\\rightleftarrows",
                "⥃": "\\leftrightarrows",
                "⥂": "\\rightleftarrows",
                "⥁": "\\circlearrowright",
                "⥀": "\\circlearrowleft",
                "⤿": "\\rightarrow",
                "⤾": "\\leftarrow",
                "⤽": "\\leftarrow",
                "⤼": "\\rightarrow",
                "⤻": "\\rightarrow",
                "⤺": "\\leftarrow",
                "⤹": "\\downarrow",
                "⤸": "\\downarrow",
                "⤷": "\\Rsh",
                "⤶": "\\Lsh",
                "⤵": "\\downarrow",
                "⤴": "\\uparrow",
                "⤳": "\\rightarrow",
                "⤲": "\\leftarrow",
                "⤱": " ",
                "⤰": " ",
                "⤯": " ",
                "⤮": " ",
                "⤭": " ",
                "⤬": "\\times",
                "⤫": "\\times",
                "⤪": " ",
                "⤩": " ",
                "⤨": " ",
                "⤧": " ",
                "⤦": " ",
                "⤥": " ",
                "⤤": " ",
                "⤣": " ",
                "⤢": " ",
                "⤡": " ",
                "⤠": "\\mapsto\\cdot",
                "⤟": "\\cdot\\leftarrow",
                "⤞": "\\rightarrow\\cdot",
                "⤝": "\\leftarrow",
                "⤜": "\\rightarrow",
                "⤛": "\\leftarrow",
                "⤚": "\\rightarrow",
                "⤙": "\\leftarrow",
                "⤘": "\\rightarrow",
                "⤗": "\\rightarrow",
                "⤖": "\\rightarrow",
                "⤕": "\\rightarrow",
                "⤔": "\\rightarrow",
                "⤓": "\\downarrow",
                "⤒": "\\uparrow",
                "⤑": "\\rightarrow",
                "⤐": "\\rightarrow",
                "⤏": "\\rightarrow",
                "⤎": "\\leftarrow",
                "⤍": "\\rightarrow",
                "⤌": "\\leftarrow",
                "⤋": "\\Downarrow",
                "⤊": "\\Uparrow",
                "⤉": "\\uparrow",
                "⤈": "\\downarrow",
                "⤇": "\\Rightarrow",
                "⤆": "\\Leftarrow",
                "⤅": "\\mapsto",
                "⤄": "\\nLeftrightarrow",
                "⤃": "\\nRightarrow",
                "⤂": "\\nLeftarrow",
                "⤁": "\\rightsquigarrow",
                "⤀": "\\rightsquigarrow",
                "⟿": "\\rightsquigarrow",
                "⟾": "\\Rightarrow",
                "⟽": "\\Leftarrow",
                "⟼": "\\mapsto",
                "⟻": "\\leftarrow",
                "⟺": "\\Longleftrightarrow",
                "⟹": "\\Longrightarrow",
                "⟸": "\\Longleftarrow",
                "⟷": "\\leftrightarrow",
                "⟶": "\\rightarrow",
                "⟵": "\\leftarrow",
                "⟱": "\\Downarrow",
                "⟰": "\\Uparrow",
                "⊸": "\\rightarrow",
                "⇿": "\\leftrightarrow",
                "⇾": "\\rightarrow",
                "⇽": "\\leftarrow",
                "⇼": "\\nleftrightarrow",
                "⇻": "\\nrightarrow",
                "⇺": "\\nleftarrow",
                "⇹": "\\nleftrightarrow",
                "⇸": "\\nrightarrow",
                "⇷": "\\nleftarrow",
                "⇶": "\\Rrightarrow",
                "⇵": "",
                "⇴": "\\rightarrow",
                "⇳": "\\Updownarrow",
                "⇲": "\\searrow",
                "⇱": "\\nwarrow",
                "⇰": "\\Leftarrow",
                "⇯": "\\Uparrow",
                "⇮": "\\Uparrow",
                "⇭": "\\Uparrow",
                "⇬": "\\Uparrow",
                "⇫": "\\Uparrow",
                "⇪": "\\Uparrow",
                "⇩": "\\Downarrow",
                "⇨": "\\Rightarrow",
                "⇧": "\\Uparrow",
                "⇦": "\\Leftarrow",
                "⇥": "\\rightarrow",
                "⇤": "\\leftarrow",
                "⇣": "\\downarrow",
                "⇢": "\\rightarrow",
                "⇡": "\\uparrow",
                "⇠": "\\leftarrow",
                "⇟": "\\downarrow",
                "⇞": "\\uparrow",
                "⇝": "\\rightsquigarrow",
                "⇜": "\\leftarrow",
                "⇛": "\\Rrightarrow",
                "⇚": "\\Lleftarrow",
                "⇙": "\\swarrow",
                "⇘": "\\searrow",
                "⇗": "\\nearrow",
                "⇖": "\\nwarrow",
                "⇕": "\\Updownarrow",
                "⇔": "\\Leftrightarrow",
                "⇓": "\\Downarrow",
                "⇒": "\\Rightarrow",
                "⇑": "\\Uparrow",
                "⇐": "\\Leftarrow",
                "⇏": "\\nRightarrow",
                "⇎": "\\nLeftrightarrow",
                "⇍": "\\nLeftarrow",
                "⇌": "\\rightleftharpoons",
                "⇋": "\\leftrightharpoons",
                "⇊": "\\downdownarrows",
                "⇉": "\\rightrightarrows",
                "⇈": "\\upuparrows",
                "⇇": "\\leftleftarrows",
                "⇆": "\\leftrightarrows",
                "⇅": "",
                "⇄": "\\rightleftarrows",
                "⇃": "\\downharpoonleft",
                "⇂": "\\downharpoonright",
                "⇁": "\\rightharpoondown",
                "⇀": "\\rightharpoonup",
                "↿": "\\upharpoonleft",
                "↾": "\\upharpoonright",
                "↽": "\\leftharpoondown",
                "↼": "\\leftharpoonup",
                "↻": "\\circlearrowright",
                "↺": "\\circlearrowleft",
                "↹": "\\leftrightarrows",
                "↸": "\\overline{\\nwarrow}",
                "↷": "\\curvearrowright",
                "↶": "\\curvearrowleft",
                "↵": "\\swarrow",
                "↴": "\\searrow",
                "↳": "\\Rsh",
                "↲": "\\Lsh",
                "↱": "\\Rsh",
                "↰": "\\Lsh",
                "↯": "\\swarrow",
                "↮": "",
                "↭": "\\leftrightsquigarrow",
                "↬": "\\looparrowright",
                "↫": "\\looparrowleft",
                "↪": "\\hookrightarrow",
                "↩": "\\hookleftarrow",
                "↨": "\\underline{\\updownarrow}",
                "↧": "\\downarrow",
                "↦": "\\rightarrowtail",
                "↥": "\\uparrow",
                "↤": "\\leftarrowtail",
                "↣": "\\rightarrowtail",
                "↢": "\\leftarrowtail",
                "↡": "\\downarrow",
                "↠": "\\twoheadrightarrow",
                "↟": "\\uparrow",
                "↞": "\\twoheadleftarrow",
                "↝": "\\nearrow",
                "↜": "\\nwarrow",
                "↚": "",
                "↙": "\\swarrow",
                "↘": "\\searrow",
                "↗": "\\nearrow",
                "↖": "\\nwarrow",
                "↕": "\\updownarrow",
                "↔": "\\leftrightarrow",
                "↓": "\\downarrow",
                "→": "\\rightarrow",
                "↑": "\\uparrow",
                "←": "\\leftarrow",
                "|||": "\\left|||\\right.",
                "||": "\\left||\\right.",
                "|": "\\mid",
                "⫾": "",
                "⫽": "//",
                "⫻": "///",
                "⫺": "",
                "⫹": "",
                "⫸": "",
                "⫷": "",
                "⫶": "\\vdots",
                "⫵": "",
                "⫴": "",
                "⫳": "",
                "⫲": "\\nparallel",
                "⫱": "",
                "⫰": "",
                "⫯": "",
                "⫮": "\\bcancel{\\mid}",
                "⫭": "",
                "⫬": "",
                "⫫": "",
                "⫪": "",
                "⫩": "",
                "⫨": "\\underline{\\perp}",
                "⫧": "\\overline{\\top}",
                "⫦": "",
                "⫥": "",
                "⫤": "",
                "⫣": "",
                "⫢": "",
                "⫡": "",
                "⫠": "\\perp",
                "⫟": "\\top",
                "⫞": "\\dashv",
                "⫝̸": "",
                "⫝": "",
                "⫛": "\\pitchfork",
                "⫚": "",
                "⫙": "",
                "⫘": "",
                "⫗": "",
                "⫖": "",
                "⫕": "",
                "⫔": "",
                "⫓": "",
                "⫒": "",
                "⫑": "",
                "⫐": "",
                "⫏": "",
                "⫎": "",
                "⫍": "",
                "⫌": "\\underset{\\neq}{\\supset}",
                "⫋": "\\underset{\\neq}{\\subset}",
                "⫊": "\\underset{\\approx}{\\supset}",
                "⫉": "\\underset{\\approx}{\\subset}",
                "⫈": "\\underset{\\sim}{\\supset}",
                "⫇": "\\underset{\\sim}{\\subset}",
                "⫆": "\\supseteqq",
                "⫅": "\\subseteqq",
                "⫄": "\\dot{\\supseteq}",
                "⫃": "\\dot{\\subseteq}",
                "⫂": "\\underset{\\times}{\\supset}",
                "⫁": "\\underset{\\times}{\\subset}",
                "⫀": "\\underset{+}{\\supset}",
                "⪿": "\\underset{+}{\\subset}",
                "⪾": "",
                "⪽": "",
                "⪼": "\\gg ",
                "⪻": "\\ll",
                "⪺": "\\underset{\\cancel{\\approx}}{\\succ}",
                "⪹": "\\underset{\\cancel{\\approx}}{\\prec}",
                "⪸": "\\underset{\\approx}{\\succ}",
                "⪷": "\\underset{\\approx}{\\prec}",
                "⪶": "\\underset{\\cancel{=}}{\\succ}",
                "⪵": "\\underset{\\cancel{=}}{\\prec}",
                "⪴": "\\underset{=}{\\succ}",
                "⪳": "\\underset{=}{\\prec}",
                "⪲": "",
                "⪱": "",
                "⪮": "",
                "⪭": "\\underline{\\hcancel{>}}",
                "⪬": "\\underline{\\hcancel{>}}",
                "⪫": "\\hcancel{>}",
                "⪪": "\\hcancel{<}",
                "⪩": "",
                "⪨": "",
                "⪧": "\\vartriangleright",
                "⪦": "\\vartriangleleft",
                "⪥": "><",
                "⪤": "><",
                "⪣": "\\underline{\\ll}",
                "⪢̸": "\\cancel{\\gg}",
                "⪢": "\\gg",
                "⪡̸": "\\cancel{\\ll}",
                "⪡": "\\ll",
                "⪠": "\\overset{\\sim}{\\geqq}",
                "⪟": "\\overset{\\sim}{\\leqq}",
                "⪞": "\\overset{\\sim}{>}",
                "⪝": "\\overset{\\sim}{<}",
                "⪜": "",
                "⪛": "",
                "⪚": "\\overset{=}{>}",
                "⪙": "\\overset{=}{<}",
                "⪘": "",
                "⪗": "",
                "⪖": "",
                "⪕": "",
                "⪔": "",
                "⪓": "",
                "⪒": "\\underset{=}{\\gtrless}",
                "⪑": "\\underset{=}{\\lessgtr}",
                "⪐": "\\underset{<}{\\gtrsim}",
                "⪏": "\\underset{>}{\\lesssim}",
                "⪎": "\\underset{\\simeq}{>}",
                "⪍": "\\underset{\\simeq}{<}",
                "⪌": "\\gtreqqless",
                "⪋": "\\lesseqqgtr",
                "⪊": "\\underset{\\cancel{\\approx}}{>}",
                "⪉": "\\underset{\\approx}{<}",
                "⪆": "\\underset{\\approx}{>}",
                "⪅": "\\underset{\\approx}{<}",
                "⪄": "",
                "⪃": "",
                "⪂": "",
                "⪁": "",
                "⪀": "",
                "⩿": "",
                "⩾̸": "\\bcancel{\\geq}",
                "⩾": "\\geq",
                "⩽̸": "\\bcancel{\\leq}",
                "⩽": "\\leq",
                "⩼": "",
                "⩻": "",
                "⩺": "",
                "⩹": "",
                "⩸": "\\overset{\\dots}{\\equiv}",
                "⩷": "",
                "⩶": "===",
                "⩵": "==",
                "⩴": "::=",
                "⩳": "",
                "⩲": "\\underset{=}{+}",
                "⩱": "\\overset{=}{+}",
                "⩰": "\\overset{\\approx}{=}",
                "⩯": "\\overset{\\wedge}{=}",
                "⩮": "\\overset{*}{=}",
                "⩭": "\\dot{\\approx}",
                "⩬": "",
                "⩫": "",
                "⩪": "\\dot{\\sim}",
                "⩩": "",
                "⩨": "",
                "⩧": "\\dot{\\equiv}",
                "⩦": "\\underset{\\cdot}{=}",
                "⩥": "",
                "⩤": "",
                "⩣": "\\underset{=}{\\vee}",
                "⩢": "\\overset{=}{\\vee}",
                "⩡": "ul(vv)",
                "⩠": "\\underset{=}{\\wedge}",
                "⩟": "\\underline{\\wedge}",
                "⩞": "\\overset{=}{\\wedge}",
                "⩝": "\\hcancel{\\vee}",
                "⩜": "\\hcancel{\\wedge}",
                "⩛": "",
                "⩚": "",
                "⩙": "",
                "⩘": "\\vee",
                "⩗": "\\wedge",
                "⩖": "",
                "⩕": "",
                "⩔": "",
                "⩓": "",
                "⩒": "\\dot{\\vee}",
                "⩑": "\\dot{\\wedge}",
                "⩐": "",
                "⩏": "",
                "⩎": "",
                "⩍": "\\overline{\\cap}",
                "⩌": "\\overline{\\cup}",
                "⩋": "",
                "⩊": "",
                "⩉": "",
                "⩈": "",
                "⩇": "",
                "⩆": "",
                "⩅": "",
                "⩄": "",
                "⩃": "\\overline{\\cap}",
                "⩂": "\\overline{\\cup}",
                "⩁": "",
                "⩀": "",
                "⨾": "",
                "⨽": "\\llcorner",
                "⨼": "\\lrcorner",
                "⨻": "",
                "⨺": "",
                "⨹": "",
                "⨸": "",
                "⨷": "",
                "⨶": "\\hat{\\otimes}",
                "⨵": "",
                "⨴": "",
                "⨳": "",
                "⨲": "\\underline{\\times}",
                "⨱": "\\underline{\\times}",
                "⨰": "\\dot{\\times}",
                "⨮": "\\bigodot",
                "⨭": "\\bigodot",
                "⨬": "",
                "⨫": "",
                "⨪": "",
                "⨩": "",
                "⨨": "",
                "⨧": "",
                "◻": "\\Box",
                "⨦": "\\underset{\\sim}{+}",
                "⨥": "\\underset{\\circ}{+}",
                "⨤": "\\overset{\\sim}{+}",
                "⨣": "\\hat{+}",
                "⨢": "\\dot{+}",
                "⨡": "\\upharpoonright",
                "⨠": ">>",
                "⨟": "",
                "⨞": "\\triangleleft",
                "⨝": "\\bowtie",
                "⧿": "",
                "⧾": "+",
                "⧻": "\\hcancel{|||}",
                "⧺": "\\hcancel{||}",
                "⧹": "\\backslash",
                "⧸": "/",
                "⧷": "hcancel{\backslash}",
                "⧶": "",
                "⧵": "\\backslash",
                "⧲": "\\Phi",
                "⧱": "",
                "⧰": "",
                "⧮": "",
                "⧭": "",
                "⧬": "",
                "⧫": "\\lozenge",
                "⧪": "",
                "⧩": "",
                "⧨": "",
                "⧧": "\\ddagger",
                "⧢": "\\sqcup\\sqcup",
                "⧡": "",
                "⧠": "\\square",
                "⧞": "",
                "⧝": "",
                "⧜": "",
                "⧛": "\\{\\{",
                "⧙": "\\{",
                "⧘": "\\}",
                "⧗": "",
                "⧖": "",
                "⧕": "\\bowtie",
                "⧔": "\\bowtie",
                "⧓": "\\bowtie",
                "⧒": "\\bowtie",
                "⧑": "\\bowtie",
                "⧐̸": "| \\not\\triangleright",
                "⧐": "| \\triangleright",
                "⧏̸": "\\not\\triangleleft |",
                "⧏": "\\triangleleft |",
                "⧎": "",
                "⧍": "\\triangle",
                "⧌": "",
                "⧋": "\\underline{\\triangle}",
                "⧊": "\\dot{\\triangle}",
                "⧉": "",
                "⧈": "\\boxed{\\circ}",
                "⧇": "\\boxed{\\circ}",
                "⧆": "\\boxed{\\rightarrow}",
                "⧅": "\\bcancel{\\square}",
                "⧄": "\\cancel{\\square}",
                "⧃": "\\odot",
                "⧂": "\\odot",
                "⦿": "\\odot",
                "⦾": "\\odot",
                "⦽": "\\varnothing",
                "⦼": "\\oplus",
                "⦻": "\\otimes",
                "⦺": "",
                "⦹": "\\varnothing",
                "⦸": "\\varnothing",
                "⦷": "\\ominus",
                "⦶": "\\ominus",
                "⦵": "\\ominus",
                "⦴": "\\vec{\\varnothing}",
                "⦳": "\\vec{\\varnothing}",
                "⦲": "\\dot{\\varnothing}",
                "⦱": "\\overline{\\varnothing}",
                "⦰": "\\varnothing",
                "⦯": "\\measuredangle",
                "⦮": "\\measuredangle",
                "⦭": "\\measuredangle",
                "⦬": "\\measuredangle",
                "⦫": "\\measuredangle",
                "⦪": "\\measuredangle",
                "⦩": "\\measuredangle",
                "⦨": "\\measuredangle",
                "⦧": "",
                "⦦": "",
                "⦥": "",
                "⦤": "",
                "⦣": "\\ulcorner",
                "⦢": "\\measuredangle",
                "⦡": "\\not\\lor",
                "⦠": "\\bcancel{>}",
                "⦂": ":",
                "⦁": "\\cdot",
                "❘": "\\mid",
                "▲": "\\bigtriangleup",
                "⋿": "\\Epsilon",
                "⋾": "\\overline{\\ni}",
                "⋽": "\\overline{\\ni}",
                "⋼": "\\in",
                "⋻": "\\in",
                "⋺": "\\in",
                "⋹": "\\underline{\\in}",
                "⋸": "\\underline{\\in}",
                "⋷": "\\overline{\\in}",
                "⋶": "\\overline{\\in}",
                "⋵": "\\dot{\\in}",
                "⋴": "\\in",
                "⋳": "\\in",
                "⋲": "\\in",
                "⋰": "\\ddots",
                "։": ":",
                "⋩": "\\underset{\\sim}{\\succ}",
                "⋨": "\\underset{\\sim}{\\prec}",
                "⋧": "\\underset{\\not\\sim}{>}",
                "⋦": "\\underset{\\not\\sim}{<}",
                "⋥": "\\not\\sqsupseteq",
                "⋤": "\\not\\sqsubseteq",
                "⋣": "\\not\\sqsupseteq",
                "⋢": "\\not\\sqsubseteq",
                "⋡": "\\nsucc",
                "⋠": "\\nprec",
                "⋟": "\\succ",
                "⋞": "\\prec",
                "⋝": "\\overline{>}",
                "⋜": "\\overline{<}",
                "⋛": "\\underset{>}{\\leq}",
                "⋚": "\\underset{<}{\\geq}",
                "⋕": "\\#",
                "⋓": "\\cup",
                "⋒": "\\cap",
                "⋑": "\\supset",
                "⋐": "\\subset",
                "⋏": "\\wedge",
                "⋎": "\\vee",
                "⋍": "\\simeq",
                "⋈": "\\Join",
                "⋇": "\\ast",
                "⋆": "\\star",
                "⋄": "\\diamond",
                "⊿": "\\triangle",
                "⊾": "\\measuredangle",
                "⊽": "\\overline{\\lor}",
                "⊼": "\\overline{\\land}",
                "⊻": "\\underline{\\lor}",
                "⊺": "\\top",
                土: "\\pm",
                十: "+",
                "⊹": "",
                "⊷": "\\circ\\multimap",
                "⊶": "\\circ\\multimap",
                "⊳": "\\triangleright",
                "⊲": "\\triangleleft",
                "⊱": "\\succ",
                "⊰": "\\prec",
                "⊫": "|\\models",
                "⊪": "|\\models",
                "⊧": "\\models",
                "⊦": "\\vdash",
                "⊝": "\\ominus",
                "⊜": "\\ominus",
                "⊛": "\\odot",
                "⊚": "\\odot",
                "⊔": "\\sqcup",
                "⊓": "\\sqcap",
                "⊒": "\\sqsupseteq",
                "⊑": "\\sqsubseteq",
                "⊐̸": "\\not\\sqsupset",
                "⊐": "\\sqsupset",
                "⊏̸": "\\not\\sqsubset",
                "⊏": "\\sqsubset",
                "⊎": "\\cup",
                "⊍": "\\cup",
                "⊌": "\\cup",
                "≿̸": "\\not\\succsim",
                "≿": "\\succsim",
                "≾": "\\precsim",
                "≹": "\\not\\overset{>}{<}",
                "≸": "\\not\\overset{>}{<}",
                "≷": "\\overset{>}{<}",
                "≶": "\\overset{<}{>}",
                "≵": "\\not\\geg",
                "≴": "\\not\\leq",
                "≳": "\\geg",
                "≲": "\\leq",
                "≬": "",
                "≧": "\\geg",
                "≦̸": "\\not\\leq",
                "≦": "\\leq",
                "≣": "\\overset{=}{=} ",
                "≞": "\\overset{m}{=} ",
                "≝": "\\overset{def}{=}",
                "≘": "=",
                "≖": "=",
                "≕": "=:",
                "≓": "\\doteq",
                "≒": "\\doteq",
                "≑": "\\doteq",
                "≐": "\\doteq",
                "≏̸": "",
                "≏": "",
                "≎̸": "",
                "≎": "",
                "≌": "\\approx",
                "≋": "\\approx",
                "≊": "\\approx",
                "≂̸": "\\neq",
                "≂": "=",
                "∿": "\\sim",
                "∾": "\\infty",
                "∽̱": "\\sim",
                "∽": "\\sim",
                "∻": "\\sim",
                "∺": ":-:",
                "∹": "-:",
                "∸": "\\bot",
                "∷": "::",
                "∶": ":",
                "∣": "\\mid",
                "∟": "\\llcorner",
                "∘": "\\circ",
                "∗": "*",
                "∕": "/",
                "∎": "\\square",
                "∍": "\\ni",
                "∊": "\\in",
                "∆": "\\Delta",
                "⁄": "/",
                "⪰̸": "\\nsucceq",
                "⪰": "\\succeq",
                "⪯̸": "\\npreceq",
                "⪯": "\\preceq",
                "⪈": "\\ngeqslant",
                "⪇": "\\nleqslant",
                "⧳": "\\Phi",
                "⧦": "\\models",
                "⧥": "\\not\\equiv",
                "⧤": "\\approx\\neq",
                "⧣": "\\neq",
                "⧁": "\\circle",
                "⧀": "\\circle",
                "◦": "\\circle",
                "◗": "\\circle",
                "◖": "\\circle",
                "●": "\\circle",
                "◎": "\\circledcirc",
                "◍": "\\circledcirc",
                "◌": "\\circledcirc",
                "◉": "\\circledcirc",
                "◈": "\\diamond",
                "◇": "\\diamond",
                "◆": "\\diamond",
                "◅": "\\triangleleft",
                "◄": "\\triangleleft",
                "◃": "\\triangleleft",
                "◂": "\\triangleleft",
                "◁": "\\triangleleft",
                "◀": "\\triangleleft",
                "▿": "\\triangledown",
                "▾": "\\triangledown",
                "▽": "\\triangledown",
                "▼": "\\triangledown",
                "▹": "\\triangleright",
                "▸": "\\triangleright",
                "▷": "\\triangleright",
                "▶": "\\triangleright",
                "▵": "\\triangle",
                "▴": "\\triangle",
                "△": "\\triangle",
                "▱": "\\square",
                "▰": "\\blacksquare",
                "▯": "\\square",
                "▮": "\\blacksquare",
                "▭": "\\square",
                "▫": "\\square",
                "▪": "\\square",
                "□": "\\square",
                "■": "\\blacksquare",
                "⋭": "\\not\\triangleright",
                "⋬": "\\not\\triangleleft",
                "⋫": "\\not\\triangleright",
                "⋪": "\\not\\triangleleft",
                "⋙": "\\ggg",
                "⋘": "\\lll",
                "⋗": "*>",
                "⋖": "<*",
                "⋔": "\\pitchfork",
                "⋌": "",
                "⋋": "\\bowtie",
                "⋊": "\\ltimes",
                "⋉": "\\rtimes",
                "⊵": "\\triangleright",
                "\\triangleleft": "",
                "⊥": "\\bot",
                "⊁": "\\nsucc",
                "⊀": "\\preceq",
                "≽": "\\succeq",
                "≼": "\\preceq",
                "≻": "\\succ",
                "≺": "\\prec",
                "≱": "\\geq/",
                "≰": "\\leq/",
                "≭": "\\neq",
                "≫̸": "\\not\\gg",
                "≫": "\\gg",
                "≪̸": "\\not\\ll",
                "≪": "\\ll",
                "≩": "\\ngeqslant",
                "≨": "\\nleqslant",
                "≡": "\\equiv",
                "≟": "\\doteq",
                "≜": "\\triangleq",
                "≛": "\\doteq",
                "≚": "\\triangleq",
                "≙": "\\triangleq",
                "≗": "\\doteq",
                "≔": ":=",
                "≍": "\\asymp",
                "≇": "\\ncong",
                "≆": "\\ncong",
                "≅": "\\cong",
                "≄": "\\not\\simeq",
                "≃": "\\simeq",
                "≁": "\\not\\sim",
                "∦": "\\not\\parallel",
                "∥": "\\parallel",
                "∤": "\\not|",
                "∝": "\\propto",
                "==": "==",
                "=": "=",
                ":=": ":=",
                "/=": "=",
                "-=": "-=",
                "+=": "+=",
                "*=": "*=",
                "!=": "!=",
                "≠": "\\neq",
                "≢": "\\equiv /",
                "≉": "\\approx /",
                "∼": "sim",
                "≈": "\\approx",
                "≮": "</",
                "<": "<",
                "≯": ">/",
                ">=": ">=",
                ">": ">",
                "≥": "\\geq",
                "≤": "\\leq",
                "<=": "<=",
                "⊋": "\\supsetneq",
                "⊊": "\\subsetneq",
                "⊉": "\\nsupseteq",
                "⊈": "\\nsubseteq",
                "⊇": "\\supseteq",
                "⊆": "\\subseteq",
                "⊅": "\\not\\supset",
                "⊄": "\\not\\subset",
                "⊃⃒": "\\supset |",
                "⊃": "\\supset",
                "⊂⃒": "\\subset |",
                "⊂": "\\subset",
                "∌": "\\not\\in",
                "∉": "\\notin",
                "∈": "\\in",
                "∁": "C",
                "∄": "\\nexists",
                "∃": "\\exists",
                "∀": "\\forall",
                "∧": "\\land",
                "&&": "\\&\\&",
                "∨": "\\lor",
                "⊯": "\\cancel{\\vDash}",
                "⊮": "\\cancel{\\Vdash}",
                "⊭": "\\nvDash",
                "⊬": "\\nvDash",
                "⊩": "\\Vdash",
                "⊨": "\\vDash",
                "⊤": "\\top",
                "⊣": "\\dashv",
                "⊢": "\\vdash",
                "∋": "\\ni",
                "⋱": "\\ddots",
                "⋯": "\\hdots",
                "⋮": "\\vdots",
                "϶": "\\ni",
                ":": ":",
                "...": "\\cdots",
                "..": "..",
                "->": "->",
                "∵": "\\because",
                "∴": "\\therefore ",
                "⁣": "\\llbracket",
                ",": ",",
                ";": ";",
                "⧽": "\\}",
                "⧼": "\\{",
                "⦘": "\\]",
                "⦗": "\\[",
                "⦖": "\\ll",
                "⦕": "\\gg",
                "⦔": "\\gg",
                "⦓": "\\ll",
                "⦒": "\\gg",
                "⦑": "\\ll",
                "⦐": "\\]",
                "⦏": "\\]",
                "⦎": "\\]",
                "⦍": "\\[",
                "⦌": "\\[",
                "⦋": "\\]",
                "⦊": "\\triangleright",
                "⦉": "\\triangleleft",
                "⦈": "|\\)",
                "⦇": "\\(|",
                "⦆": "|\\)",
                "⦅": "\\(\\(",
                "⦄": "|\\}",
                "⦃": "\\{|",
                "⦀": "\\||",
                "⟯": "\\left. \\right]",
                "⟮": "\\left[ \\right.",
                "⟭": "\\left. \\right]]",
                "⟬": "\\left[[ \\right.",
                "⟫": "\\gg",
                "⟪": "\\ll",
                "⟧": "\\)|",
                "⟦": "\\(|",
                "❳": "\\left.\\right)",
                "❲": "\\left(\\right.",
                "〉": "\\rangle",
                "〈": "\\langle",
                "⌋": "\\rfloor",
                "⌊": "\\lfloor",
                "⌉": "\\rceil",
                "⌈": "\\lceil",
                "‖": "\\parallel",
                "}": "\\left.\\right\\}",
                "{": "\\left\\{\\right.",
                "]": "\\left]\\right.",
                "[": "\\left[\\right.",
                ")": "\\left.\\right)",
                "(": "\\left(\\right.",
                "”": '\\"',
                "“": "\\text{``}",
                "’": "'",
                "‘": "`",
                α: "\\alpha",
                β: "\\beta",
                γ: "\\gamma",
                Γ: "\\Gamma",
                δ: "\\delta",
                Δ: "\\Delta",
                ϵ: "\\epsilon",
                ζ: "\\zeta",
                η: "\\eta",
                θ: "\\theta",
                Θ: "\\Theta",
                ι: "\\iota",
                κ: "\\kappa",
                λ: "\\lambda",
                ν: "\\nu",
                ο: "\\omicron",
                π: "\\pi",
                Π: "\\Pi",
                ρ: "\\rho",
                σ: "\\sigma",
                Σ: "\\Sigma",
                τ: "\\tau",
                υ: "\\upsilon",
                Υ: "\\Upsilon",
                ϕ: "\\phi",
                Φ: "\\Phi",
                χ: "\\chi",
                ψ: "\\psi",
                Ψ: "\\Psi",
                ω: "\\omega",
                Ω: "\\Omega",
                Ω: "\\Omega",
                "∅": "\\emptyset",
                "⟲": "\\circlearrowleft",
                "⟳": "\\circlearrowright",
                "×": "\\times",
                "½": "\\dfrac{1}{2}",
                μ: "\\mu",
                Ө: "\\theta",
                "✓": "\\checkmark",
                "⟩": "\\rangle",
                "⟨": "\\langle",
                "¼": "\\dfrac{1}{4}",
                "…": "\\ldots",
                ℏ: "\\hbar",
                ℜ: "\\mathfrak{R}",
                Ѳ: "\\theta",
                Ø: "\\emptyset",
                ϱ: "\\varrho",
                ф: "\\phi",
                ℇ: "\\varepsilon",
                T: "T",
                "∙": "\\cdot",
                Ρ: "P",
                "∞": "\\infty",
                ᐁ: "\\nabla",
                ƞ: "\\eta",
                "⁺": "^{+}",
                "⁻": "^{-}",
                "⁼": "^{=}",
                "⁽": "^{(}",
                "⁾": "^{)}",
                "〗": "\\)|",
                "〖": "\\langle",
                ";": ";",
                "൦": "\\circ",
                "┴": "\\perp",
                "✕": "\\times",
                "⎻": "-",
                "»": "\\gg",
                "⬆": "\\uparrow",
                "⬇": "\\downarrow",
                "⬅": "\\leftarrow",
                "➡": "\\rightarrow",
                "⎼": "-",
                "⎜": "\\mid",
                "⎥": "\\mid",
                ħ: "\\hbar",
                "⮕": "\\rightarrow",
                "・": "\\cdot",
                "¦": "\\mid",
                "£": "\\pounds",
                "¥": "\\yen",
                "✗": "\\times",
                "✔": "\\checkmark",
                ⁿ: "^{n}",
                "«": "\\ll",
                เ: "\\prime",
                "†": "\\dagger",
                "│": "\\mid",
                $: "\\$",
                "#": "\\#",
                "℃": "\\text{\\textdegree C}",
                "℉": "\\text{\\textdegree F}",
                "█": "\\blacksquare",
                "℧": "\\mho",
                ⅇ: "\\text{e}",
                ɼ: "r",
                "‡": "\\ddagger",
                ἱ: "i",
                ϒ: "\\Upsilon",
                𝛿: "\\delta",
                "˳": "\\cdot",
                ѳ: "\\theta",
                𝜙: "\\phi",
                П: "\\prod",
                о: "o",
                ђ: "\\hbar",
                Ʌ: "\\Lambda",
                "।": "\\mid",
                "€": "\\euro",
                ῡ: "\\bar{u}",
                φ: "\\varphi",
                ȼ: "c",
                𝞮: "\\epsilon",
                Χ: "\\mathsf{X}",
                ₙ: "_{n}"
            }
        }
        ,
        2335: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.allMathSymbolsByChar = void 0,
            t.allMathSymbolsByChar = {
                "&#xA0;": "\\textrm{ }",
                "&#x2203;": "\\exists",
                "&#x2200;": "\\forall",
                "&#x21D4;": "\\iff",
                "&#x21D2;": "=>",
                "&#xAC;": "\\neg",
                "&#x2124;": "\\mathbb{Z}",
                "&#x211D;": "\\mathbb{R}",
                "&#x211A;": "\\mathbb{Q}",
                "&#x2115;": "\\mathbb{N}",
                "&#x2102;": "CC",
                "&#x25A1;": "\\square",
                "&#x22C4;": "\\diamond",
                "&#x25B3;": "\\triangle",
                "&#x2322;": "\\frown",
                "&#x2220;": "\\angle",
                "&#x22F1;": "\\ddots",
                "&#x22EE;": "\\vdots",
                "&#x2235;": "\\because",
                "&#x2234;": "\\therefore",
                "&#x2135;": "\\aleph",
                "&#x2205;": "\\oslash",
                "&#xB1;": "\\pm",
                "&#x2207;": "\\nabla",
                "&#x2202;": "\\partial",
                "&#x222E;": "\\oint",
                "&#x222B;": "\\int",
                "&#x22C3;": "\\cup",
                "&#x222A;": "\\cup",
                "&#x22C2;": "\\cap",
                "&#x2229;": "\\cap",
                "&#x22C1;": "\\vee",
                "&#x2228;": "\\vee",
                "&#x22C0;": "\\wedge",
                "&#x2227;": "\\wedge",
                "&#x220F;": "\\prod",
                "&#x2211;": "\\sum",
                "&#x2299;": "\\bigodot",
                "&#x2297;": "\\bigoplus",
                "&#x2295;": "o+",
                "&#x2218;": "@",
                "&#x22C8;": "\\bowtie",
                "&#x22CA;": "\\rtimes",
                "&#x22C9;": "\\ltimes",
                "&#xF7;": "\\div",
                "&#xD7;": "\\times",
                "\\": "\\backslash",
                "&#x22C6;": "\\star",
                "&#x2217;": "\\star",
                "&#x22C5;": "\\cdot",
                "&#x3A9;": "\\Omega",
                "&#x3C9;": "\\omega",
                "&#x3A8;": "\\Psi",
                "&#x3C8;": "\\psi",
                "&#x3C7;": "\\chi",
                "&#x3C6;": "\\varphi",
                "&#x3A6;": "\\Phi",
                "&#x3D5;": "\\phi",
                "&#x3C5;": "\\upsilon",
                "&#x3C4;": "\\tau",
                "&#x3A3;": "\\Sigma",
                "&#x3C3;": "\\sigma",
                "&#x3C1;": "\\rho",
                "&#x3A0;": "\\Pi",
                "&#x3C0;": "\\pi",
                "&#x39E;": "\\Xi",
                "&#x3BE;": "\\xi",
                "&#x3BD;": "\\nu",
                "&#x3BC;": "\\mu",
                "&#x39B;": "\\Lambda",
                "&#x3BB;": "\\lambda",
                "&#x3BA;": "\\kappa",
                "&#x3B9;": "\\iota",
                "&#x3D1;": "\\vartheta",
                "&#x398;": "\\Theta",
                "&#x3B8;": "\\theta",
                "&#x3B7;": "\\eta",
                "&#x3B6;": "\\zeta",
                "&#x25B;": "\\varepsilon",
                "&#x3B5;": "\\epsilon",
                "&#x394;": "\\Delta",
                "&#x3B4;": "\\delta",
                "&#x393;": "\\Gamma",
                "&#x3B3;": "\\gamma",
                "&#x3B2;": "\\beta",
                "&#x3B1;": "\\alpha",
                "&#x221E;": "\\infty",
                "‬": "\\text{\\textdir TRT}",
                "‎": "\\text{\\textdir LTR}"
            }
        }
        ,
        4104: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.allMathSymbolsByGlyph = void 0,
            t.allMathSymbolsByGlyph = {
                " ": "\\textrm{ }",
                "∃": "\\exists",
                "∀": "\\forall",
                "⇔": "\\iff",
                "⇒": "\\Rightarrow",
                "¬": "\\neg",
                "□": "\\square",
                "⋄": "\\diamond",
                "△": "\\triangle",
                "⌢": "\\frown",
                "∠": "\\angle",
                "⋱": "\\ddots",
                "⋮": "\\vdots",
                "∵": "\\because",
                "∴": "\\therefore",
                ℵ: "\\aleph",
                "∅": "\\emptyset",
                "±": "\\pm",
                "∇": "\\nabla",
                "∂": "\\partial",
                "∮": "\\oint",
                "∫": "\\int",
                "⋃": "\\cup",
                "∪": "\\cup",
                "⋂": "\\cap",
                "∩": "\\cap",
                "⋁": "\\vee",
                "∨": "\\vee",
                "⋀": "\\wedge",
                "∧": "\\wedge",
                "∏": "\\prod",
                "∑": "\\sum",
                "⊙": "\\bigodot",
                "⊗": "\\bigoplus",
                "⊕": "o+",
                "∘": "@",
                "⋈": "\\bowtie",
                "⋊": "\\rtimes",
                "⋉": "\\ltimes",
                "÷": "\\div",
                "×": "\\times",
                "\\": "\\backslash",
                "⋆": "\\star",
                "∗": "\\star",
                "⋅": "\\cdot",
                Ω: "\\Omega",
                ω: "\\omega",
                Ψ: "\\Psi",
                ψ: "\\psi",
                χ: "\\chi",
                φ: "\\varphi",
                Φ: "\\Phi",
                ϕ: "\\phi",
                υ: "\\upsilon",
                τ: "\\tau",
                Σ: "\\Sigma",
                σ: "\\sigma",
                ρ: "\\rho",
                Π: "\\Pi",
                π: "\\pi",
                Ξ: "\\Xi",
                ξ: "\\xi",
                ν: "\\nu",
                μ: "\\mu",
                Λ: "\\Lambda",
                λ: "\\lambda",
                κ: "\\kappa",
                ι: "\\iota",
                ϑ: "\\vartheta",
                Θ: "\\Theta",
                θ: "\\theta",
                η: "\\eta",
                ζ: "\\zeta",
                ɛ: "\\varepsilon",
                ε: "\\epsilon",
                Δ: "\\Delta",
                δ: "\\delta",
                Γ: "\\Gamma",
                γ: "\\gamma",
                β: "\\beta",
                α: "\\alpha",
                "∞": "\\infty",
                ϵ: "\\epsilon",
                µ: "\\mu",
                "²": "^{2}",
                ı: "\\imath",
                "∎": "\\blacksquare",
                ม: "\\mathbf{m}",
                Ω: "\\Omega",
                "⟲": "\\circlearrowleft",
                "⟳": "\\circlearrowright",
                त: " ",
                "¥": "\\yen",
                "⁽": "^{(}",
                "⁾": "^{)}",
                ß: "\\ss",
                Ћ: "\\hbar",
                "⦵": "\\ominus",
                "⊿": "\\bigtriangleup",
                "↛'": "\\nrightarrow",
                "†": "\\dagger",
                เ: "\\prime",
                白: " ",
                "⿱": " ",
                ℸ: "\\wp",
                퓰: " ",
                ⁿ: "^{n}",
                "✔": "\\checkmark",
                "✗": "\\times",
                "½": "\\dfrac{1}{2}",
                Ө: "\\theta",
                "✓": "\\checkmark",
                "⟩": "\\rangle",
                "⟨": "\\langle",
                "〈": "\\langle",
                "¼": "\\dfrac{1}{4}",
                "…": "\\ldots",
                ℏ: "\\hbar",
                ℜ: "\\mathfrak{R}",
                Ѳ: "\\theta",
                Ø: "\\emptyset",
                ϱ: "\\varrho",
                ф: "\\phi",
                T: "T",
                "∙": "\\cdot",
                Ρ: "P",
                ᐁ: "\\nabla",
                ƞ: "\\eta",
                ɣ: "\\gamma",
                ћ: "\\hbar",
                Ɛ: "\\varepsilon",
                ⅅ: "\\_{D}",
                𝜆: "\\lambda",
                "〗": "\\rangle",
                "〖": "\\langle",
                ";": ";",
                𝑥: "x",
                𝑦: "y",
                𝑧: "z",
                𝑖: "i",
                𝑗: "j",
                𝑘: "k",
                𝑚: "m",
                𝑒: "e",
                𝑟: "r",
                ɳ: "\\eta",
                𝛽: "\\beta",
                "⍵": "\\omega",
                ℘: "\\wp",
                𝜋: "\\pi",
                Є: "\\epsilon",
                є: "\\epsilon",
                𝜀: "\\epsilon",
                п: "\\pi",
                Ν: "\\nu",
                ɵ: "\\theta",
                𝜓: "\\psi",
                ϴ: "\\theta",
                ɸ: "\\phi",
                Ӷ: "\\Gamma",
                ɭ: "\\ell",
                ʋ: "\\upsilon",
                𝛟: "\\varphi",
                "⍬": "\\theta",
                Ф: "\\Phi",
                𝜑: "\\varphi",
                ⅈ: "i",
                ο: "o",
                ơ: "o",
                ƒ: "f",
                "⍴": "\\rho",
                "🇽": "x",
                𝑝: "p",
                𝑞: "q",
                𝑠: "s",
                𝑡: "t",
                𝑢: "u",
                𝑣: "v",
                𝑤: "w",
                𝑎: "a",
                𝑏: "b",
                𝑐: "c",
                𝑑: "d",
                𝑓: "f",
                𝑔: "g",
                𝑙: "l",
                𝑛: "n",
                𝑜: "o",
                𝔀: "w",
                𝚟: "v",
                ṁ: "m",
                "൦": "\\circ",
                "┴": "\\perp",
                "✕": "\\times",
                "∣": "\\mid",
                Փ: "\\Phi",
                "⎜": "\\mid",
                ħ: "\\hbar",
                ፈ: " ",
                "⦨": "\\llbracket",
                ế: "\\hat{e}",
                "¢": "\\cent",
                "⤹": "\\downarrow",
                "⤸": "\\downarrow",
                "⤷": "\\Rsh",
                "⤶": "\\Lsh",
                "⤵": "\\downarrow",
                "⤴": "\\uparrow",
                "⤳": "\\rightarrow",
                "|": "\\mid",
                "⎥": "\\mid",
                "♥": "\\heartsuit",
                О: "0",
                Υ: "Y",
                х: "x",
                𝓏: "z",
                𝓎: "y",
                𝓍: "x",
                р: "p",
                а: "a",
                "£": "\\pounds",
                m: "m",
                𝚵: "\\Xi",
                "⓪": "\\textcircled{0}",
                "①": "\\textcircled{1}",
                "②": "\\textcircled{2}",
                "③": "\\textcircled{3}",
                "④": "\\textcircled{4}",
                "⑤": "\\textcircled{5}",
                "⑥": "\\textcircled{6}",
                "⑦": "\\textcircled{7}",
                "⑧": "\\textcircled{8}",
                "⑨": "\\textcircled{9}",
                "⑩": "\\textcircled{10}",
                "⑪": "\\textcircled{11}",
                "⑫": "\\textcircled{12}",
                "⑬": "\\textcircled{13}",
                "⑭": "\\textcircled{14}",
                "⑮": "\\textcircled{15}",
                "⑯": "\\textcircled{16}",
                "⑰": "\\textcircled{17}",
                "⑱": "\\textcircled{18}",
                "⑲": "\\textcircled{19}",
                "⑳": "\\textcircled{20}",
                "㉑": "\\textcircled{21}",
                "㉒": "\\textcircled{22}",
                "㉓": "\\textcircled{23}",
                "㉔": "\\textcircled{24}",
                "㉕": "\\textcircled{25}",
                "㉖": "\\textcircled{26}",
                "㉗": "\\textcircled{27}",
                "㉘": "\\textcircled{28}",
                "㉙": "\\textcircled{29}",
                "㉚": "\\textcircled{30}",
                "㉛": "\\textcircled{31}",
                "㉜": "\\textcircled{32}",
                "㉝": "\\textcircled{33}",
                "㉞": "\\textcircled{34}",
                "㉟": "\\textcircled{35}",
                "㊱": "\\textcircled{36}",
                "㊲": "\\textcircled{37}",
                "㊳": "\\textcircled{38}",
                "㊴": "\\textcircled{39}",
                "㊵": "\\textcircled{40}",
                "㊶": "\\textcircled{41}",
                "㊷": "\\textcircled{42}",
                "㊸": "\\textcircled{43}",
                "㊹": "\\textcircled{44}",
                "㊺": "\\textcircled{45}",
                "㊻": "\\textcircled{46}",
                "㊼": "\\textcircled{47}",
                "㊽": "\\textcircled{48}",
                "㊾": "\\textcircled{49}",
                "㊿": "\\textcircled{50}",
                "&": "\\&",
                "‖": "\\parallel",
                "%": "\\%",
                "“": "\\text{``}",
                $: "\\$",
                "#": "\\#",
                "℃": "\\text{\\textdegree C}",
                "℉": "\\text{\\textdegree F}",
                "█": "\\blacksquare",
                "℧": "\\mho",
                "⌋": "\\rfloor",
                "⌊": "\\lfloor",
                "⌉": "\\rceil",
                "⌈": "\\lceil",
                ℇ: "\\varepsilon",
                ⅇ: "\\text{e}",
                ɼ: "r",
                "↛": "\\nrightarrow",
                ˆ: "\\hat{}",
                "‾": "\\overline",
                "→": "\\rightarrow",
                "‡": "\\ddagger",
                "・": "\\cdot",
                "▱": "\\square",
                "∆": "\\Delta",
                ἱ: "i",
                "∡": "\\angle",
                ϒ: "\\Upsilon",
                "↓": "\\downarrow",
                "↑": "\\uparrow",
                "»": "\\gg",
                "⊤": "\\top",
                "⧸": "/",
                𝛿: "\\delta",
                "˳": "\\cdot",
                "։": ":",
                "⦪": "\\measuredangle",
                "⦩": "\\measuredangle",
                "⦫": "\\measuredangle",
                "⦁": "\\cdot",
                ѳ: "\\theta",
                "⦢": "\\measuredangle",
                "¸": ",",
                "⎻": "\\overline",
                "⟦": "\\llbracket",
                𝜙: "\\phi",
                П: "\\prod",
                о: "o",
                "≈": "\\approx",
                "≤": "\\leq",
                ђ: "\\hbar",
                Ʌ: "\\Lambda",
                土: "\\pm",
                "⎼": "-",
                十: "+",
                "≠": "\\neq",
                "←": "\\leftarrow",
                "।": "\\mid",
                "€": "\\euro",
                "˘": " ",
                ῡ: "\\bar{u}",
                "∥": "\\parallel",
                "↔": "\\leftrightarrow",
                "√": "\\sqrt{}",
                ȼ: "c",
                𝞮: "\\epsilon",
                "·": "\\cdot",
                "⦬": "\\measuredangle",
                "⦮": "\\measuredangle",
                "⦭": "\\measuredangle",
                "«": "\\ll",
                Χ: "\\mathsf{X}",
                "│": "\\mid",
                "〉": "\\rangle",
                ₙ: "_{n}",
                "▫": "\\square",
                "●": "\\circle",
                "”": '\\"'
            }
        }
        ,
        2408: function(e, t, r) {
            "use strict";
            var a = this && this.__createBinding || (Object.create ? function(e, t, r, a) {
                void 0 === a && (a = r);
                var n = Object.getOwnPropertyDescriptor(t, r);
                n && !("get"in n ? !t.__esModule : n.writable || n.configurable) || (n = {
                    enumerable: !0,
                    get: function() {
                        return t[r]
                    }
                }),
                Object.defineProperty(e, a, n)
            }
            : function(e, t, r, a) {
                void 0 === a && (a = r),
                e[a] = t[r]
            }
            )
              , n = this && this.__exportStar || function(e, t) {
                for (var r in e)
                    "default" === r || Object.prototype.hasOwnProperty.call(t, r) || a(t, e, r)
            }
            ;
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            n(r(1868), t),
            n(r(598), t),
            n(r(2335), t),
            n(r(4104), t),
            n(r(6626), t),
            n(r(2103), t),
            n(r(4317), t)
        },
        6626: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.latexAccents = void 0,
            t.latexAccents = ["\\hat", "\\bar", "\\underbrace", "\\overbrace"]
        }
        ,
        2103: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.mathNumberByGlyph = void 0,
            t.mathNumberByGlyph = {
                "₀": "_{0}",
                "₁": "_{1}",
                "₂": "_{2}",
                "₃": "_{3}",
                "₄": "_{4}",
                "₅": "_{5}",
                "₆": "_{6}",
                "₇": "_{7}",
                "₈": "_{8}",
                "₉": "_{9}",
                "⁰": "^{0}",
                "¹": "^{1}",
                "²": "^{2}",
                "³": "^{3}",
                "⁴": "^{4}",
                "⁵": "^{5}",
                "⁶": "^{6}",
                "⁷": "^{7}",
                "⁸": "^{8}",
                "⁹": "^{9}",
                ⁿ: "^{n}",
                ₙ: "_{n}",
                "⓪": "\\textcircled{0}",
                "①": "\\textcircled{1}",
                "②": "\\textcircled{2}",
                "③": "\\textcircled{3}",
                "④": "\\textcircled{4}",
                "⑤": "\\textcircled{5}",
                "⑥": "\\textcircled{6}",
                "⑦": "\\textcircled{7}",
                "⑧": "\\textcircled{8}",
                "⑨": "\\textcircled{9}",
                "⑩": "\\textcircled{10}",
                "⑪": "\\textcircled{11}",
                "⑫": "\\textcircled{12}",
                "⑬": "\\textcircled{13}",
                "⑭": "\\textcircled{14}",
                "⑮": "\\textcircled{15}",
                "⑯": "\\textcircled{16}",
                "⑰": "\\textcircled{17}",
                "⑱": "\\textcircled{18}",
                "⑲": "\\textcircled{19}",
                "⑳": "\\textcircled{20}",
                "㉑": "\\textcircled{21}",
                "㉒": "\\textcircled{22}",
                "㉓": "\\textcircled{23}",
                "㉔": "\\textcircled{24}",
                "㉕": "\\textcircled{25}",
                "㉖": "\\textcircled{26}",
                "㉗": "\\textcircled{27}",
                "㉘": "\\textcircled{28}",
                "㉙": "\\textcircled{29}",
                "㉚": "\\textcircled{30}",
                "㉛": "\\textcircled{31}",
                "㉜": "\\textcircled{32}",
                "㉝": "\\textcircled{33}",
                "㉞": "\\textcircled{34}",
                "㉟": "\\textcircled{35}",
                "㊱": "\\textcircled{36}",
                "㊲": "\\textcircled{37}",
                "㊳": "\\textcircled{38}",
                "㊴": "\\textcircled{39}",
                "㊵": "\\textcircled{40}",
                "㊶": "\\textcircled{41}",
                "㊷": "\\textcircled{42}",
                "㊸": "\\textcircled{43}",
                "㊹": "\\textcircled{44}",
                "㊺": "\\textcircled{45}",
                "㊻": "\\textcircled{46}",
                "㊼": "\\textcircled{47}",
                "㊽": "\\textcircled{48}",
                "㊾": "\\textcircled{49}",
                "㊿": "\\textcircled{50}",
                "½": "\\dfrac{1}{2}",
                "⅓": "\\dfrac{1}{3}",
                "⅔": "\\dfrac{2}{3}",
                "¼": "\\dfrac{1}{4}",
                "¾": "\\dfrac{3}{4}",
                "⅕": "\\dfrac{1}{5}",
                "⅖": "\\dfrac{2}{5}",
                "⅗": "\\dfrac{3}{5}",
                "⅘": "\\dfrac{4}{5}",
                "⅙": "\\dfrac{1}{6}",
                "⅚": "\\dfrac{5}{6}",
                "⅐": "\\dfrac{1}{7}",
                "⅛": "\\dfrac{1}{8}",
                "⅜": "\\dfrac{3}{8}",
                "⅝": "\\dfrac{5}{8}",
                "⅞": "\\dfrac{7}{8}",
                "⅑": "\\dfrac{1}{9}",
                "⅒": "\\dfrac{1}{10}"
            }
        }
        ,
        4317: (e,t)=>{
            "use strict";
            Object.defineProperty(t, "__esModule", {
                value: !0
            }),
            t.HashUTF8ToLtXConverter = void 0,
            t.HashUTF8ToLtXConverter = class {
                convert(e) {
                    if (e.match(/[a-z0-9]/i))
                        return e;
                    const t = r[e];
                    if (t) {
                        return this.convertAccentCharToLtX(t) || e
                    }
                    return this.convertSpecialCharToLtX(e) || e
                }
                convertAccentCharToLtX(e) {
                    const {char: t, accent: r} = e
                      , n = a[r];
                    return n ? `\\${n}{${t}}` : null
                }
                convertSpecialCharToLtX(e) {
                    const t = n[e];
                    if (!t)
                        return null;
                    const {letter: r, fontCmd: a} = t;
                    return `\\${a}{${r}}`
                }
            }
            ;
            const r = {
                á: {
                    char: "a",
                    accent: "´"
                },
                à: {
                    char: "a",
                    accent: "`"
                },
                â: {
                    char: "a",
                    accent: "^"
                },
                ã: {
                    char: "a",
                    accent: "~"
                },
                ä: {
                    char: "a",
                    accent: "¨"
                },
                å: {
                    char: "a",
                    accent: "˚"
                },
                ą: {
                    char: "a",
                    accent: "˙"
                },
                ă: {
                    char: "a",
                    accent: "˘"
                },
                ǎ: {
                    char: "a",
                    accent: "ˇ"
                },
                ǟ: {
                    char: "a",
                    accent: "ˆ"
                },
                ǻ: {
                    char: "a",
                    accent: "˙"
                },
                ǡ: {
                    char: "a",
                    accent: "-"
                },
                ā: {
                    char: "a",
                    accent: "-"
                },
                é: {
                    char: "e",
                    accent: "´"
                },
                è: {
                    char: "e",
                    accent: "`"
                },
                ê: {
                    char: "e",
                    accent: "^"
                },
                ë: {
                    char: "e",
                    accent: "¨"
                },
                ę: {
                    char: "e",
                    accent: "˙"
                },
                ě: {
                    char: "e",
                    accent: "ˇ"
                },
                ȇ: {
                    char: "i",
                    accent: "^"
                },
                ё: {
                    char: "e",
                    accent: "¨"
                },
                ē: {
                    char: "e",
                    accent: "-"
                },
                í: {
                    char: "i",
                    accent: "´"
                },
                ì: {
                    char: "i",
                    accent: "`"
                },
                î: {
                    char: "i",
                    accent: "^"
                },
                ï: {
                    char: "i",
                    accent: "¨"
                },
                į: {
                    char: "i",
                    accent: "˙"
                },
                ǐ: {
                    char: "i",
                    accent: "ˇ"
                },
                ȉ: {
                    char: "i",
                    accent: "`"
                },
                ȋ: {
                    char: "i",
                    accent: "¨"
                },
                ī: {
                    char: "i",
                    accent: "-"
                },
                ó: {
                    char: "o",
                    accent: "´"
                },
                ò: {
                    char: "o",
                    accent: "`"
                },
                ô: {
                    char: "o",
                    accent: "^"
                },
                õ: {
                    char: "o",
                    accent: "~"
                },
                ö: {
                    char: "o",
                    accent: "¨"
                },
                ő: {
                    char: "o",
                    accent: "˝"
                },
                ǒ: {
                    char: "o",
                    accent: "ˇ"
                },
                ȍ: {
                    char: "o",
                    accent: "`"
                },
                ȏ: {
                    char: "o",
                    accent: "¨"
                },
                ȫ: {
                    char: "o",
                    accent: "˘"
                },
                ȭ: {
                    char: "o",
                    accent: "˝"
                },
                ȯ: {
                    char: "o",
                    accent: "˙"
                },
                ō: {
                    char: "o",
                    accent: "-"
                },
                ú: {
                    char: "u",
                    accent: "´"
                },
                ù: {
                    char: "u",
                    accent: "`"
                },
                û: {
                    char: "u",
                    accent: "^"
                },
                ü: {
                    char: "u",
                    accent: "¨"
                },
                ű: {
                    char: "u",
                    accent: "˝"
                },
                ǔ: {
                    char: "u",
                    accent: "ˇ"
                },
                ǖ: {
                    char: "u",
                    accent: "¨"
                },
                ǘ: {
                    char: "u",
                    accent: "¨"
                },
                ǚ: {
                    char: "u",
                    accent: "¨"
                },
                ǜ: {
                    char: "u",
                    accent: "¨"
                },
                ȕ: {
                    char: "u",
                    accent: "`"
                },
                ȗ: {
                    char: "u",
                    accent: "¨"
                },
                ū: {
                    char: "u",
                    accent: "-"
                },
                ý: {
                    char: "y",
                    accent: "´"
                },
                ỳ: {
                    char: "y",
                    accent: "`"
                },
                ŷ: {
                    char: "y",
                    accent: "^"
                },
                ÿ: {
                    char: "y",
                    accent: "¨"
                },
                ȳ: {
                    char: "y",
                    accent: "-"
                },
                Á: {
                    char: "A",
                    accent: "´"
                },
                À: {
                    char: "A",
                    accent: "`"
                },
                Â: {
                    char: "A",
                    accent: "^"
                },
                Ã: {
                    char: "A",
                    accent: "~"
                },
                Ä: {
                    char: "A",
                    accent: "¨"
                },
                Å: {
                    char: "A",
                    accent: "˚"
                },
                Å: {
                    char: "A",
                    accent: "˚"
                },
                Ȧ: {
                    char: "A",
                    accent: "˙"
                },
                Ă: {
                    char: "A",
                    accent: "˘"
                },
                Ǎ: {
                    char: "A",
                    accent: "ˇ"
                },
                Ǟ: {
                    char: "A",
                    accent: "˝"
                },
                Ǻ: {
                    char: "A",
                    accent: "˚"
                },
                Ǡ: {
                    char: "A",
                    accent: "-"
                },
                Ā: {
                    char: "A",
                    accent: "-"
                },
                É: {
                    char: "E",
                    accent: "´"
                },
                È: {
                    char: "E",
                    accent: "`"
                },
                Ė: {
                    char: "E",
                    accent: "˙"
                },
                Ê: {
                    char: "E",
                    accent: "^"
                },
                Ë: {
                    char: "E",
                    accent: "¨"
                },
                Ě: {
                    char: "E",
                    accent: "ˇ"
                },
                Ȅ: {
                    char: "E",
                    accent: "`"
                },
                Ȇ: {
                    char: "E",
                    accent: "¨"
                },
                Ē: {
                    char: "E",
                    accent: "-"
                },
                Í: {
                    char: "I",
                    accent: "´"
                },
                Ì: {
                    char: "I",
                    accent: "`"
                },
                Î: {
                    char: "I",
                    accent: "^"
                },
                Ï: {
                    char: "I",
                    accent: "¨"
                },
                Ĭ: {
                    char: "I",
                    accent: "˘"
                },
                Ǐ: {
                    char: "I",
                    accent: "ˇ"
                },
                Ȉ: {
                    char: "I",
                    accent: "`"
                },
                Ȋ: {
                    char: "I",
                    accent: "¨"
                },
                Ī: {
                    char: "I",
                    accent: "-"
                },
                Ó: {
                    char: "O",
                    accent: "´"
                },
                Ò: {
                    char: "O",
                    accent: "`"
                },
                Ô: {
                    char: "O",
                    accent: "^"
                },
                Õ: {
                    char: "O",
                    accent: "~"
                },
                Ö: {
                    char: "O",
                    accent: "¨"
                },
                Ő: {
                    char: "O",
                    accent: "˝"
                },
                Ǒ: {
                    char: "O",
                    accent: "ˇ"
                },
                Ȍ: {
                    char: "O",
                    accent: "`"
                },
                Ȏ: {
                    char: "O",
                    accent: "¨"
                },
                Ȫ: {
                    char: "O",
                    accent: "˘"
                },
                Ȭ: {
                    char: "O",
                    accent: "˝"
                },
                Ȯ: {
                    char: "O",
                    accent: "˙"
                },
                Ō: {
                    char: "O",
                    accent: "-"
                },
                Ú: {
                    char: "U",
                    accent: "´"
                },
                Ù: {
                    char: "U",
                    accent: "`"
                },
                Û: {
                    char: "U",
                    accent: "^"
                },
                Ü: {
                    char: "U",
                    accent: "¨"
                },
                Ű: {
                    char: "U",
                    accent: "˝"
                },
                Ǔ: {
                    char: "U",
                    accent: "ˇ"
                },
                Ǖ: {
                    char: "U",
                    accent: "¨"
                },
                Ȕ: {
                    char: "U",
                    accent: "`"
                },
                Ȗ: {
                    char: "U",
                    accent: "¨"
                },
                Ū: {
                    char: "U",
                    accent: "-"
                },
                Ý: {
                    char: "Y",
                    accent: "´"
                },
                Ỳ: {
                    char: "Y",
                    accent: "`"
                },
                Ŷ: {
                    char: "Y",
                    accent: "^"
                },
                Ÿ: {
                    char: "Y",
                    accent: "¨"
                },
                Ȳ: {
                    char: "Y",
                    accent: "-"
                },
                ñ: {
                    char: "n",
                    accent: "~"
                },
                Ñ: {
                    char: "N",
                    accent: "~"
                },
                ç: {
                    char: "c",
                    accent: "˙"
                },
                Ç: {
                    char: "C",
                    accent: "˙"
                },
                ṽ: {
                    char: "v",
                    accent: "~"
                },
                Ṽ: {
                    char: "V",
                    accent: "~"
                },
                ĵ: {
                    char: "j",
                    accent: "^"
                },
                Ĵ: {
                    char: "J",
                    accent: "^"
                },
                ź: {
                    char: "z",
                    accent: "´"
                },
                Ź: {
                    char: "Z",
                    accent: "´"
                },
                Ż: {
                    char: "Z",
                    accent: "^"
                },
                ż: {
                    char: "z",
                    accent: "^"
                },
                Ž: {
                    char: "Z",
                    accent: "ˇ"
                },
                ž: {
                    char: "z",
                    accent: "ˇ"
                },
                ẑ: {
                    char: "z",
                    accent: "ˆ"
                }
            }
              , a = {
                "´": "grave",
                "`": "acute",
                "^": "hat",
                "~": "tilde",
                "¨": "ddot",
                "˚": "mathring",
                "˘": "breve",
                ˇ: "check",
                "˝": "ddot",
                "˙": "dot",
                "-": "bar",
                ˆ: "hat",
                "˜": "tilde"
            }
              , n = {
                𝐀: {
                    letter: "A",
                    fontCmd: "mathbf"
                },
                𝐁: {
                    letter: "B",
                    fontCmd: "mathbf"
                },
                𝐂: {
                    letter: "C",
                    fontCmd: "mathbf"
                },
                𝐃: {
                    letter: "D",
                    fontCmd: "mathbf"
                },
                𝐄: {
                    letter: "E",
                    fontCmd: "mathbf"
                },
                Ε: {
                    letter: "E",
                    fontCmd: "mathbf"
                },
                𝐅: {
                    letter: "F",
                    fontCmd: "mathbf"
                },
                𝐆: {
                    letter: "G",
                    fontCmd: "mathbf"
                },
                𝐇: {
                    letter: "H",
                    fontCmd: "mathbf"
                },
                𝐈: {
                    letter: "I",
                    fontCmd: "mathbf"
                },
                𝐉: {
                    letter: "J",
                    fontCmd: "mathbf"
                },
                𝐊: {
                    letter: "K",
                    fontCmd: "mathbf"
                },
                𝐋: {
                    letter: "L",
                    fontCmd: "mathbf"
                },
                𝐌: {
                    letter: "M",
                    fontCmd: "mathbf"
                },
                𝐍: {
                    letter: "N",
                    fontCmd: "mathbf"
                },
                𝐎: {
                    letter: "O",
                    fontCmd: "mathbf"
                },
                𝐏: {
                    letter: "P",
                    fontCmd: "mathbf"
                },
                𝐐: {
                    letter: "Q",
                    fontCmd: "mathbf"
                },
                𝐑: {
                    letter: "R",
                    fontCmd: "mathbf"
                },
                𝐒: {
                    letter: "S",
                    fontCmd: "mathbf"
                },
                𝐓: {
                    letter: "T",
                    fontCmd: "mathbf"
                },
                𝐔: {
                    letter: "U",
                    fontCmd: "mathbf"
                },
                𝐕: {
                    letter: "V",
                    fontCmd: "mathbf"
                },
                𝐖: {
                    letter: "W",
                    fontCmd: "mathbf"
                },
                𝐗: {
                    letter: "X",
                    fontCmd: "mathbf"
                },
                𝞆: {
                    letter: "X",
                    fontCmd: "mathbf"
                },
                𝐘: {
                    letter: "Y",
                    fontCmd: "mathbf"
                },
                𝐙: {
                    letter: "Z",
                    fontCmd: "mathbf"
                },
                "𝟎": {
                    letter: "0",
                    fontCmd: "mathbf"
                },
                "𝟏": {
                    letter: "1",
                    fontCmd: "mathbf"
                },
                "𝟐": {
                    letter: "2",
                    fontCmd: "mathbf"
                },
                "𝟑": {
                    letter: "3",
                    fontCmd: "mathbf"
                },
                "𝟒": {
                    letter: "4",
                    fontCmd: "mathbf"
                },
                "𝟓": {
                    letter: "5",
                    fontCmd: "mathbf"
                },
                "𝟔": {
                    letter: "6",
                    fontCmd: "mathbf"
                },
                "𝟕": {
                    letter: "7",
                    fontCmd: "mathbf"
                },
                "𝟖": {
                    letter: "8",
                    fontCmd: "mathbf"
                },
                "𝟗": {
                    letter: "9",
                    fontCmd: "mathbf"
                },
                𝐴: {
                    letter: "A",
                    fontCmd: "mathit"
                },
                𝐵: {
                    letter: "B",
                    fontCmd: "mathit"
                },
                𝐶: {
                    letter: "C",
                    fontCmd: "mathit"
                },
                𝐷: {
                    letter: "D",
                    fontCmd: "mathit"
                },
                𝐸: {
                    letter: "E",
                    fontCmd: "mathit"
                },
                𝐹: {
                    letter: "F",
                    fontCmd: "mathit"
                },
                𝐺: {
                    letter: "G",
                    fontCmd: "mathit"
                },
                𝐻: {
                    letter: "H",
                    fontCmd: "mathit"
                },
                𝐼: {
                    letter: "I",
                    fontCmd: "mathit"
                },
                Ι: {
                    letter: "I",
                    fontCmd: "mathit"
                },
                𝐽: {
                    letter: "J",
                    fontCmd: "mathit"
                },
                𝐾: {
                    letter: "K",
                    fontCmd: "mathit"
                },
                𝐿: {
                    letter: "L",
                    fontCmd: "mathit"
                },
                𝑀: {
                    letter: "M",
                    fontCmd: "mathit"
                },
                𝑁: {
                    letter: "N",
                    fontCmd: "mathit"
                },
                𝑂: {
                    letter: "O",
                    fontCmd: "mathit"
                },
                𝑃: {
                    letter: "P",
                    fontCmd: "mathit"
                },
                𝑄: {
                    letter: "Q",
                    fontCmd: "mathit"
                },
                𝑅: {
                    letter: "R",
                    fontCmd: "mathit"
                },
                𝑆: {
                    letter: "S",
                    fontCmd: "mathit"
                },
                𝑇: {
                    letter: "T",
                    fontCmd: "mathit"
                },
                𝑈: {
                    letter: "U",
                    fontCmd: "mathit"
                },
                𝑉: {
                    letter: "V",
                    fontCmd: "mathit"
                },
                𝑊: {
                    letter: "W",
                    fontCmd: "mathit"
                },
                𝑋: {
                    letter: "X",
                    fontCmd: "mathit"
                },
                𝑌: {
                    letter: "Y",
                    fontCmd: "mathit"
                },
                𝑍: {
                    letter: "Z",
                    fontCmd: "mathit"
                },
                𝔸: {
                    letter: "A",
                    fontCmd: "mathbb"
                },
                𝔹: {
                    letter: "B",
                    fontCmd: "mathbb"
                },
                ℂ: {
                    letter: "C",
                    fontCmd: "mathbb"
                },
                𝔻: {
                    letter: "D",
                    fontCmd: "mathbb"
                },
                𝔼: {
                    letter: "E",
                    fontCmd: "mathbb"
                },
                𝔽: {
                    letter: "F",
                    fontCmd: "mathbb"
                },
                𝔾: {
                    letter: "G",
                    fontCmd: "mathbb"
                },
                ℍ: {
                    letter: "H",
                    fontCmd: "mathbb"
                },
                𝕀: {
                    letter: "I",
                    fontCmd: "mathbb"
                },
                𝕁: {
                    letter: "J",
                    fontCmd: "mathbb"
                },
                𝕂: {
                    letter: "K",
                    fontCmd: "mathbb"
                },
                𝕃: {
                    letter: "L",
                    fontCmd: "mathbb"
                },
                𝕄: {
                    letter: "M",
                    fontCmd: "mathbb"
                },
                ℕ: {
                    letter: "N",
                    fontCmd: "mathbb"
                },
                𝕆: {
                    letter: "O",
                    fontCmd: "mathbb"
                },
                ℙ: {
                    letter: "P",
                    fontCmd: "mathbb"
                },
                ℚ: {
                    letter: "Q",
                    fontCmd: "mathbb"
                },
                ℝ: {
                    letter: "R",
                    fontCmd: "mathbb"
                },
                𝕊: {
                    letter: "S",
                    fontCmd: "mathbb"
                },
                𝕋: {
                    letter: "T",
                    fontCmd: "mathbb"
                },
                𝕌: {
                    letter: "U",
                    fontCmd: "mathbb"
                },
                𝕍: {
                    letter: "V",
                    fontCmd: "mathbb"
                },
                𝕎: {
                    letter: "W",
                    fontCmd: "mathbb"
                },
                𝕏: {
                    letter: "X",
                    fontCmd: "mathbb"
                },
                𝕐: {
                    letter: "Y",
                    fontCmd: "mathbb"
                },
                ℤ: {
                    letter: "Z",
                    fontCmd: "mathbb"
                },
                "𝟘": {
                    letter: "0",
                    fontCmd: "mathbb"
                },
                "𝟙": {
                    letter: "1",
                    fontCmd: "mathbb"
                },
                "𝟚": {
                    letter: "2",
                    fontCmd: "mathbb"
                },
                "𝟛": {
                    letter: "3",
                    fontCmd: "mathbb"
                },
                "𝟜": {
                    letter: "4",
                    fontCmd: "mathbb"
                },
                "𝟝": {
                    letter: "5",
                    fontCmd: "mathbb"
                },
                "𝟞": {
                    letter: "6",
                    fontCmd: "mathbb"
                },
                "𝟟": {
                    letter: "7",
                    fontCmd: "mathbb"
                },
                "𝟠": {
                    letter: "8",
                    fontCmd: "mathbb"
                },
                "𝟡": {
                    letter: "9",
                    fontCmd: "mathbb"
                },
                𝒜: {
                    letter: "A",
                    fontCmd: "mathcal"
                },
                𝓐: {
                    letter: "A",
                    fontCmd: "mathcal"
                },
                ℬ: {
                    letter: "B",
                    fontCmd: "mathcal"
                },
                𝒞: {
                    letter: "C",
                    fontCmd: "mathcal"
                },
                𝒟: {
                    letter: "D",
                    fontCmd: "mathcal"
                },
                𝓓: {
                    letter: "D",
                    fontCmd: "mathcal"
                },
                ℰ: {
                    letter: "E",
                    fontCmd: "mathcal"
                },
                ℱ: {
                    letter: "F",
                    fontCmd: "mathcal"
                },
                𝓕: {
                    letter: "F",
                    fontCmd: "mathcal"
                },
                𝒢: {
                    letter: "G",
                    fontCmd: "mathcal"
                },
                ℋ: {
                    letter: "H",
                    fontCmd: "mathcal"
                },
                ℐ: {
                    letter: "I",
                    fontCmd: "mathcal"
                },
                𝒥: {
                    letter: "J",
                    fontCmd: "mathcal"
                },
                𝒦: {
                    letter: "K",
                    fontCmd: "mathcal"
                },
                ℒ: {
                    letter: "L",
                    fontCmd: "mathcal"
                },
                𝓛: {
                    letter: "L",
                    fontCmd: "mathcal"
                },
                ℳ: {
                    letter: "M",
                    fontCmd: "mathcal"
                },
                𝒩: {
                    letter: "N",
                    fontCmd: "mathcal"
                },
                𝒪: {
                    letter: "O",
                    fontCmd: "mathcal"
                },
                𝓞: {
                    letter: "O",
                    fontCmd: "mathcal"
                },
                𝒫: {
                    letter: "P",
                    fontCmd: "mathcal"
                },
                𝒬: {
                    letter: "Q",
                    fontCmd: "mathcal"
                },
                ℛ: {
                    letter: "R",
                    fontCmd: "mathcal"
                },
                𝕽: {
                    letter: "R",
                    fontCmd: "mathcal"
                },
                "℟": {
                    letter: "R",
                    fontCmd: "mathcal"
                },
                𝒮: {
                    letter: "S",
                    fontCmd: "mathcal"
                },
                𝒯: {
                    letter: "T",
                    fontCmd: "mathcal"
                },
                𝒰: {
                    letter: "U",
                    fontCmd: "mathcal"
                },
                𝒱: {
                    letter: "V",
                    fontCmd: "mathcal"
                },
                𝒲: {
                    letter: "W",
                    fontCmd: "mathcal"
                },
                𝒳: {
                    letter: "X",
                    fontCmd: "mathcal"
                },
                𝒴: {
                    letter: "Y",
                    fontCmd: "mathcal"
                },
                𝒵: {
                    letter: "Z",
                    fontCmd: "mathcal"
                },
                𝔄: {
                    letter: "A",
                    fontCmd: "mathfrak"
                },
                𝔅: {
                    letter: "B",
                    fontCmd: "mathfrak"
                },
                ℭ: {
                    letter: "C",
                    fontCmd: "mathfrak"
                },
                𝔇: {
                    letter: "D",
                    fontCmd: "mathfrak"
                },
                𝔈: {
                    letter: "E",
                    fontCmd: "mathfrak"
                },
                𝔉: {
                    letter: "F",
                    fontCmd: "mathfrak"
                },
                𝔊: {
                    letter: "G",
                    fontCmd: "mathfrak"
                },
                ℌ: {
                    letter: "H",
                    fontCmd: "mathfrak"
                },
                ℑ: {
                    letter: "I",
                    fontCmd: "mathfrak"
                },
                𝔍: {
                    letter: "J",
                    fontCmd: "mathfrak"
                },
                𝔎: {
                    letter: "K",
                    fontCmd: "mathfrak"
                },
                𝔏: {
                    letter: "L",
                    fontCmd: "mathfrak"
                },
                𝔐: {
                    letter: "M",
                    fontCmd: "mathfrak"
                },
                𝔑: {
                    letter: "N",
                    fontCmd: "mathfrak"
                },
                𝔒: {
                    letter: "O",
                    fontCmd: "mathfrak"
                },
                𝔓: {
                    letter: "P",
                    fontCmd: "mathfrak"
                },
                𝔔: {
                    letter: "Q",
                    fontCmd: "mathfrak"
                },
                ℜ: {
                    letter: "R",
                    fontCmd: "mathfrak"
                },
                𝔖: {
                    letter: "S",
                    fontCmd: "mathfrak"
                },
                𝔗: {
                    letter: "T",
                    fontCmd: "mathfrak"
                },
                𝔘: {
                    letter: "U",
                    fontCmd: "mathfrak"
                },
                𝔙: {
                    letter: "V",
                    fontCmd: "mathfrak"
                },
                𝔚: {
                    letter: "W",
                    fontCmd: "mathfrak"
                },
                𝔛: {
                    letter: "X",
                    fontCmd: "mathfrak"
                },
                𝔜: {
                    letter: "Y",
                    fontCmd: "mathfrak"
                },
                ℨ: {
                    letter: "Z",
                    fontCmd: "mathfrak"
                },
                𝖠: {
                    letter: "A",
                    fontCmd: "mathsf"
                },
                Α: {
                    letter: "A",
                    fontCmd: "mathsf"
                },
                𝖡: {
                    letter: "B",
                    fontCmd: "mathsf"
                },
                Β: {
                    letter: "B",
                    fontCmd: "mathsf"
                },
                𝖢: {
                    letter: "C",
                    fontCmd: "mathsf"
                },
                𝖣: {
                    letter: "D",
                    fontCmd: "mathsf"
                },
                𝖤: {
                    letter: "E",
                    fontCmd: "mathsf"
                },
                𝖥: {
                    letter: "F",
                    fontCmd: "mathsf"
                },
                𝖦: {
                    letter: "G",
                    fontCmd: "mathsf"
                },
                𝖧: {
                    letter: "H",
                    fontCmd: "mathsf"
                },
                𝖨: {
                    letter: "I",
                    fontCmd: "mathsf"
                },
                𝖩: {
                    letter: "J",
                    fontCmd: "mathsf"
                },
                ȷ: {
                    letter: "J",
                    fontCmd: "mathsf"
                },
                𝖪: {
                    letter: "K",
                    fontCmd: "mathsf"
                },
                Κ: {
                    letter: "K",
                    fontCmd: "mathsf"
                },
                𝖫: {
                    letter: "L",
                    fontCmd: "mathsf"
                },
                𝖬: {
                    letter: "M",
                    fontCmd: "mathsf"
                },
                𝖭: {
                    letter: "N",
                    fontCmd: "mathsf"
                },
                𝖮: {
                    letter: "O",
                    fontCmd: "mathsf"
                },
                𝖯: {
                    letter: "P",
                    fontCmd: "mathsf"
                },
                𝖰: {
                    letter: "Q",
                    fontCmd: "mathsf"
                },
                𝖱: {
                    letter: "R",
                    fontCmd: "mathsf"
                },
                𝖲: {
                    letter: "S",
                    fontCmd: "mathsf"
                },
                𝖳: {
                    letter: "T",
                    fontCmd: "mathsf"
                },
                𝖴: {
                    letter: "U",
                    fontCmd: "mathsf"
                },
                𝖵: {
                    letter: "V",
                    fontCmd: "mathsf"
                },
                𝖶: {
                    letter: "W",
                    fontCmd: "mathsf"
                },
                𝖷: {
                    letter: "X",
                    fontCmd: "mathsf"
                },
                Χ: {
                    letter: "X",
                    fontCmd: "mathsf"
                },
                𝖸: {
                    letter: "Y",
                    fontCmd: "mathsf"
                },
                𝖹: {
                    letter: "Z",
                    fontCmd: "mathsf"
                },
                𝚨: {
                    letter: "A",
                    fontCmd: "mathtt"
                },
                𝚩: {
                    letter: "B",
                    fontCmd: "mathtt"
                },
                𝚪: {
                    letter: "\\Gamma",
                    fontCmd: "mathtt"
                },
                𝚫: {
                    letter: "\\Delta",
                    fontCmd: "mathtt"
                },
                𝚬: {
                    letter: "E",
                    fontCmd: "mathtt"
                },
                𝚭: {
                    letter: "F",
                    fontCmd: "mathtt"
                },
                𝚮: {
                    letter: "G",
                    fontCmd: "mathtt"
                },
                𝚯: {
                    letter: "\\Theta",
                    fontCmd: "mathtt"
                },
                𝚰: {
                    letter: "I",
                    fontCmd: "mathtt"
                },
                𝚱: {
                    letter: "J",
                    fontCmd: "mathtt"
                },
                𝚲: {
                    letter: "\\Lambda",
                    fontCmd: "mathtt"
                },
                𝚳: {
                    letter: "L",
                    fontCmd: "mathtt"
                },
                𝚴: {
                    letter: "M",
                    fontCmd: "mathtt"
                },
                𝚵: {
                    letter: "\\Pi",
                    fontCmd: "mathtt"
                },
                𝚶: {
                    letter: "O",
                    fontCmd: "mathtt"
                },
                𝚷: {
                    letter: "\\Pi",
                    fontCmd: "mathtt"
                },
                𝚸: {
                    letter: "Q",
                    fontCmd: "mathtt"
                },
                𝚹: {
                    letter: "R",
                    fontCmd: "mathtt"
                },
                𝚺: {
                    letter: "S",
                    fontCmd: "mathtt"
                },
                𝚻: {
                    letter: "T",
                    fontCmd: "mathtt"
                },
                𝚼: {
                    letter: "U",
                    fontCmd: "mathtt"
                },
                𝚽: {
                    letter: "\\Phi",
                    fontCmd: "mathtt"
                },
                𝚾: {
                    letter: "W",
                    fontCmd: "mathtt"
                },
                𝚿: {
                    letter: "\\Psi",
                    fontCmd: "mathtt"
                },
                𝛀: {
                    letter: "\\Omega",
                    fontCmd: "mathtt"
                }
            }
        }
    }
      , t = {};
    function r(a) {
        var n = t[a];
        if (void 0 !== n)
            return n.exports;
        var o = t[a] = {
            exports: {}
        };
        return e[a].call(o.exports, o, o.exports, r),
        o.exports
    }
    var a = {};
    return (()=>{
        "use strict";
        var e = a;
        Object.defineProperty(e, "__esModule", {
            value: !0
        }),
        e.MathMLToLaTeX = void 0;
        var t = r(8863);
        Object.defineProperty(e, "MathMLToLaTeX", {
            enumerable: !0,
            get: function() {
                return t.MathMLToLaTeX
            }
        })
    }
    )(),
    a
}
)()));
//# sourceMappingURL=bundle.min.js.map

/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'sv', {
	alt: 'Alternativ text',
	btnUpload: 'Skick<PERSON> till server',
	captioned: 'Rubricerad bild',
	captionPlaceholder: 'Bildtext',
	infoTab: 'Bildinformation',
	lockRatio: '<PERSON><PERSON>s höjd/bredd förhållanden',
	menu: 'Bildegenskaper',
	pathName: 'bild',
	pathNameCaption: 'rubrik',
	resetSize: '<PERSON>terställ storlek',
	resizer: '<PERSON><PERSON><PERSON> och drag för att ändra storlek',
	title: 'Bildegenskaper',
	uploadTab: 'Ladda upp',
	urlMissing: 'Bildkällans URL saknas.',
	altMissing: 'Alternativ text saknas'
} );

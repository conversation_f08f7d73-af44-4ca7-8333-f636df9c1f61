// 用户管理JavaScript

document.addEventListener('DOMContentLoaded', function() {
  // 初始化模态框
  const createAdminModal = new bootstrap.Modal(document.getElementById('createAdminModal'));
  const userDetailModal = new bootstrap.Modal(document.getElementById('userDetailModal'));
  
  // 当前登录用户信息（从localStorage获取）
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');

  // 页面加载时获取数据
  loadUserStats();
  loadPendingUsers();
  loadAllUsers();

  // 刷新按钮事件
  document.getElementById('refresh-btn').addEventListener('click', () => {
    loadUserStats();
    loadPendingUsers();
    loadAllUsers();
  });

  // 创建管理员按钮事件
  document.getElementById('create-admin-btn').addEventListener('click', () => {
    document.getElementById('create-admin-form').reset();
    createAdminModal.show();
  });

  // 保存管理员按钮事件
  document.getElementById('save-admin-btn').addEventListener('click', createAdmin);

  // 标签页切换事件
  document.getElementById('all-tab').addEventListener('click', () => {
    loadAllUsers();
  });

  /**
   * 加载用户统计信息
   */
  function loadUserStats() {
    fetch('/api/users/admin/stats')
      .then(response => response.json())
      .then(stats => {
        displayUserStats(stats);
      })
      .catch(error => {
        console.error('加载用户统计失败:', error);
        showAlert('加载用户统计失败', 'danger');
      });
  }

  /**
   * 显示用户统计信息
   */
  function displayUserStats(stats) {
    const container = document.getElementById('user-stats');
    container.innerHTML = `
      <div class="col-md-2">
        <div class="text-center">
          <h3 class="mb-0">${stats.total}</h3>
          <small>总用户数</small>
        </div>
      </div>
      <div class="col-md-2">
        <div class="text-center">
          <h3 class="mb-0">${stats.active}</h3>
          <small>活跃用户</small>
        </div>
      </div>
      <div class="col-md-2">
        <div class="text-center">
          <h3 class="mb-0">${stats.pending}</h3>
          <small>待审核</small>
        </div>
      </div>
      <div class="col-md-2">
        <div class="text-center">
          <h3 class="mb-0">${stats.disabled}</h3>
          <small>已禁用</small>
        </div>
      </div>
      <div class="col-md-2">
        <div class="text-center">
          <h3 class="mb-0">${stats.admins}</h3>
          <small>管理员</small>
        </div>
      </div>
      <div class="col-md-2">
        <div class="text-center">
          <h3 class="mb-0">${stats.users}</h3>
          <small>普通用户</small>
        </div>
      </div>
    `;
    
    // 更新待审核用户数量徽章
    document.getElementById('pending-count').textContent = stats.pending;
  }

  /**
   * 加载待审核用户
   */
  function loadPendingUsers() {
    fetch('/api/users/admin/pending')
      .then(response => response.json())
      .then(users => {
        displayPendingUsers(users);
      })
      .catch(error => {
        console.error('加载待审核用户失败:', error);
        showAlert('加载待审核用户失败', 'danger');
      });
  }

  /**
   * 显示待审核用户
   */
  function displayPendingUsers(users) {
    const container = document.getElementById('pending-users-container');
    
    if (users.length === 0) {
      container.innerHTML = `
        <div class="text-center py-5">
          <i class="bi bi-check-circle" style="font-size: 3rem; color: #28a745;"></i>
          <h4 class="mt-3 text-muted">暂无待审核用户</h4>
          <p class="text-muted">所有用户都已审核完成</p>
        </div>
      `;
      return;
    }

    let html = '';
    users.forEach(user => {
      html += `
        <div class="user-card">
          <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
              <div class="d-flex align-items-center mb-2">
                <i class="bi bi-person-circle me-2" style="font-size: 1.5rem;"></i>
                <div>
                  <h5 class="mb-0">${user.displayName || user.username}</h5>
                  <small class="text-muted">@${user.username}</small>
                </div>
                <span class="badge bg-warning status-badge ms-2">待审核</span>
              </div>
              ${user.email ? `
                <p class="text-muted mb-1">
                  <i class="bi bi-envelope me-1"></i>
                  ${user.email}
                </p>
              ` : ''}
              <small class="text-muted">
                注册时间: ${new Date(user.createdAt).toLocaleString()}
              </small>
            </div>
            <div class="btn-group" role="group">
              <button class="btn btn-sm btn-outline-info" onclick="viewUserDetail('${user.id}')">
                <i class="bi bi-eye"></i> 查看
              </button>
              <button class="btn btn-sm btn-success" onclick="approveUser('${user.id}', true)">
                <i class="bi bi-check"></i> 通过
              </button>
              <button class="btn btn-sm btn-danger" onclick="approveUser('${user.id}', false)">
                <i class="bi bi-x"></i> 拒绝
              </button>
            </div>
          </div>
        </div>
      `;
    });

    container.innerHTML = html;
  }

  /**
   * 加载所有用户
   */
  function loadAllUsers() {
    fetch('/api/users')
      .then(response => response.json())
      .then(users => {
        displayAllUsers(users);
      })
      .catch(error => {
        console.error('加载用户列表失败:', error);
        showAlert('加载用户列表失败', 'danger');
      });
  }

  /**
   * 显示所有用户
   */
  function displayAllUsers(users) {
    const container = document.getElementById('all-users-container');
    
    if (users.length === 0) {
      container.innerHTML = `
        <div class="text-center py-5">
          <i class="bi bi-people" style="font-size: 3rem; color: #6c757d;"></i>
          <h4 class="mt-3 text-muted">暂无用户</h4>
        </div>
      `;
      return;
    }

    let html = '';
    users.forEach(user => {
      const statusColor = getStatusColor(user.status);
      const roleIcon = user.role === 'admin' ? 'shield-check' : 'person';
      
      html += `
        <div class="user-card">
          <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
              <div class="d-flex align-items-center mb-2">
                <i class="bi bi-${roleIcon} me-2" style="font-size: 1.5rem;"></i>
                <div>
                  <h5 class="mb-0">${user.displayName || user.username}</h5>
                  <small class="text-muted">@${user.username}</small>
                </div>
                <span class="badge bg-${statusColor} status-badge ms-2">${getStatusText(user.status)}</span>
                ${user.role === 'admin' ? '<span class="badge bg-primary status-badge ms-1">管理员</span>' : ''}
              </div>
              ${user.email ? `
                <p class="text-muted mb-1">
                  <i class="bi bi-envelope me-1"></i>
                  ${user.email}
                </p>
              ` : ''}
              <div class="row">
                <div class="col-md-6">
                  <small class="text-muted">
                    注册时间: ${new Date(user.createdAt).toLocaleString()}
                  </small>
                </div>
                ${user.lastLoginAt ? `
                  <div class="col-md-6">
                    <small class="text-muted">
                      最后登录: ${new Date(user.lastLoginAt).toLocaleString()}
                    </small>
                  </div>
                ` : ''}
              </div>
            </div>
            <div class="btn-group" role="group">
              <button class="btn btn-sm btn-outline-info" onclick="viewUserDetail('${user.id}')">
                <i class="bi bi-eye"></i> 查看
              </button>
              ${user.status === 'active' ? `
                <button class="btn btn-sm btn-warning" onclick="updateUserStatus('${user.id}', 'disabled')">
                  <i class="bi bi-ban"></i> 禁用
                </button>
              ` : user.status === 'disabled' ? `
                <button class="btn btn-sm btn-success" onclick="updateUserStatus('${user.id}', 'active')">
                  <i class="bi bi-check"></i> 启用
                </button>
              ` : ''}
              ${user.id !== currentUser.id ? `
                <button class="btn btn-sm btn-outline-danger" onclick="deleteUser('${user.id}', '${user.username}')">
                  <i class="bi bi-trash"></i> 删除
                </button>
              ` : ''}
            </div>
          </div>
        </div>
      `;
    });

    container.innerHTML = html;
  }

  /**
   * 创建管理员
   */
  function createAdmin() {
    const username = document.getElementById('admin-username').value;
    const password = document.getElementById('admin-password').value;
    const email = document.getElementById('admin-email').value;
    const displayName = document.getElementById('admin-displayName').value;

    if (!username || !password) {
      showAlert('用户名和密码为必填项', 'danger');
      return;
    }

    fetch('/api/users/admin/create', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username,
        password,
        email,
        displayName
      })
    })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          showAlert(data.error, 'danger');
          return;
        }
        
        createAdminModal.hide();
        loadUserStats();
        loadAllUsers();
        showAlert('管理员创建成功', 'success');
      })
      .catch(error => {
        console.error('创建管理员失败:', error);
        showAlert('创建管理员失败', 'danger');
      });
  }

  /**
   * 审核用户
   */
  window.approveUser = function(userId, approved) {
    const action = approved ? '通过' : '拒绝';
    if (confirm(`确定要${action}这个用户的注册申请吗？`)) {
      fetch(`/api/users/${userId}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          approved,
          adminId: currentUser.id
        })
      })
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            showAlert(data.error, 'danger');
            return;
          }
          
          loadUserStats();
          loadPendingUsers();
          loadAllUsers();
          showAlert(data.message, 'success');
        })
        .catch(error => {
          console.error('审核用户失败:', error);
          showAlert('审核用户失败', 'danger');
        });
    }
  };

  /**
   * 更新用户状态
   */
  window.updateUserStatus = function(userId, status) {
    const statusText = status === 'active' ? '启用' : '禁用';
    if (confirm(`确定要${statusText}这个用户吗？`)) {
      fetch(`/api/users/${userId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          status,
          adminId: currentUser.id
        })
      })
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            showAlert(data.error, 'danger');
            return;
          }
          
          loadUserStats();
          loadAllUsers();
          showAlert(data.message, 'success');
        })
        .catch(error => {
          console.error('更新用户状态失败:', error);
          showAlert('更新用户状态失败', 'danger');
        });
    }
  };

  /**
   * 删除用户
   */
  window.deleteUser = function(userId, username) {
    if (confirm(`确定要删除用户"${username}"吗？此操作不可撤销。`)) {
      fetch(`/api/users/${userId}`, {
        method: 'DELETE'
      })
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            showAlert(data.error, 'danger');
            return;
          }
          
          loadUserStats();
          loadPendingUsers();
          loadAllUsers();
          showAlert('用户删除成功', 'success');
        })
        .catch(error => {
          console.error('删除用户失败:', error);
          showAlert('删除用户失败', 'danger');
        });
    }
  };

  /**
   * 查看用户详情
   */
  window.viewUserDetail = function(userId) {
    fetch(`/api/users/${userId}`)
      .then(response => response.json())
      .then(user => {
        if (user.error) {
          showAlert(user.error, 'danger');
          return;
        }
        
        displayUserDetail(user);
        userDetailModal.show();
      })
      .catch(error => {
        console.error('获取用户详情失败:', error);
        showAlert('获取用户详情失败', 'danger');
      });
  };

  /**
   * 显示用户详情
   */
  function displayUserDetail(user) {
    const content = document.getElementById('user-detail-content');
    const statusColor = getStatusColor(user.status);
    
    content.innerHTML = `
      <div class="row">
        <div class="col-md-6">
          <h6>基本信息</h6>
          <table class="table table-sm">
            <tr>
              <td><strong>用户名:</strong></td>
              <td>${user.username}</td>
            </tr>
            <tr>
              <td><strong>显示名称:</strong></td>
              <td>${user.displayName || '-'}</td>
            </tr>
            <tr>
              <td><strong>邮箱:</strong></td>
              <td>${user.email || '-'}</td>
            </tr>
            <tr>
              <td><strong>角色:</strong></td>
              <td>
                <span class="badge ${user.role === 'admin' ? 'bg-primary' : 'bg-secondary'}">
                  ${user.role === 'admin' ? '管理员' : '普通用户'}
                </span>
              </td>
            </tr>
            <tr>
              <td><strong>状态:</strong></td>
              <td><span class="badge bg-${statusColor}">${getStatusText(user.status)}</span></td>
            </tr>
          </table>
        </div>
        <div class="col-md-6">
          <h6>时间信息</h6>
          <table class="table table-sm">
            <tr>
              <td><strong>注册时间:</strong></td>
              <td>${new Date(user.createdAt).toLocaleString()}</td>
            </tr>
            <tr>
              <td><strong>更新时间:</strong></td>
              <td>${new Date(user.updatedAt).toLocaleString()}</td>
            </tr>
            ${user.approvedAt ? `
              <tr>
                <td><strong>审核时间:</strong></td>
                <td>${new Date(user.approvedAt).toLocaleString()}</td>
              </tr>
            ` : ''}
            ${user.lastLoginAt ? `
              <tr>
                <td><strong>最后登录:</strong></td>
                <td>${new Date(user.lastLoginAt).toLocaleString()}</td>
              </tr>
            ` : ''}
          </table>
        </div>
      </div>
    `;
  }

  /**
   * 获取状态颜色
   */
  function getStatusColor(status) {
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'disabled': return 'danger';
      default: return 'secondary';
    }
  }

  /**
   * 获取状态文本
   */
  function getStatusText(status) {
    switch (status) {
      case 'active': return '活跃';
      case 'pending': return '待审核';
      case 'disabled': return '已禁用';
      default: return '未知';
    }
  }

  /**
   * 显示提示信息
   */
  function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    alertDiv.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.parentNode.removeChild(alertDiv);
      }
    }, 3000);
  }
});

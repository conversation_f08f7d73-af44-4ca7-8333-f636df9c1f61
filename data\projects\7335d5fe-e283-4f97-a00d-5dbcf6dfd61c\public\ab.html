<!--
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-12-04 09:47:41
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-02-25 11:08:23
-->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C30扫描作业</title>
    <style>
        /* 重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        /* 按钮容器样式 */
        .ab-wrap {
            position: fixed;
            left: calc((100vw - 373mm) / 2);
            z-index: 100;
        }

        /* 按钮基础样式 */
        .ab-btn {
            width: 68px;
            line-height: 40px;
            background: #fff;
            border-radius: 10px 0 0 10px;
            text-align: center;
            color: #606266;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: -2px 2px 5px rgba(0,0,0,0.1);
            opacity: 0;
            transform: translateX(-10px);
        }

        /* 按钮显示动画 */
        .ab-btn.show {
            opacity: 1;
            transform: translateX(0);
        }

        .ab-btn:hover {
            color: #409eff;
            background: #f5f7fa;
        }

        .ab-btn.active {
            color: #409eff;
            background: #ecf5ff;
        }

        #abtn {
            margin-top: 25px;
        }

        #bbtn {
            margin-top: 10px;
        }

        /* iframe 样式 */
        .card-frame {
            width: 100%;
            height: 100%;
            border: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            position: absolute;
            top: 0;
            left: 0;
        }

        .card-frame.show {
            opacity: 1;
            z-index: 1;
        }
    </style>
</head>
<body>
    <div class="ab-wrap">
        <div id="abtn" class="ab-btn active" title="切换到A卡">A卡</div>
        <div id="bbtn" class="ab-btn" title="切换到B卡">B卡</div>
    </div>
    <iframe id="a-card" class="card-frame" src="" frameborder="0" allowfullscreen></iframe>
    <iframe id="b-card" class="card-frame" src="" frameborder="0" allowfullscreen></iframe>

    <script>
        // URL参数处理工具
        const URLUtils = {
            getQueryString(name) {
                const url = window.location.search;
                const reg = new RegExp(`(^|&)${name}=([^&]*)(&|$)`);
                const r = url.substr(1).match(reg);
                return r ? decodeURIComponent(r[2]) : '';
            },

            getCardUrl(id,ab) {
                const params = [
                    `id=${id}`,
                    `examName=${this.getQueryString('examName')}`,
                    `cardType=${this.getQueryString('cardType')}`,
                    `correctType=${this.getQueryString('correctType')}`,
                    `pageLayout=${this.getQueryString('pageLayout')}`,
                    `subjectId=${this.getQueryString('subjectId')}`,
                    `editPower=${this.getQueryString('editPower')}`,
                    `abType=${ab}`,
                    `examId=${this.getQueryString('examId')}`,
                    `relateCard=${this.getQueryString('relateCard')}`,
                    `personBookId=${this.getQueryString('personBookId')}`,
                    `relateCardType=${this.getQueryString('relateCardType')}`,
                    `abCardSheetType=${this.getQueryString('abCardSheetType')}`,
                    `token=${this.getQueryString('token')}`
                ];
                if(window.location.protocol == 'file:'){
                    return `http://localhost:9801/mc/index?${params.join('&')}`;
                }else if(window.location.protocol == 'http:'){
                    return `/index?${params.join('&')}`;
                }else{
                    return `/mc/index?${params.join('&')}`;
                }
            }
        };

        // 卡片切换控制器
        class CardController {
            constructor() {
                this.aBtn = document.getElementById('abtn');
                this.bBtn = document.getElementById('bbtn');
                this.aCard = document.getElementById('a-card');
                this.bCard = document.getElementById('b-card');
                
                this.init();
                this.bindEvents();
            }

            init() {
                const ids = URLUtils.getQueryString('id')?.split(',') || [];
                if (ids.length < 2) {
                    console.error('缺少必要的ID参数');
                    return;
                }

                // 设置iframe源
                this.aCard.src = URLUtils.getCardUrl(ids[0],'A');
                this.bCard.src = URLUtils.getCardUrl(ids[1],'B');

                // 初始显示A卡
                this.showCard('a');
            }

            bindEvents() {
                // 加载完成后显示按钮
                this.aCard.addEventListener('load', () => {
                    this.aBtn.classList.add('show');
                    this.bBtn.classList.add('show');
                });

                // 切换事件
                this.aBtn.addEventListener('click', () => this.showCard('a'));
                this.bBtn.addEventListener('click', () => this.showCard('b'));

                // 消息监听
                window.addEventListener('message', this.handleMessage.bind(this));
            }

            showCard(type) {
                const isShowA = type === 'a';
                
                // 更新按钮状态
                this.aBtn.classList.toggle('active', isShowA);
                this.bBtn.classList.toggle('active', !isShowA);

                // 更新iframe显示状态
                this.aCard.classList.toggle('show', isShowA);
                this.bCard.classList.toggle('show', !isShowA);
            }

            handleMessage(event) {
                // TODO: 根据实际域名配置
                let allowedOrigin = '';
                if(window.location.protocol == 'file:'){
                    allowedOrigin = 'http://localhost:9801';
                }else{
                    allowedOrigin = 'iclass30.com';
                }
                if (!event.origin.includes(allowedOrigin)) {
                    console.warn('收到未知来源消息:', event.origin);
                    return;
                }

                try {
                    const message = JSON.parse(event.data);
                    // 检查消息类型
                    if (message && ['updateQuestions','updateInfos'].includes(message.type)) {
                        console.log('处理消息:', message);
                        if(message.from == 'A') {
                            // 转发消息给B卡
                            this.bCard.contentWindow.postMessage(message, '*');
                        } else {
                            // 转发消息给A卡
                            this.aCard.contentWindow.postMessage(message, '*');
                        }
                        if(message.func == 'completeCard'){
                            if(message.from == 'A') {
                                this.showCard('b')
                            }else{
                                this.showCard('a')
                            } 
                        }
                    }else if(message.type == 'completeCard'){
                        console.log('完成制卡:', message);
                        // setTimeout(() => {
                            // window.location.reload();
                            //window.location.replace('about:blank');
                            // window.close();
                        // }, 1000);
                    }else if(message.type == 'hideSlider'){
                        if(message.arguments){
                            document.getElementById('abtn').style.display = 'none';
                            document.getElementById('bbtn').style.display = 'none';
                        }else{
                            document.getElementById('abtn').style.display = 'block';
                            document.getElementById('bbtn').style.display = 'block';
                        }
                    }
                } catch (error) {
                    console.error('消息处理失败:', error.message);
                }
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            new CardController();
        });
    </script>
</body>
</html>
<script>
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "https://hm.baidu.com/hm.js?15da8d1ed9aa611653b7e1b7289b82ea";
      var s = document.getElementsByTagName("script")[0]; 
      s.parentNode.insertBefore(hm, s);
    })();
    </script>
    

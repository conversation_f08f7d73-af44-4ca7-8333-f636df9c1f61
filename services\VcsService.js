// services/VcsService.js
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);
const path = require('path');
const fs = require('fs-extra');
const VcsCredential = require('../models/VcsCredential');

class VcsService {
  constructor() {
    this.tempDir = path.join(process.cwd(), 'temp', 'vcs');
    fs.ensureDirSync(this.tempDir);
  }

  /**
   * 克隆或检出仓库
   * @param {string} repositoryUrl - 仓库URL
   * @param {string} targetDir - 目标目录
   * @param {string} vcsType - VCS类型 (git/svn)
   * @param {string} credentialId - 凭据ID（可选）
   * @param {string} branch - 分支名（Git专用，可选）
   */
  async cloneRepository(repositoryUrl, targetDir, vcsType = 'git', credentialId = null, branch = 'main') {
    try {
      let credential = null;
      if (credentialId) {
        credential = await VcsCredential.getByIdWithPassword(credentialId);
        if (!credential) {
          throw new Error('VCS凭据不存在');
        }
      }

      if (vcsType === 'git') {
        return await this.cloneGitRepository(repositoryUrl, targetDir, credential, branch);
      } else if (vcsType === 'svn') {
        return await this.checkoutSvnRepository(repositoryUrl, targetDir, credential);
      } else {
        throw new Error(`不支持的VCS类型: ${vcsType}`);
      }
    } catch (error) {
      throw new Error(`克隆仓库失败: ${error.message}`);
    }
  }

  /**
   * 更新仓库
   * @param {string} projectDir - 项目目录
   * @param {string} vcsType - VCS类型
   * @param {string} credentialId - 凭据ID（可选）
   */
  async updateRepository(projectDir, vcsType = 'git', credentialId = null) {
    try {
      let credential = null;
      if (credentialId) {
        credential = await VcsCredential.getByIdWithPassword(credentialId);
      }

      if (vcsType === 'git') {
        return await this.pullGitRepository(projectDir, credential);
      } else if (vcsType === 'svn') {
        return await this.updateSvnRepository(projectDir, credential);
      } else {
        throw new Error(`不支持的VCS类型: ${vcsType}`);
      }
    } catch (error) {
      throw new Error(`更新仓库失败: ${error.message}`);
    }
  }

  /**
   * 切换分支（Git专用）
   * @param {string} projectDir - 项目目录
   * @param {string} branch - 分支名
   * @param {string} credentialId - 凭据ID（可选）
   */
  async checkoutBranch(projectDir, branch, credentialId = null) {
    try {
      let credential = null;
      if (credentialId) {
        credential = await VcsCredential.getByIdWithPassword(credentialId);
      }

      // 检查是否为Git仓库
      const gitDir = path.join(projectDir, '.git');
      if (!(await fs.pathExists(gitDir))) {
        throw new Error('不是Git仓库，无法切换分支');
      }

      const result = await execPromise(`git checkout ${branch}`, { cwd: projectDir });
      return { success: true, stdout: result.stdout, stderr: result.stderr };
    } catch (error) {
      throw new Error(`切换分支失败: ${error.message}`);
    }
  }

  /**
   * 克隆Git仓库
   */
  async cloneGitRepository(repositoryUrl, targetDir, credential = null, branch = 'main') {
    let authUrl = repositoryUrl;
    
    if (credential) {
      // 构建带认证信息的URL
      const urlObj = new URL(repositoryUrl);
      urlObj.username = credential.username;
      urlObj.password = credential.password;
      authUrl = urlObj.toString();
    }

    const command = `git clone -b ${branch} "${authUrl}" "${targetDir}"`;
    const result = await execPromise(command);
    
    return { success: true, stdout: result.stdout, stderr: result.stderr };
  }

  /**
   * 拉取Git仓库更新
   */
  async pullGitRepository(projectDir, credential = null) {
    let command = 'git pull';
    
    if (credential) {
      // 如果有凭据，可能需要设置认证信息
      // 这里可以根据需要实现更复杂的认证逻辑
    }

    const result = await execPromise(command, { cwd: projectDir });
    return { success: true, stdout: result.stdout, stderr: result.stderr };
  }

  /**
   * 检出SVN仓库
   */
  async checkoutSvnRepository(repositoryUrl, targetDir, credential = null) {
    let command = `svn checkout "${repositoryUrl}" "${targetDir}"`;
    
    if (credential) {
      command += ` --username "${credential.username}" --password "${credential.password}" --non-interactive --trust-server-cert`;
    }

    const result = await execPromise(command);
    return { success: true, stdout: result.stdout, stderr: result.stderr };
  }

  /**
   * 更新SVN仓库
   */
  async updateSvnRepository(projectDir, credential = null) {
    let command = 'svn update';
    
    if (credential) {
      command += ` --username "${credential.username}" --password "${credential.password}" --non-interactive --trust-server-cert`;
    }

    const result = await execPromise(command, { cwd: projectDir });
    return { success: true, stdout: result.stdout, stderr: result.stderr };
  }

  /**
   * 检测仓库类型
   * @param {string} projectDir - 项目目录
   */
  async detectVcsType(projectDir) {
    try {
      const gitDir = path.join(projectDir, '.git');
      const svnDir = path.join(projectDir, '.svn');
      
      if (await fs.pathExists(gitDir)) {
        return 'git';
      } else if (await fs.pathExists(svnDir)) {
        return 'svn';
      } else {
        return null;
      }
    } catch (error) {
      return null;
    }
  }

  /**
   * 获取仓库信息
   * @param {string} projectDir - 项目目录
   * @param {string} vcsType - VCS类型
   */
  async getRepositoryInfo(projectDir, vcsType) {
    try {
      if (vcsType === 'git') {
        const remoteResult = await execPromise('git remote get-url origin', { cwd: projectDir });
        const branchResult = await execPromise('git branch --show-current', { cwd: projectDir });
        
        return {
          url: remoteResult.stdout.trim(),
          branch: branchResult.stdout.trim(),
          type: 'git'
        };
      } else if (vcsType === 'svn') {
        const infoResult = await execPromise('svn info --show-item url', { cwd: projectDir });
        
        return {
          url: infoResult.stdout.trim(),
          type: 'svn'
        };
      }
    } catch (error) {
      throw new Error(`获取仓库信息失败: ${error.message}`);
    }
  }

  /**
   * 测试VCS连接
   * @param {string} repositoryUrl - 仓库URL
   * @param {string} vcsType - VCS类型
   * @param {string} credentialId - 凭据ID（可选）
   */
  async testConnection(repositoryUrl, vcsType, credentialId = null) {
    try {
      let credential = null;
      if (credentialId) {
        credential = await VcsCredential.getByIdWithPassword(credentialId);
      }

      const testDir = path.join(this.tempDir, `test_${Date.now()}`);
      
      try {
        if (vcsType === 'git') {
          await this.cloneGitRepository(repositoryUrl, testDir, credential);
        } else if (vcsType === 'svn') {
          await this.checkoutSvnRepository(repositoryUrl, testDir, credential);
        }
        
        // 清理测试目录
        await fs.remove(testDir);
        
        return { success: true, message: '连接测试成功' };
      } catch (error) {
        // 清理测试目录
        await fs.remove(testDir).catch(() => {});
        throw error;
      }
    } catch (error) {
      return { success: false, message: `连接测试失败: ${error.message}` };
    }
  }
}

module.exports = new VcsService();

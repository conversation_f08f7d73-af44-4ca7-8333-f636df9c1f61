﻿<template>
  <el-input
    v-model="curKeyWord"
    :clearable="clearable"
    :placeholder="placeholder"
    :style="{ width: width + 'px' }"
    :class="['mk-search', { round }]"
    @change="search"
  >
    <template #append>
      <el-button
        icon="icon iconfont iconicon-test3"
        @click="search"
      ></el-button>
    </template>
  </el-input>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from "vue";

export default defineComponent({
  name: "mk-search",

  props: {
    // 关键词
    keyWord: {
      type: String,
      default: "",
    },
    // 占位文本
    placeholder: {
      type: String,
      default: "请输入关键词搜索",
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: true,
    },
    // 是否可清空
    readonly: {
      type: Boolean,
      default: false,
    },
    // 宽度
    width: {
      type: Number,
      default: 300,
    },
    // 是否圆角
    round: {
      type: Boolean,
      default: false,
    },
  },

  emits: ["search"],

  setup(props, ctx) {
    const state = reactive({
      // 当前关键词
      curKeyWord: ref(props.keyWord),
    });

    /**
     * @name: 点击查询事件传递
     */
    const search = () => {
      ctx.emit("search", state.curKeyWord);
    };

    return {
      ...toRefs(state),
      search,
    };
  },
});
</script>

<style lang="scss" scoped>
.mk-search {
  &.round {
    ::v-deep(.el-input__inner) {
      border-radius: 25px 0 0 25px;
    }
    ::v-deep(.el-input-group__append) {
      border-radius: 0 25px 25px 0;
    }
  }
}
</style>

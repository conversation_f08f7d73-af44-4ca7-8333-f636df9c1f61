<template>
  <el-row>
    <el-col :span="24">
      <el-cascader
        ref="areaCascaderRef"
        placeholder="请选择区域"
        style="width: 100%"
        v-model="value"
        :options="ascriptionList"
        :props="propDefault"
        :disabled="disabled"
        clearable
        @change="areaChange"
      >
      </el-cascader>
    </el-col>
  </el-row>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch, computed } from "vue";
import { getRegionAPI } from "@/library/src/service/API/base";
import { ElMessage } from "element-plus";

export default defineComponent({
  name: "area-select",
  props: {
    // 区域数据
    ascription: {
      type: Array,
      default: ["", "", ""]
    },
    // 区域数据
    regionAdminArea: {
      type: Array,
      default: ["", "", ""]
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },
  setup(props, context) {
    // 存省市区数据信息
    const areaMap = new Map();
    const state = reactive({
      // 省市区id(级联框参数集合)
      value: computed(() => props.ascription),
      ascriptionList: [],
      // 级联框配置项
      propDefault: {
        lazy: true,
        // 严格的遵守父子节点不互相关联
        checkStrictly: true,
        // 加载动态数据的方法
        lazyLoad: lazyLoad
      }
    });

    function lazyLoad(node: any, resolve: any) {
      // 禁用选项直接结束,避免接口多次请求
      if (node.data && node.data.disabled) {
        resolve([]);
        return;
      }
      const { level } = node;
      node.loading = level <= 2;
      if (node.loading) {
        const id: any = node.data && node.data.value ? node.data.value : "";
        const type: any = level + 1;
        getRegion(type, id).then(res => {
          // 通过调用resolve将子节点数据返回，通知组件数据加载完成
          let data = [];
          for (let i = 0; i < res.length; i++) {
            const item = res[i];
            if (
              !props.regionAdminArea[level] ||
              item.id == props.regionAdminArea[level]
            ) {
              data.push({
                label: item.areaName,
                value: item.id,
                children: [],
                type,
                leaf: level >= 2
              });
            }
          }
          resolve(data);
        });
      }
    }

    /**
     * @name 获取区域消息
     */
    const getRegion = async (type: any, id: any) => {
      let res: any = await getRegionAPI({ type: type, id: id || "" });
      if (res.code == 1) {
        return res.data;
      }
      ElMessage.error("获取区域数据失败,请重试!");
      return null;
    };
    /**
     * @name: 节点选中
     * @params val 返回的节点id 或 id集合
     */
    const areaChange = (val: any) => {
      let result;
      if (val === null) {
        result = {
          districtId: "",
          districtName: "",
          provinceId: "",
          provinceName: "",
          cityId: "",
          cityName: ""
        };
      } else {
        result = {
          provinceId: val[0],
          provinceName: areaMap.get(val[0]),
          cityId: val[1],
          cityName: areaMap.get(val[1]),
          districtId: val[2],
          districtName: areaMap.get(val[2])
        };
      }
      context.emit("get-select-area", result || {});
    };

    watch(
      () => props.ascription,
      (val: any) => {
        state.value = val;
      },
      {
        deep: true
      }
    );

    return {
      ...toRefs(state),
      lazyLoad,
      areaChange
    };
  }
});
</script>

import { Module } from "vuex";
import { IGlobalState } from "..";
import Types from "../action-types";
import { IAppState, ISite, IPlatForm } from "../../typings/app";
import {
  getPlatFormInfoByPlatformDomainAPI,
  getContentByDomainAPI,
} from "../../service/API/base";
import {
  setFavicon,
  setTitle,
  setTheme,
  setChannel,
  setPlatFormType,
  setMainWidth
} from "../../utils/setSite";

const state: IAppState = {
  siteInfo: {
    icon: "",
    title: "",
    logo: "",
    menuDataJson: [],
    functionDataJson: [],
    footDataJson: [],
    theme: "",
    setting: {
      mainWidth: 1400,
      grayscale: 0,
      maxUpload: 5
    }
  },
  platFormInfo: {
    platformType: "",
    platformDomain: "",
    platformName: "",
    region: [],
    regionPlatformType: "",
    platformState: 0,
    id: "",
    schoolId: "",
  },
  source: "",
};

const app: Module<IAppState, IGlobalState> = {
  namespaced: true,
  state,
  mutations: {
    [Types.SET_SITE_INFO](state, sitenInfo: ISite) {
      state.siteInfo = sitenInfo;
      state.siteInfo.theme && setTheme(state.siteInfo.theme);
      state.siteInfo.icon && setFavicon(state.siteInfo.icon);
      state.siteInfo.title && setTitle(state.siteInfo.title);
      state.siteInfo.setting?.mainWidth && setMainWidth(state.siteInfo.setting.mainWidth);
      setChannel();
    },
    [Types.SET_REGION_NAME](
      state,
      name: { text: string; color: string; isShow: false }
    ) {
      state.siteInfo.name = name;
    },
    [Types.SET_REGION_SLOGAN](
      state,
      slogan: { text: string; color: string; isShow: false }
    ) {
      state.siteInfo.slogan = slogan;
    },
    [Types.SET_REGION_LOGO](state, hlogo: { text: string; isShow: false }) {
      state.siteInfo.hlogo = hlogo;
    },
    [Types.SET_PLATFORM_INFO](state, platFormInfo: IPlatForm) {
      state.platFormInfo = platFormInfo;
      setPlatFormType(state.platFormInfo.platformType);
    },
    [Types.SET_SOURCE](state, source: string = "") {
      state.source = source;
    },
  },
  actions: {
    async [Types.GET_SITE_INFO]({ commit, state }): Promise<any> {
      return new Promise(async (resolve, reject) => {
        const domainName =
          document.domain === "localhost"
            ? "zypt.gzjyc.org"
            : window.location.host;
        let params = { domainName, source: state.source };
        const res = await getContentByDomainAPI(params).catch(() => {
          reject();
        });
        if (res.code === 1) {
          try {
            let data = JSON.parse(res.data);
            data = data[0] || data;
            data.footDataList = data.footDataList[0] || data.footDataList;
            let siteInfo: ISite = {
              icon: data.icon || state.siteInfo.icon,
              title: data.title || state.siteInfo.title,
              logo: data.logoUrl || state.siteInfo.logo,
              theme: data.theme || state.siteInfo.theme,
              menuDataJson: data.menuList || state.siteInfo.menuDataJson,
              functionDataJson:
                data.functionList || state.siteInfo.functionDataJson,
              footDataJson: data.footDataList || state.siteInfo.footDataJson,
              setting: data.setting || state.siteInfo.setting
            };
            commit(Types.SET_SITE_INFO, siteInfo);
          } catch (e) { }
        }
        resolve(true);
      });
    },
    async [Types.GET_PLATFORM_INFO]({ commit, state }) {
      // regiontest
      // gxschooltest
      const platformDomain =
        document.domain === "localhost"
          ? "zypt.gzjyc.org"
          : document.domain;
      const params = { platformDomain: platformDomain, source: state.source };
      const res = await getPlatFormInfoByPlatformDomainAPI(params);
      if (res.code === 1) {
        commit(Types.SET_PLATFORM_INFO, res.data);
      }
    },
  },
};

export default app;

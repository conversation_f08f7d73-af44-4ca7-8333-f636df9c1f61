/*
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-09-20 14:58:50
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-11-06 14:15:00
 */
CKEDITOR.dialog.add("TianziLine", function (editor) {
  var getHtml = function (count, type) {
    let container = document.createElement("div");
    // container.className = "tianzi-grid-container";
    let size = 17;
    if (type == 'big') {
      size = 17;
    } else if (type == 'medium') {
      size = 15;
    } else {
      size = 13;
    }
    for (let i = 0; i < count; i++) {
      let gird = document.createElement("div");
      gird.className = 'tianzi-grid split-tag';
      gird.style.width = size + 'mm';
      gird.style.height = size + 'mm';
      gird.contentEditable = false;
      container.appendChild(gird);
    }
    return container.innerHTML + `<p>&ZeroWidthSpace;</p>`;
  };
  return {
    title: "插入田字格", //对话框标题
    minWidth: 300, //对话框宽度
    minHeight: 200, //对话框高度
    contents: [
      {
        //对话框内容
        id: "TianziLine",
        name: "TianziLine",
        elements: [
          {
            label: "格子大小",
            id: "type",
            type: "radio",
            items: [
              ["小格", "small"],
              ["中格", "medium"],
              ["大格", "big"],
            ],
            default: "medium",
          },
          {
            type: "text",
            id: "count",
            label: "格字数",
            default: 5,
            onShow: function (params) {
              // 添加输入验证
              var input = this.getInputElement();
              input.on("input", function () {
                var value = input.$.value.replace(/[^0-9]/g, "");
                if (value.length > 4) {
                  value = value.slice(0, 4);
                }
                if (value !== input.$.value) {
                  input.$.value = value;
                }
              });
            },
          },
        ],
      },
    ],
    onOk: function () {
      var count = this.getValueOf("TianziLine", "count");
      var type = this.getValueOf("TianziLine", "type");
      var html = getHtml(Number(count), type);

      try {
        let $ele = editor.getSelectedRanges()[0].endContainer.$
        let cName = $ele.className;
        if (!cName || !cName.includes("subject-para-p")) {
          const paras = Array.from(editor.element.$.getElementsByClassName("subject-para-p"));
          if (paras[0].contains($ele)) {
            // 跳过首个分数段落
            paras[0].insertAdjacentHTML("afterend", html);
          } else {
            editor.insertHtml(html);
          }
        } else {
          editor.insertHtml(html);
        }
      } catch (e) {
        editor.insertHtml(html + "<p><br/></p>");
      }

      editor.fire('insertWritingCells')
    },
  };
});

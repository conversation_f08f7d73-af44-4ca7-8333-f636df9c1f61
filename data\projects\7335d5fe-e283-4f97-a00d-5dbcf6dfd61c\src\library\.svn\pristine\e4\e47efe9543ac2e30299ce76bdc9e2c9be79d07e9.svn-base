﻿<template>
  <mk-dialog
    :isShowDialog="true"
    :isShowFooter="false"
    :title="title"
    :dialogWidth="disabled ? '20%' : '45%'"
    :customClass="'choose-user-model'"
    @click-cancel="onCancel"
  >
    <template #customSlot>
      <el-row class="choose-user-dialog" gutter="20">
        <el-col :span="12" v-if="!disabled">
          <!--头部tab切换-->
          <el-row
            type="flex"
            align="middle"
            justify="center"
            class="user-header"
          >
            <el-col
              v-for="(item, index) in tabList"
              :key="index"
              :class="{ active: curTab === item.type }"
              :span="6"
              class="tab-item"
              @click.native="switchTab(item.type)"
            >
              <img :src="curTab === item.type ? item.activeIcon : item.icon" />
              <p>{{ item.name }}</p>
            </el-col>
          </el-row>
          <!--选学校老师-->
          <school-user
            v-if="curTab === TAB_TYPE.SCHOOL && isShowSchool"
            ref="userTreeRef"
            :userList="choosedUserList"
            :disabledKeys="disabledKeys"
            :isOriginSchool="isOriginSchool"
            :curSchoolObj="curSchoolObj"
            :isCustomContent="isCustomContent"
            @switch-teacher="switchSchoolUser"
            @on-clear="clear"
          >
            <template #customSchool>
              <slot name="customSchool"></slot>
            </template>
          </school-user>
          <!--选局端老师-->
          <department-user
            ref="userTreeRef"
            :userList="choosedUserList"
            :choosedDepartmentList="departmentList"
            :disabledKeys="disabledKeys"
            :hasCheckedDepartmentPower="hasCheckedDepartmentPower"
            v-else-if="curTab === TAB_TYPE.DEPARTMENT"
            @switch-teacher="switchDepartmentUser"
            @switch-department="switchDepartment"
          ></department-user>
          <!--选群组老师-->
          <group-user
            v-else-if="curTab == TAB_TYPE.GROUP"
            ref="userTreeRef"
            :group="groupList"
            :disabledKeys="disabledKeys"
            @on-edit-group="onEdit"
            @switch-group="switchGroup"
            @updata-group="getGroupList"
          ></group-user>
          <!--直接搜索-->
          <template v-else>
            <search-user
              ref="searchUserTreeRef"
              @switch-teacher="switchSchoolUser"
              :userList="choosedUserList"
              :disabledKeys="disabledKeys"
            ></search-user>
          </template>
        </el-col>
        <el-col :span="disabled ? 24 : 12">
          <p v-if="isEdit" class="flex-center" style="padding: 0 20px">
            <span style="min-width: 75px">群组名称：</span>
            <el-input
              placeholder="请输入群组名称"
              v-model="defaultGroup.groupName"
              :maxlength="30"
              show-word-limit
              clearable
            ></el-input>
          </p>
          <el-header :class="['flex-between user-header', { isEdit }]">
            <template v-if="disabled">
              <span style="display: inline-block; width: 100%"
                >已选：
                <span>
                  <b>{{ choosedUserList.length }}</b
                  >人</span
                >
                <span v-if="isShowDepart && hasCheckedDepartmentPower">
                  、<b>{{ departmentList.length }}</b
                  >部门</span
                >
                <span v-if="isShowGroup">
                  、<b>{{ groupList.length }}</b
                  >群组</span
                >
              </span>
            </template>
            <template v-else>
              <span v-if="isEdit"
                >群组成员：
                <span>
                  <b>{{ choosedUserList.length }}</b> 人</span
                >
              </span>
              <span v-else style="display: inline-block; width: 80%"
                >已选：
                <span>
                  <b>{{ choosedUserList.length }}</b
                  >人</span
                >
                <span v-if="isShowDepart && hasCheckedDepartmentPower">
                  、<b>{{ departmentList.length }}</b
                  >部门</span
                >
                <span v-if="isShowGroup">
                  、<b>{{ groupList.length }}</b
                  >群组</span
                >
              </span>

              <span
                v-if="allUserCount > 0 && disabledKeys.length === 0"
                @click="clear"
                class="clear-icon"
              >
                <i class="icon iconfont iconshanchu3"></i> 清空
              </span>
            </template>
          </el-header>
          <ul
            class="user-content"
            v-if="
              choosedUserList.length ||
              departmentList.length ||
              groupList.length
            "
          >
            <!--用户-->
            <template v-if="choosedUserList.length">
              <li
                v-for="item in choosedUserList"
                :key="item.userId"
                class="flex-between user-item"
              >
                <span>
                  <el-avatar
                    :size="30"
                    :src="item.avatar"
                    style="vertical-align: middle"
                  >
                    <img
                      :src="addFsUrl('aliba/avatar/default/default.png')"
                    />
                  </el-avatar>
                  {{ item.realName || item.userName }}
                  <template v-if="item.schoolName">
                    | {{ item.schoolName }}
                  </template>
                </span>
                <span
                  v-if="!disabled && !disabledKeys.includes(item.userId)"
                  @click="deleteTeachger(item)"
                  class="delete-icon"
                >
                </span>
              </li>
            </template>

            <!--部门-->
            <template v-if="departmentList.length" class="user-content">
              <li
                v-for="(item, index) in departmentList"
                :key="index"
                class="flex-between user-item"
              >
                <span>
                  <el-avatar
                    :src="item.avatar"
                    style="
                      vertical-align: text-bottom;
                      background-color: transparent;
                    "
                    :size="20"
                  >
                    <img :src="require('../assets/department.png')" />
                  </el-avatar>
                  {{ item.departmentName }}
                  ({{ item.departmentNumberOfPeople }}人)
                </span>
                <span
                  v-if="!disabled && !disabledKeys.includes(item.id)"
                  @click="deleteDepartment(item)"
                  class="delete-icon"
                ></span>
              </li>
            </template>
            <!--群组-->
            <template v-if="groupList.length">
              <li
                v-for="item in groupList"
                :key="item.groupId"
                class="flex-between user-item"
              >
                <span>
                  <el-avatar
                    :src="require('../assets/group.png')"
                    shape="square"
                    style="vertical-align: middle; background: transparent"
                  ></el-avatar>
                  {{ item.groupName }}
                  ({{ item.peopleNum }}人)
                </span>
                <span
                  v-if="!disabled && !disabledKeys.includes(item.groupId)"
                  @click="deleteGroup(item)"
                  class="delete-icon"
                ></span>
              </li>
            </template>
          </ul>
          <mk-no-data
            class="user-no-data"
            v-else
            style="padding-top: 80px"
          ></mk-no-data>

          <!--底部按钮-->
          <div class="choose-user-footer" v-if="!disabled">
            <el-row type="flex" align="middle" justify="space-between">
              <el-checkbox
                v-if="!isEdit && isShowGroup"
                v-model="isCopyToMyGroup"
                :label="true"
                :disabled="choosedUserList.length < 2 || !hasCopyGroupPower"
                @change="getGroupList"
                >同步至我的群组</el-checkbox
              >
            </el-row>

            <el-row type="flex" align="middle" justify="end">
              <el-button @click="onCancel">取消</el-button>
              <el-button @click="onConfirm" type="primary">确认</el-button>
            </el-row>
          </div>
        </el-col>

        <div class="line" v-if="!disabled"></div>
      </el-row>
    </template>
  </mk-dialog>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  watch,
  computed,
  onMounted,
  nextTick
} from 'vue';
import DepartmentUser from '@/library/ui/mk-share-res/ChooseUserDialog/DepartmentUser.vue';
import SchoolUser from '@/library/ui/mk-share-res/ChooseUserDialog/SchoolUser.vue';
import GroupUser from '@/library/ui/mk-share-res/ChooseUserDialog/GroupUser.vue';
import SearchUser from '@/library/ui/mk-share-res/ChooseUserDialog/SearchUser.vue';
import { ElMessage, ElMessageBox, ElAvatar } from 'element-plus';
import { getUserRegionGroupListAPI } from '@/library/src/service/API/tearesearch';
import { IGlobalState } from '@/library/src/store';
import { useStore } from 'vuex';
import { unique,addFsUrl } from '@/library/src/utils/globalFunction';
import { PLATFORM_TYPE } from '@/library/src/utils/gloableEnum';

export default defineComponent({
  name: 'choose-user-model',

  components: { SchoolUser, DepartmentUser, GroupUser, SearchUser, ElAvatar },

  emits: ['on-confirm', 'on-cancle', 'on-edit-group', 'on-create-group'],

  props: {
    // 标题
    title: {
      type: String,
      default: '选择用户'
    },
    // 是否显示选择学校老师图标
    isShowSchoolIcon: {
      type: Boolean,
      default: true
    },
    // 是否显示学校老师
    isShowSchool: {
      type: Boolean,
      default: true
    },
    // 是否显示群组
    isShowGroup: {
      type: Boolean,
      default: true
    },
    // 是否显示部门
    isShowDepart: {
      type: Boolean,
      default: true
    },
    // 是否显示直接搜索
    isShowSearch: {
      type: Boolean,
      default: true
    },
    // 是否编辑模式
    isEdit: {
      type: Boolean,
      default: false
    },
    // 人数限制
    limit: {
      type: Number,
      default: 300
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 已选用户列表
    userList: {
      type: Array,
      default: () => []
    },
    // 已选部门列表
    department: {
      type: Array,
      default: () => []
    },
    // 已选群组列表
    group: {
      type: Array,
      default: () => []
    },
    // 群组信息
    groupInfo: {
      type: Object,
      default: () => {
        return {
          groupId: '',
          groupName: ''
        };
      }
    },
    // 是否本校
    isOriginSchool: {
      type: Boolean,
      default: false
    },
    // 是否有选择部门的权限
    hasCheckedDepartmentPower: {
      type: Boolean,
      default: true
    },
    // 禁用列表
    disabledKeys: {
      type: Array,
      default: () => []
    },
    // 是否自定义学校列表
    isCustomContent: {
      type: Boolean,
      default: false
    },
    // 当前学校
    curSchoolObj: {
      type: Object,
      default: {
        phase: '',
        schoolId: '',
        schoolName: '全部学校'
      }
    }
  },

  setup(props, ctx) {
    /** tab类型 */
    enum TAB_TYPE {
      /** 学校 */
      SCHOOL,
      /** 局端 */
      DEPARTMENT,
      /** 群组 */
      GROUP,
      /** 直接搜索 */
      SEARCH
    }
    const store = useStore<IGlobalState>();
    const userTreeRef = ref<any>(null);
    const searchUserTreeRef = ref<any>(null);
    const state = reactive({
      // tab切换类型
      tabList: computed(() => {
        let tabList = [];
        if (props.isShowSchool && props.isShowSchoolIcon) {
          tabList.push({
            icon: require('../assets/school_icon_nor.png'),
            activeIcon: require('../assets/school_icon_hover.png'),
            name: '选学校老师',
            type: TAB_TYPE.SCHOOL
          });
        }
        if (
          props.isShowDepart &&
          store.state.app.platFormInfo.platformType !== PLATFORM_TYPE.SCHOOL
        ) {
          tabList.push({
            icon: require('../assets/department_icon_nor.png'),
            activeIcon: require('../assets/department_icon_hover.png'),
            name: '选局端老师',
            type: TAB_TYPE.DEPARTMENT
          });
        }
        if (props.isShowGroup) {
          tabList.push({
            icon: require('../assets/group_icon_nor.png'),
            activeIcon: require('../assets/group_icon_hover.png'),
            name: '选群组老师',
            type: TAB_TYPE.GROUP
          });
        }
        if (props.isShowSearch) {
          tabList.push({
            icon: require('../assets/search_nor.png'),
            activeIcon: require('../assets/search_hover.png'),
            name: '直接搜索',
            type: TAB_TYPE.SEARCH
          });
        }
        return tabList;
      }),
      // 当前tab
      curTab: props.isShowSchool ? TAB_TYPE.SCHOOL : TAB_TYPE.DEPARTMENT,

      // 当前群组
      defaultGroup: props.groupInfo,
      // 是否同步至我的群组
      isCopyToMyGroup: false,
      // 是否有同步群组的权限
      hasCopyGroupPower: true,

      // 已选择分组列表
      groupList: props.group as any[],
      // 已选择部门列表
      departmentList: props.department as any[],
      // 已选择学校教师列表
      schoolUserList: props.userList as any[],
      // 已选择局端教师列表
      departmentUserList: props.userList as any[]
    });
    // 已选人数列表
    const choosedUserList = computed(() => {
      const list = [...state.schoolUserList, ...state.departmentUserList];
      return unique(list, 'userId');
    });
    // 群组人数
    const groupUserCount = computed(() => {
      let count = 0;
      state.groupList.forEach((item: any) => {
        count += item.peopleNum;
      });
      return count;
    });
    // 部门人数
    const departmentUserCount = computed(() => {
      let count = 0;
      state.departmentList.forEach((item: any) => {
        count += item.departmentNumberOfPeople;
      });
      return count;
    });
    // 总人数
    const allUserCount = computed(() => {
      return (
        choosedUserList.value.length +
        groupUserCount.value +
        departmentUserCount.value
      );
    });

    /**
     * @name: 切换tab
     * @param type 当前tab类型
     */
    const switchTab = (type: TAB_TYPE) => {
      state.curTab = type;
    };
    /**
     * @name: 切换已选学校教师
     * @param teacherList 已选教师列表
     */
    const switchSchoolUser = (teacherList: any[], deleteList: any[] = []) => {
      state.schoolUserList = teacherList;
      //departmentUserList去除需要删除的用户
      state.departmentUserList = state.departmentUserList && state.departmentUserList.filter(
        (item: any) => {
          return !deleteList.some((sitem: any) => {
            return sitem.userId === item.userId;
          });
        }
      );
    };
    /**
     * @name: 切换已选部门教师
     * @param teacherList 已选教师列表
     */
    const switchDepartmentUser = (
      teacherList: any[],
      deleteList: any[] = []
    ) => {
      state.departmentUserList = teacherList;
      //schoolUserList去除需要删除的用户
      state.schoolUserList = state.schoolUserList.filter((item: any) => {
        return !deleteList.some((sitem: any) => {
          return sitem.userId === item.userId;
        });
      });
    };
    /**
     * @name: 切换已选部门
     * @param departmentList 已选部门列表
     */
    const switchDepartment = (departmentList: any[]) => {
      state.departmentList = unique(departmentList, 'id');
      const departmentIds = [...state.departmentList].map((item: any) => {
        return item.id;
      });
      const departmentUserList = [...state.departmentUserList].filter(
        (item: any) => {
          return !departmentIds.includes(item.departmentId);
        }
      );
      switchDepartmentUser(departmentUserList);
    };
    /**
     * @name: 切换群组
     * @param groupList 已选群组列表
     */
    const switchGroup = (groupList: any[]) => {
      state.groupList = unique(groupList, 'groupId');
    };

    /**
     * @name: 确认完成
     */
    const onConfirm = () => {
      if (allUserCount.value > props.limit) {
        ElMessage.warning(
          `当前人数${allUserCount.value}人，已选人数不支持超过${props.limit}人!`
        );
        return;
      }

      const userList = unique(
        [...state.schoolUserList, ...state.departmentUserList],
        'userId'
      );
      if (props.isEdit) {
        if (userList.length === 0) {
          ElMessage.warning('请选择教师!');
          return;
        } else if (userList.length === 1) {
          ElMessage.warning('群组老师最少需要两人!');
          return;
        } else if (userList.length > props.limit) {
          ElMessage.warning(`群组老师最多支持${props.limit}人!`);
          return;
        }
        if (state.defaultGroup.groupName === '') {
          ElMessage.warning('请输入群组名称!');
          return;
        }
      }
      let params = {
        // 全部已选老师
        userList,
        // 全部分组
        groupList: state.groupList,
        // 全部部门
        departmentList: state.departmentList,
        // 当前分组
        groupIndfo: state.defaultGroup,
        //全部人数
        allUserCount: allUserCount.value
      };
      if (state.isCopyToMyGroup) {
        if (!state.hasCopyGroupPower) {
          ElMessage.warning('最多支持20个群组,不可同步群组!');
        }
        ElMessageBox.prompt('请输入同步群组名称', '同步群组', {
          cancelButtonText: '取消',
          confirmButtonText: '确定'
        }).then(({ value }) => {
          params.groupIndfo.groupName = value;

          ctx.emit('on-create-group', params);
          ctx.emit('on-confirm', params);
          onCancel();
        });
      } else {
        ctx.emit('on-confirm', params);
        onCancel();
      }
    };
    /**
     * @name: 取消
     */
    const onCancel = () => {
      ctx.emit('on-cancle');
    };
    /**
     * @name: 编辑/新建分组
     * @param item 当前分组对象,为null时为新增
     */
    const onEdit = (item: any, groupUserList: any[]) => {
      ctx.emit('on-edit-group', item, groupUserList);
    };
    /**
     * @name: 清空已选
     */
    const clear = () => {
      state.groupList = [];
      state.departmentList = [];

      state.schoolUserList = [];
      state.departmentUserList = [];
      nextTick(() => {
        userTreeRef.value &&
          userTreeRef.value.clear &&
          userTreeRef.value.clear();
        searchUserTreeRef.value &&
          searchUserTreeRef.value.clear &&
          searchUserTreeRef.value.clear();
      });
    };
    /**
     * @name: 删除已选教师
     * @param item 已选教师对象
     */
    const deleteTeachger = (item: any) => {
      state.schoolUserList = state.schoolUserList.filter((sitem: any) => {
        return sitem.userId !== item.userId;
      });
      state.departmentUserList = state.departmentUserList.filter(
        (sitem: any) => {
          return sitem.userId !== item.userId;
        }
      );
      nextTick(() => {
        userTreeRef.value &&
          userTreeRef.value.deleteUser &&
          userTreeRef.value.deleteUser(item);
        searchUserTreeRef.value &&
          searchUserTreeRef.value.deleteUser &&
          searchUserTreeRef.value.deleteUser(item);
      });
    };
    /**
     * @name: 删除已选部门
     * @param item 已选部门对象
     */
    const deleteDepartment = (item: any) => {
      state.departmentList = state.departmentList.filter((sitem: any) => {
        return sitem.id !== item.id;
      });
      nextTick(() => {
        userTreeRef.value &&
          userTreeRef.value.deleteDepartment &&
          userTreeRef.value.deleteDepartment(item);
      });
    };
    /**
     * @name: 删除已选群组
     * @param item 已选群组对象
     */
    const deleteGroup = (item: any) => {
      state.groupList = state.groupList.filter((sitem: any) => {
        return sitem.groupId !== item.groupId;
      });
      const ids = [...state.groupList].map((item: any) => {
        return item.groupId;
      });
      userTreeRef.value &&
        userTreeRef.value.resetGroup &&
        userTreeRef.value.resetGroup(ids);
    };
    /**
     * @name: 更新群组列表
     */
    const updateGroup = () => {
      userTreeRef.value &&
        userTreeRef.value.getGroupList &&
        userTreeRef.value.getGroupList();
    };
    /**
     * @name: 获取群组列表
     */
    const getGroupList = async () => {
      const res = await getUserRegionGroupListAPI({
        userId: store.state.user.userId,
        keyWord: ''
      });
      const groupList = res.code === 1 ? res.data : [];
      state.hasCopyGroupPower = groupList.length < 20;
    };

    /**
     * @name: 页面初始化
     */
    const init = () => {
      switchTab(state.curTab);
      //初始化获取全部群组，判断是否超过20个
      getGroupList();
    };

    onMounted(() => {
      init();
    });

    watch(
      () => props.userList,
      () => {
        state.schoolUserList = props.userList;
        state.departmentUserList = props.userList;
      }
    );

    return {
      ...toRefs(state),
      userTreeRef,
      searchUserTreeRef,
      choosedUserList,
      groupUserCount,
      departmentUserCount,
      allUserCount,
      addFsUrl,
      TAB_TYPE,
      switchTab,
      onConfirm,
      onCancel,
      onEdit,
      switchSchoolUser,
      switchDepartmentUser,
      switchDepartment,
      clear,
      deleteTeachger,
      deleteDepartment,
      deleteGroup,
      updateGroup,
      switchGroup,
      getGroupList
    };
  }
});
</script>

<style lang="scss" scoped>
.choose-user-dialog {
  .tab-item {
    text-align: center;
    cursor: pointer;
    img {
      padding: 5px;
      border: 1px solid #fff;
      background: #f8f8f8;
    }
    &.active,
    &:hover {
      color: #009bff;
    }
    &.active {
      color: #009bff;
      img {
        border: 1px solid #1988f4;
        border-radius: 6px;
      }
    }
  }

  .user-header {
    margin-top: 5px;
    &.isEdit {
      height: unset !important;
      margin: 4px 0 !important;
    }
    .clear-icon {
      display: inline-flex;
      align-items: center;
      cursor: pointer;
      &:hover {
        color: red;
      }
    }
  }
  .user-content {
    height: 435px;
    overflow-y: auto;
  }
  .user-item {
    padding: 10px;
    span {
      display: inline-block;
      max-width: 95%;
    }
    &:hover {
      background-color: #f5f7fa;
      .delete-icon {
        display: block;
      }
    }
    .delete-icon {
      display: none;
      cursor: pointer;
      width: 20px;
      height: 20px;
      background: url('../assets/close.png') no-repeat center/100%;
    }
  }
  .line {
    width: 1px;
    height: 500%;
    background: #e2e9ed;
    position: absolute;
    left: 50%;
  }
  .choose-user-footer {
    position: absolute;
    bottom: -50px;
    right: 0;
    left: 20px;
    background-color: #fff;
    z-index: 1;
  }
}
</style>
<style lang="scss">
.choose-user-model {
  overflow: hidden;
  .el-dialog__body {
    padding: 5px 20px 40px 20px !important;
  }
}
.user-no-data {
  .no_pic {
    height: 200px !important;
  }
}
</style>

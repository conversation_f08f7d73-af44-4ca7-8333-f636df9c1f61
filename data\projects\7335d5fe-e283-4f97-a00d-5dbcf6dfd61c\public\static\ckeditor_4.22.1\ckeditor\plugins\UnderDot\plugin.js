/**
 * 自定义着重号
 */
function selectionStyle(className) {
  const selection = window.getSelection();
          if (!selection) return;
          const range = selection.getRangeAt(0);
          const optionClass = className;
          const parentNode = range.startContainer.parentElement;
          if(parentNode.classList.contains(optionClass)){
            parentNode.outerHTML = parentNode.innerText
            return;
          }
          const spanNode = document.createElement("span");
          const rangeString = range.toString();
          
          spanNode.className = optionClass;
          range.surroundContents(spanNode);
          spanNode.innerText = rangeString;
}
CKEDITOR.plugins.add("UnderDot", {
    init: function(editor) {
      editor.ui.addButton("UnderDot", {
        label: "着重号",
        icon: this.path + "icon.png",
        command: "UnderDot",
        click: function(e) {
          selectionStyle("tb-dot")
        },
      });
      editor.ui.addButton("SolidWaveline", {
        label : '波浪线',
        icon : this.path + "wave.png",
        command: "SolidWaveline",
        click : function(e) {
          selectionStyle("tb-wave")
        }
      });
    },
  });
  
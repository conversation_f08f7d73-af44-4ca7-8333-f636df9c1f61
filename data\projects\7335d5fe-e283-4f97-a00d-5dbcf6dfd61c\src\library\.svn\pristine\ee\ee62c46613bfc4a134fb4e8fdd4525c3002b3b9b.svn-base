﻿<template>
  <choose-user-model
    v-if="isShowChooseDialog"
    ref="userModelRef"
    :title="title"
    :limit="limit"
    :isOriginSchool="isOriginSchool"
    :isShowSchool="isShowSchool"
    :isShowSchoolIcon="isShowSchoolIcon"
    :isShowDepart="
      departType == 1 ? !isOriginSchool : departType == 2 ? true : false
    "
    :isShowGroup="isShowGroup"
    :isShowSearch="isShowSearch"
    :hasCheckedDepartmentPower="hasCheckedDepartmentPower"
    :disabled="disabled"
    :userList="userList"
    :department="department"
    :group="group"
    :disabledKeys="disabledKeys"
    :isCustomContent="isCustomContent"
    :curSchoolObj="curSchoolObj"
    @on-edit-group="onEditGroup"
    @on-create-group="saveGroup"
    @on-confirm="onConfirm"
    @on-cancle="onCancel"
  >
    <template #customSchool>
      <slot name="customSchool"></slot>
    </template>
  </choose-user-model>

  <choose-user-model
    v-if="editDialogOptions.isShow"
    :isShowSchoolIcon="isShowSchoolIcon"
    :isShowGroup="false"
    :isShowDepart="
      departType == 1 ? !isOriginSchool : departType == 2 ? true : false
    "
    :hasCheckedDepartmentPower="false"
    :isEdit="true"
    :isOriginSchool="isOriginSchool"
    :disabled="disabled"
    :limit="limit"
    :title="editDialogOptions.groupId ? '编辑群组' : '创建群组'"
    :userList="editDialogOptions.userList"
    :groupInfo="{
      groupId: editDialogOptions.groupId,
      groupName: editDialogOptions.groupName
    }"
    @on-confirm="saveGroup"
    @on-cancle="editDialogOptions.isShow = false"
  ></choose-user-model>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from "vue";
import ChooseUserModel from "@/library/ui/mk-share-res/ChooseUserDialog/ChooseUserModel.vue";
import { saveRegionGroupAPI } from "@/library/src/service/API/tearesearch";
import { IGlobalState } from "@/library/src/store";
import { useStore } from "vuex";

export default defineComponent({
  name: "choose-user-dialog",

  emits: ["on-confirm", "on-cancle"],

  props: {
    // 标题
    title: {
      type: String,
      default: "选择用户"
    },
    // 人数限制, -1时为不限制
    limit: {
      type: Number,
      default: 300
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 已选用户列表
    userList: {
      type: Array,
      default: () => []
    },
    // 已选部门列表
    department: {
      type: Array,
      default: () => []
    },
    // 已选群组列表
    group: {
      type: Array,
      default: () => []
    },
    // 是否显示学校老师
    isShowSchool: {
      type: Boolean,
      default: true
    },
    // 是否显示选择学校老师图标
    isShowSchoolIcon: {
      type: Boolean,
      default: true
    },
    // 是否本校
    isOriginSchool: {
      type: Boolean,
      default: false
    },
    // 1:根据isOriginSchool判断展示  2:展示  3:不展示
    departType: {
      type: Number,
      default: 1
    },
    // 是否显示群组
    isShowGroup: {
      type: Boolean,
      default: true
    },
    // 是否显示直接搜索
    isShowSearch: {
      type: Boolean,
      default: true
    },
    // 是否有选择部门的权限
    hasCheckedDepartmentPower: {
      type: Boolean,
      default: true
    },
    // 禁用列表
    disabledKeys: {
      type: Array,
      default: () => []
    },
    // 是否自定义学校列表
    isCustomContent: {
      type: Boolean,
      default: false
    },
    // 当前学校
    curSchoolObj: {
      type: Object,
      default: {
        phase: "",
        schoolId: "",
        schoolName: "全部学校"
      }
    }
  },

  components: { ChooseUserModel },

  setup(props, ctx) {
    const userModelRef = ref<any>(null);
    const store = useStore<IGlobalState>();
    const state = reactive({
      isShowChooseDialog: true,
      editDialogOptions: {
        groupId: "",
        groupName: "",
        isShow: false,
        userList: [] as any[]
      }
    });
    /**
     * @name: 确认
     */
    const onConfirm = (params: any) => {
      if (props.limit !== -1) {
        params.userList = params.userList.splice(0, props.limit);
      }
      ctx.emit("on-confirm", params);
      onCancel();
    };
    /**
     * @name: 取消
     */
    const onCancel = () => {
      state.isShowChooseDialog = false;
      state.editDialogOptions = {
        groupId: "",
        groupName: "",
        isShow: false,
        userList: []
      };
      ctx.emit("on-cancle");
    };
    /**
     * @name: 编辑,新增群组
     * @param item 当前群组对象,为null时为新增
     */
    const onEditGroup = (item: any, userList: any[]) => {
      if (item === null) {
        state.editDialogOptions = {
          groupId: "",
          groupName: "",
          isShow: true,
          userList: []
        };
      } else {
        state.editDialogOptions = {
          groupId: item.groupId,
          groupName: item.groupName,
          isShow: true,
          userList
        };
      }
    };
    /**
     * @name: 保存群组
     */
    const saveGroup = async (params: any) => {
      const userList = params.userList.map((item: any) => {
        return {
          userId: item.userId,
          userName: item.realName
        };
      });
      state.editDialogOptions.groupName = params.groupIndfo.groupName;
      await saveRegionGroupAPI({
        groupId: state.editDialogOptions.groupId,
        groupName: state.editDialogOptions.groupName,
        createUserId: store.state.user.userId,
        regionId: store.state.app.platFormInfo.id,
        userList: JSON.stringify(userList)
      });
      userModelRef.value &&
        userModelRef.value.updateGroup &&
        userModelRef.value.updateGroup();
    };

    return {
      ...toRefs(state),
      userModelRef,
      onEditGroup,
      onConfirm,
      onCancel,
      saveGroup
    };
  }
});
</script>

<template>
  <el-col :span="24">
    <el-cascader
      style="width:100%;"
      ref="areaCascaderRef"
      v-model="value"
      :disabled="disabled"
      placeholder="请选择区域"
      :option="ascriptionList"
      :props="defaultProps"
      clearable
      @change="areaChange"
    ></el-cascader>
  </el-col>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch, ref } from "vue";
import { getRegionAPI } from "@/library/src/service/API/base";
import { ElMessage } from "element-plus";

/** 归属类型 */
export enum ASCRIPTION_TYPE {
  /** 省 */
  PROVINCE = 1,
  /** 市 */
  CITY = 2,
  /** 区 */
  DISTRICT = 3
}

export default defineComponent({
  name: "mk-area-select",

  emits: ["get-select-area"],

  props: {
    //区域数据
    ascription: {
      type: Array,
      default: () => {
        return [];
      }
    },
    //区域数据
    regionAdminArea: {
      type: Array,
      default: () => {
        return [];
      }
    },
    //是否禁用
    disabled: {
      type: Boolean,
      default: false
    }
  },

  setup(props, ctx) {
    /**
     * @name 懒加载数据
     */
    const lazyLoad = (node: any, resolve: any) => {
      // 禁用选项直接结束,避免接口多次请求
      if (node.data && node.data.disabled) {
        resolve([]);
        return;
      }
      const { level } = node;
      node.loading = level <= 2;
      if (node.loading) {
        const id = node.data && node.data.value ? node.data.value : "";
        const type = level + 1;
        getRegion(type, id).then((res: any) => {
          if (res) {
            // 通过调用resolve将子节点数据返回，通知组件数据加载完成
            let data = [];
            for (let i = 0; i < res.length; i++) {
              const item = res[i];
              if (
                !props.regionAdminArea[level] ||
                item.id == props.regionAdminArea[level]
              ) {
                data.push({
                  label: item.areaName,
                  value: item.id,
                  children: [],
                  type,
                  leaf: level >= 2
                });
              }
            }
            resolve(data);
          }
        });
      }
    };

    const areaCascaderRef = ref<any>(null);
    const state = reactive({
      // 当前值
      value: props.ascription,
      // 已选值
      ascriptionList: [],
      // 配置项
      defaultProps: {
        lazy: true,
        checkStrictly: true,
        lazyLoad
      }
    });

    /**
     * @name 获取省市区数据
     */
    const getRegion = async (type: any, id: string) => {
      const res = await getRegionAPI({ type: type, id: id });
      if (res.code == 1) {
        return res.data;
      }
      ElMessage.warning("获取区域数据失败,请重试!");
      return null;
    };
    /**
     * @name: 节点选中
     * @params val 获取的节点id
     */
    const areaChange = (val: string) => {
      const nameList = areaCascaderRef.value.getCheckedNodes()[0].pathLabels;
      const provinceId = val[0] || "";
      const provinceName = nameList[0] || "";
      const cityId = val[1] || "";
      const cityName = nameList[1] || "";
      const districtId = val[2] || "";
      const districtName = nameList[2] || "";
      const areaId = districtId
        ? `${provinceId}-${cityId}-${districtId}`
        : cityId
        ? `${provinceId}-${cityId}`
        : provinceId;
      const areaName = districtName
        ? `${provinceName}>${cityName}>${districtName}`
        : cityName
        ? `${provinceName}>${cityName}`
        : provinceName;

      const area = {
        provinceId,
        provinceName,
        cityId,
        cityName,
        districtId,
        districtName,
        areaId,
        areaName
      };
      ctx.emit("get-select-area", area || {});
    };

    /**
     * @name 监听默认值的变化
     */
    watch(
      () => props.ascription,
      val => {
        let list = [];
        if (val[0]) {
          list.push(val[0]);
          if (val[1]) {
            list.push(val[1]);
            if (val[2]) {
              list.push(val[2]);
            }
          }
        }
        state.value = list;
      }
    );

    return {
      ...toRefs(state),
      areaCascaderRef,
      areaChange
    };
  }
});
</script>

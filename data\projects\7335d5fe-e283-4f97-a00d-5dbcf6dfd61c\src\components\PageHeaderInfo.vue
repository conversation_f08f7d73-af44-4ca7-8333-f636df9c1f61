<template>
  <div style="
      font-size: 4.8mm;
      line-height: 4mm;
      font-family: 宋体, SimSun, Times New Roman;
    ">
    <PageHeaderTitle :page="page" />

    <div class="header-table">
      <div class="headerinfo left" :style="{
        width: '110mm',
      }">
        <div v-if="!Paper.isSealLine" class="stu-info-wrap m-b-4" name="sealLine" style="line-height: 7mm">
          <div :style="{
            marginTop: Paper.correctType == ICorrectType.WEB ? '0mm' : '5mm',
            marginBottom: Paper.correctType == ICorrectType.WEB ? 0 : '8mm',
          }">
            <div style="display: inline-block">
              <span style="width: 10mm; padding-left: 2mm">班&nbsp;级：</span>
              <div class="card-color-info"></div>
            </div>
            <div style="display: inline-block" id="stu-name">
              <span style="width: 10mm; padding-left: 2mm">姓&nbsp;名：</span>
              <div class="card-color-info"></div>
            </div>
          </div>
          <div style="margin-top: 2mm" v-if="Paper.correctType == ICorrectType.WEB">
            <div style="display: inline-block">
              <span style="width: 10mm; padding-left: 2mm">考场号：</span>
              <div class="card-color-info"></div>
            </div>
            <div style="display: inline-block">
              <span style="width: 10mm; padding-left: 2mm">座位号：</span>
              <div class="card-color-info"></div>
            </div>
          </div>
        </div>
        <div class="header-info" :style="{ 'margin-top': Paper.isSealLine ? '10mm' : '-1mm' }">
          <div :id="'qrcode' + page" class="qrcode" :page="page" style="width: 20mm; height: 20mm; display: inline-block">
            <img class="nosave" v-if="QRCodeUrl" :src="QRCodeUrl" style="width: 20mm; height: 20mm" />
          </div>
          <div style="margin-left: 2mm" id="notice">
            <div class="notice-info" contenteditable="true" style="font-size: 3mm !important">
              <p class="notice-title" style="font-size: 3.7mm !important; margin-bottom: null">
                注意事项
              </p>
              <p class="notice-infop">
                1．答题前请将姓名、班级、考场、学号等填写清楚
              </p>
              <p class="notice-infop">
                2．客观题答题，必须使用2B铅笔填涂，修改时用橡皮擦干净
              </p>
              <p class="notice-infop">3．主观题答题，必须使用黑色签字笔书写</p>
              <p class="notice-infop">
                4．须在题号对应的答题区域内作答，超出答题区域书写无效
              </p>
            </div>
            <NoticeVue v-if="Paper.numberLayout == INUMBERLAYOUT.TICKET"></NoticeVue>
          </div>
        </div>
      </div>
      <div class="headerinfo right">
        <StuNumberFillCard number-block="bracket" v-if="Paper.numberLayout == INUMBERLAYOUT.TICKET" />

        <div class="barcode-wrapper" v-if="Paper.numberLayout == INUMBERLAYOUT.BARCODE">
          <div class="stu-bar-code-container" id="stu-no-three-tr">
            <div class="stu-bar-code">
              <div class="text text--top gray"><span>贴条形码区</span></div>
              <div class="text text--bottom gray">
                <span>(正面朝上，切勿贴出虚线方框)</span>
              </div>
            </div>
          </div>
          <NoticeVue class="full-wrap" style="margin-top: 5mm"></NoticeVue>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  ref,
  nextTick,
  watch,
} from "vue";
import Paper from "../../src/views/paper";
import QRCode from "qrcode";
import StuNumberFillCard from "./stuNumberFillCard.vue";
import { INUMBERLAYOUT, ICorrectType } from "@/typings/card";
import NoticeVue from "./Notice.vue";
import PageHeaderTitle from '@/components/PageHeaderTitle.vue';

export default defineComponent({
  props: {
    id: {
      type: String,
      default: "",
    },
    page: {
      type: Number,
      default: 1,
    },
  },
  components: {
    NoticeVue,
    StuNumberFillCard,
    PageHeaderTitle
  },
  setup(props: any, ctx: any) {
    const state = reactive({
      Paper: Paper,
      INUMBERLAYOUT,
      ICorrectType,
      qrValue: "",
      QRCodeUrl: "",
    });

    /**
     * 页面一开始加载
     */
    onMounted(async () => {
      state.qrValue = Paper.paperId + props.page.toString().padStart(2, "0");
      let opts = {
        errorCorrectionLevel: "M",
        type: "image/jpeg",
        width: "20mm",
        height: "20mm",
        quality: 0.8,
        margin: 0,
        color: {
          dark: "#020202",
          light: "#fff",
        },
      };
      state.QRCodeUrl = await QRCode.toDataURL(state.qrValue, opts);
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang="scss" scoped>
.header {
  padding: 0;
  margin: 0;
  margin-bottom: 1mm;
  border-top: 0.1mm solid #fff;
  border-bottom: 0.1mm solid #000;
  position: relative;
  width: 100%;
  font-family: 宋体, SimSun, Times New Roman;
}

.header-table {
  width: 100%;
  display: flex;
  padding-top: 2mm;

  .headerinfo {
    display: inline-block;
  }

  .left {
    width: 110mm;
  }

  .right {
    // width: calc(100% - 110mm);
    position: relative;
  }

  tr {
    width: 100%;
  }

  td {
    vertical-align: inherit;
  }

  .header-info {
    display: flex;
  }

  .card-color-info {
    display: inline-block;
    width: 20mm;
    border-bottom: 0.1mm solid;
  }
}

.header-name {
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100%;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  max-height: 21mm;
  width: 100%;
}

.flex-end {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.m-b-4 {
  margin-bottom: 4mm !important;
}

.flex-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.barcode-wrapper {
  width: 65mm;
}

.stu-bar-code-container {
  width: 100%;
  height: 30mm;
  padding: 5mm;
  border: 1px dashed #000;
}

.stu-bar-code {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
  height: 100%;
  background-color: #e4e4e4;

  .text {
    color: #a5a5a5;
  }

  .text--top {
    font-size: 20px;
  }

  .text--bottom {
    font-size: 12px;
  }
}

.td-flex {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.table-left-td {
  height: 100%;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.table-notice-div {
  width: 100%;
}

.table-left-td,
.table-notice-div {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.stu-info-wrap {
  padding-top: 2mm;
  text-align: center;
  position: relative;
  font-size: 3.5mm !important;
}

.notice-info {
  display: inline-block;
  width: 100%;
  height: 21mm;
  max-width: 90mm;
  text-align: left;
  border: 0.1mm solid #000;
  line-height: 4mm;
  padding: 0;
  word-break: break-all;
  overflow: hidden;
}

.notice-title {
  text-align: center;
  font-size: 3.5mm;
  margin: 0;
  padding: 0;
  color: #000;
}

.notice-infop {
  margin: 0 1mm;
  padding: 0;
  color: #000;
}

.card-color {
  color: #000;
}

.full-wrap {
  margin-top: 1mm;
  max-width: 90mm;
  width: 100%;
  line-height: 7mm;
  font-size: 3mm !important;
  border: 0.1mm solid #000;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.full-item {
  vertical-align: middle;

  span {
    margin-right: 3mm;
  }
}

.full-sec {
  display: inline-block;
  border-width: 1.5mm 2.5mm;
  border-style: solid;
  border-color: #000;
  width: 0;
  height: 0;
}

.full-example {
  margin: 0;
  padding: 0;
  width: 5mm;
  height: 3mm;
  border: 0.1mm solid #000;
  font-size: 5mm;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}
</style>
<style lang="scss">
.headerinfo {
  .el-input__inner {
    height: 30px !important;
    line-height: 30px !important;
    // padding: 1.2mm 1mm !important;
  }
}

.ques-list {
  .el-input__inner {
    // text-align: center;
  }
}

.preview .qrcode {
  background: url(../assets/qrcode.png) 100% 100%;
  background-size: cover;
  // background: url('../assets/qrcode.png') no-repeat;
}
</style>

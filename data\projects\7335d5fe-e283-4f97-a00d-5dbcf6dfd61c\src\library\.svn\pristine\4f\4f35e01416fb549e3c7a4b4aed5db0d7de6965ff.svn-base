import store from "@/store";
import Types from "@/store/action-types";

declare let ssoClient: any;

function checkRoute(router: any, isForcelogin: boolean = false,cb?:Function) {
  router.beforeEach(async (to: any, from: any, next: any) => {
    const lastUid = ssoClient.getUserInfo().id;
    ssoClient.check(process.env.VUE_APP_APPID, false);
    const userInfo = ssoClient.getUserInfo();
    const userId = userInfo.id;
    // 如果两次userid不同 重新获取用户信息
    if (userId) {
      if (lastUid !== userId) {
        await getBaseInfo(next, userId,cb)
      } else {
        next();
      }
    } else {
      if (isForcelogin) {
        ssoClient.login(process.env.VUE_APP_APPID);
      } else {
        next();
      }
    }
  });
}
async function  getBaseInfo  (next: any,userId: string,cb?:Function){
  await store.dispatch(`app/${Types.GET_SITE_INFO}`);
  await store.dispatch(`app/${Types.GET_PLATFORM_INFO}`);
  await store.dispatch(`user/${Types.GET_USER_INFO}`, { userId: userId })
      .then(() => {
        cb && cb()
        next();
      })
      .catch(() => {
        next("/");
      });
}

export { checkRoute };

﻿<template>
  <!--添加个人教材-->
  <div class="add-user-textbook-box">
    <mk-dialog
      :isShowFooter="false"
      :isShowDialog="true"
      :dialogWidth="'38%'"
      @click-cancel="cancelDialog"
    >
      <template #title>
        <el-tabs v-model="activeName" class="tab-header" @tab-click="switchTab">
          <el-tab-pane
            label="统一教材"
            :name="TAB_TYPE.textbook"
            v-if="isShowTab"
          ></el-tab-pane>
          <el-tab-pane
            label="校本教材"
            :name="TAB_TYPE.schoolbook"
            v-if="isShowTab && shareType == SHARE_TYPE.school"
          ></el-tab-pane>
          <el-tab-pane
            :label="shareType == SHARE_TYPE.school ? '专题资源' : '专题资源'"
            :name="TAB_TYPE.special"
          ></el-tab-pane>
        </el-tabs>
      </template>

      <template #customSlot>
        <!--统一教材-->
        <template v-if="activeName === TAB_TYPE.textbook">
          <!--选择书本弹窗-->
          <div class="select-book-box" v-if="isShowChooseBookDialog">
            <choose-book
              class="select-book-cascader"
              :bookCode="curBook.bookCode"
              :isSchoolPlat="isSchoolPlat"
              :shareType="shareType"
              :schoolRadioType="schoolRadioType"
              @close-choose="isShowChooseBookDialog = false"
              @sure-add="sureSelectBook"
            ></choose-book>
          </div>
          <div class="user-textbook-box" v-else>
            <!-- 添加编辑操作 -->
            <div class="add-book-box" :class="isEdit ? 'end' : 'between'">
              <div
                class="add-book"
                @click="isShowChooseBookDialog = true"
                v-if="!isEdit"
              >
                <i class="el-icon-plus plus"></i>
                <span class="name">添加教材</span>
              </div>
              <div v-if="total > 0" class="edit-opt-box" @click="editTextbook">
                <i class="el-icon-edit-outline edit"></i>
                <span class="text">{{ isEdit ? '取消编辑' : '编辑' }}</span>
              </div>
            </div>
            <!-- 个人教材列表 -->
            <template v-if="total > 0">
              <div class="textbook-list-box">
                <div
                  class="textbook-list-ul"
                  v-for="(item, index) in bookList"
                  :key="index"
                  @click="switchTextbook(item)"
                >
                  <el-row
                    class="list-box"
                    type="flex"
                    justify="start"
                    align="middle"
                    :gutter="20"
                  >
                    <el-col :span="4" class="flex-center">
                      <div class="book-img">
                        <img
                          :src="
                            item.cover ||
                            require('@/library/ui/mk-share-res/assets/book_empty.png')
                          "
                          alt=""
                        />
                      </div>
                    </el-col>
                    <el-col :span="14" class="book-name-col">
                      <div class="book-name ellipsis" :title="item.bookName">
                        {{ item.bookName }}
                      </div>
                    </el-col>
                    <el-col :span="6" class="book-name-col">
                      <div class="book-opt-box">
                        <div
                          class="del-box"
                          v-if="isEdit && item.bookCode != curBook.bookCode"
                          @click="delCurrentBook(item.id)"
                        >
                          <span>
                            <i class="el-icon-error"></i>
                            移除
                          </span>
                        </div>
                        <div
                          class="cur-book-box"
                          v-if="item.bookCode == curBook.bookCode"
                        >
                          <span>
                            <i class="el-icon-success"></i>
                            当前教材
                          </span>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </template>

            <mk-no-data
              v-else
              :background="true"
              :noDataTipTex="'暂无教材,请添加相关教材!'"
            ></mk-no-data>
          </div>
        </template>

        <template v-else-if="activeName === TAB_TYPE.schoolbook">
          <!--校本教材-->
          <div v-if="schBookTotal > 0">
            <!-- 校本教材列表 -->
            <div class="textbook-list-box">
              <div
                class="textbook-list-ul"
                v-for="(item, index) in schBookList"
                :key="index"
                @click="switchTextbook(item)"
              >
                <el-row
                  class="list-box"
                  type="flex"
                  justify="start"
                  align="middle"
                  :gutter="20"
                >
                  <el-col :span="4" class="flex-center">
                    <div class="book-img">
                      <img
                        :src="
                          item.cover ||
                          require('@/library/ui/mk-share-res/assets/book_empty.png')
                        "
                        alt=""
                      />
                    </div>
                  </el-col>
                  <el-col :span="14" class="book-name-col">
                    <div class="book-name ellipsis" :title="item.bookName">
                      {{ item.bookName }}
                    </div>
                  </el-col>
                  <el-col :span="6" class="book-name-col">
                    <div class="book-opt-box">
                      <div
                        class="cur-book-box"
                        v-if="item.bookCode == curBook.bookCode"
                      >
                        <span>
                          <i class="el-icon-success"></i>
                          当前教材
                        </span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
            <mk-pagination
              :pageSize="schBookParams.limit"
              :total="schBookTotal"
              :currentPage="schBookParams.page"
              @switch-page="changeSchBookPage"
            ></mk-pagination>
          </div>
          <mk-no-data
            v-else
            :background="true"
            :noDataTipTex="'暂无校本教材,请添加相关教材!'"
          ></mk-no-data>
        </template>

        <template v-else>
          <!--专题资源-->
          <div class="flex-between">
            <span class="special-count"
              >共 <b>{{ specialTotal || 0 }}</b> 个专题</span
            >
            <div v-if="shareType == SHARE_TYPE.school">
              <mk-region-subject
                  :isSelect="true"
                  :isShowGrade="false"
                  :subjectCode="subjectId"
                  :isShowAllPhase="false"
                  :gradeCode="gradeId"
                  :phase="phase"
                  @on-change="switchSubject"
              ></mk-region-subject>
<!--              <el-select-->
<!--                ref="gradeRef"-->
<!--                placeholder="请选择年级"-->
<!--                v-model="gradeId"-->
<!--                @change="changeGrade"-->
<!--                style="width: 105px"-->
<!--              >-->
<!--                <el-option label="全部年级" value=""></el-option>-->
<!--                <el-option-->
<!--                  v-for="item in gradeList"-->
<!--                  :key="item.id"-->
<!--                  :label="item.name"-->
<!--                  :value="item.id"-->
<!--                >-->
<!--                </el-option>-->
<!--              </el-select>-->
<!--              <el-select-->
<!--                placeholder="请选择学科"-->
<!--                v-model="subjectId"-->
<!--                @change="querySpecial"-->
<!--                style="width: 105px"-->
<!--              >-->
<!--                <el-option label="全部学科" value=""></el-option>-->
<!--                <el-option-->
<!--                  v-for="item in subjectList"-->
<!--                  :key="item.id"-->
<!--                  :value="item.id"-->
<!--                  :label="item.subject_name"-->
<!--                ></el-option>-->
<!--              </el-select>-->
            </div>
            <el-input
              v-model="specialAPIParams.keyWord"
              clearable
              :style="
                shareType == SHARE_TYPE.school ? 'width:36%' : 'width: 60%'
              "
              @change="querySpecial"
              placeholder="请输入关键词搜索专题"
              suffix-icon="el-icon-search"
            >
            </el-input>
          </div>

          <template v-if="specialTotal > 0">
            <div class="textbook-list-box">
              <div
                class="textbook-list-ul"
                v-for="(item, index) in specialList"
                :key="index"
                @click="switchSpecial(item)"
              >
                <el-row
                  class="list-box"
                  type="flex"
                  justify="start"
                  align="middle"
                >
                  <el-col :span="8" class="flex-center">
                    <div class="special-img">
                      <img
                        :src="
                          addFsUrl(item.thumbnail || 'aliba/threeclass/classroom/default/schoolThematic.png')
                        "
                      />
                    </div>
                  </el-col>
                  <el-col :span="11" v-if="shareType == SHARE_TYPE.school">
                    <div class="book-name ellipsis" :title="item.title">
                      {{ item.title }}
                    </div>
                    <span style="margin-right: 10px">{{
                      item.gradeName || '全部年级'
                    }}</span>
                    <span>{{ item.subjectName || '全部学科' }}</span>
                  </el-col>
                  <el-col :span="11" class="book-name-col" v-else>
                    <div class="book-name ellipsis" :title="item.title">
                      {{ item.title }}
                    </div>
                  </el-col>
                  <el-col :span="5" class="book-name-col">
                    <div class="book-opt-box">
                      <div
                        class="cur-book-box"
                        v-if="item.id === curSpecial.id"
                      >
                        <span>
                          <i class="el-icon-success"></i>
                          当前专题
                        </span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>

            <mk-pagination
              :pageSize="specialAPIParams.limit"
              :total="specialTotal"
              :currentPage="specialAPIParams.page"
              @switch-page="changeSpecialPage"
            ></mk-pagination>
          </template>

          <mk-no-data
            v-else
            :background="true"
            :noDataTipTex="'暂无专题!'"
          ></mk-no-data>
        </template>
      </template>
    </mk-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';
import {
  queryThematicManagementAPI,
  screenQueryThematicManagementAPI,
  getRegionBookListAPI,
  getPlatFromInfoBySchoolIdAPI,
  getRegionBookConfigAPI
} from '@/library/src/service/API/tearesearch';
import {
  userbookListAPI,
  insertUserBookAPI,
  deleteUserBookAPI,
  listBySchoolIdAPI,
  gradeListAPI,
  getSchoolBookConfigAPI
} from '@/library/src/service/API/resource';
import { getSubjectListByPhaseAPI } from '@/library/src/service/API/base';
import { IGlobalState } from '@/library/src/store';
import ChooseBook from '@/library/ui/mk-share-res/modules/ChooseBook.vue';
import MkRegionSubject from '@/library/ui/mk-region-subject';
import { PLATFORM_TYPE } from '@/library/src/utils/gloableEnum';
import { addFsUrl } from '@/library/src/utils/globalFunction';
/** tab类型 */
export enum TAB_TYPE {
  /** 统一教材 */
  textbook = 'textbook',
  /** 专题资源 */
  special = 'special',
  /** 校本教材 */
  schoolbook = 'schoolbook'
}
/** 分享类型 */
export enum SHARE_TYPE {
  /** 校本资源 */
  school = 'school',
  /** 区域资源 */
  platform = 'platform'
}
export default defineComponent({
  name: 'platform-book-dialog',

  emits: ['on-cancle', 'switch-book', 'switch-special'],

  components: { ChooseBook , MkRegionSubject},

  props: {
    // 当前书本
    curBookData: {
      type: Object,
      default: () => {
        return {
          gradeCode: '',
          gradeName: '',
          subjectCode: '',
          subjectName: '',
          editionCode: '',
          editionName: '',
          bookCode: '',
          bookName: '',
          catalogCode: '',
          catalogName: ''
        };
      }
    },
    //当前专题
    curSpecialData: {
      type: Object,
      default: {}
    },
    // 平台id
    platFromId: {
      type: String,
      required: true
    },
    // 默认tab
    tab: {
      type: String,
      default: TAB_TYPE.textbook
    },
    //分享类型
    shareType: {
      type: String,
      default: ''
    },
    //是否展示tab页切换
    isShowTab: {
      type: Boolean,
      default: true
    }
  },

  setup(props, ctx) {
    const store = useStore<IGlobalState>();
    const state = reactive({
      // 当前激活tab
      activeName: props.tab,
      // 是否显示选择教材弹窗
      isShowChooseBookDialog: false,
      // 当前教材书本
      curBook: props.curBookData,
      // 教材同步请求参数
      textAPIParams: {
        regionId: props.platFromId,
        // 年级编码
        gradeCode: '',
        // 学科编码
        subjectCode: '',
        // 版本编码
        editionCode: '',
        // 关键词
        keyWord: '',
        // 页码
        page: 1,
        // 请求条目
        limit: 5
      },
      // 教材列表
      bookList: [] as any[],
      //校本教材列表
      schBookList: [] as any[],
      // 总条数
      total: 0,
      // 是否开启编辑教材
      isEdit: false,
      // 专题资源请求参数
      specialAPIParams: {
        regionId: props.platFromId,
        keyWord: '',
        //前台展示1：已发布 校级展示-1：全部 区级展示空为全部
        isPublish: props.isShowTab
          ? '1'
          : props.shareType == SHARE_TYPE.school
          ? '-1'
          : '',
        isSystem: 1,
        limit: 10,
        page: 1
      },
      // 专题资源列表
      specialList: [],
      // 专题总数
      specialTotal: 0,
      // 当前专题
      curSpecial: props.curSpecialData,
      //年级列表
      gradeList: [] as any[],
      //学科列表
      subjectList: [] as any[],
      //年级
      gradeId: '',
      //学段
      phase: store.state.user.phase.substr(0,1),
      //学科
      subjectId: '',
      //校本教材请求参数
      schBookParams: {
        schoolId: store.state.user.schoolId,
        page: 1,
        limit: 10
      },
      //校本教材总数
      schBookTotal: 0,
      //是否开通校级平台
      isSchoolPlat: false,
      // 统一教材的配置
      schoolRadioType: 0,
      regionRadioType: 0
    });

    /**
     * @name: tab切换
     */
    const switchTab = () => {
      if (state.activeName === TAB_TYPE.textbook) {
        getUserBookList();
      } else if (state.activeName === TAB_TYPE.schoolbook) {
        changeSchBookPage(1);
      } else {
        changeSpecialPage(1);
      }
    };
    /**
     * @name 分页
     * @params page 页数
     */
    const changeTextPage = (page: number) => {
      state.textAPIParams.page = page;
      getUserBookList();
    };
    /**
     * @name:获取用户统一教材列表
     */
    const getUserBookList = async () => {
      let params = {
        userId: store.state.user.userId
      };
      const res = await userbookListAPI(params);
      if (res && parseInt(res.code) == 1) {
        state.bookList = res.data.map((item:any)=>{
          item.bookType = 1
          return item
        });
        state.total = res.data.length;
      } else {
        ElMessage({ message: '获取教材列表失败', type: 'error' });
      }
    };
    /**
     * @name 获取校本教材
     */
    const getSchoolBankBookList = async () => {
      if (
        state.isSchoolPlat &&
        store.state.app.platFormInfo.platformType === PLATFORM_TYPE.SCHOOL
      ) {
        getSchoolBookList();
      } else {
        listBySchoolId();
      }
    };
    /**
     * @name 获取开通校级平台的校本教材
     */
    const getSchoolBookList = async () => {
      const params = {
        ...state.schBookParams,
        regionId: store.state.app.platFormInfo.id,
        platType: 2,
        bookType: 2
      };
      const res = await getRegionBookListAPI(params);
      if (res.code == 1) {
        if (res.data.rows.length) {
          state.schBookList = res.data.rows.map((item: any) => {
            item.catalogCode = '';
            item.catalogName = '';
            item.bookType = 2
            return item;
          });
          state.schBookTotal = res.data.total_rows;
        } else {
          state.schBookList = [];
        }
      } else {
        ElMessage({ message: '获取教材列表失败', type: 'error' });
      }
    };
    /**
     * @name 获取未开通校级平台的校本教材
     */
    const listBySchoolId = async () => {
      const params = {
        gradeCode: '',
        subjectCode: '',
        editionCode: '',
        userId: store.state.user.userId,
        ...state.schBookParams
      };
      const res = await listBySchoolIdAPI(params);
      if (res.code == 1) {
        if (res.data.rows.length) {
          state.schBookList = res.data.rows.map((item: any) => {
            item.gradeCode = item.grade;
            item.subjectCode = item.subject;
            item.editionCode = item.edition;
            item.bookCode = item.code;
            item.bookName = item.name;
            item.catalogCode = '';
            item.catalogName = '';
            item.bookType = 2
            return item;
          });
          state.schBookTotal = res.data.total_rows;
        } else {
          state.schBookList = [];
        }
      } else {
        ElMessage({ message: '获取教材列表失败', type: 'error' });
      }
    };
    /**
     * @name 校本教材分页
     * @param page 当前页码
     */
    const changeSchBookPage = (page: number) => {
      state.schBookParams.page = page;
      getSchoolBankBookList();
    };
    /**
     * @name:点击确定教材按钮回调
     */
    const sureSelectBook = async (selectBookData: any) => {
      const params = {
        userId: store.state.user.userId,
        gradeCode: selectBookData.gradeCode,
        gradeName: selectBookData.gradeName,
        subjectCode: selectBookData.subjectCode,
        subjectName: selectBookData.subjectName,
        editionCode: selectBookData.editionCode,
        editionName: selectBookData.editionName,
        bookCode: selectBookData.bookCode,
        bookName: selectBookData.bookName,
      };
      const res = await insertUserBookAPI(params);
      if (res && parseInt(res.code) == 1) {
        // 调用教材列表
        getUserBookList();
        // 清空本次选择的书本
        selectBookData.bookId = '';
      } else {
        ElMessage({ message: '添加教材失败', type: 'error' });
      }
    };
    /**
     * @name: 弹窗取消按钮
     */
    const cancelDialog = () => {
      ctx.emit('on-cancle');
    };
    /**
     * @name: 是否开启编辑教材模式
     */
    const editTextbook = () => {
      state.isEdit = !state.isEdit;
    };
    /**
     * @name: 点击移除教材列表里面的教材--确定删除,调用删除接口
     */
    const delCurrentBook = async (id: string) => {
      let params = {
        id: id
      };
      const res = await deleteUserBookAPI(params);
      if (res && parseInt(res.code) === 1) {
        ElMessage({ message: '删除成功', type: 'success' });
        let page = state.textAPIParams.page;
        if (state.bookList.length == 1) {
          page -= 1;
          page = page <= 1 ? 1 : page;
        }
        changeTextPage(page);
      } else {
        ElMessage({ message: '删除失败', type: 'error' });
      }
    };
    /**
     * @name: 切换当前教材
     */
    const switchTextbook = (item: any) => {
      if (state.isEdit) {
        return;
      }
      let obj = {
        gradeCode: item.gradeCode,
        gradeName: item.gradeName,
        subjectCode: item.subjectCode,
        subjectName: item.subjectName,
        editionCode: item.editionCode,
        editionName: item.editionName,
        bookCode: item.bookCode,
        bookName: item.bookName,
        catalogCode: '',
        catalogName: '',
        bookType:item.bookType
      };
      state.curBook = obj;
      ctx.emit('switch-book', state.curBook, state.activeName);
      cancelDialog();
    };
    /**
     * @name 切换年级
     */
    const changeGrade = (value: any) => {
      if (value == 'more') {
        //获取全部年级
        getGradeList();
        state.gradeId = '';
        state.phase = '';
      } else {
        state.gradeList.forEach((item: any) => {
          if (value == item.id) {
            state.phase = item.phase;
          }
        });
      }
      //切换年级获取学科
      getSubjectList();
      //获取校本专题列表
      querySpecial();
    };
    const switchSubject = (data: any) => {
      state.phase = data.phase;
      state.gradeId = data.gradeInfo.code;
      state.subjectId = data.subjectInfo.code;
      querySpecial();
    };
    /**
     * @name 获取全部年级
     */
    const getGradeList = (phase = '') => {
      let params = {
        phase: phase
      };
      gradeListAPI(params)
        .then((res: any) => {
          if (res.code == 1) {
            if (phase) {
              state.gradeList = res.data;
              state.gradeList.push({
                id: 'more',
                name: '更多年级'
              });
            } else {
              state.gradeList = res.data;
            }
          } else {
            ElMessage({ message: res.msg, type: 'error' });
          }
        })
        .catch((err: any) => {
          ElMessage({ message: err.msg, type: 'error' });
        });
    };
    /**
     * @name 获取全部学科
     */
    const getSubjectList = () => {
      let params = {
        phase: state.phase
      };
      getSubjectListByPhaseAPI(params)
        .then((res: any) => {
          if (res.code == 1) {
            state.subjectList = res.data;
          } else {
            ElMessage({ message: res.msg, type: 'error' });
          }
        })
        .catch((err: any) => {
          ElMessage({ message: err.msg, type: 'error' });
        });
    };
    /**
     * @name:搜索专题列表
     */
    const querySpecial = () => {
      state.specialAPIParams.page = 1;
      getSpecialList();
    };
    /**
     * @name 专题列表分页
     * @params page 页数
     */
    const changeSpecialPage = (page: number) => {
      state.specialAPIParams.page = page;
      getSpecialList();
    };
    /**
     * @name: 获取专题列表
     */
    const getSpecialList = async () => {
      if (props.shareType == SHARE_TYPE.school) {
        let params = {
          keyWord: state.specialAPIParams.keyWord,
          gradeId: state.gradeId,
          subjectId: state.subjectId,
          schoolId: store.state.user.schoolId,
          limit: state.specialAPIParams.limit,
          page: state.specialAPIParams.page,
          isPublish: state.specialAPIParams.isPublish
        };
        const res = await screenQueryThematicManagementAPI(params);
        if (res && parseInt(res.code) === 1) {
          state.specialList = res.data.rows;
          state.specialTotal = res.data.total_rows;
        } else {
          state.specialList = [];
          state.specialTotal = 0;
          ElMessage({ message: '获取校级专题失败', type: 'error' });
        }
      } else {
        const res = await queryThematicManagementAPI(state.specialAPIParams);
        if (res && parseInt(res.code) === 1) {
          state.specialList = res.data.rows;
          state.specialTotal = res.data.total_rows;
        } else {
          state.specialList = [];
          state.specialTotal = 0;
          ElMessage({ message: '获取专题失败', type: 'error' });
        }
      }
    };
    /**
     * @name: 切换专题
     * @param item 当前专题对象
     */
    const switchSpecial = (item: any) => {
      state.curSpecial = item;
      ctx.emit('switch-special', state.curSpecial);
      cancelDialog();
    };
    /**
     * @name 判断当前学校是否开通校级
     */
    const getPlatFromInfoBySchoolId = async () => {
      let params = {
        schoolId: store.state.user.schoolId
      };
      const res = await getPlatFromInfoBySchoolIdAPI(params);
      if (res && parseInt(res.code) === 1) {
        let isSchoolPlat = false;
        if (res.data) {
          for (let i = 0; i < res.data.length; i++) {
            if (
              res.data[i].platformType == '5' &&
              res.data[i].platformState == 1
            ) {
              isSchoolPlat = true;
            }
          }
        }
        state.isSchoolPlat = isSchoolPlat;
      } else {
        ElMessage({ message: res.msg, type: 'error' });
      }
    };
    /**
     * @name 获取校本教材配置
     */
    const getSchoolBookConfig = async () => {
      const res = await getSchoolBookConfigAPI({
        schoolId: store.state.user.schoolId
      });
      if (res && parseInt(res.code) === 1) {
        state.schoolRadioType = res.data;
      } else {
        ElMessage.warning(res.msg);
      }
    };
    /**
     * @name 获取区域教材配置
     */
    const getRegionBookConfig = async () => {
      const res = await getRegionBookConfigAPI({
        regionId: store.state.app.platFormInfo.id
      });
      if (res && parseInt(res.code) === 1) {
        state.regionRadioType = res.data;
      } else {
        ElMessage.warning(res.msg);
      }
    };
    onMounted(() => {
      switchTab();
      if (props.shareType == SHARE_TYPE.school) {
        getPlatFromInfoBySchoolId();
        getGradeList(store.state.user.phase);
        getSubjectList();
        getSchoolBookConfig();
      } else {
        getRegionBookConfig();
      }
    });

    return {
      ...toRefs(state),
      TAB_TYPE,
      SHARE_TYPE,
      switchTab,
      editTextbook,
      switchTextbook,
      switchSpecial,
      delCurrentBook,
      sureSelectBook,
      cancelDialog,
      changeTextPage,
      querySpecial,
      changeSpecialPage,
      changeGrade,
      switchSubject,
      changeSchBookPage,
      addFsUrl
    };
  }
});
</script>

<style lang="scss" scoped>
.add-user-textbook-box {
  .user-textbook-box {
    .add-book-box {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 0 10px;

      span {
        display: inline-block;
      }

      .add-book {
        @include theme_color();
        height: 36px;
        line-height: 36px;
        font-size: 16px;
        cursor: pointer;

        .plus {
          @include theme_color();
        }

        .name {
          margin-left: 5px;
        }
      }

      .edit-opt-box {
        cursor: pointer;

        .edit {
          font-size: 18px;
        }

        .text {
          margin-left: 5px;
          text-align: right;
        }
      }
    }

    .end {
      justify-content: flex-end;
    }

    .between {
      justify-content: space-between;
    }

    .start {
      justify-content: flex-start;
    }
  }

  .textbook-list-box {
    width: 100%;
    max-height: 400px;
    overflow-y: auto;

    .textbook-list-ul {
      border-bottom: 1px solid #e6eef1;
      padding: 10px 15px;
      cursor: pointer;

      &:hover {
        @include theme_hover_background-color();
      }

      .list-box {
        .book-img {
          width: 64px;
          height: 88px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .special-img {
          width: 140px;
          height: 68px;
          border-radius: 4px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .book-name-col {
          display: flex !important;
          justify-content: flex-start !important;
          align-items: center !important;
        }

        .book-name {
          font-size: 18px;
          color: #2a3034;
        }

        .book-opt-box {
          font-size: 16px;

          .del-box {
            color: red;
            cursor: pointer;
          }

          .cur-book-box {
            @include theme_color();

            .iconicon-test13 {
              @include theme_color();
              margin-right: 10px;
            }
          }
        }
      }
    }
  }

  .select-book-box {
    .select-book-cascader {
      width: 100%;
      margin-top: 5px;

      .el-input__inner {
        border-radius: 38px;
        background: #f2f2f2;
        border: none;
      }
    }

    .opt-btn-footer {
      text-align: center;
      margin-top: 30px;

      .cancel-btn {
        margin-right: 50px;
      }
    }
  }

  .special-count {
    display: inline-block;
    width: 23%;
    font-size: 16px;
    color: #7c8394;
  }
}
</style>

<style lang="scss">
.add-user-textbook-box {
  user-select: none;

  .tab-header {
    position: absolute;
    left: 50%;
    height: 40px;
    transform: translateX(-50%);

    .el-tabs__item.is-active {
      background-color: transparent !important;
    }

    .el-tabs__item {
      padding: 0 40px;
      font-size: 18px;
    }

    .el-tabs__nav-wrap::after {
      display: none;
    }
  }

  .el-dialog__body {
    padding: 20px 10px;
  }

  .el-input__inner {
    border-radius: 18px;
  }
}
</style>

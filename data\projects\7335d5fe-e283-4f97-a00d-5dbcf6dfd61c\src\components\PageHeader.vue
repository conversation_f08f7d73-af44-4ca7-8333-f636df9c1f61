<template>
  <div class="page-header">
    <div class="header" style="height: 13mm;font-size: 4.8mm;line-height: 1.125;">
      <div v-if="position.left" class="locatePoint-l"></div>
      <div v-if="position.right" class="locatePoint-r"></div>
      <span class="styles_left styles_idAndNameBase">
        编号：{{ Paper.paperId }}
      </span>
      <span class="custom-name"
        style="left: 0;text-align: center;right: 0;position: absolute;bottom: 1mm;font-size: 3.2mm;font-family: 宋体;">
        {{ customName }}
      </span>
    </div>
    <div style="width: 100%; height: 1mm"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, watch } from "vue";
import Paper from "@/views/paper";
import bus from "@/utils/bus";

export default defineComponent({
  props: {
    idx: {
      type: Number,
      default: 0,
    },
    id: {
      type: String,
      default: "",
    },
    position: {
      type: Object,
      default: function () {
        return { left: true, right: true };
      },
    },
  },
  components: {},
  setup(props: any, ctx: any) {
    const state = reactive({
      Paper: Paper,
      customName: Paper.headerName
    });

    /**
     * 页面一开始加载
     */
    onMounted(async () => {
      bus.on("updateHeaderName", (val:string) => {
        state.customName = val
      })
    });

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang="scss" scoped>
.header {
  padding: 0;
  margin: 0;
  position: relative;
  width: 100%;
  border-bottom: 0.1mm solid rgb(0, 0, 0);
  font-family: 宋体, SimSun, Times New Roman;

  .locatePoint-l {
    top: 7mm;
    left: -7mm;
    width: 7mm;
    height: 3mm;
    position: absolute;
    background-color: black;
    display: block;
  }

  .locatePoint-r {
    top: 7mm;
    right: -7mm;
    width: 7mm;
    height: 3mm;
    position: absolute;
    background-color: black;
    display: block;
  }
}

.styles_left {
  left: 0;
}

.styles_right {
  position: absolute;
  bottom: 0;
  padding: 0;
  margin: 0;
  font-size: 3.8mm;
  overflow: hidden;
  right: 0;
  min-width: 8mm;
}

.styles_idAndNameBase {
  position: absolute;
  bottom: 1mm;
  padding: 0;
  margin: 0;
  font-size: 3.8mm;
  overflow: hidden;
  color: #3d3d3d;
}
</style>

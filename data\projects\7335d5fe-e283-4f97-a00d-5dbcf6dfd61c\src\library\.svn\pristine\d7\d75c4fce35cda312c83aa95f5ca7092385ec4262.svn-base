<template>
  <mk-dialog
    :isShowDialog="true"
    :title="title"
    :dialogWidth="'35%'"
    @click-sure="sure"
    @click-cancel="cancel"
  >
    <template #customSlot>
      <div class="auto-publish">
        {{ content }}
      </div>
      <div class="resource-synchronize-check" v-if="isShowCheck">
        <el-checkbox v-model="checked">{{ label }}</el-checkbox>
        <div class="resource-synchronize-title">
          前提：老师开启了资源包分享到区域资源，管理员才可以进行资源同步操作<br />
          同步区域资源后若想删除/取消发布请去区域资源操作~
        </div>
      </div>
    </template>
  </mk-dialog>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';

export default defineComponent({
  name: 'auto-display-dialog',
  props: {
    //标题
    title: {
      type: String,
      default: '自动显示'
    },
    //内容
    content: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    //是否展示勾选项
    isShowCheck: {
      type: Boolean,
      default: false
    }
  },
  setup(props, ctx) {
    const state = reactive({
      //是否同步资源开启关闭
      checked: false
    });
    /**
     * @name 取消按钮
     */
    const cancel = () => {
      ctx.emit('close-auto-display');
    };

    /**
     * @name 确认按钮
     */
    const sure = () => {
      ctx.emit('sure-auto-display', state.checked);
    };
    return {
      ...toRefs(state),
      cancel,
      sure
    };
  }
});
</script>
<style lang='scss' scoped>
.auto-publish {
  font-size: 16px;
  margin-bottom: 10px;
}
.resource-synchronize-check {
  font-size: 16px;
  .resource-synchronize-title {
    font-size: 16px;
    margin-top: 10px;
    color: #aaaaaa;
    margin-left: 24px;
  }
}
</style>
<style lang="scss">
.resource-synchronize-check {
  .el-checkbox {
    display: flex;
    align-items: center;
    .el-checkbox__label {
      font-size: 16px;
      white-space: normal;
    }
  }
}
</style>

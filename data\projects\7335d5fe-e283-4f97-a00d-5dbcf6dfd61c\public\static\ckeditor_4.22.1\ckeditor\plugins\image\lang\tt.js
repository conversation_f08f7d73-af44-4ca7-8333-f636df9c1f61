/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'tt', {
	alt: 'Альтернатив текст',
	border: 'Чик',
	btnUpload: 'Серверга җибәрү',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: 'Горизонталь ара',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'Рәсем тасвирламасы',
	linkTab: 'Сылталама',
	lockRatio: 'Lock Ratio', // MISSING
	menu: 'Рәсем үзлекләре',
	resetSize: 'Баштагы зурлык',
	title: 'Рәсем үзлекләре',
	titleButton: 'Рәсемле төймə үзлекләре',
	upload: 'Йөкләү',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: 'Вертикаль ара',
	validateBorder: 'Чик киңлеге сан булырга тиеш.',
	validateHSpace: 'Горизонталь ара бөтен сан булырга тиеш.',
	validateVSpace: 'Вертикаль ара бөтен сан булырга тиеш.'
} );

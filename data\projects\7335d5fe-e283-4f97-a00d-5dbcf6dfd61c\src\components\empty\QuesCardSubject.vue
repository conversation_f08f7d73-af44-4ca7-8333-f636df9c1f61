<template>
  <div ref="quesCardRef" class="q-opt card" :data-typeid="item.typeId" :data-iswriting="!!item.isWriting"
    :class="{ onlycard: isOnlyCard, web: !isHandCorrent, hand: isHandCorrent, 'is-big-mixin': isMixinSubjectParent, 'layout-a33': isA33Layout, 'is-small-mixin': isMixinSmallQues }"
    :id="qId" :data-boxheight="isWritingQues ? height : 0"
    :style="{ fontSize: Paper.fontSize, fontFamily: Paper.fontFamily }">
    <div class="ques-content" :class="{ 'is-small-mixin': isMixinSmallQues }">
      <!-- 大题标题 -->
      <bigques-name :class="{ 'split-tag': isMixinSubjectParent }" :item="item" v-if="showBigTitle">
        <div class="edit-button-tools btn-tool nosave">
          <div class="edit nosave click-element" @click="editQues">
          </div>
        </div>
      </bigques-name>

      <template v-if="!isMixinSubjectParent">
        <div class="splitnode-container" v-for="subItem in item.data" :key="subItem.id">
          <!-- 按拆分节点渲染 -->
          <template v-for="(spItem, spIndex) in splitNodes" :key="spItem.id">
            <div class="ques-box-container split-card op1" :qid="`${questionCard.data[0].id}`" :data-id="spItem.id"
              :data-index="spIndex" :style="{ width: splitNodes.length > 1 ? spItem.width : '100%' }"
              v-if="spItem.type === 'ques'">
              <!-- 简答题 -->
              <!-- mixinMode：过滤掉混合模式的父级题目 -->
              <div class="ques-box-wrap" v-if="!subItem.mixinMode">
                <div class="choose-ques-content" style="margin-left: 10px;"
                  v-if="spIndex == 0 && subItem?.targetIds?.includes(subItem.id)">
                  <span>选做题（{{ subItem.chooseIds.length }}选{{ subItem.doCount }}）</span>
                  <span v-for="(name, ni) in subItem.chooseName"
                    :id="'choose_' + subItem.chooseIds[ni] + '_' + subItem.id"
                    style="line-height: 3mm;margin: 0 2mm;">[<span
                      style="padding: 0px 1mm;margin: 0px;font-size: 3mm;">{{ name }}</span>]</span>
                </div>
                <!-- 简答题可编辑区域:S -->
                <!-- editContent:直接渲染存在模板 -->
                <div class="ques-box subject-box split-tag" :id="`cke_${qId}_${spIndex}`" :data-id="spItem.id"
                  :class="{ 'is-wrigting': isWritingQues, 'not-writing': !isWritingQues }" :data-tag="boxTag"
                  :data-index="spIndex" :style="{ height: spItem.height }" @mouseenter="handleEditorOver"
                  @scroll="handleSubjectBoxScoll" v-html="converMathtex(editContent)" v-if="editContent && spIndex === 0">
                </div>

                <!-- editContent:其他分段手动填充 -->
                <div class="ques-box subject-box split-tag" :id="`cke_${qId}_${spIndex}`" :data-id="spItem.id"
                  :class="{ 'is-wrigting': isWritingQues, 'not-writing': !isWritingQues }" :data-tag="boxTag"
                  :data-index="spIndex" :style="{ height: spItem.height }" @mouseenter="handleEditorOver"
                  @scroll="handleSubjectBoxScoll" v-else-if="editContent && spIndex > 0">
                </div>

                <!-- 渲染空白题目 -->
                <div class="ques-box subject-box split-tag subject-box--blank" :id="`cke_${qId}_${spIndex}`"
                  :data-id="spItem.id" :data-index="spIndex" :data-tag="boxTag"
                  :class="{ 'is-wrigting': isWritingQues, 'not-writing': !isWritingQues }"
                  :style="{ height: spItem.height }" @mouseenter="handleEditorOver" v-else>
                  <!-- subject-para-p在作文类型仅保留第一个分段的标签 -->
                  <div class="subject-para-p" :class="{ 'remove-node': isWritingQues && spIndex !== 0 }"
                    v-if="!isWritingQues || spIndex == 0">
                    <template v-if="spIndex == 0 && !subItem?.isChooseDo">
                      <span class="ques-sort">{{ subItem.quesName || subItem.quesNos }}</span>
                      <span>.</span>
                      <span class="ques-score">(<span class="item-score">{{ subItem.score
                      }}</span>分)</span>
                    </template>
                  </div>
                </div>
                <!-- 简答题可编辑区域:E -->

                <!-- 编辑工具 -->
                <div class="edit-button-tools btn-tool nosave" :class="{ 'is-writing': isWritingQues }">
                  <div class="split click-element" @click="splitSubjectiveQuestions"
                    v-if="isOnlyCardType && isOnlySubjectQues" title="点击拆分"></div>

                  <!-- 切换作文格尺寸 -->
                  <div class="writing-seting nosave" v-if="isWritingQues">
                    <el-radio-group v-model="subItem.cellSize" @change="onChangeCellSize(subItem.id)">
                      <el-radio label="small">小格</el-radio>
                      <el-radio label="medium">中格</el-radio>
                      <el-radio label="big">大格</el-radio>
                    </el-radio-group>
                  </div>

                  <!-- 切换是否显示横线 -->
                  <div class="line nosave click-element" title="是否显示横线" @click="changeLine(item, subItem, spItem)"
                    v-else-if="isOnlySubjectQues">
                  </div>
                </div>

                <!-- 调整高度工具 -->
                <div class="pop down nosave" v-if="showSubjectTools">
                  <div class="pop-icon" @click="setHeight('half', spIndex, spItem.pageSize)" title="点击调整至页面一半">
                    <span class="half"></span>
                  </div>
                  <div class="pop-icon" @click="setHeight('full', spIndex, spItem.pageSize)" title="点击调整至页面底部">
                    <span class="bottom"></span>
                  </div>
                  <div class="pop-icon pop-icon-free" @mousedown="startDrag($event, spIndex)" title="拖动鼠标调整高度">
                    <span class="free"></span>
                  </div>
                </div>

              </div>
            </div>

            <!-- 分页底部空白区域 -->
            <div class="page-splitor" :data-id="spItem.id" :style="{ height: spItem.height }" v-else>
            </div>
          </template>
        </div>
      </template>

    </div>

    <!-- 简答题拆分线 -->
    <div class="split-line split-line--subject" :class="{ 'hide-block': !enableSubjectSplitLine }"
      @click="mergeSubjectiveQuestions()"
      :style="{ width: splitNodes.length > 1 ? splitNodes[splitNodes.length - 1].width : '100%' }" title="点击合并"
      v-if="showSplitLine">
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  ref,
  nextTick,
  render,
  h,
  computed,
  onBeforeUnmount,
  watch,
} from 'vue';
import Paper from '@/views/paper';
import {
  DECIMAL_TYPE,
  GRIDPLACE_TYPE,
  GRID_TYPE,
  JUDGE_TYPE,
  MARK_TYPE,
  QUES_TYPE,
  IPAGELAYOUT,
  LINE_WIDTH,
  ICorrectType,
  ICARDMODEL,
  SUBJECT_MODE,
} from '@/typings/card';
import { arabToChinese, deepClone, generateUUID } from '@/utils/util';
import { pageHeight, footerKeepHeight, getHeaderInfoHeight } from '@/utils/config';
import bus from '@/utils/bus';
import { getNameElement, pxConversionMm, getAccHeight, getAccHeightByMM, getPxConversionMm, isChildDomEmpty } from '@/utils/dom';
import { SplitNodes } from '@/typings/card';

import MarkScore from '../MarkScore.vue';
import SubjectWriting from './SubjectWriting.vue';
import PageHeader from '@/components/PageHeader.vue';
import BigquesName from './BigquesName.vue';
import { PaperConstant } from '@/views/paper.constant';

type SpliteLayout = 'A33' | 'A4' | 'auto';

export default defineComponent({
  props: {
    qId: {
      type: String,
      default: '',
    },
    // 题干信息
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 题目下标
    index: {
      type: Number,
      default: 0,
    },
    // 大题目下标
    bigIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ['updateList'],
  components: {
    MarkScore,
    PageHeader,
    BigquesName,
    SubjectWriting,
  },
  setup(props: any, ctx: any) {
    const quesCardRef = ref<any>(null);
    const editContent = props.item.data[0].editContent;
    let ckeditorList = [];
    const state = reactive({
      QUES_TYPE: QUES_TYPE,
      GRIDPLACE_TYPE: GRIDPLACE_TYPE,
      JUDGE_TYPE: JUDGE_TYPE,
      MARK_TYPE: MARK_TYPE,
      GRID_TYPE: GRID_TYPE,
      DECIMAL_TYPE: DECIMAL_TYPE,
      ICorrectType: ICorrectType,
      LINE_WIDTH: LINE_WIDTH,
      Paper: Paper,
      isFocus: false,
      isEdit: true,
      wrapHeight: 0,
      isObj: false,
      az: '0ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      isOnlySubjectQues: !props.item.isWriting,
      // 是否展示作文题设置
      isWritingOver: false,
      // 是否混合题的小问
      isMixinSmallQues: !!props.item.parentId,
      // 是否混合简答题父级
      isMixinSubjectParent: false,
      // 兼容数据：定义较模糊，不轻易修改
      isOnlyCard: Paper.cardType != ICARDMODEL.QUESCARD,
      isOnlyCardType: Paper.cardType == ICARDMODEL.BLANKCARD,
      isHandCorrent: Paper.correctType == ICorrectType.HAND,
      // 卡片高度
      height: 0,
      // 客观题是否合并
      isObjectiveMerged: Paper.mergeQues,
      boxTag: 'subjectbox_' + props.qId,
      // 拆分节点，type:'ques'|'blank'   63mm高度保证五行多一点冗余
      splitNodes: [{ id: generateUUID(), type: 'ques', height: '63mm' }] as SplitNodes[],
      // 简答题编辑内容
      editContent,
      // 作文格尺寸
      cellSize: props.item.data[0].cellSize,
      instanceReady: false,
      // 显示创建作文格弹窗
      showCreateCellsDialog: false,
      // 首个分段布局是否A33
      isFisrtSplitA33: false,
      editor: null,
      // 是否A33布局
      isA33Layout: false,
      // 是否横线初始化
      isLineCreating: props.item.hasLine && !editContent,

      // 强制刷新key
      refreshKey: 0,
    });

    const checkIfMixinSubjectParent = () => {
      let subItem = props.item.data[0];
      state.isMixinSubjectParent = subItem.mixinMode && !subItem.parentId;
    };

    const renderChooseDoOrQuesNos = () => {
      const subItem = props.item.data[0];
      const pDom = document.getElementById('cke_' + subItem.id + '_0');
      if (!pDom) return;

      if (subItem.isChooseDo) {
        if (pDom.children.length > 0 && pDom.children[0].classList.contains('subject-para-p')) {
          pDom.children[0].innerHTML = '';
        }
      } else {
        // FIXME 选做题取消添加序号和简答题删除序号更新有冲突，这里暂时屏蔽
        // const html = `<div class="subject-para-p"><span class="ques-sort">${subItem.quesName || subItem.quesNos}</span><span>.</span><span class="ques-score">(<span class="item-score">${subItem.score}</span>分)</span></div>`;
        // if (pDom.children.length > 0 && !pDom.getElementsByClassName('ques-sort').length) {
        //   pDom.innerHTML = html + pDom.innerHTML;
        // } else {
        //   // pDom.innerHTML = html;
        // }
      }
    }

    watch(
      () => props.item.data,
      () => {
        if (!props.item.data) return;
        checkIfMixinSubjectParent();
        renderChooseDoOrQuesNos();

        //监听分值变化，替换题目内容中分值
        let pDom = document.getElementById('cke_' + props.item.data[0].id + '_0');
        if (!pDom) return;

        // FIXME 同步修改小题标题和分数
        // const sdom = pDom.getElementsByClassName('item-score')[0];
        // sdom && (sdom.textContent = props.item.data[0].score);
        // const ndom = pDom.getElementsByClassName('ques-sort')[0];
        // let quesName = props.item.data[0].quesName || props.item.data[0].quesNos;
        // ndom && (ndom.textContent = quesName);

        setTimeout(() => {
          renderScore();
        }, 100);
      }
    );

    watch(
      () => props.item.score,
      () => {
        if (!props.item.data) return;

        let pDom = document.getElementById('cke_' + props.item.data[0].id + '_0');
        if (!pDom) return;
        const sdom = pDom.getElementsByClassName('item-score')[0];
        sdom && (sdom.textContent = props.item.data[0].score);
      }
    );

    watch(
      () => props.item.quesNos,
      () => {
        if (!props.item.data) return;

        let pDom = document.getElementById('cke_' + props.item.data[0].id + '_0');
        const ndom = pDom.getElementsByClassName('ques-sort')[0];
        let quesName = props.item.data[0].quesNos || props.item.data[0].quesName;
        ndom && (ndom.textContent = quesName);
      }
    );

    watch(
      () => props.item.cellSize,
      () => {
        state.cellSize = props.item.data[0].cellSize;
        changeCellSize();
      }
    );

    checkIfMixinSubjectParent();

    // 是否作文题
    const isWritingQues = computed(() => {
      if (!props.item.isWriting) return false;
      if (!props.item.hasFaceScore) return true;

      let ques = props.item.data[0];
      return !(ques.typeMode && ques.typeMode == SUBJECT_MODE.face);
    });

    // 显示拆分线
    const showSplitLine = computed(() => {
      return (
        !state.isHandCorrent &&
        state.isOnlyCardType &&
        state.isOnlySubjectQues &&
        !state.isMixinSubjectParent &&
        !props.item.isLastIndex
      );
    });

    // 显示简答题拆分线
    const enableSubjectSplitLine = computed(() => {
      let isSplited = props.item.data[0].isSplited;
      return isSplited == null ? true : isSplited;
    });

    // 显示简答题工具
    const showSubjectTools = computed(() => {
      return !state.isMixinSubjectParent;
    });

    const cardHeight = computed(() => {
      return state.splitNodes.length === 1 && state.height != 0 ? state.height + 'mm' : 'auto';
    });

    const questionCard = computed(() => {
      return props.item;
    });

    // 是否显示大标题
    const showBigTitle = computed(() => {
      return props.item.showName == '1' && props.item.hasTitle && !state.isMixinSmallQues;
    });

    // 是否显示编辑按钮
    const showEditTools = computed(() => {
      if (state.isOnlySubjectQues) {
        return props.item.Index != null;
      }
      return true;
    });

    /* 处理页面布局变动事件 */
    const reCaculQuestionPos = async () => {
      state.isObjectiveMerged = Paper.mergeQues;
      state.isA33Layout = checkIsA33Layout();
      await nextTick();
      renderScore();
      renderSubject('reCaculQuestionPos');
    };

    /* 处理拆分卡片的消息 - 跨页拆分 */
    const handlePageSplitCard = async ({
      id,
      splitNodes,
    }: {
      id: string;
      splitNodes: SplitNodes[];
    }) => {
      if (props.qId !== id) return;

      destroyCKEditors(splitNodes.map(item => item.id));
      state.splitNodes = splitNodes;
      state.refreshKey++;
      await nextTick();

      renderSubject('handlePageSplitCard');
      getEditorBox().forEach(initEditor);
    };

    /* 合并所有拆分的卡片 - 跨页合并 */
    const mergeAllPageSplitedCards = async (id: string) => {
      if ((id !== null && props.qId !== id) || state.splitNodes.length === 1) return;

      // 销毁第一个之外的分段
      let firstEditor = ckeditorList[0];
      ckeditorList.forEach((editor: any, index: number) => {
        if (index === 0) return;

        while (
          firstEditor.element.$.firstElementChild &&
          firstEditor.element.$.childElementCount > 1 &&
          !firstEditor.element.$.firstElementChild.className?.includes('subject-para-p')
        ) {
          if (firstEditor.element.$.firstElementChild.className?.includes('dynamic')) return;

          firstEditor.element.$.firstElementChild.remove();
        }
        // 将所有分段的dom内容合并到一起
        editor.element.getChildren().$.forEach((child: HTMLElement, cIndex: number) => {
          // 如果第一个元素为动态插入的元素,如作文格，需要手动拼接到第一个分段内
          if (cIndex === 0 && child.className?.includes('dynamic') && !isChildDomEmpty(child)) {
            let dynamics = firstEditor.element.$.querySelectorAll('.dynamic');
            let $lastDynamic = dynamics.length ? dynamics[dynamics.length - 1] : null;
            if ($lastDynamic) {
              while (child.firstElementChild) {
                $lastDynamic.appendChild(child.firstElementChild);
              }
              return;
            }
          }
          firstEditor.element.$.appendChild(child.cloneNode(true));
        });

        editor.destroy();
      });
      ckeditorList.splice(1, ckeditorList.length - 1);

      let firstNode = deepClone(state.splitNodes[0]);
      firstNode.height = getSplitSumQuesH() + 'mm';
      state.splitNodes = [firstNode];
      state.refreshKey++;

      await nextTick();
      renderSubject('mergeAllPageSplitedCards');
    };

    const renderScore = () => {
      state.isOnlyCard = Paper.cardType != ICARDMODEL.QUESCARD;
      state.isHandCorrent = Paper.correctType == ICorrectType.HAND;

      let markType = props.item.data[0].markType;
      const splitEls = document.querySelectorAll(`[qid="${props.item.data[0].id}"]`);
      let quesEl: any = null;
      Array.from(splitEls).forEach((item: any) => {
        item?.getElementsByClassName('score-container')[0]?.remove();
        if (Paper.correctType == ICorrectType.WEB) return;

        if (item.getElementsByClassName('ques-box').length) {
          if (markType == MARK_TYPE.YESORNO) {
            quesEl = item;
          } else {
            !quesEl && (quesEl = item);
          }
        }
      });

      if (!markType) return;
      if (Number(props.item.data[0].score) == 0) return;

      let el = document.createElement('span');
      el.className = 'score-container noeditsave before';
      if (splitEls.length > 1 && markType != MARK_TYPE.YESORNO) {
        el.className += ' remove-ques';
      }
      let subItem = props.item.data[0];

      render(
        h(MarkScore, {
          typeId: props.item.typeId,
          'mark-type': markType,
          score: Number(subItem.score),
          step: subItem.step || props.item.step,
          isSplit: (subItem.gridType || props.item.gridType) == GRID_TYPE.YES,
          isDecimal: (subItem.decimal || props.item.decimal) == DECIMAL_TYPE.HAVE,
          id: subItem.id,
        }),
        el
      );

      if (markType == MARK_TYPE.YESORNO) {
        el.className += ' mark-list after';
      } else if (markType == MARK_TYPE.WRITE) {
        el.className += ' write-score';
      }

      quesEl?.insertBefore(el, quesEl.firstElementChild);
    };

    /**
     * @description: 确认创建作文格
     * @return {*}
     */
    const confirmCreatCells = async () => {
      state.showCreateCellsDialog = false;
      let isUpdated = await checkUpdateSubjectHeight();
      if (isUpdated) bus.emit('renderAllCard');
    };

    /* 初始化CKEDitor */
    const initEditor = ($el: any) => {
      for (const editor of ckeditorList) {
        if ($el.dataset.id === editor.element.data('id')) return;
      }

      // 超过单页的需要参与分页，不绑定编辑器
      if (pxConversionMm($el.offsetHeight) > pageHeight) return;

      $el.setAttribute('contenteditable', 'true');
      const editor = CKEDITOR.inline($el);
      if (!editor) return;

      editor.on('instanceReady', async function (event: any) {
        let _editor = event.editor;
        let editorIndex = _editor.element.data('index');
        let $dom = _editor.element.$;

        let splitItem = state.splitNodes[editorIndex];
        if (
          state.isOnlySubjectQues &&
          state.splitNodes.length > 1 &&
          splitItem.splitDoms &&
          splitItem.splitDoms.childNodes.length
        ) {
          $dom.innerHTML = '';
          $dom.appendChild(splitItem.splitDoms);
          // 简答题的横线模式，在跨页后首行横线的高度调整往下一点，方便书写
          $dom.style['padding-top'] = props.item.hasLine ? '3.2mm' : 0;
        }
        if (state.editContent || state.instanceReady) return;

        let needFullBlank = editorIndex == 0 && $dom.childElementCount === 1;
        if (needFullBlank) {
          // 补充首行空格方便编辑
          fullEditBlank($dom);
        }
        state.instanceReady = true;

        let removeNodes = _editor.element.find('p.remove-node').$ || [];
        let removeBr = _editor.element.find('br').$ || [];

        removeNodes.forEach($el => $el.remove());
        removeBr.forEach($el => $el.remove());

        _editor.element.removeAttribute('title');
      });

      editor.on('blur', handleEditorBlur);

      editor.on('change', function (event: any) {
        // 【偶现编辑器莫名在body前加入&ZeroWidthSpace占位字符】
        try {
          const htmlChildrenNodes = document.getElementById('nav')?.childNodes;
          if (htmlChildrenNodes) {
            const nodeLen = htmlChildrenNodes?.length || 0;
            for (let i = 0; i < nodeLen; i++) {
              const text = htmlChildrenNodes[i];
              if (text.nodeType && text.nodeType == 3) {
                text.textContent = text?.textContent?.replace(/[\u200B-\u200D\uFEFF]/g, '') || '';
              }
            }
          }
        } catch (e) {
          console.log(e);
        }
      });

      editor.on('insertWritingCells', async () => {
        await nextTick();

        let isUpdated = await checkUpdateSubjectHeight();
        if (isUpdated) bus.emit('renderAllCard');
      });

      ckeditorList.push(editor);
    };

    const handleEditorOver = () => {
      if (!isWritingQues.value || state.splitNodes.length === 1) return;

      // 鼠标经过时重新检测作文格的渲染数量，防止意外多渲染的问题
      const splitEls = getEditorBox();
      if (!splitEls.length) return;

      let $dom = splitEls[0];
      let domHeight = getAccHeightByMM($dom);

      let isA33Card = checkIsA33Layout();
      let spliteNode = state.splitNodes[0];
      let isA33Split =
        splitEls.length > 1 ? spliteNode.width && spliteNode.width === '130mm' : isA33Card;
      let { tdH } = getWritingRowCell(isA33Split);
      let rowNum = Math.trunc(domHeight / tdH);
      // 获取现有格子dom数量
      let lineDoms = $dom.querySelectorAll('.writing-line');

      if (lineDoms.length <= rowNum) return;

      renderSubject('handleEditorOver');
    };

    /* 编辑失焦后检查简答题高度 */
    const handleEditorBlur = async (event: any) => {
      let $editor = event.editor.element;
      if ($editor) $editor.$.scrollTop = 0;

      if (isWritingQues.value) {
        // 作文题
        checkUpdateWritingHeight();
      }
    };

    /**
     * @description: 检查普通简答题高度并更新合理的高度值
     * @return {boolean} 是否更新
     */
    const checkUpdateSubjectHeight = async (): Promise<boolean> => {
      const splitEls = getEditorBox();
      if (!splitEls.length) return false;

      let $dom = splitEls[0];
      let domHeight = getAccHeight($dom);
      let childsH = 0;
      Array.from($dom.children).forEach((child: HTMLElement) => (childsH += child.offsetHeight));
      if (domHeight >= childsH) return false;

      state.splitNodes[0].height = `${pxConversionMm(childsH)}mm`;
      await nextTick();
      setQuesCardHeight();
      state.refreshKey++;
      renderSubject('checkUpdateSubjectHeight');

      return true;
    };

    /**
     * @description: 检查作文题高度并更新合理的高度值
     * @return {*}
     */
    const checkUpdateWritingHeight = () => {
      const splitEls = getEditorBox();
      if (!splitEls.length) return;

      // 作文题
      if (state.splitNodes.length === 1) return;

      let $dom = splitEls[0];
      let domHeight = getAccHeightByMM($dom);
      let isA33Card = checkIsA33Layout();
      let spliteNode = state.splitNodes[0];
      let isA33Split =
        splitEls.length > 1 ? spliteNode.width && spliteNode.width === '130mm' : isA33Card;
      let { tds, tdH } = getWritingRowCell(isA33Split);
      // 累加的标题高度
      let totalTitleH = 0;
      $dom.querySelectorAll('.subject-para-p').forEach(($el: any) => {
        totalTitleH += pxConversionMm($el.offsetHeight);
      });
      let rowNum = Math.trunc((domHeight - totalTitleH) / tdH);

      // 获取现有格子dom数量
      let lineDoms = $dom.querySelectorAll('.writing-line');
      if (lineDoms.length === rowNum) return;
      renderSubject('checkUpdateWritingHeight');
    };

    /**
     * @description: 转换数学公式
     * @param {*} content
     * @return {*}
     */
    const converMathtex = (content: string) => {
      if (content.indexOf('ck-math-tex') < 0) {
        //移除换行标签，避免正则识别失效，
        content = content
          .replace(/\n/g, ' ')
          .replace(/\\\n/g, ' ')
          .replace(
            /\\\((.*?)\\\)/g,
            '<span tex="\\($1\\)" contenteditable="false" class="ck-math-tex">\\($1\\)</span>'
          );
      }
      return content;
    };

    /* 销毁所有编辑器 */
    const destroyCKEditors = (exIds: string[] = []) => {
      if (!exIds.length) {
        ckeditorList.forEach(editor => editor.destroy());
        ckeditorList = [];
      } else {
        ckeditorList.forEach((editor, index) => {
          if (exIds.includes(editor.element?.data('id'))) return;

          editor.destroy();
          ckeditorList.splice(index, 1);
        });
      }
    };
    const destroyCKEditorById = (id: string) => {
      ckeditorList.forEach((editor, index) => {
        if (editor.element.data('id') !== id) return;

        editor.destroy();
        ckeditorList.splice(index, 1);
      });
    };

    const getEditorBox = () => {
      let $doms = document.querySelectorAll(`[data-tag="${state.boxTag}"]`);
      return Array.from($doms);
    };

    /* 初始化所有编辑器 */
    const initCKEditors = () => {
      destroyCKEditors();
      getEditorBox().forEach(initEditor);
    };

    /* 获取作文题排列数 */
    const getWritingRowCell = (isA33Layout: boolean) => {
      let tds = 0,
        rows = 0,
        // 单元格高度 +2上下边距
        tdH = 0,
        wordNumber: number = props.item.data[0].wordNumber;

      if (state.cellSize == 'small') {
        tds = isA33Layout ? 18 : 26;
        tdH = 9;
      } else if (state.cellSize == 'big') {
        tds = isA33Layout ? 13 : 20;
        tdH = 11.5;
      } else {
        tds = isA33Layout ? 15 : 23;
        tdH = 10;
      }
      // 总行数多渲染一行，防止跨页后不足的高度限制
      rows = Math.ceil(wordNumber / tds) + 1;

      // 兼容：保留当前赋值
      props.item.rowNum = rows;
      props.item.cells = tds;

      return { rows, tds, tdH };
    };

    /* 获取作文题高度和 */
    const getWirtingSumHeight = spliteLayout => {
      let { rows, tdH } = getWritingRowCell(spliteLayout);

      // TIP：多渲染两行，方便拆分后补充剩余高度
      return (rows + 2) * tdH;
    };

    /* 处理触底事件 */
    const handleReachBottom = async ({ id, pageSize }: { id: string; pageSize: number }) => {
      if (props.qId !== id) return;

      await nextTick();
      if (state.splitNodes.length === 1) {
        setHeight('full', 0, pageSize, true);
      } else {
        setHeight('full', state.splitNodes.length - 1, pageSize, true);
      }

      // console.log('reachBottom', id);
    };

    /* 处理卡片布局切换事件：可用于正3反2 */
    const handleCardLayoutChange = async (id: string) => {
      if (id !== props.qId || !isWritingQues.value) return;

      await nextTick();
      // 应用于作文题的转换
      renderSubject('handleCardLayoutChange');
    };

    /**
     * 页面一开始加载
     */
    onMounted(async () => {
      bus.on('cardLayoutChange', handleCardLayoutChange);
      bus.on('splitPageCard', handlePageSplitCard);
      bus.on('mergePageSplitedCard', mergeAllPageSplitedCards);
      bus.on('reachBottom', handleReachBottom);
      bus.on('switchCorrect', renderScore);

      Paper.on('changeLayout', reCaculQuestionPos);
      Paper.on('changeCardType', reCaculQuestionPos);
      Paper.on('updateAllQustions', reCaculQuestionPos);
      Paper.on('changeNumberLayout', reCaculQuestionPos);

      state.isA33Layout = checkIsA33Layout();
      renderScore();
      await nextTick();
      let editorHeight = props.item.data[0].height;
      if (editorHeight) {
        // 兼容不小心保存为0的情况
        state.height = editorHeight.replace('mm', '');
        state.splitNodes[0].height =
          editorHeight === '0mm' ? '60mm' : getSplitSumQuesH(state.height) + 'mm';
      } else if (isWritingQues.value) {
        let isA33Card = checkIsA33Layout();
        state.splitNodes[0].height = getWirtingSumHeight(isA33Card) + 'mm';
        props.item.data[0].height = state.splitNodes[0].height;
      }
      state.refreshKey++;
      await nextTick();

      renderSubject('onMounted');
      await nextTick();
      initCKEditors();
    });

    onBeforeUnmount(() => {
      destroyCKEditors();

      bus.off('cardLayoutChange', handleCardLayoutChange);
      bus.off('reachBottom', handleReachBottom);
      bus.off('switchCorrect', renderScore);
      bus.off('splitPageCard', handlePageSplitCard);
      bus.off('mergePageSplitedCard', mergeAllPageSplitedCards);

      Paper.off('changeLayout', reCaculQuestionPos);
      Paper.off('changeCardType', reCaculQuestionPos);
      Paper.off('updateAllQustions', reCaculQuestionPos);
      Paper.off('changeNumberLayout', reCaculQuestionPos);
    });

    /* 同步设置到源数据 */
    const syncSettingToOrigin = (qId: string, key: string, value: any) => {
      if (Paper.cardType != ICARDMODEL.ONLYCARD) return;

      let qIndex = Paper.findBigIndexByQuesId(props.item.parentId || props.item.id);
      if (qIndex === -1) return;

      let qItem = Paper.getQuesInfos()[qIndex].data.find(subItem => qId === subItem.id);
      if (qItem) qItem[key] = value;
    };

    /* 设置题目卡片的总高度 */
    const setQuesCardHeight = () => {
      let offsetHeight = getAccHeight(quesCardRef.value);
      let splitorDoms = quesCardRef.value.querySelectorAll('.page-splitor');
      let splitorDomsHeight = 0;
      if (splitorDoms.length) {
        Array.from(splitorDoms).forEach(($dom: HTMLElement) => {
          splitorDomsHeight += getAccHeight($dom);
        });
      }
      let tHeight = pxConversionMm(offsetHeight - splitorDomsHeight);

      props.item.data[0].height = tHeight + 'mm';
      state.height = tHeight;
      syncSettingToOrigin(props.item.id, 'height', props.item.data[0].height);

      return tHeight;
    };

    /* 获取跨页拆分的高度和 */
    const getSplitSumQuesH = (tHeight?: number) => {
      tHeight = tHeight || setQuesCardHeight();
      let $dom = quesCardRef.value;
      // 减去分数和标题栏高度，手写打分栏不参与计算
      let $score = $dom.getElementsByClassName('score-container');
      if ($score.length && !$score[0].classList.contains('write-score')) {
        tHeight -= getAccHeightByMM($score[0]);
      }
      let $chooseWarp = $dom.getElementsByClassName('choose-ques-content');
      if ($chooseWarp.length) {
        tHeight -= getAccHeightByMM($chooseWarp[0]);
      }
      let $title = getNameElement($dom);
      if ($title) {
        tHeight -= getAccHeightByMM($title);
      }

      return tHeight;
    };

    /**
     * @name 检查是否A33布局
     */
    const checkIsA33Layout = () => {
      let pageLayout = Number(Paper.pageLayout);
      if ([IPAGELAYOUT.A4, IPAGELAYOUT.A3, IPAGELAYOUT.A33].includes(pageLayout))
        return [IPAGELAYOUT.A33].includes(pageLayout);

      let isA33Card = [IPAGELAYOUT.A33].includes(pageLayout);
      let $cardDom: any = document.getElementById(props.qId);
      if (!$cardDom) return isA33Card;

      if ($cardDom.style.width) {
        isA33Card = $cardDom.style.width === PaperConstant.WIDTH_A33;
      } else if ($cardDom.previousElementSibling.style.width) {
        isA33Card = $cardDom.previousElementSibling.style.width === PaperConstant.WIDTH_A33;
      }

      return isA33Card;
    };

    /**
     * @name 简答题渲染
     */
    const renderSubject = (from: string = 'normal') => {
      // console.log('renderSubject: ', from);
      const splitEls = getEditorBox();
      if (!splitEls.length) return;

      let isA33Card = checkIsA33Layout();
      // 所有分段高度和
      const domSplitsHeight = getPxConversionMm(splitEls.reduce((ac, cu) => ac + getAccHeight(cu), 0));
      if (isWritingQues.value) {
        // 单元格累计渲染位置索引
        let wordStartIndex = 0;
        splitEls.forEach(($dom: any, index: number) => {
          let spIndex = $dom.dataset.index;
          const domHeight = getAccHeightByMM($dom);

          let spliteNode = state.splitNodes[spIndex];
          let isA33Split =
            splitEls.length === 1 || !spliteNode.width ? isA33Card : spliteNode.width === '130mm';

          let $writingDom = $dom.getElementsByClassName('writing-container');
          let el = $writingDom.length ? $writingDom[0] : null;
          let isNewELement = !el;
          console.debug("isWritingQues", { isNewELement })

          // 格子高度mm
          let { tds, tdH } = getWritingRowCell(isA33Split);
          // 累加的标题高度
          let totalTitleH = 0;
          $dom.querySelectorAll('.subject-para-p').forEach($el => {
            totalTitleH += pxConversionMm($el.offsetHeight);
          });

          let rowNum = Math.trunc((domHeight - totalTitleH) / tdH);
          // 获取现有格子dom数量
          let lineDoms = $dom.querySelectorAll('.writing-line');

          if (!isNewELement && index === 0 && lineDoms.length > rowNum) {
            Array.from(lineDoms).forEach(($line: HTMLElement, index: number) => {
              if (index < rowNum) return;

              $line.remove();
            });
            $dom.dataset.wordindex = wordStartIndex;
            wordStartIndex += rowNum * tds;
            return;
          }
          if (rowNum <= 0) return;

          if (isNewELement) {
            el = document.createElement('div');
            el.className = 'writing-container';
            el.dataset.index = spIndex;

            render(
              h(SubjectWriting, {
                cellSize: state.cellSize,
                cells: props.item.cells,
                rowNum: rowNum,
                startIndex: wordStartIndex,
              }),
              el
            );
            $dom.appendChild(el);
            wordStartIndex += rowNum * tds;
          } else {
            Array.from($writingDom).forEach(($el: HTMLElement) => {
              if ($el.className.includes('dynamic')) return;

              $el.remove();
            });

            el = document.createElement('div');
            el.className = 'writing-container';
            el.dataset.index = spIndex;

            if (index === 0) {
              wordStartIndex = 0;
              $dom.dataset.wordindex = wordStartIndex;
              render(
                h(SubjectWriting, {
                  cellSize: state.cellSize,
                  cells: props.item.cells,
                  rowNum,
                  startIndex: wordStartIndex,
                }),
                el
              );

              $dom.appendChild(el);
              wordStartIndex = rowNum * tds;
            } else {
              // rowNum -= lineDoms.length;
              rowNum = Math.trunc(domHeight / tdH);
              render(
                h(SubjectWriting, {
                  cellSize: state.cellSize,
                  cells: props.item.cells,
                  rowNum,
                  startIndex: wordStartIndex,
                }),
                el
              );

              $dom.appendChild(el);
              wordStartIndex += rowNum * tds;
            }
          }
        });

      } else if (props.item.hasLine && state.isLineCreating) {
        console.debug("hasLine", { isLineCreating: state.isLineCreating })

        // 渲染横线
        let $dom: any = splitEls[0];
        let spliteNode = state.splitNodes[0];
        let isA33Split =
          splitEls.length === 1 || !spliteNode.width ? isA33Card : spliteNode.width === '130mm';

        // 线条高度mm
        const LINE_HEIGHT = 9.25;
        let titleH = getPxConversionMm($dom.firstElementChild ? $dom.firstElementChild.offsetHeight : 0);
        let rowNum = Math.floor((domSplitsHeight - titleH) / LINE_HEIGHT);
        props.item.rowNum = rowNum;

        state.isLineCreating = false;
        const oneLineBlankFull = isA33Split ? 66 : 100;
        let total = oneLineBlankFull * rowNum;

        const oneLineBlank = isA33Split ? 11 : 10;
        let count = oneLineBlank;
        let blanks = "";
        while (count > 0) {
          blanks += '&ensp;';
          count--;
        }

        let lines = Math.round(total / oneLineBlank);
        let underline = "";
        while (lines > 0) {
          underline += `<span style="line-height:2.5;"><u class="split-tag" style="font-family: Times New Roman;">${blanks}</u></span>`
          lines--;
        }

        const underlineContainer = `<p class="subject-para-p dynamic">${underline}</p>`
        const parser = new DOMParser();
        const doc = parser.parseFromString(underlineContainer, 'text/html');
        const $underlineContainer = doc.body.firstElementChild;

        $dom.appendChild($underlineContainer);
      }

    };

    let startY = 0;
    let startHeight = 0;
    let startSpIndex = 0;
    let $dragDom = document.getElementById(`cke_${props.qId}_${startSpIndex}`);

    const startDrag = (e: MouseEvent, spIndex: number) => {
      startSpIndex = spIndex;
      startY = e.clientY;
      $dragDom = document.getElementById(`cke_${props.qId}_${startSpIndex}`);

      startHeight = getAccHeight($dragDom);
      document.addEventListener('mousemove', handleDrag);
      document.addEventListener('mouseup', stopDrag);
    };

    let newHeight = 0;
    let isDrag = false;
    function handleDrag(e: MouseEvent) {
      const deltaY = e.clientY - startY;
      newHeight = startHeight + deltaY;
      newHeight = newHeight <= 2 ? 0 : newHeight;
      if (startSpIndex === 0) {
        newHeight = newHeight <= 50 ? 50 : newHeight;
      }
      $dragDom.style.height = newHeight + 'px';
      isDrag = Math.abs(deltaY) > 2;
    }

    async function stopDrag() {
      document.removeEventListener('mousemove', handleDrag);
      document.removeEventListener('mouseup', stopDrag);

      if (!isDrag) {
        $dragDom.style.height = startHeight + 'px';
        return;
      }

      isDrag = false;
      await checkUpdateSubjectHeight();
      state.splitNodes[startSpIndex].height = `${pxConversionMm(newHeight)}mm`;
      await nextTick();

      setQuesCardHeight();
      // 高度小于0.1mm直接删除当前分段
      if (newHeight <= 0.1) state.splitNodes.splice(startSpIndex, 1);
      await nextTick();

      renderSubject('stopDrag');
      await nextTick();
      bus.emit('renderAllCard');
      startSpIndex = 0;
    }

    const getConetntHeight = () => {
      //页面高度 - 首尾高度
      let height = pageHeight - footerKeepHeight * 2;
      const dom = document.getElementById(props.item.data[0].id);
      const page = parseInt(dom?.getAttribute('page') || '1');
      //当前页是否包含头部信息
      let hasInfo = true;
      if (Paper.pageLayout == IPAGELAYOUT.A4) {
        hasInfo = page % 2 == 1;
      } else if (Paper.pageLayout == IPAGELAYOUT.A3) {
        hasInfo = page % 4 == 1;
      } else {
        hasInfo = page % 6 == 1;
      }
      //减去头部信息高度
      if (hasInfo) {
        height -= getHeaderInfoHeight();
      }
      return height;
    };

    //获取当前题目对象，不包含分割的
    const getCurQuesDom = (id: string) => {
      const els = document.querySelectorAll(`[id="${id}"]`);
      return els[els.length - 1] as HTMLElement;
    };

    const setHeight = async (
      type: 'half' | 'full',
      spIndex: number,
      pagesize: number,
      isSupple: boolean = false
    ) => {
      const pDom = getCurQuesDom(props.qId);
      let dom = document.getElementById(`cke_${props.qId}_${spIndex}`);
      let height = getConetntHeight();
      let scoreH = 0;
      try {
        let $score: any = pDom.getElementsByClassName('score-table');
        if ($score.length) {
          scoreH += getAccHeightByMM($score[0]);
        }
        let $chooseWarp = pDom.getElementsByClassName('choose-ques-content');
        if ($chooseWarp.length) {
          scoreH += getAccHeightByMM($chooseWarp[0]);
        }
        let $title = getNameElement(pDom);
        if ($title) {
          scoreH += getAccHeightByMM($title);
        }
      } catch (error) { }

      if (type == 'half') {
        height = height / 2 - scoreH;
      } else {
        if (!pagesize || spIndex === 0) {
          pagesize = Number(pDom.getAttribute('page'));
        }
        let offsetTop = pxConversionMm(pDom.offsetTop);
        if (!dom) {
          await nextTick();
          dom = document.getElementById(`cke_${props.qId}_${spIndex}`);
        }

        if (getAccHeight(pDom) - getAccHeight(dom) > 3 && spIndex > 0) {
          // 当拆分组件和父组件不相等，组件认定为已拆分，当前组件索引大于0
          offsetTop = pxConversionMm(pDom.offsetTop + (getAccHeight(pDom) - getAccHeight(dom)));
        }

        offsetTop = offsetTop - pageHeight * (pagesize - 1);
        height = pageHeight - footerKeepHeight - offsetTop;
        if (spIndex === 0) {
          height -= scoreH;
        }
      }

      let splistHeight = Number(state.splitNodes[spIndex].height.replace('mm', ''));
      let differ = Math.abs(splistHeight - height);
      if (differ <= 0.3) return;
      // 当补充的高度超过定值，去除所有补充高度
      if (isSupple && differ > 7) return;

      state.splitNodes[spIndex].height = `${height}mm`;
      await nextTick();
      setQuesCardHeight();
      state.refreshKey++;
      await nextTick();

      renderSubject('setHeight');
      await nextTick();

      !isSupple && bus.emit('renderAllCard');
    };

    const editQues = () => {
      let qIndex = Paper.findBigIndexByQuesId(props.item.parentId || props.item.id);
      let qItem = Paper.getQuesInfos()[qIndex];

      bus.emit('editQues', { item: deepClone(qItem), index: qIndex });
    };

    /* 切换作文格尺寸 */
    const onChangeCellSize = async (subId: string) => {
      state.cellSize = props.item.data[0].cellSize;
      changeCellSize();
      let smallQues = Paper.findSmallQuesByQuesId(subId);
      smallQues.cellSize = state.cellSize;

      Paper.notifyUpdateData('card');
    };

    const changeCellSize = async () => {
      // 清理已经存在的作文格
      Array.from(quesCardRef.value.getElementsByClassName('writing-container')).forEach(
        ($el: HTMLElement) => {
          if ($el.className.includes('dynamic')) return;

          $el.remove();
        }
      );
      renderSubject('changeCellSize');
      await nextTick();

      // 删除切换后产生的多余P标签
      let editor = ckeditorList[0];
      if (!editor) return;
      let removeNodes = editor.element.find('p').$ || [];
      removeNodes.forEach(($el, index: number) => {
        if (index === 0) return;

        $el.remove();
      });
    };

    /* 拆分题目 */
    const splitSubjectiveQuestions = () => {
      let ques = props.item;
      let qId = ques.parentId || ques.id;

      if (!props.item.mixinMode) qId = ques.id;

      Paper.splitSubjectiveQuestions(qId);
      Paper.notifyUpdateData('right');

      setTimeout(function () {
        bus.emit('renderAllCard');
      }, 10);
    };

    /* 获取源数据中的大题对象 */
    const getBigquesFromOrigin = () => {
      let ques = props.item.data[0];
      let qId = ques.parentId || ques.id;
      let index = Paper.findBigIndexByQuesId(qId);

      return {
        qId,
        bigQues: Paper.getQuesInfos()[index],
      };
    };

    /* 合并主观题 */
    const mergeSubjectiveQuestions = async (subIndex?: number) => {
      let ques = props.item;
      let { qId, bigQues } = getBigquesFromOrigin();
      if (bigQues) {
        subIndex = bigQues.data.findIndex(subItem => subItem.id == ques.data[0].id);

        renderSubject('mergeSubjectiveQuestions1');
        Paper.mergeSubjectiveQuestions(qId, subIndex);
        Paper.notifyUpdateData('right');
      } else {
        ques.data[0].isSplited = false;
        renderSubject('mergeSubjectiveQuestions2');
      }

      setTimeout(function () {
        bus.emit('renderAllCard');
      }, 10);
    };

    const convertSort = (sort: number) => {
      return arabToChinese(sort);
    };

    /**
     * @name 简答题有无下划线
     */
    const changeLine = async (item: any, subItem: any, spItem: any) => {
      let hasLine = !item.hasLine;
      item.hasLine = hasLine;
      state.isLineCreating = hasLine;
      let hasEditConent = !!state.editContent;
      // 如果有内容，清空后组件会被更换，需要重新绑定
      if (hasEditConent) destroyCKEditors();
      state.editContent = '';
      await nextTick();

      item.isWriting = false;
      subItem.hasLine = hasLine;
      subItem.isWriting = false;
      if (subItem.data) {
        subItem.data[0].hasLine = hasLine;
        subItem.data[0].isWriting = false;
      }
      syncSettingToOrigin(props.item.id, 'hasLine', hasLine);

      // 切换前先清空横线
      const splitEls = getEditorBox();
      splitEls.forEach(($dom, index) => {
        Array.from($dom.children).forEach(($child, cIndex) => {
          if (index === 0 && cIndex === 0) return;

          $child.remove();
        });
        if (!hasLine && !hasEditConent && $dom.childElementCount === 1) {
          // 补充首行空格方便编辑
          fullEditBlank($dom);
        }
      });
      renderSubject('changeLine');

      await nextTick();
      // 如果之前有内容，清空后组件会被更换，需要重新绑定
      if (hasEditConent) {
        getEditorBox().forEach(initEditor);
      }
      bus.emit('renderAllCard');
    };

    /**
     * @description: 补充首行空格方便编辑
     * @return {*}
     */
    const fullEditBlank = ($dom: Element) => {
      var blank = document
        .createRange()
        .createContextualFragment('<div class="subject-para-p"><br/></div>').firstElementChild;
      $dom.appendChild(blank);
    };

    /**
     * @name 始终控制简答题内的滚动在顶部
     */
    const handleSubjectBoxScoll = event => {
      event.target.scrollTop = 0;
    };

    return {
      ...toRefs(state),
      enableSubjectSplitLine,
      showSubjectTools,
      questionCard,
      showEditTools,
      quesCardRef,
      cardHeight,
      showBigTitle,
      isWritingQues,
      showSplitLine,
      editQues,
      setHeight,
      startDrag,
      changeLine,
      convertSort,
      converMathtex,
      renderSubject,
      handleEditorOver,
      handleEditorBlur,
      onChangeCellSize,
      confirmCreatCells,
      handleSubjectBoxScoll,
      mergeSubjectiveQuestions,
      splitSubjectiveQuestions,
    };
  },
});
</script>

<style lang="scss" scoped>
.q-opt.onlycard {
  ::v-deep {
    .writing-container {
      display: inline-block;
      word-break: break-all;
      text-align: center;
      width: 100%;
    }
  }

  &:hover {
    ::v-deep .ques-content-name {
      .edit-button-tools {
        display: block;
      }
    }
  }
}

.subject-box {
  padding: unset;
  position: relative;
  overflow: hidden;
  text-align: center;

  ::v-deep {
    >* {
      text-align: left;
    }

    .ques-sort {
      width: auto;
      font-size: inherit;
    }
  }
}
</style>

<style lang="scss">
.score-container.mark-list {
  position: absolute;
  bottom: 3mm;
  background: white;
  right: 3mm;
  margin: 0 0 0 1mm !important;
  z-index: 100;
}

.score-container.write-score {
  position: absolute;
  right: 0;
}
</style>
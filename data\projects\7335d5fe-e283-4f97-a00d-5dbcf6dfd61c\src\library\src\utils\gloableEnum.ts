/*
 * @Descripttion: 全局公共枚举类
 * @Author: liuyu<PERSON>
 * @Date: 2021-04-09 10:52:32
 * @LastEditors: liuyue
 * @LastEditTime: 2021-04-09 11:12:21
 */

/** 管理员类型 */
export enum ADMIN_TYPE {
  /** 非管理员 */
  NOADMIN,
  /** 总管理员 */
  SYSTEMADMIN,
  /** 校级管理员 */
  SCHOOLADMIN,
  /** 班级管理员 */
  CLASSADMIN,
  /** 区域管理员 */
  REGIONADMIN,
}
/** 用户类型 */
export enum USER_TYPE {
  /** 注册用户 */
  REGISTER,
  /** 教师 */
  TEACHER,
  /** 学生 */
  STUDENT,
  /** 家长 */
  PARENT,
  /** 代理商 */
  AGENT,
  /** 运营 */
  OPERATE,
  /** 区域用户 */
  REGION,
}
/** 学段类型 */
export enum PHASE_TYPE {
  /** 小学 */
  PRIMARY = 1,
  /** 中学 */
  MIDDLE = 2,
  /** 高中 */
  HEIGHT = 3,
  /** 中职 */
  VOCATIONAL = 4,
}
/** 导出类型 */
export enum EXPORT_TYPE {
  /** 普通作业导出 */
  HOMEWORk = "001",
  /** 考试导出 */
  EXAM = "002",
  /** 问卷调查未提交情况导出 */
  NOTSUBMIT = "003",
  /** 问卷调查客观题导出 */
  OBJECT = "004",
  /** 问卷调查主观题导出 */
  SUBJECT = "005",
  /** 单份班级作业导出 */
  CLASS_HOME = "006",
}
/** 区域归属类型 */
export enum REGION_TYPE {
  /** 省 */
  PROVINCE = "1",
  /** 市 */
  CITY = "2",
  /** 区 */
  DISTRICT = "3",
  /** 自定义 */
  CUSTOM = "4",
  /** 校级 */
  SCHOOL = "5",
}
/** 文件类型 */
export enum FILE_TYPE {
  // 其他
  OTHER,
  // 文档
  DOC,
  // 视频
  VIDEO,
  // 音频
  AUDIO,
  // 图片
  IMG,
}
/**
* @name 文件转化状态
* */
export enum CONVER_STATE {
  /** 转化失败 */
  FAIL = -2,
  /** 转化中 */
  WAIT = -1,
  /** 待转化 */
  START = 0,
  /** 转化成功 */
  SUCCESS = 1
}
/** 平台类型 */
export enum PLATFORM_TYPE {
  /** 区域平台 */
  REGION = "1",
  /** 校级平台 */
  SCHOOL = "2",
}
/** 平台来源类型 */
export enum SOURCE_TYPE {
  /** 区域平台 */
  REGION = "",
  /** 研修平台 */
  RESEARCH = "research",
  /** 教研平台 */
  TEARESEARCH = "tearesearch",
  /** 资源平台 */
  RESOURCE = "resource",
  /** 数据平台 */
  DATA = "data",
}
/* 所有学段类型 */
export enum ALL_PHASE_TYPE {
  //全部
  all = "-1",
  //幼儿园
  kinder = "0",
  //小学
  primary = "1",
  //初中
  junior = "2",
  //高中
  high = "3",
  //中职
  secondary = "4",
  //高职
  vocational = "5",
  //大学
  university = "6",
  //特殊学校
  special = "7",
  // 其他
  other = "8",
}
/* 文件转化状态 */
export enum CONVERT_STATE {
  /**转换失败 */
  FAIL = -2,
  /**转换中 */
  ING = -1,
  /**待转换 */
  WAIT = 0,
  /**转换成功 */
  SUCCESS = 1
}

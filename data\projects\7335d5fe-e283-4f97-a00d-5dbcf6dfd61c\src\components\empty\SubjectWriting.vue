<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2024-03-11 16:09:38
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-02-28 10:46:17
-->
<template>
  <div class="writing-line split-tag" data-index="0" :class="`writing-line-${rindex}`" v-for="(rows, rindex) in rowNum"
    :key="rindex">
    <div class="writing-cell"
      :class="[`writing-cell-${cellSize}`, { 'writing-cell--mark': (startIndex + cells * rindex + (cindex + 1)) % 100 === 0 }]"
      :aria-colcount="startIndex + cells * rindex + (cindex + 1)" v-for="(cItem, cindex) in cells" :key="cindex"><br>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  //题干信息
  item: {
    type: Object,
    default: () => {
      return {};
    },
  },
  // 格子尺寸
  cellSize: {
    type: String,
    default: 'medium',
  },
  // 格子列数
  cells: {
    type: Number,
    default: 18,
  },
  // 格子行数
  rowNum: {
    type: Number,
    default: 0,
  },
  startIndex: {
    type: Number,
    default: 0,
  },
})
</script>

<style lang="scss">
.writing-line {
  width: 100%;
  padding: 1mm 0;
  margin: 0;
  line-height: 7mm;
}

.writing-cell {
  position: relative;
  margin: 0;
  padding: 0;
  border: 1px solid #999;
  display: inline-block;
  vertical-align: bottom;
  font-size: 4.2mm;
  border-right: 0px;
  text-align: center;

  &:last-child {
    border-right: 1px solid #999 !important;
  }

  &.writing-cell--mark::after {
    content: "▲" attr(aria-colcount)"字";
    position: absolute;
    font-size: 12px;
    zoom: .7;
    word-break: keep-all;
    top: 100%;
    left: 0;
    line-height: normal;
  }
}

.writing-mark {
  font-size: 12px;
  transform: scale(0.7);
  height: 1mm;
  word-break: keep-all;
  position: absolute;
  bottom: 1px;
  left: -4px;
}

.writing-cell-small {
  width: 7mm;
  height: 7mm;
  line-height: 7mm;
}

.writing-cell-medium {
  width: 8mm;
  height: 8mm;
  line-height: 8mm;
}

.writing-cell-big {
  width: 9.2mm;
  height: 9.2mm;
  line-height: 9.2mm;
}
</style>
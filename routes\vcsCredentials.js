const express = require('express');
const router = express.Router();
const VcsCredential = require('../models/VcsCredential');

// 获取所有VCS凭据
router.get('/', async (req, res) => {
  try {
    const credentials = await VcsCredential.getAll();
    res.json(credentials);
  } catch (error) {
    req.logger.error(`获取VCS凭据列表失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 根据类型获取VCS凭据
router.get('/type/:type', async (req, res) => {
  try {
    const { type } = req.params;
    if (!['git', 'svn'].includes(type)) {
      return res.status(400).json({ error: '不支持的VCS类型' });
    }
    
    const credentials = await VcsCredential.getByType(type);
    res.json(credentials);
  } catch (error) {
    req.logger.error(`获取${req.params.type}凭据列表失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 根据ID获取VCS凭据
router.get('/:id', async (req, res) => {
  try {
    const credential = await VcsCredential.getById(req.params.id);
    if (!credential) {
      return res.status(404).json({ error: 'VCS凭据不存在' });
    }
    res.json(credential);
  } catch (error) {
    req.logger.error(`获取VCS凭据详情失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 创建新VCS凭据
router.post('/', async (req, res) => {
  try {
    const { name, type, username, password, email, description } = req.body;
    
    if (!name || !type || !username) {
      return res.status(400).json({ error: '凭据名称、类型和用户名为必填项' });
    }
    
    if (!['git', 'svn'].includes(type)) {
      return res.status(400).json({ error: '不支持的VCS类型，仅支持git和svn' });
    }
    
    const credential = await VcsCredential.create({
      name,
      type,
      username,
      password,
      email,
      description
    });
    
    res.status(201).json(credential);
  } catch (error) {
    req.logger.error(`创建VCS凭据失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 更新VCS凭据
router.put('/:id', async (req, res) => {
  try {
    const { name, type, username, password, email, description } = req.body;
    
    if (type && !['git', 'svn'].includes(type)) {
      return res.status(400).json({ error: '不支持的VCS类型，仅支持git和svn' });
    }
    
    const credential = await VcsCredential.update(req.params.id, {
      name,
      type,
      username,
      password,
      email,
      description
    });
    
    res.json(credential);
  } catch (error) {
    req.logger.error(`更新VCS凭据失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 删除VCS凭据
router.delete('/:id', async (req, res) => {
  try {
    const result = await VcsCredential.delete(req.params.id);
    if (!result) {
      return res.status(404).json({ error: 'VCS凭据不存在' });
    }
    
    res.json({ success: true });
  } catch (error) {
    req.logger.error(`删除VCS凭据失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 测试VCS凭据连接
router.post('/:id/test', async (req, res) => {
  try {
    const { repositoryUrl } = req.body;
    
    if (!repositoryUrl) {
      return res.status(400).json({ error: '仓库URL为必填项' });
    }
    
    const credential = await VcsCredential.getByIdWithPassword(req.params.id);
    if (!credential) {
      return res.status(404).json({ error: 'VCS凭据不存在' });
    }
    
    // 这里可以添加实际的连接测试逻辑
    // 目前返回模拟结果
    res.json({ 
      success: true, 
      message: `${credential.type.toUpperCase()}凭据测试成功`,
      credential: {
        name: credential.name,
        type: credential.type,
        username: credential.username
      }
    });
  } catch (error) {
    req.logger.error(`测试VCS凭据失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;

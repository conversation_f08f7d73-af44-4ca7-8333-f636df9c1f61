<template>
    <el-select v-model="curSubject" value-key="subjectName" class="subject-select-div" @change="switchSubject"
        :title="`${curSubject.subjectName} (${curSubject.subjectType === CLASS_TYPE.substitute ? '个人授课' : '备课组长'})`">
        <template v-if="subjectInfo.substituteSubjectList.length">
            <span class="option-label">个人授课</span>
            <el-option v-for="item in subjectInfo.substituteSubjectList" :key="item.subjectId + item.type"
                :label="`${item.subjectName} (个人授课)`" :value="item" :title="item.subjectName" class="subject-select-item">
                {{ item.subjectName }}
            </el-option>
        </template>

        <template v-if="subjectInfo.lessonsSubjectList.length">
            <span class="option-label">备课组长</span>
            <el-option v-for="(item, index) in subjectInfo.lessonsSubjectList" :key="index + item.subjectId"
                :label="`${item.subjectName} (备课组长)`" :value="item" :title="item.subjectName" class="subject-select-item">
                {{ item.subjectName }}
            </el-option>
        </template>
    </el-select>
</template>

<script lang="ts">
import {
    defineComponent,
    reactive,
    toRefs,
    onMounted,
    ref,
    watch
} from 'vue';
import { CLASS_TYPE } from '@/typings/card';
import Paper from '../../src/views/paper';
import { getTeacherSubjectListAPI } from '@/service/api';
import { ElMessage } from 'element-plus';
export default defineComponent({
    emits: ["switch-subject"],
    setup(props: any, ctx: any) {
        const state = reactive({
            // 当前学科对象
            curSubject: {},
            // 学科信息
            subjectInfo: {
                // 备课列表
                lessonsSubjectList: [],
                // 授课列表
                substituteSubjectList: []
            },
            // 授课班级类型
            CLASS_TYPE: CLASS_TYPE
        });
        // 用户id
        const userId = () => {
            return Paper.userId;
        }
        // 学校id
        const schoolId = () => {
            return Paper.schoolId;
        }
        /**
        * @name: 学科切换
        * @param subItem 学科对象
        */
        const switchSubject = (subItem: any) => {
            localStorage.setItem(`select_subject_${Paper.userId}`, JSON.stringify(subItem))
            state.curSubject = subItem
            ctx.emit('switch-subject', subItem)
        }
        /**
         * @name: 获取代/备课学科列表
         */
        const getSubjectList = () => {
            getTeacherSubjectListAPI({
                userId: Paper.userId,
                schoolId: Paper.schoolId
            }).then((res: any) => {
                if (res.code === 1) {
                    let curSubject = localStorage.getItem(`select_subject_${Paper.userId}`)
                    curSubject = curSubject ? JSON.parse(curSubject) : res.data.selectedSubject
                    state.subjectInfo.lessonsSubjectList = res.data.lessonsSubjectList
                    state.subjectInfo.substituteSubjectList = res.data.substituteSubjectList
                    switchSubject(curSubject)
                }
            }).catch((err: any) => {
                ElMessage({
                    message: err.msg,
                    type: "error",
                });
            })
        }
        /**
         * 页面一开始加载
         */
        onMounted(async () => {
            getSubjectList();
        });


        return {
            ...toRefs(state),
            switchSubject
        };
    }
});
</script>

<style lang="scss" scoped></style>
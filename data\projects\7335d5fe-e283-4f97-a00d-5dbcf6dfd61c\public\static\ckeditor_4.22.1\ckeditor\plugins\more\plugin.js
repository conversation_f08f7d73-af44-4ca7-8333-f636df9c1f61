/*
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2024-10-21 14:48:30
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2024-10-26 10:02:08
 */
CKEDITOR.plugins.add("more", {
  requires: "richcombo",
  init: function (editor) {
    var config = editor.config;
    var pluginName = "more";

    // 创建RichCombo框
    editor.ui.addRichCombo(pluginName, {
      label: "作文格",
      title: "作文格",
      className: 'more-combo-class',
      allowedContent: 'abbr[title]',
      panel: {
        css: [CKEDITOR.skin.getPath('editor')].concat(config.contentsCss),
        multiSelect: false,
        attributes: { 'aria-label': editor.lang.lineheight.title }
      },
      init: function () {
        this.add("GridLine", "作文格");
        this.add("TianziLine", "田字格");
        this.add("FourLine", "四线格");
      },
      onRender: function () {
        // this.setValue("1"); // 设置默认值
      },
      onClick: function (value) {
        editor.execCommand(value);
      },
    });
    const style = document.createElement('style');
    style.innerHTML = `
      .cke_toolbar .more-combo-class .cke_combo_text {
        width: 40px !important;
      }
    `;
    document.head.appendChild(style);
  }
});
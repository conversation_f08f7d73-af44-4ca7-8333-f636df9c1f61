<template>
    <div :class="['header-head', Paper.isEdit ? 'uneditstyle' : '']">
        <div :id="'name-txt' + page" class="name-txt" :style="{
            fontSize: nameSize + ' !important',
            fontWeight: nameFont ? 'bold !important' : 'unset !important',
            maxHeight: '12mm !important',
            lineHeight: lineHeight + ' !important',
        }" :contenteditable="true" v-html="Paper.nameHtml.html"></div>
        <div class="nameset nosave">
            <el-checkbox v-model="nameFont" label="加粗" size="small" />
            <el-select v-model="nameSize" style="width: 80px" :teleported="false" placeholder="Select" size="small">
                <el-option v-for="item in fontSizeList" :key="item.value" :label="item.text" :value="item.value" />
            </el-select>
        </div>
    </div>
</template>

<script lang="ts">
import {
    defineComponent,
    reactive,
    toRefs,
    onMounted,
    ref,
    nextTick,
    watch,
} from "vue";
import Paper from "../../src/views/paper";
import QRCode from "qrcode";

export default defineComponent({
    props: {
        page: {
            type: Number,
            default: 1,
        },
    },
    setup(props: any, ctx: any) {
        const state = reactive({
            Paper: Paper,
            //字号集合
            fontSizeList: [
                {
                    id: 1,
                    text: "小五",
                    value: "3.2mm",
                },
                {
                    id: 2,
                    text: "五号",
                    value: "3.5mm",
                },
                {
                    id: 3,
                    text: "小四",
                    value: "3.7mm",
                },
                {
                    id: 4,
                    text: "四号",
                    value: "4mm",
                },
                {
                    id: 5,
                    text: "小三",
                    value: "4.2mm",
                },
                {
                    id: 6,
                    text: "三号",
                    value: "4.4mm",
                },
                {
                    id: 7,
                    text: "小二",
                    value: "4.8mm",
                },
            ],
            nameSize: "4.8mm",
            nameFont: true,
            lineHeight: "4.8mm",
        });

        watch(
            () => [state.nameSize, state.nameFont],
            () => {
                Paper.nameHtml.fontBold = state.nameFont;
                Paper.nameHtml.size = state.nameSize;
                if (Number(state.nameSize.replace("mm", "")) > 4) {
                    state.lineHeight = "4.8mm";
                } else {
                    state.lineHeight = "4mm";
                }
            }
        );

        /**
         * 页面一开始加载
         */
        onMounted(async () => {
            state.nameFont = Paper.nameHtml.fontBold;
            state.nameSize = Paper.nameHtml.size;
            if (Number(state.nameSize.replace("mm", "")) > 4) {
                state.lineHeight = "4.8mm";
            } else {
                state.lineHeight = "4mm";
            }
            nextTick(() => {
                const contenteditableDiv = document.getElementById(
                    "name-txt" + props.page.toString()
                );
                contenteditableDiv?.addEventListener("paste", function (e: any) {
                    e.preventDefault();
                    document.execCommand(
                        "insertHTML",
                        false,
                        e?.clipboardData.getData("text")
                    );
                });
                contenteditableDiv?.addEventListener("blur", function (e: any) {
                    e.target.innerHTML = e.target.innerHTML.replace(
                        /(style)="[^"]*"/g,
                        ""
                    );
                    Paper.name = e.target.textContent;
                    Paper.nameHtml = {
                        html: e.target.innerHTML,
                        size: state.nameSize,
                        fontBold: state.nameFont,
                    };
                    Array.from(document.getElementsByClassName("name-txt")).forEach(
                        (el: any) => {
                            el.innerHTML = Paper.nameHtml.html;
                        }
                    );
                });
            });
        });

        return {
            ...toRefs(state),
        };
    },
});
</script>

<style lang="scss" scoped>
.header-head {
    padding-top: 2mm;
}

.name-txt {
    display: block;
    font-size: 4.3mm;
    max-width: 100%;
    margin: 0 auto;
    font-weight: 700;
    word-break: break-all;
    text-align: center;
    overflow: hidden;
    -webkit-line-clamp: 1;
    line-height: 6.5mm;
    min-height: 6.5mm;
    font-family: 宋体, SimSun, Times New Roman;

    &:hover {
        outline: 1px solid #000;
    }

    &:hover~.nameset {
        visibility: inherit;
    }
}

.nameset {
    visibility: hidden;
    position: absolute;
    top: 13px;
    right: 0;

    &:hover {
        visibility: inherit;
    }
}
</style>

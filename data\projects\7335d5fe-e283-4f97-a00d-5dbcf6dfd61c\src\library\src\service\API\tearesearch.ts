﻿import { API } from "../index";

const TEASEARCH_APIURL = process.env.VUE_APP_TEASEARCH_APIURL;

/****************************** regionResource *****************************************/
/**
 * @name 根据地区, 标签名称, 选项查询标签列表
 **/
export const queryRegionLabelListAPI = (params: {
  schoolId?: string;
  regionId?: string;
  isCustom: number;
  keyWord?: string;
}) => {
  return API.GET(
    "/regionResource/regionlabel/queryRegionLabelList",
    params,
    TEASEARCH_APIURL
  );
};

/**
 * @name 据区域id查询后台栏目列表
 **/
export const queryColumnListAPI = (params: {
  keyWord?: string;
  regionId?: string;
  isPublish: number;
  page: number;
  limit: number;
}) => {
  return API.GET(
    "/regionResource/regioncolumn/queryColumnList",
    params,
    TEASEARCH_APIURL
  );
};

/**
 * @name 查询栏目详情
 **/
export const queryColumnAPI = (params: { id: string; regionId?: string }) => {
  return API.GET(
    "/regionResource/regioncolumn/queryColumn",
    params,
    TEASEARCH_APIURL
  );
};
/**
 * @name: 获取教材同步详情
 */
export const getResourceExamineInfoAPI = (params: {
  id: string;
  userId?: string;
  regionId?: string;
}) => {
  return API.GET(
    "/regionResource/resourceExamine/getResourceExamineInfo",
    params,
    TEASEARCH_APIURL
  );
};
/**
 * @name: 获取专题列表
 */
export const queryThematicManagementAPI = (params: object) => {
  return API.POST(
    "/regionResource/thematicManagement/queryThematicManagement",
    params,
    TEASEARCH_APIURL
  );
};
/**
 * @name: 获取专题详情
 */
export const queryThematicManagementByIdAPI = (params: object) => {
  return API.POST(
    "/regionResource/thematicManagement/queryThematicManagementById",
    params,
    TEASEARCH_APIURL
  );
};
/**
 * @name: 获取专题目录
 */
export const queryThematicCatalogueAPI = (params: object) => {
  return API.POST(
    "/regionResource/thematicResource/queryThematicCatalogue",
    params,
    TEASEARCH_APIURL
  );
};
/**
 * @name: 获取主题资源的目录（校本专题资源）
 * @param thematicId 专题id
 * @param schoolId 学校id
 */
export const queryScreenThematicCatalogueAPI = (params: {
  thematicId: string;
  schoolId: any;
}) => {
  return API.GET(
    "/regionResource/thematicResource/queryScreenThematicCatalogue",
    params,
    TEASEARCH_APIURL
  );
};
/**
 * @name: 查询专题列表（校本专题用）
 */
export const screenQueryThematicManagementAPI = (params: object) => {
  return API.GET(
    "/regionResource/thematicManagement/screenQueryThematicManagement",
    params,
    TEASEARCH_APIURL
  );
};

/**
 * @name: 获取区域教材列表
 */
export const getRegionBookListAPI = (params: object) => {
  return API.GET(
    "/regionResource/resourceExamine/getRegionBookList",
    params,
    TEASEARCH_APIURL
  );
};



/****************************** teachingResearch**********************************/
export const getUserRegionGroupListAPI = (params: object) => {
  return API.POST(
    "/teachingResearch/group/getUserRegionGroupList",
    params,
    TEASEARCH_APIURL
  );
};
export const getRegionGroupRelationListAPI = (params: object) => {
  return API.POST(
    "/teachingResearch/group/getRegionGroupRelationList",
    params,
    TEASEARCH_APIURL
  );
};
export const getRegionUserListByKeyWordAPI = (params: object) => {
  return API.POST("/userMgr/baselogin/getRegionUserListByKeyWord", params);
};
export const saveRegionGroupAPI = (params: object) => {
  return API.POST(
    "/teachingResearch/group/saveRegionGroup",
    params,
    TEASEARCH_APIURL
  );
};
export const editRegionGroupAPI = (params: object) => {
  return API.POST(
    "/teachingResearch/group/editRegionGroup",
    params,
    TEASEARCH_APIURL
  );
};
export const delRegionGroupAPI = (params: object) => {
  return API.POST(
    "/teachingResearch/group/delRegionGroup",
    params,
    TEASEARCH_APIURL
  );
};
export const getPlatFromInfoBySchoolIdAPI = (params: object) => {
  return API.GET("/platformManage/getPlatFromInfoBySchoolId",params);
};
/**
 * @name: 获取区域教材版本列表
 * @param {params} 接口参数
 */
export const getRegionBookEditionListAPI = (params: object) => {
  return API.POST(
    "/regionResource/resourceExamine/getRegionBookEditionList",
    params,
    TEASEARCH_APIURL
  );
};
/**
 * @name: 获取区域教材学科列表
 * @param {params} 接口参数
 */
export const getRegionBookSubjectListAPI = (params: object) => {
  return API.POST(
    "/regionResource/resourceExamine/getRegionBookSubjectList",
    params,
    TEASEARCH_APIURL
  );
};
/**
 * @name: 获取区域教材年级列表
 * @param {params} 接口参数
 */
export const getRegionBookGradeListAPI = (params: object) => {
  return API.POST(
    "/regionResource/resourceExamine/getRegionBookGradeList",
    params,
    TEASEARCH_APIURL
  );
};
/**
 * @name: 获取区域全部教材资源
 * @param {params} 接口参数
 */
export const getSchoolRegionBookListAPI = (params: object) => {
  return API.GET("/regionResource/schoolBook/getBookList", params,TEASEARCH_APIURL);
};
/**
 * @name: 获取区域全部教材资源
 * @param {params} 接口参数
 */
export const regionBookListAPI = (params: object) => {
  return API.GET("/regionResource/resourceExamine/regionBookList", params,TEASEARCH_APIURL);
};
/**
 * @name: 获取区域学科(不分页)
 */
export const listRegionSubjectAPI = (params: object) => {
  return API.POST(
      "/teachingResearch/regionSubject/listRegionSubject",
      params,
      TEASEARCH_APIURL
  );
};

/**
 * @name 查询区域教材配置范围
 */
export const getRegionBookConfigAPI = (params: object) => {
  return API.GET("/regionResource/resourceExamine/getRegionBookConfig", params,TEASEARCH_APIURL);
};
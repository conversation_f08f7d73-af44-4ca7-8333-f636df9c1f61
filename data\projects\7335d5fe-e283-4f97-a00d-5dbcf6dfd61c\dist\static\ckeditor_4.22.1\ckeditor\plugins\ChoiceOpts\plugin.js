/**
 * 自定义选择题标识
 */
CKEDITOR.plugins.add("ChoiceOpts", {
  init: function(editor) {
    editor.ui.addButton("ChoiceOpts", {
      label: "标记/取消选择题选项",
      icon: this.path + "icon.png",
      command: "ChoiceOpts",
      click: function(e) {
        const selection = window.getSelection();
        if (!selection) return;
        const range = selection.getRangeAt(0);
        const parentNode = range.startContainer.parentElement;
        const az = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const spanNode = document.createElement("span");
        let rangeString = range.toString().toUpperCase();
        rangeString = rangeString
          .replace(/[\u200B-\u200D\uFEFF]/g, "")
          .replace(/ /, "");
        const optionClass = "ocr-pos";
        const optionStyle =
          "display: inline-block; vertical-align: middle; text-align: center; padding: 0px 1mm; margin: 0px; position: relative;";
        if (parentNode.classList.contains(optionClass)) {
          const oldInnerText = parentNode.innerText;
          let charOption = "";
          for (let i = 0; i < oldInnerText.length; i++) {
            if (az.indexOf(oldInnerText[i]) >= 0) {
              charOption = oldInnerText[i];
              break;
            }
          }
          if (charOption !== "") {
            parentNode.outerHTML = charOption;
          }
          return;
        }
        if (
          !(
            (rangeString.length === 1 && az.indexOf(rangeString) >= 0) ||
            (rangeString.length === 3 && rangeString.match(/\[[a-zA-Z]{1}\]/g))
          )
        ) {
          if (
            rangeString.length === 0 &&
            range.startContainer.classList.contains(optionClass)
          ) {
            parentNode.outerHTML = "";
          }
          return;
        }

        spanNode.className = optionClass;
        spanNode.style = optionStyle;
        let optionChar;
        range.surroundContents(spanNode);
        if (rangeString.length === 1) {
          optionChar = rangeString.toUpperCase();
        } else {
          optionChar = rangeString.charAt(1).toUpperCase();
        }
        spanNode.innerHTML =
          '[<span style="padding: 0px 0.6mm; margin: 0px;">' +
          optionChar +
          "</span>]";
      },
    });
  },
});

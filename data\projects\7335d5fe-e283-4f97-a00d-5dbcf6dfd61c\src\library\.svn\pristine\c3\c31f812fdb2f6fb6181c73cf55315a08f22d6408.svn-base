<template>
  <div class="catalog-tree">
    <el-tree
      ref="menuTree"
      :data="datalist"
      v-if="datalist.length > 0"
      node-key="id"
      :check-on-click-node="true"
      :highlight-current="true"
      :props="defaultProps"
      empty-text=""
      @node-click="selectMenu"
      :default-checked-keys="[currentNodeKey]"
      :default-expanded-keys="[currentNodeKey]"
      :current-node-key="currentNodeKey"
    >
    </el-tree>
    <div v-else class="empty-catalog">
      <img
        class="no_pic"
        :src="require('@/library/ui/mk-share-res//assets/empty-catalog.png')"
        alt=""
      />
      <p class="txt">暂无目录</p>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, ref } from 'vue';

export default defineComponent({
  name: 'bookmenu-tree',

  emits: ['switch-catalog'],

  props: {
    //目录数据
    datalist: {
      type: Array,
      default: []
    },
    // 当前需要激活的node-key
    currentNodeKey: {
      type: String,
      default: '0'
    }
  },
  setup(props, context) {
    const menuTree = ref<any>(null);
    const state = reactive({
      // 默认树结构
      defaultProps: {
        children: 'children',
        label: 'title'
      }
    });

    /**
     * @name 选择目录
     */
    const selectMenu = (data: any, node: any) => {
      const { code, title } = data;
      const pathList = getSelectPath(node, [{ code, title }]);
      context.emit('switch-catalog', data, pathList);
    };

    const getSelectPath = (node: any, list: any[]) => {
      if (node.level > 1 && node.parent) {
        const { code, title } = node.parent.data;
        list.unshift({ code, title });
        list = getSelectPath(node.parent, list);
      }
      return list;
    };

    return {
      ...toRefs(state),
      menuTree,
      selectMenu
    };
  }
});
</script>
<style lang="scss">
.catalog-tree {
  max-height: 720px;
  .empty-catalog {
    width: 300px;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    overflow: hidden;
    .no_pic {
      display: block;
      margin: auto;
      max-width: 100%;
    }
    .txt {
      text-align: center;
      margin: auto;
      display: inherit;
      color: #999;
      font-size: 16px;
    }
  }
  .el-tree-node__expand-icon {
    width: 16px;
    height: 16px;
    margin-right: 10px;
    background : {
      repeat: no-repeat;
    }
    background-position: center !important;
    background: url('~@/library/ui/mk-share-res/assets/unfold.png');
    border-radius: 50%;
    &:before {
      display: none;
    }
    &.is-leaf {
      background: none !important;
    }
    &.expanded {
      transform: none;
      transition: none;
      background-position: center !important;
      background: url('~@/library/ui/mk-share-res/assets/click_fold.png');
      border-radius: 50%;
    }
  }
  .el-tree-node__content {
    height: 32px;
    &:hover {
      background-color: transparent;
    }
  }
  .el-tree {
    background-color: transparent;
    .el-tree-node {
      &.is-current {
        & > .el-tree-node__content {
          background-color: transparent;
          .el-tree-node__expand-icon {
            background-position: center !important;
            background: url('~@/library/ui/mk-share-res/assets/unfold.png');
            border-radius: 50%;
            &.expanded {
              background-position: center !important;
              background: url('~@/library/ui/mk-share-res/assets/click_fold.png');
              border-radius: 50%;
            }
          }
          .el-tree-node__label {
            @include theme_color();
          }
        }
      }
    }
  }
}
</style>

<!--
 * @Description: 
 * @Author: liuyue <EMAIL>
 * @Date: 2025-07-15 11:42:49
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-07-15 13:56:00
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>前端部署工具</title>
  <link href="/bootstrap/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/bootstrap/bootstrap-icons.css">
  <link rel="stylesheet" href="/css/style.css">
</head>
<body>
  <div class="container-fluid">
    <div class="row">
      <!-- 侧边栏 -->
      <div class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
        <div class="position-sticky pt-3">
          <div class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-dark text-decoration-none">
            <span class="fs-4 ms-2">部署工具</span>
          </div>
          <hr>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link active" href="#" data-page="dashboard">
                <i class="bi bi-speedometer2 me-2"></i>
                控制台
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-page="projects">
                <i class="bi bi-folder me-2"></i>
                项目管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-page="servers">
                <i class="bi bi-hdd-rack me-2"></i>
                服务器管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/vcs-credentials">
                <i class="bi bi-key me-2"></i>
                VCS凭据管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/user-management">
                <i class="bi bi-people me-2"></i>
                用户管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-page="nodeVersions">
                <i class="bi bi-nodes me-2"></i>
                Node.js管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="#" data-page="queue">
                <i class="bi bi-list-check me-2"></i>
                构建队列
              </a>
            </li>
          </ul>
          
          <hr>
          <div class="user-info px-3 pb-3 d-none" id="sidebar-user-info">
            <div class="d-flex align-items-center">
              <i class="bi bi-person-circle me-2"></i>
              <span id="sidebar-username">用户名</span>
            </div>
            <button class="btn btn-sm btn-outline-secondary mt-2" id="logout-btn">
              <i class="bi bi-box-arrow-right"></i> 退出登录
            </button>
          </div>
        </div>
      </div>

      <!-- 主内容区 -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 class="h2" id="page-title">控制台</h1>
          <div class="btn-toolbar mb-2 mb-md-0">
            <div class="btn-group me-2" id="page-actions">
              <!-- 页面特定操作按钮将在这里添加 -->
            </div>
          </div>
        </div>

        <!-- 内容页面 -->
        <div id="page-dashboard" class="page-content active">
          <div class="row">
            <div class="col-md-6">
              <div class="card mb-4">
                <div class="card-header">
                  <h5 class="card-title">最近部署</h5>
                </div>
                <div class="card-body">
                  <div id="recent-deployments">
                    <p class="text-muted">暂无最近部署</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card mb-4">
                <div class="card-header">
                  <h5 class="card-title">快速部署</h5>
                </div>
                <div class="card-body">
                  <form id="quick-deploy-form">
                    <div class="mb-3">
                      <label for="project-select" class="form-label">项目</label>
                      <select class="form-select" id="project-select" required>
                        <option value="" selected disabled>选择项目</option>
                      </select>
                    </div>
                    <div class="mb-3">
                      <label for="branch-select" class="form-label">分支</label>
                      <select class="form-select" id="branch-select" required disabled>
                        <option value="" selected disabled>选择分支</option>
                      </select>
                    </div>
                    <div class="mb-3">
                      <label class="form-label">部署服务器</label>
                      <div id="server-deploy-options">
                        <p class="text-muted">请先选择项目</p>
                      </div>
                    </div>
                    <button type="submit" class="btn btn-primary" id="deploy-btn" disabled>
                      <i class="bi bi-cloud-upload"></i> 部署
                    </button>
                  </form>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 活跃构建 -->
          <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">活跃构建</h5>
              <a href="/queue" class="btn btn-sm btn-outline-primary">查看全部</a>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>项目</th>
                      <th>分支</th>
                      <th>状态</th>
                      <th>提交人</th>
                      <th>提交时间</th>
                    </tr>
                  </thead>
                  <tbody id="active-builds-table">
                    <tr>
                      <td colspan="6" class="text-center">加载中...</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <div id="page-projects" class="page-content">
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead>
                <tr>
                  <th>名称</th>
                  <th>Git URL</th>
                  <th>构建命令</th>
                  <th>服务器</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody id="projects-table-body">
                <!-- 项目将在这里加载 -->
              </tbody>
            </table>
          </div>
        </div>

        <div id="page-servers" class="page-content">
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead>
                <tr>
                  <th>名称</th>
                  <th>主机</th>
                  <th>用户名</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody id="servers-table-body">
                <!-- 服务器将在这里加载 -->
              </tbody>
            </table>
          </div>
        </div>

        <div id="page-nodeVersions" class="page-content">
          <div class="row mb-4">
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title">已安装的Node.js版本</h5>
                </div>
                <div class="card-body">
                  <div id="installed-versions-container">
                    <p class="text-muted">加载中...</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-6">
              <div class="card">
                <div class="card-header">
                  <h5 class="card-title">安装Node.js版本</h5>
                </div>
                <div class="card-body">
                  <div class="alert alert-info mb-3">
                    <p class="mb-0">输入需要安装的Node.js版本号，例如：14.17.0、16.13.0、18.12.0</p>
                  </div>
                  <div class="mb-3">
                    <label for="node-version-input" class="form-label">版本号</label>
                    <div class="input-group">
                      <input type="text" class="form-control" id="node-version-input" placeholder="例如：14.17.0">
                      <button class="btn btn-primary" id="install-version-direct-btn">安装</button>
                    </div>
                  </div>
                  <div id="install-result" class="mt-3 d-none">
                    <div class="progress mb-3">
                      <div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 100%"></div>
                    </div>
                    <p class="text-center" id="install-direct-status">正在安装...</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="card">
            <div class="card-header">
              <h5 class="card-title">使用说明</h5>
            </div>
            <div class="card-body">
              <div class="alert alert-info">
                <p><strong>Node.js版本管理功能使用说明：</strong></p>
                <ul>
                  <li>每个项目可以在创建或编辑时指定特定的Node.js版本</li>
                  <li>系统会在构建时自动安装和使用指定的Node.js版本</li>
                  <li>如果本地已安装NVS (Node Version Switcher)，系统会利用它来管理版本</li>
                  <li>如果未安装NVS，系统将尝试使用当前系统Node.js版本</li>
                  <li>推荐版本: 14.x, 16.x, 18.x LTS版本</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
        
        <div id="page-queue" class="page-content">
          <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">当前构建队列</h5>
              <span class="badge bg-primary" id="active-builds-count">0</span>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>项目</th>
                      <th>分支</th>
                      <th>状态</th>
                      <th>提交人</th>
                      <th>提交时间</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody id="queue-table-body">
                    <tr>
                      <td colspan="7" class="text-center">加载中...</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h5 class="card-title">构建历史</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>项目</th>
                      <th>分支</th>
                      <th>状态</th>
                      <th>提交人</th>
                      <th>完成时间</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody id="history-table-body">
                    <tr>
                      <td colspan="7" class="text-center">加载中...</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
        
      </main>
    </div>
  </div>

  <!-- 模态框 -->
  <!-- 添加项目模态框 -->
  <div class="modal fade" id="addProjectModal" tabindex="-1" aria-labelledby="addProjectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addProjectModalLabel">添加项目</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="add-project-form">
            <div class="mb-3">
              <label for="project-name" class="form-label">项目名称</label>
              <input type="text" class="form-control" id="project-name" required>
            </div>
            <div class="mb-3">
              <label for="vcs-type" class="form-label">版本控制系统</label>
              <select class="form-select" id="vcs-type" required>
                <option value="git">Git</option>
                <option value="svn">SVN</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="repository-url" class="form-label">仓库URL</label>
              <input type="text" class="form-control" id="repository-url" required>
              <div class="form-text">Git或SVN仓库的URL地址</div>
            </div>
            <div class="mb-3">
              <label for="vcs-credential" class="form-label">VCS凭据</label>
              <select class="form-select" id="vcs-credential">
                <option value="">无需凭据（公开仓库）</option>
              </select>
              <div class="form-text">
                选择用于访问仓库的凭据。如果没有合适的凭据，请先到
                <a href="/vcs-credentials" target="_blank">VCS凭据管理</a> 页面创建。
              </div>
            </div>
            <div class="mb-3">
              <label for="build-command" class="form-label">构建命令</label>
              <input type="text" class="form-control" id="build-command" value="npm run build">
            </div>
            <div class="mb-3">
              <label for="output-dir" class="form-label">输出目录</label>
              <input type="text" class="form-control" id="output-dir" value="dist">
            </div>
            <div class="mb-3">
              <label for="node-version" class="form-label">Node.js 版本</label>
              <select class="form-select" id="node-version">
                <option value="" selected>使用系统默认版本</option>
                <!-- Node.js 版本将在这里动态加载 -->
              </select>
              <small class="form-text text-muted">为项目指定特定的 Node.js 版本（将自动使用 nvs 进行切换）</small>
            </div>
            <div class="mb-3">
              <label for="project-description" class="form-label">描述</label>
              <textarea class="form-control" id="project-description" rows="3"></textarea>
            </div>
            <div class="mb-3">
              <label class="form-label">部署服务器</label>
              <div id="server-checkboxes">
                <!-- 服务器复选框将在这里添加 -->
              </div>
              <small class="form-text text-muted">选择要部署此项目的服务器，每个服务器可以配置不同的部署路径</small>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="save-project-btn">保存项目</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加服务器模态框 -->
  <div class="modal fade" id="addServerModal" tabindex="-1" aria-labelledby="addServerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addServerModalLabel">添加服务器</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="add-server-form">
            <div class="mb-3">
              <label for="server-name" class="form-label">服务器名称</label>
              <input type="text" class="form-control" id="server-name" required>
            </div>
            <div class="mb-3">
              <label for="server-host" class="form-label">主机</label>
              <input type="text" class="form-control" id="server-host" required>
            </div>
            <div class="mb-3">
              <label for="server-port" class="form-label">端口</label>
              <input type="number" class="form-control" id="server-port" value="22">
            </div>
            <div class="mb-3">
              <label for="server-username" class="form-label">用户名</label>
              <input type="text" class="form-control" id="server-username" required>
            </div>
            <div class="mb-3">
              <label for="server-password" class="form-label">密码</label>
              <input type="password" class="form-control" id="server-password" required>
            </div>
            <div class="mb-3">
              <label for="server-description" class="form-label">描述</label>
              <textarea class="form-control" id="server-description" rows="3"></textarea>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="save-server-btn">保存服务器</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 部署模态框 -->
  <div class="modal fade" id="deployModal" tabindex="-1" aria-labelledby="deployModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="deployModalLabel">部署进度</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="deployment-info mb-3">
            <p><strong>项目:</strong> <span id="deploy-project-name"></span></p>
            <p><strong>分支:</strong> <span id="deploy-branch-name"></span></p>
            <p><strong>状态:</strong> <span id="deploy-status">排队中</span></p>
          </div>
          <div class="deployment-log-container">
            <div id="deploy-log-content" class="log-content">
              <!-- 部署日志将在这里显示 -->
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 构建日志模态框 -->
  <div class="modal fade" id="buildLogModal" tabindex="-1" aria-labelledby="buildLogModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="buildLogModalLabel">构建日志</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="build-info mb-3">
            <p><strong>项目:</strong> <span id="log-project-name"></span></p>
            <p><strong>分支:</strong> <span id="log-branch-name"></span></p>
            <p><strong>状态:</strong> <span id="log-status"></span></p>
          </div>
          <div class="log-container bg-dark text-light p-3" style="height: 400px; overflow-y: auto;">
            <pre id="build-log-content" class="mb-0"></pre>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <script src="/bootstrap/bootstrap.bundle.min.js"></script>
  <script src="/js/socket.io.min.js"></script>
  <script src="/js/main.js"></script>
</body>
</html>
/**
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2023-09-20 17:05:19
 * @LastEditors: l<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>@class30.com
 * @LastEditTime: 2023-12-06 17:31:32
 */
window.CkDom = window.CkDom || {};
(function (CkDom) {
  function isChromeVersionGreaterThan127() {
    const userAgent = navigator.userAgent;

    // 查找 "Chrome/XX" 或 "Chromium/XX" 版本号
    const chromeMatch = userAgent.match(/Chrome\/(\d+)/) || userAgent.match(/Chromium\/(\d+)/);

    if (chromeMatch && chromeMatch[1]) {
      const version = parseInt(chromeMatch[1], 10);
      return version > 127;
    }

    // 如果不是 Chrome 或 Chromium，返回 false
    return false;
  }

  function getZoom(){
    return isChromeVersionGreaterThan127() ? 1 : window.EDIT_ZOOM || 1;
  }

  class Image {
    ImageType = {
      FLOAT_RIGHT: "1",
      LEFT: "2",
      CENTER: "3",
      RIGHT: "4",
    };
    _instance = null;
    constructor() {
      this.bindMoveEvent();
      this.initEvent();
      this.appendStyle();
    }
    initEvent = function () {
      const self = this;
      let _menu;
      let _resize;
      document.onclick = function (event) {
        const el = event.target;
        _menu && _menu.hide();
        _resize && _resize.hide();
        if ($(el).parents(".q-opt").length && el.tagName == "IMG") {
          self._instance = el;
          _menu = self.appendMenu(el);
          _resize = self.appendResize(el);
        } else if ($(el).hasClass("dom_imgsetting")) {
          self.switchPostion(el);
        }
      };
    };
    appendStyle = function () {
      const _style = document.createElement("style");
      _style.innerHTML =
        `.imageMenu .dom_imgsetting{ display: block;width: 100px;height: 18px;font-size: 10px;cursor: pointer;line-height: 18px;background-color: #409EFF;color: white;text-align: center;border-radius: 0 10px 10px 0px;font-family: "Microsoft Yahei", arial, sans-serif;margin-top: 1px; }
        .imageResize{width: 25px;height: 25px;cursor: se-resize;z-index: 99;}`;

      document.head.appendChild(_style);
    };
    appendMenu = function (e) {
      //非题卡合一答题卡填空题不显示图片菜单
      const isCardFill = $(this._instance).parents('.imglist-content').length;
      if (e.height <= 30 || isCardFill) {
        return;
      }
      $(document.body)
        .find(".imageMenu")
        .remove();
      $(document.body).append(
        ' <div class="imageMenu">\n<span class="dom_imgsetting" type="1">四周型 右对齐</span>\n<span class="dom_imgsetting" type="2">嵌入型 右对齐</span>\n<span class="dom_imgsetting" type="3">嵌入型 居 &nbsp;&nbsp;中</span>\n                <span class="dom_imgsetting" type="4">嵌入型 左对齐</span>\n            </div> '
      );
      const zoom = getZoom();
      const _menu = $(document.body).find(".imageMenu");
      _menu
        .css("position", "absolute")
        .css("display", "block")
        .css("top", e.getBoundingClientRect().top * zoom)
        .css("left", e.getBoundingClientRect().right * zoom);
      return _menu;
    };
    appendResize = function (e) {
      if (e.height < 30) {
        return;
      }
      $(document.body)
        .find(".imageResize")
        .remove();
      $(document.body).append(' <div class="imageResize" title="拖拽调整图片尺寸，按住shift锁住比例"></div> ');
      const _resize = $(document.body).find(".imageResize");
      const zoom = getZoom();
      _resize
        .css("position", "absolute")
        .css("display", "block")
        .css("top", (e.getBoundingClientRect().bottom * zoom - 25))
        .css("left", (e.getBoundingClientRect().right * zoom - 25));
      return _resize;
    };
    switchPostion = function (e) {
      const type = e.getAttribute("type");
      const isCard = $(this._instance).parents('.card').length;
      let parent;
      if(isCard){
        parent = $(this._instance).parents('.subject-para-p').get(0);
      }else{
        parent = $(this._instance).parents('.hascontent,.ques-content,.tmpcontent').get(0);
      }
      const width = this._instance.style.width;
      const height = this._instance.style.height;
      this._instance.style = "";
      this._instance.style.height = height;
      this._instance.style.width = width;
      switch (type) {
        case this.ImageType.FLOAT_RIGHT:
          this._instance.remove();
          parent.prepend(this._instance);
          $(this._instance)
            .css("position", "static")
            .css("float", "right")
            .css("right", 0)
            .css("margin-top", '2px')
            .css("clear", 'both');
          break;
        case this.ImageType.RIGHT:
          this._instance.remove();
          parent.append(this._instance);
          $(this._instance)
            .css("position", "static")
            .css("float", "none")
            .css("display", "block")
            .css("margin", "0");
          break;
        case this.ImageType.CENTER:
          this._instance.remove();
          parent.append(this._instance);
          $(this._instance)
            .css("position", "static")
            .css("float", "none")
            .css("display", "block")
            .css("margin", "auto");
          break;
        case this.ImageType.LEFT:
          this._instance.remove();
          parent.append(this._instance);
          $(this._instance)
            .css("position", "static")
            .css("float", "none")
            .css("display", "block")
            .css("margin", "0 0 0 auto");
          break;
        default:
          break;
      }
    };
    bindMoveEvent = function () {
      const self = this;
      let params = {
        left: 0,
        top: 0,
        currentX: 0,
        currentY: 0,
        width: 0,
        height: 0,
        flag: false,
        el: null,
        eventTime: 0,
        type: 0, //0：移动 1：缩放
      };
      //获取相关CSS属性
      function getTop(e) {
        let offset = e.offsetTop;
        if (e.height > e.offsetParent.clientHeight) {
          offset += getTop(e.offsetParent);
        }
        return offset;
      }
      function getLeft(e) {
        let offset = e.offsetLeft;
        if (e.width > e.offsetParent.clientWidth) {
          offset += getLeft(e.offsetParent);
        }
        return offset;
      }

      //e是移动对象
      document.onmousedown = function (event) {
        params.eventTime = new Date().getTime();
        params.el = event.target;
        if (
          (params.el.tagName !== "IMG" || !$(params.el).parents(".q-opt").length) && 
          params.el.className !== "imageResize" 
        )
          return;
        if (params.el.className == "imageResize") {
          params.type = 1;
          params.width = $(self._instance).width();
          params.height = $(self._instance).height();
          document.body.style['user-select'] = 'none';
        } else {
          params.type = 0;
          params.el.setAttribute("draggable", false);
          // //拖拽的实现
          params.left = Math.max(getLeft(params.el), 0);
          params.top = Math.max(getTop(params.el), 0);
          // console.log(params.left, params.top);
        }
        params.currentX = event.clientX;
        params.currentY = event.clientY;
        params.flag = true;
      };
      
      document.onmouseup = function () {
        if (!params.flag) return;
        params.flag = false;
        params.left = getLeft(params.el);
        params.top = getTop(params.el);
        document.body.style['user-select'] = '';
      };

      document.onmousemove = function (event) {
        //增加100ms的延迟，避免down和move连续触发，影响click的事件判断
        if (new Date().getTime() - params.eventTime < 200) {
          return;
        }

        event = event || window.event;
        if (params.flag) {
          if ($(".imageMenu").is(":visible")) {
            $(".imageMenu").hide();
          }
          params.el.style.position = "absolute";
          params.el.style.float = "none";
          params.el.style.zIndex = "9";
          const nowX = event.clientX,
            nowY = event.clientY;
          let disX = nowX - params.currentX,
            disY = nowY - params.currentY;
          // console.log(disX, disY);
          if (params.type == 0) {
            const left = parseInt(params.left) + disX;
            const top = parseInt(params.top) + disY;
            if (
              left > 0 &&
              left <
              $(params.el)
                .parents(".ques-box")
                .width() -
              $(params.el).width() / 2
            ) {
              params.el.style.left = left + "px";
            }
            if (top > 0 && top <
              $(params.el)
                .parents(".ques-box")
                .height() - $(params.el).height() / 2) {
              params.el.style.top = top + "px";
            }
          } else {
            let lockRatio = event.shiftKey;
            let oriRatio = params.width / params.height;
            if (lockRatio) {
              disY = disX / oriRatio;
            }

            const width = Math.max(params.width + disX, 80);
            const height = Math.max(params.height + disY, 80);
            $(self._instance)
              .width(width)
              .height(height);
          }
        }
      };
    };
  }

  CkDom.Image = new Image();
})(window.CkDom);

﻿<template>
  <el-form-item :label="label" :required="required">
    <el-row class="mk-cover" type="flex" align="start">
      <el-col :span="span">
        <mk-water-marker
          :url="curUrl"
          :options="{
            color: curColor,
            text: curText,
            fontSize
          }"
          :width="width"
          :height="height"
          @on-water-marker="onWaterMarker"
        ></mk-water-marker>
      </el-col>
      <el-col :span="24 - span" v-if="!disabled">
        <ul class="cover-box">
          <li>
            <span
              class="local-cover cover-upload-btn"
              @click="switchCoverType(COVER_TYPE.LOCAL)"
            >
              <mk-upload
                style="width: 100%; height: 100%; display: block"
                :fileLimit="1"
                :isAutoUplodOss="false"
                :accept="'.jpg,.jpeg,.png'"
                :beforeUploadCallback="beforeUpload"
              >
                <template #uploadSlot>本地上传</template>
              </mk-upload>
            </span>
            <span
              class="net-cover cover-upload-btn"
              @click="switchCoverType(COVER_TYPE.SYSTEM)"
            >
              <slot name="netCoverBtn">
                <span
                  style="display: block; width: 100%; height: 100%"
                  @click="onClickNetCover"
                  >系统封面</span
                >
              </slot>
            </span>
          </li>
          <li>
            <span style="margin-right: 10px">封面文字</span>
            <el-switch
              v-model="isShowName"
              @change="switchShowName"
            />

            <!-- active-color="#4a94ff" -->
            <template v-if="isShowName">
              <span style="margin: 0 10px 0 40px">封面文字颜色:</span>
              <el-tag
                v-for="(item, index) in predefineColors"
                :key="index"
                :color="item"
                :class="['cover-color-item', { active: curColor == item }]"
                @click.native="switchColor(item)"
              ></el-tag>
              <el-tag class="cover-color-item picker">
                <el-color-picker v-model="curColor"></el-color-picker>
              </el-tag>
            </template>
          </li>

          <template v-if="isShowName">
            <li class="cover-name">
              <span class="cove-title">封面名称:</span>
              <el-radio-group
                v-model="textType"
                class="cover-text"
                @change="switchTextType"
              >
                <el-radio :label="TEXT_TYPE.SYSTEM">
                  使用活动名称
                  <span style="color: #bbb; font-size: 14px; margin-left: 10px"
                    >(系统自动截取活动名称前{{ maxlength }}个字显示)</span
                  >
                </el-radio>
                <el-radio :label="TEXT_TYPE.INPUT">
                  手动输入文字
                  <el-input
                    style="width: 350px; margin-left: 10px"
                    type="text"
                    placeholder="请输入封面文字"
                    v-model="curText"
                    :maxlength="maxlength"
                    :disabled="textType === TEXT_TYPE.SYSTEM"
                    show-word-limit
                  >
                  </el-input>
                </el-radio>
              </el-radio-group>
            </li>
          </template>
        </ul>
      </el-col>
    </el-row>
  </el-form-item>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, toRefs, watch } from "vue";
import {
  ElRow,
  ElCol,
  ElSwitch,
  ElColorPicker,
  ElRadioGroup,
  ElRadio,
  ElInput,
  ElFormItem,
  ElTag,
  ElMessage
} from "element-plus";
import MkWaterMarker from "@/library/ui/mk-water-marker";
import MkUpload from "@/library/ui/mk-upload";
import { isTest } from "@/library/src/utils/valiteSite";
import { getToday, generateUUID } from "@/library/src/utils/globalFunction";
import store from "@/store";
/** 封面文字类型 */
enum TEXT_TYPE {
  /** 系统名称 */
  SYSTEM = 1,
  /** 手动输入 */
  INPUT = 2
}
/** 封面类型 */
export enum COVER_TYPE {
  /** 系统 */
  SYSTEM = 1,
  /** 本地上传 */
  LOCAL = 2
}
/**
 * @Name: 封面组件
 * @Descripttion: 支持设置水印封面,水印支持设置文字大小,颜色,字数
 * @Author: gaohan
 * @Date: 2022/9/7 16:07
 * @LastEditors: gaohan
 * @LastEditTime: 2022/9/7
 * @slot: netCoverBtn 系统封面按钮
 */
export default defineComponent({
  name: "mk-cover",

  emits: ["on-upload-cover"],

  components: {
    MkWaterMarker,
    MkUpload,
    ElRow,
    ElCol,
    ElSwitch,
    ElColorPicker,
    ElRadioGroup,
    ElRadio,
    ElInput,
    ElFormItem,
    ElTag
  },

  props: {
    // 默认文字
    text: {
      type: String,
      default: ""
    },
    // 系统文字
    title: {
      type: String,
      default: ""
    },
    // 文字大小
    fontSize: {
      type: String,
      default: "20px"
    },
    // 文字大小
    color: {
      type: String,
      default: "#fff"
    },
    // 默认图片
    url: {
      type: String,
      default: ""
    },
    // 源地址
    origin: {
      type: String,
      default: ""
    },
    // 文字长度限制
    maxlength: {
      type: Number,
      default: 20
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // label文案
    label: {
      type: String,
      default: "封面: "
    },
    // 是否必选
    required: {
      type: Boolean,
      default: true
    },
    // 本地上传地址
    uploadPath: {
      type: String,
      default: "aliba/resourcecenter/thumbnail"
    },
    // 宽度
    width: {
      type: Number,
      default: 275
    },
    // 宽度
    height: {
      type: Number,
      default: 150
    },
    // 栅格
    span: {
      type: Number,
      default: 6
    },
    // 是否展示封面
    isShowText: {
      type: Boolean,
      default: true
    },
    // 文字类型
    type: {
      type: Number,
      default: TEXT_TYPE.SYSTEM
    }
  },

  setup(props, ctx) {
    const state = reactive({
      // 封面文字颜色
      curColor: props.color,
      // 预定义颜色
      predefineColors: ["#fff", "#333", "#3D83D2", "#9B56BE", "#F29341"],
      // 是否显示封面文字
      isShowName: props.isShowText,
      // 封面文字类型
      textType: props.type,
      // 封面文字
      curText: "",
      // 封面地址
      curUrl: props.url,
      // 封面leix
      coverType: COVER_TYPE.SYSTEM,
      // 本地上传临时地址,用于还原上次记录
      localUrl: "",
      // 绘制临时url,用于上传
      canvasUrl: "",
      // 源地址
      originUrl: props.origin
    });

    /**
     * @name: 切换颜色
     * @param color 当前颜色
     */
    const switchColor = (color: string | null) => {
      if (color === null) {
        state.curColor = "#fff";
      } else {
        state.curColor = color;
      }
    };
    /**
     * @name: 切换是否展示文字
     * @param isShow 是否展示
     */
    const switchShowName = (isShow: boolean) => {
      state.curUrl = state.coverType === COVER_TYPE.LOCAL ? state.localUrl : props.origin;

      if (state.textType === TEXT_TYPE.SYSTEM) {
        state.curText = isShow ? props.title.substr(0, props.maxlength) : "";
      } else {
        state.curText = isShow ? props.text.substr(0, props.maxlength) : "";
      }
      state.isShowName = isShow;
    };
    /**
     * @name: 切换文字类型
     * @param textType 文字类型
     */
    const switchTextType = (textType: TEXT_TYPE) => {
      state.textType = textType;
      switchShowName(state.isShowName);
    };
    /**
     * @name: 切换封面类型
     * @param coverType 封面类型
     */
    const switchCoverType = (coverType: COVER_TYPE) => {
      state.coverType = coverType;
    };
    /**
     * @name: 添加本地封面
     * @param file 文件
     */
    const beforeUpload = (file: any) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const filePath = `${
          props.uploadPath
        }${getToday()}${generateUUID()}/f.png`;
        minioUpload
          .uploadBase64File(reader.result, filePath)
          .then((res: any) => {
            state.curUrl = res[0];
            if( !state.originUrl ){
              state.originUrl = res[0];
            }
            state.localUrl = res[0];
          });
      };
    };
    /**
     * @name: 点击系统封面
     */
    const onClickNetCover = () => {
      ElMessage.warning("暂无系统封面!");
    };
    /**
     * @name: 传递事件
     * @param url 图片地址
     */
    const onWaterMarker = (url: string) => {
      state.canvasUrl = url;
    };
    /**
     * @name: 上传封面
     */
    const uploadCover = () => {
      if (state.canvasUrl === "") {
        ElMessage.warning("请选择封面!");
        return;
      }
      const p = new Promise((resolve: any, reject: any) => {
        const filePath = `${
          props.uploadPath
        }${getToday()}${generateUUID()}/f.png`;
        minioUpload
          .uploadBase64File(state.canvasUrl, filePath)
          .then((res: any) => {
            ctx.emit("on-upload-cover", {
              url: res[0],
              origin: state.originUrl,
              fontSize: props.fontSize,
              color: state.curColor,
              text: state.curText,
              isShowText: state.isShowName,
              textType: state.textType
            });
            state.curUrl = res[0];
            resolve(res[0]);
          })
          .catch((err: any) => {
            reject(err);
          });
      });
      return p;
    };

    watch(
      () => props.text,
      () => {
        switchTextType(state.textType);
      }
    );
    watch(
      () => props.title,
      () => {
        switchTextType(state.textType);
      }
    );
    watch(
      () => props.url,
      () => {
        state.curUrl = props.url;
      }
    );
    watch(
      () => props.origin,
      () => {
        state.curUrl = props.origin;
        state.originUrl = props.origin;
      }
    );
    watch(
      () => props.color,
      () => {
        state.curColor = props.color;
      }
    );
    watch(
      () => props.type,
      () => {
        state.textType = props.type;
        switchShowName(props.isShowText);
      }
    );
    watch(
      () => props.isShowText,
      () => {
        switchShowName(props.isShowText);
      }
    );

    onMounted(() => {
      minioUpload.initConfig(
        isTest(),
        store.state.user.schoolId,
        "datedu",
        store.state.app.platFormInfo.id
      );
    });

    return {
      ...toRefs(state),
      TEXT_TYPE,
      COVER_TYPE,
      switchColor,
      switchShowName,
      switchTextType,
      beforeUpload,
      onClickNetCover,
      onWaterMarker,
      switchCoverType,
      uploadCover
    };
  }
});
</script>

<style lang="scss" scoped>
.cover-box {
  padding-left: 30px;
  margin-top: 0;
  margin-bottom: 0;

}
.cover-upload-btn {
  display: inline-block;
  border: 1px solid;
  @include theme_border-color();
  text-align: center;
  width: 90px;
  height: 38px;
  margin-right: 10px;
  border-radius: 4px;
  cursor: pointer;
  &:hover {
    @include theme_color;
  }
}
.cover-name {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  .cove-title {
    display: inline-block;
    min-width: 80px;
  }
}
.cover-text {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-top: 15px;
  .el-radio {
    width: 100%;
  }
}
</style>
<style lang="scss">
.mk-cover {
  width: 100%;
  .cover-color-item {
    position: relative;
    width: 20px;
    height: 20px;
    padding: 0 9px;
    border-radius: 50%;
    margin-right: 5px;
    cursor: pointer;
    vertical-align: middle;
    &.picker {
      background: url("./picker.png") no-repeat center/cover;
      .el-color-picker,
      .el-color-picker__trigger {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: 0;
      }
    }
    &.active {
      &:after {
        display: block;
        position: absolute;
        content: "";
        width: 22px;
        height: 22px;
        z-index: 1;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        border: 1px solid #409eff;
        border-radius: 50%;
      }
    }
  }
  .el-upload {
    width: 100%;
  }
  ul,
  li {
    list-style: none;
  }
}
</style>

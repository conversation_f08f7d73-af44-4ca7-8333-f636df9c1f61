<template>
  <div class="search-user">
    <el-input
        placeholder="请输入搜索信息进行检索"
        v-model="keyWord"
        clearable
    >
      <template #prepend>
        <el-select v-model="select" style="width: 122px">
          <el-option label="用户名" value="user_name"></el-option>
          <el-option label="真实姓名" value="realname"></el-option>
          <el-option label="手机号" value="mobile"></el-option>
        </el-select>
      </template>
      <template #append>
        <el-button icon="el-icon-search" @click="searchUser"></el-button>
      </template>
    </el-input>
    <el-checkbox-group
        v-model="choosedUserList"
        v-loading="loading"
        class="user-content"
        @change="switchUser"
    >
      <template v-if="teacherList.length">
        <el-checkbox
            border
            v-for="item in teacherList"
            :label="item"
            :key="item.id"
            :disabled="disabledKeys.includes(item.id)"
        >
          <el-avatar
              :size="30"
              :src="item.avatar" style="vertical-align: middle;">
            <img :src="addFsUrl('aliba/avatar/default/default.png')"/>
          </el-avatar>
          <div>
            <p>{{item.realname}}<template v-if="item.schoolName"> | {{ item.schoolName }}</template></p>
            <p>{{item.userName}}<span style="margin-left: 10px">{{item.mobile}}</span></p>
          </div>
        </el-checkbox>
      </template>

      <mk-no-data v-else></mk-no-data>
    </el-checkbox-group>
  </div>
</template>

<script lang="ts">
import {defineComponent, onMounted, reactive, toRefs} from 'vue'
import {getRegionUserListByKeyWordAPI} from "@/library/src/service/API/tearesearch"
import {ElMessage,ElSelect,ElOption,ElInput,ElButton,ElAvatar} from "element-plus"
import MkNoData from '@/library/ui/mk-no-data'
import {addFsUrl} from "@/library/src/utils/globalFunction";

export default defineComponent({
  name: 'search-user',

  emits: ['switch-teacher'],

  components: {ElSelect,ElOption,ElInput,ElButton,ElAvatar,MkNoData},

  props: {
    // 禁用列表
    disabledKeys: {
      type: Array,
      default: ()=>[]
    },
    // 已选用户列表
    userList: {
      type: Array,
      default: ()=>[]
    },
  },

  setup(props, ctx) {

    const state = reactive({
      // 关键词
      keyWord: '',
      // 当前select值
      select: 'realname',
      // 页码
      page: 1,
      // 用户列表
      teacherList: [] as any[],
      // 已选用户列表
      choosedUserList: [] as any [],
      // 正在加载中
      loading: false,
    })

    /**
     * @name: 查询用户
     */
    const searchUser = ()=>{
      if( state.keyWord === '' ){
        ElMessage.warning('请输入检索信息!')
        return
      }
      state.loading = true
      getRegionUserListByKeyWordAPI({
        keyWord: state.keyWord,
        field: state.select,
        page: state.page,
        limit: 50
      }).then((res:any)=>{
          if(res.code === 1){
            state.choosedUserList = [...props.userList].map((item:any)=>{
              const newItem = res.data.rows.find((sitem:any)=>{return sitem.id === item.userId})
              if( newItem ){
                 return newItem
              }
              return {
                id: item.userId,
                realname: item.realName,
                avatar: item.avatar,
                schoolid: item.schoolId,
                schoolName: item.school_name,
                rowNum: item.rowNum,
                ...item,
              }
            })
            state.teacherList = res.data.rows
            switchUser()
          }else{
            ElMessage.warning(res.msg)
          }
          state.loading = false
      }).catch((err: any)=>{
          state.loading = false
          ElMessage.warning(err.msg)
      })
    }

    /**
     * @name: 切换用户
     */
    const switchUser = ()=>{
      const userList = [...state.choosedUserList].map((item:any)=>{
        return {
          userId: item.userId || item.id,
          realName: item.realName || item.realname,
          schoolId: item.schoolId || item.schoolid,
          school_name: item.school_name || item.schoolName,
          ...item,
        }
      })
      ctx.emit('switch-teacher', userList)
    }
    /**
     * @name: 清空用户
     */
    const clear = ()=>{
      state.choosedUserList = []
      switchUser()
    }
    /**
     * @name: 删除用户
     */
    const deleteUser = (item:any)=>{
      state.choosedUserList = state.choosedUserList.filter((sitem:any)=>{
        return sitem.id !== item.userId
      })
      switchUser()
    }

    onMounted(()=>{
      state.choosedUserList = props.userList
    })

    return {
      ...toRefs(state),
      addFsUrl,
      searchUser,
      switchUser,
      clear,
      deleteUser
    }
  }
})
</script>

<style lang="scss">
.search-user{
  .el-input{
    width: calc(100% - 20px);
    margin-left: 10px;
  }

  .el-input-group__prepend{
    border-radius: 20px 0 0 20px;
    .el-select .el-input.is-focus .el-input__inner{
      border-color: transparent !important;
    }
  }
  .el-input-group__append{
    border-radius: 0 20px 20px 0;
  }
  .user-content{
    max-height: 500px;
    overflow: auto;
    .el-checkbox{
      display: flex;
      align-items: center;
      margin: 15px 10px;
      height: unset;
      .el-checkbox__label{
        display: flex;
        align-items: center;
        .el-avatar{
          margin-right: 8px;
        }
      }
    }
  }
}
</style>

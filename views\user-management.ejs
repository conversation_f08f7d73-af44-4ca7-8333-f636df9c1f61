<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>用户管理 - 前端部署工具</title>
  <link href="/bootstrap/bootstrap.min.css" rel="stylesheet">
  <link href="/bootstrap/bootstrap-icons.css" rel="stylesheet">
  <style>
    .sidebar {
      min-height: 100vh;
      background-color: #f8f9fa;
    }
    .nav-link.active {
      background-color: #0d6efd;
      color: white !important;
    }
    .content-area {
      padding: 20px;
    }
    .user-card {
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
      background-color: #fff;
    }
    .status-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }
    .stats-card {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="container-fluid">
    <div class="row">
      <!-- 侧边栏 -->
      <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
        <div class="position-sticky pt-3">
          <h5 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>管理面板</span>
          </h5>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="/">
                <i class="bi bi-house"></i> 仪表盘
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/?page=projects">
                <i class="bi bi-folder"></i> 项目管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/?page=servers">
                <i class="bi bi-server"></i> 服务器管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/vcs-credentials">
                <i class="bi bi-key"></i> VCS凭据管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="/user-management">
                <i class="bi bi-people"></i> 用户管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/queue">
                <i class="bi bi-list-task"></i> 构建队列
              </a>
            </li>
          </ul>
        </div>
      </nav>

      <!-- 主内容区域 -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 class="h2">用户管理</h1>
          <div class="btn-toolbar mb-2 mb-md-0">
            <button class="btn btn-sm btn-outline-primary me-2" id="refresh-btn">
              <i class="bi bi-arrow-clockwise"></i> 刷新
            </button>
            <button class="btn btn-sm btn-outline-success" id="create-admin-btn">
              <i class="bi bi-person-plus"></i> 创建管理员
            </button>
          </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4">
          <div class="col-12">
            <div class="stats-card">
              <div class="row" id="user-stats">
                <!-- 统计数据将在这里动态加载 -->
              </div>
            </div>
          </div>
        </div>

        <!-- 标签页 -->
        <ul class="nav nav-tabs" id="userTabs" role="tablist">
          <li class="nav-item" role="presentation">
            <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab">
              <i class="bi bi-clock"></i> 待审核用户 <span class="badge bg-warning ms-1" id="pending-count">0</span>
            </button>
          </li>
          <li class="nav-item" role="presentation">
            <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
              <i class="bi bi-people"></i> 所有用户
            </button>
          </li>
        </ul>

        <!-- 标签页内容 -->
        <div class="tab-content" id="userTabContent">
          <!-- 待审核用户 -->
          <div class="tab-pane fade show active" id="pending" role="tabpanel">
            <div class="mt-3">
              <div id="pending-users-container">
                <!-- 待审核用户列表将在这里动态加载 -->
              </div>
            </div>
          </div>

          <!-- 所有用户 -->
          <div class="tab-pane fade" id="all" role="tabpanel">
            <div class="mt-3">
              <div id="all-users-container">
                <!-- 所有用户列表将在这里动态加载 -->
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 创建管理员模态框 -->
  <div class="modal fade" id="createAdminModal" tabindex="-1" aria-labelledby="createAdminModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="createAdminModalLabel">创建管理员</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="create-admin-form">
            <div class="mb-3">
              <label for="admin-username" class="form-label">用户名</label>
              <input type="text" class="form-control" id="admin-username" required>
            </div>
            <div class="mb-3">
              <label for="admin-password" class="form-label">密码</label>
              <input type="password" class="form-control" id="admin-password" required>
            </div>
            <div class="mb-3">
              <label for="admin-email" class="form-label">邮箱</label>
              <input type="email" class="form-control" id="admin-email">
            </div>
            <div class="mb-3">
              <label for="admin-displayName" class="form-label">显示名称</label>
              <input type="text" class="form-control" id="admin-displayName">
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="save-admin-btn">创建管理员</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 用户详情模态框 -->
  <div class="modal fade" id="userDetailModal" tabindex="-1" aria-labelledby="userDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="userDetailModalLabel">用户详情</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body" id="user-detail-content">
          <!-- 用户详情将在这里动态加载 -->
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
        </div>
      </div>
    </div>
  </div>

  <script src="/bootstrap/bootstrap.bundle.min.js"></script>
  <script src="/js/user-management.js"></script>
</body>
</html>

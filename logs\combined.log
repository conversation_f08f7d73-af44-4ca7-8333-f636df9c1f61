{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:18:26.523Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:18:27.054Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:18:27.112Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:18:30.180Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:18:30.827Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:18:30.925Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:19:15.808Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:19:25.528Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:19:25.584Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:19:46.628Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:19:47.214Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:19:47.235Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:20:17.702Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:20:18.363Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:20:18.430Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:22:08.477Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:22:09.095Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:22:09.172Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:22:10.700Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:22:11.271Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:22:11.348Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:23:36.146Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:23:36.803Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:23:36.861Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:24:00.327Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:24:01.175Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:24:01.309Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:24:16.740Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:24:17.405Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:24:17.500Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:25:02.351Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:25:30.795Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:25:49.059Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:25:49.543Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:25:49.571Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:29:01.587Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:29:01.843Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:29:01.866Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T01:34:16.300Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:34:19.800Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:34:22.273Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:34:22.686Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:34:22.730Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:34:23.747Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:34:23.874Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:34:23.889Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T01:38:48.438Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:38:48.804Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:38:52.496Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:38:52.792Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:38:52.812Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T01:40:59.498Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:41:04.806Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:41:34.745Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:41:35.012Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:41:35.034Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:45:38.603Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:45:38.617Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T01:48:01.299Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:48:01.812Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:48:06.222Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:48:06.606Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:48:06.667Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:50:38.169Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:50:38.174Z"}
{"level":"error","message":"获取构建队列失败: 获取构建队列失败: D:\\代码\\datedu-hw\\deploy-dev\\data\\queue\\queue.json: Unexpected end of JSON input","timestamp":"2025-07-16T01:50:42.354Z"}
{"level":"error","message":"构建失败: 更新构建状态失败: 构建任务不存在","timestamp":"2025-07-16T01:51:28.722Z"}
{"level":"error","message":"处理构建队列失败: 更新构建状态失败: 构建任务不存在","timestamp":"2025-07-16T01:51:28.726Z"}
{"level":"error","message":"构建失败: 更新构建状态失败: 构建任务不存在","timestamp":"2025-07-16T01:51:28.767Z"}
{"level":"error","message":"处理构建队列失败: 更新构建状态失败: 构建任务不存在","timestamp":"2025-07-16T01:51:28.773Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T01:52:27.179Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:52:27.350Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T01:52:49.291Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:52:49.863Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:52:49.899Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T01:56:09.971Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T01:56:14.054Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T01:56:14.097Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T02:01:46.219Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:02:19.640Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T02:02:20.706Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:02:21.173Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T02:02:21.194Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T02:08:57.327Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:09:03.746Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T02:09:04.024Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:09:04.427Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T02:09:04.476Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T02:10:00.097Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:10:00.102Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T02:12:37.861Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:12:40.807Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T02:12:44.779Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:12:45.229Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T02:12:45.280Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T02:14:27.333Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:14:54.794Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T02:16:52.167Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:16:52.315Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T02:17:09.148Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:17:09.655Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T02:17:09.725Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T02:20:27.127Z"}
{"level":"error","message":"构建失败: 部署失败: 构建项目失败: Command failed: D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752632261897.bat\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nDebugger attached.\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T02_17_51_908Z-debug-0.log\nWaiting for the debugger to disconnect...\r\n","timestamp":"2025-07-16T02:20:27.136Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T02:20:55.437Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:20:58.384Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T02:20:58.681Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T02:20:59.163Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T02:20:59.210Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T03:12:09.786Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T03:12:17.417Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T03:12:18.778Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T03:12:19.202Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T03:12:19.280Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T03:19:28.968Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T03:19:29.070Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T03:19:32.167Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T03:19:32.570Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T03:19:32.619Z"}
{"level":"error","message":"构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c \"D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752635992036.bat\"\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T03_19_53_915Z-debug-0.log\n","timestamp":"2025-07-16T03:19:53.993Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T03:21:20.237Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T03:21:25.797Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T03:21:28.418Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T03:21:28.880Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T03:21:28.922Z"}
{"level":"error","message":"构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c \"D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752636123612.bat\"\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nDebugger attached.\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T03_22_06_362Z-debug-0.log\nWaiting for the debugger to disconnect...\r\n","timestamp":"2025-07-16T03:22:07.777Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T03:26:08.234Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T03:26:08.239Z"}
{"level":"error","message":"构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c \"D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752636370057.bat\"\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nDebugger attached.\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T03_26_15_439Z-debug-0.log\nWaiting for the debugger to disconnect...\r\n","timestamp":"2025-07-16T03:26:18.003Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T03:49:07.338Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T03:49:07.391Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T03:49:32.875Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T03:49:33.439Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T03:49:33.537Z"}
{"level":"error","message":"构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752637811569.bat\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nDebugger attached.\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T03_50_16_595Z-debug-0.log\nWaiting for the debugger to disconnect...\r\n","timestamp":"2025-07-16T03:50:24.771Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T05:49:22.780Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T05:49:22.833Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T05:49:27.333Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T05:49:27.716Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T05:49:27.798Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-16T05:51:44.218Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T05:51:45.800Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-16T05:52:00.463Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-16T05:52:00.698Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-16T05:52:00.725Z"}
{"level":"error","message":"构建失败: 部署失败: 安装依赖失败: 安装依赖失败: Command failed: cmd.exe /c D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\npm_install_1752645136714.bat\n'nvs' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n","timestamp":"2025-07-16T05:52:16.858Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-19T00:53:54.265Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-19T00:54:13.033Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-19T00:54:13.087Z"}
{"level":"error","message":"构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752886636849.bat\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nnpm error Missing script: \"build:test\"\nnpm error\nnpm error To see a list of scripts, run:\nnpm error   npm run\n\nnpm error A complete log of this run can be found in: C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-19T00_57_18_035Z-debug-0.log\n","timestamp":"2025-07-19T00:57:18.305Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-19T01:02:15.283Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-19T01:02:15.504Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-19T01:02:17.674Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-19T01:02:18.318Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-19T01:02:18.373Z"}
{"level":"info","message":"服务器运行在端口 3000","timestamp":"2025-07-19T01:11:36.839Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-19T01:11:36.889Z"}
{"level":"info","message":"客户端已断开连接","timestamp":"2025-07-19T01:12:12.044Z"}
{"level":"info","message":"客户端已连接","timestamp":"2025-07-19T01:12:12.435Z"}
{"level":"info","message":"用户 yueliu 已连接","timestamp":"2025-07-19T01:12:12.484Z"}

const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

class User {
  constructor() {
    this.configDir = path.join(process.cwd(), 'data', 'users');
    fs.ensureDirSync(this.configDir);
    this.usersFile = path.join(this.configDir, 'users.json');
    this.initUsersFile();
  }

  // 初始化用户配置文件
  async initUsersFile() {
    try {
      if (!(await fs.pathExists(this.usersFile))) {
        await fs.writeJson(this.usersFile, { users: [] }, { spaces: 2 });
      }
    } catch (error) {
      throw new Error(`初始化用户配置文件失败: ${error.message}`);
    }
  }

  // 获取所有用户
  async getAll() {
    try {
      const data = await fs.readJson(this.usersFile);
      // 返回不含密码的用户信息
      return data.users.map(user => {
        const { password, salt, ...safeUser } = user;
        return safeUser;
      });
    } catch (error) {
      throw new Error(`获取用户列表失败: ${error.message}`);
    }
  }

  // 根据ID获取用户
  async getById(id) {
    try {
      const data = await fs.readJson(this.usersFile);
      const user = data.users.find(user => user.id === id);
      if (!user) return null;
      
      // 返回不含密码的用户信息
      const { password, salt, ...safeUser } = user;
      return safeUser;
    } catch (error) {
      throw new Error(`获取用户详情失败: ${error.message}`);
    }
  }

  // 根据用户名获取用户
  async getByUsername(username) {
    try {
      const data = await fs.readJson(this.usersFile);
      return data.users.find(user => user.username === username) || null;
    } catch (error) {
      throw new Error(`获取用户详情失败: ${error.message}`);
    }
  }

  // 创建新用户
  async create(userData) {
    try {
      const data = await fs.readJson(this.usersFile);
      
      // 检查用户名是否已存在
      if (data.users.some(user => user.username === userData.username)) {
        throw new Error('用户名已存在');
      }
      
      const id = uuidv4();
      const salt = crypto.randomBytes(16).toString('hex');
      const hashedPassword = this.hashPassword(userData.password, salt);
      
      const user = {
        id,
        username: userData.username,
        displayName: userData.displayName || userData.username,
        email: userData.email,
        password: hashedPassword,
        salt,
        role: userData.role || 'user', // 'admin' 或 'user'
        status: userData.status || 'pending', // 'pending', 'active', 'disabled'
        approvedBy: null, // 审核人ID
        approvedAt: null, // 审核时间
        lastLoginAt: null, // 最后登录时间
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      data.users.push(user);
      await fs.writeJson(this.usersFile, data, { spaces: 2 });
      
      // 返回不含密码的用户信息
      const { password, salt: userSalt, ...safeUser } = user;
      return safeUser;
    } catch (error) {
      throw new Error(`创建用户失败: ${error.message}`);
    }
  }

  // 更新用户
  async update(id, userData) {
    try {
      const data = await fs.readJson(this.usersFile);
      const userIndex = data.users.findIndex(user => user.id === id);
      
      if (userIndex === -1) {
        throw new Error('用户不存在');
      }
      
      const updatedUser = {
        ...data.users[userIndex],
        ...userData,
        updatedAt: new Date().toISOString()
      };
      
      // 如果更新了密码，重新哈希
      if (userData.password) {
        updatedUser.salt = crypto.randomBytes(16).toString('hex');
        updatedUser.password = this.hashPassword(userData.password, updatedUser.salt);
      }
      
      data.users[userIndex] = updatedUser;
      await fs.writeJson(this.usersFile, data, { spaces: 2 });
      
      // 返回不含密码的用户信息
      const { password, salt, ...safeUser } = updatedUser;
      return safeUser;
    } catch (error) {
      throw new Error(`更新用户失败: ${error.message}`);
    }
  }

  // 删除用户
  async delete(id) {
    try {
      const data = await fs.readJson(this.usersFile);
      const userIndex = data.users.findIndex(user => user.id === id);
      
      if (userIndex === -1) {
        return false;
      }
      
      data.users.splice(userIndex, 1);
      await fs.writeJson(this.usersFile, data, { spaces: 2 });
      return true;
    } catch (error) {
      throw new Error(`删除用户失败: ${error.message}`);
    }
  }

  // 验证用户密码
  async validateUser(username, password) {
    try {
      const user = await this.getByUsername(username);
      if (!user) return null;

      // 检查用户状态
      if (user.status !== 'active') {
        return { error: 'account_not_active', status: user.status };
      }

      const hashedPassword = this.hashPassword(password, user.salt);
      if (hashedPassword === user.password) {
        // 更新最后登录时间
        await this.updateLastLogin(user.id);

        // 返回不含密码的用户信息
        const { password, salt, ...safeUser } = user;
        return safeUser;
      }

      return null;
    } catch (error) {
      throw new Error(`验证用户失败: ${error.message}`);
    }
  }

  // 审核用户（批准或拒绝）
  async approveUser(userId, adminId, approved = true) {
    try {
      const data = await fs.readJson(this.usersFile);
      const userIndex = data.users.findIndex(user => user.id === userId);

      if (userIndex === -1) {
        throw new Error('用户不存在');
      }

      const user = data.users[userIndex];
      if (user.status !== 'pending') {
        throw new Error('用户状态不是待审核状态');
      }

      user.status = approved ? 'active' : 'disabled';
      user.approvedBy = adminId;
      user.approvedAt = new Date().toISOString();
      user.updatedAt = new Date().toISOString();

      data.users[userIndex] = user;
      await fs.writeJson(this.usersFile, data, { spaces: 2 });

      // 返回不含密码的用户信息
      const { password, salt, ...safeUser } = user;
      return safeUser;
    } catch (error) {
      throw new Error(`审核用户失败: ${error.message}`);
    }
  }

  // 更新用户状态
  async updateStatus(userId, status, adminId = null) {
    try {
      const data = await fs.readJson(this.usersFile);
      const userIndex = data.users.findIndex(user => user.id === userId);

      if (userIndex === -1) {
        throw new Error('用户不存在');
      }

      const validStatuses = ['pending', 'active', 'disabled'];
      if (!validStatuses.includes(status)) {
        throw new Error('无效的用户状态');
      }

      const user = data.users[userIndex];
      user.status = status;
      user.updatedAt = new Date().toISOString();

      if (adminId && (status === 'active' || status === 'disabled')) {
        user.approvedBy = adminId;
        user.approvedAt = new Date().toISOString();
      }

      data.users[userIndex] = user;
      await fs.writeJson(this.usersFile, data, { spaces: 2 });

      // 返回不含密码的用户信息
      const { password, salt, ...safeUser } = user;
      return safeUser;
    } catch (error) {
      throw new Error(`更新用户状态失败: ${error.message}`);
    }
  }

  // 更新最后登录时间
  async updateLastLogin(userId) {
    try {
      const data = await fs.readJson(this.usersFile);
      const userIndex = data.users.findIndex(user => user.id === userId);

      if (userIndex !== -1) {
        data.users[userIndex].lastLoginAt = new Date().toISOString();
        await fs.writeJson(this.usersFile, data, { spaces: 2 });
      }
    } catch (error) {
      // 忽略更新最后登录时间的错误
      console.error('更新最后登录时间失败:', error.message);
    }
  }

  // 获取待审核用户
  async getPendingUsers() {
    try {
      const data = await fs.readJson(this.usersFile);
      return data.users
        .filter(user => user.status === 'pending')
        .map(user => {
          const { password, salt, ...safeUser } = user;
          return safeUser;
        });
    } catch (error) {
      throw new Error(`获取待审核用户失败: ${error.message}`);
    }
  }

  // 获取用户统计信息
  async getUserStats() {
    try {
      const data = await fs.readJson(this.usersFile);
      const stats = {
        total: data.users.length,
        active: data.users.filter(u => u.status === 'active').length,
        pending: data.users.filter(u => u.status === 'pending').length,
        disabled: data.users.filter(u => u.status === 'disabled').length,
        admins: data.users.filter(u => u.role === 'admin').length,
        users: data.users.filter(u => u.role === 'user').length
      };
      return stats;
    } catch (error) {
      throw new Error(`获取用户统计失败: ${error.message}`);
    }
  }

  // 哈希密码
  hashPassword(password, salt) {
    return crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512').toString('hex');
  }
}

module.exports = new User(); 
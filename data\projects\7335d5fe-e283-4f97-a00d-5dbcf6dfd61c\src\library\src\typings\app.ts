/**
 * @name 面包屑
 */
export interface IBreadcrumb {
  /** 标题 */
  title: string;
  /** 路由路径 */
  path: string;
  /** 路由名称 */
  name: string;
  /** 路由参数 */
  query: any;
  /** 是否是可跳转的链接 */
  isLink: boolean;
}

export interface IAppState {
  /** 平台信息 */
  platFormInfo: IPlatForm;
  /** 站点信息 */
  siteInfo: ISite;
  /** 来源 区域: '', 教师空间: teachermain 网络研修:research */
  source?: string;
}

/** 站点信息 */
export interface ISite {
  /** logo */
  logo: string;
  /** icon */
  icon: string;
  /** 站点名称 */
  title: string;
  /** 主题色 */
  theme: string;
  /** 菜单数据json */
  menuDataJson: any[];
  /** 功能数据json */
  functionDataJson: any[];
  /** 页脚数据数据json */
  footDataJson: any;
  /** 名称 */
  name?: {
    /** 文本内容 */
    text: string;
    /** 是否展示 */
    isShow: boolean;
    /** 颜色 */
    color: string;
  };
  /** 标语 */
  slogan?: {
    /** 文本内容 */
    text: string;
    /** 是否展示 */
    isShow: boolean;
    /** 颜色 */
    color: string;
  };
  /** 头部标题 */
  hlogo?: {
    /** 文本内容 */
    text: string;
    /** 是否展示 */
    isShow: boolean;
  };
  /** 页眉信息 */
  header?: {
    /** 文本颜色 */
    color: string;
    /** 背景色 */
    bgColor: string;
    /** 是否展示 */
    isShow: boolean;
  };
  /** 页尾信息 */
  footer?: {
    /** 文本颜色 */
    color: string;
    /** 背景色 */
    bgColor: string;
    /** 是否展示 */
    isShow: boolean;
  };
  /** 公共配置 */
  setting?: {
    /** 页面宽度 */
    mainWidth: number | string
    /** 首页置灰比 */
    grayscale: number
    /** 上传最大限制 */
    maxUpload: number
  }

}

/** 平台信息 */
export interface IPlatForm {
  /** 平台类型 1:区域平台 2:校级平台 */
  platformType: string;
  /** 域名 */
  platformDomain: string;
  /** 平台名称 */
  platformName: string;
  /** 区域信息 */
  region: IRegion[];
  /** 区域平台类型 1:省级 2:市级 3:区级 4:自定义 5:校级*/
  regionPlatformType: string;
  /** 平台开发状态 */
  platformState: number;
  /** 平台id */
  id: string;
  /** 校级平台学校id */
  schoolId?: string;
}

/** 区域信息 */
export interface IRegion {
  /** 市名称 */
  cityName: string;
  /** 市id */
  cityId: string;
  /** 省名称 */
  provinceName: string;
  /** 区id */
  districtId: string;
  /** 区名称 */
  districtName: string;
  /** 省id */
  provinceId: string;
}

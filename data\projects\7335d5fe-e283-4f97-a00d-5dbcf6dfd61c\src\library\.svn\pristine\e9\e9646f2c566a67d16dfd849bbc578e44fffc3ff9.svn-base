<template>
  <div class="res-box">
    <!--切换教材-->
    <div class="book_box clearfix">
      <template v-if="isSpecial">
        <div
          class="currentBook ellipsis"
          :title="specialData.title"
          v-show="specialData.title"
        >
          <img
            class="iconfont"
            :src="require('@/library/ui/mk-share-res/assets/book_name.png')"
          />
          {{ specialData.title }}
        </div>
      </template>
      <template v-else>
        <div
          class="currentBook ellipsis"
          :title="bookData.bookName"
          v-show="bookData.bookName"
        >
          <img
            class="iconfont"
            :src="require('@/library/ui/mk-share-res/assets/book_name.png')"
          />
          {{ bookData.bookName }}
        </div>
      </template>

      <div class="chanke_book flex-end" @click="showDialog">
        <img
          :src="require('@/library/ui/mk-share-res/assets/change_book.png')"
          class="iconfont"
        />
        {{ isSpecial ? '切换专题' : '切换教材' }}
      </div>
    </div>

    <div class="book-menu">
      <platform-label
        ref="platformLabelRef"
        :bookCode="bookData.bookCode"
        :catalogCode="bookData.catalogCode"
        :platFromId="platFromId"
        :specialId="specialData.id"
        :isSpecial="isSpecial"
        :shareType="shareType"
        @switch-label="switchLabel"
        @switch-catalog="switchCatalog"
        @switch-customlabel="switchCustomLabel"
        @switch-knowledge="switchKnowLedge"
      >
      </platform-label>
    </div>
    <!--类型-->
    <template v-if="isShowTypeList">
      <div class="res-tips" v-if="isShowCancle && typeTips">{{ typeTips }}</div>
      <div class="dic_list">

        <div class="dic_box">
          <el-radio
            class="dic_item"
            v-model="typeCode"
            :label="item.dict_code"
            v-for="item of shareTypeList"
            :key="item.id"
            @change="chooseType(item)"
            :disabled="item.disabled"
          >
            {{ item.dict_name }}
          </el-radio>
        </div>
      </div>

      <p class="tip"><i class="tooltip-icon el-icon-warning"></i> 一旦管理员确认同步至资源中心，分享者无法取消同步</p>
    </template>
    <!--底部按钮插槽-->
    <slot name="footer">
      <div class="flex-center share-btn">
        <el-button @click="closeDialog" v-if="isShowCancle">取 消</el-button>
        <el-button type="primary" @click="confirmDialog">
          <slot name="confirmText">确 定</slot>
        </el-button>
      </div>
    </slot>

    <!--切换教材,专题弹窗-->
    <platform-book-dialog
      v-if="isShowPlatformBookDialog"
      :platFromId="platFromId"
      :tab="isSpecial ? TAB_TYPE.special : TAB_TYPE.textbook"
      :curBookData="bookData"
      :curSpecialData="specialData"
      :isShowTab="isShowTab"
      :shareType="shareType"
      @on-cancle="isShowPlatformBookDialog = false"
      @switch-book="getCurrentTextbook"
      @switch-special="switchSpecial"
    >
    </platform-book-dialog>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, ref, watch } from 'vue';
import { useStore } from 'vuex';
import { IGlobalState } from '@/library/src/store';
import { getDictListAPI } from '@/library/src/service/API/base';
import {
  getCommonCatchListAPI,
  insertCommonCatchAPI
} from '@/library/src/service/API/export';
import { ElMessage } from 'element-plus';
import PlatformLabel from '@/library/ui/mk-share-res/modules/PlatformLabel.vue';
import PlatformBookDialog from '@/library/ui/mk-share-res/modules/PlatformBookDialog.vue';
import { LABEL_TYPE } from '@/library/ui/mk-share-res/modules/PlatformLabelItem.vue';
/** tab类型 */
export enum TAB_TYPE {
  /** 统一教材 */
  textbook = 'textbook',
  /** 专题资源 */
  special = 'special'
}
export default defineComponent({
  name: 'index',

  components: {
    PlatformLabel,
    PlatformBookDialog
  },

  props: {
    //分享类型
    shareType: {
      type: String,
      default: ''
    },
    //资源类型
    type: {
      type: String,
      default: ''
    },
    // 主键id
    id: {
      type: String,
      default: ''
    },
    //区域id
    platFromId: {
      type: String,
      default: ''
    },
    //是否展示取消按钮
    isShowCancle: {
      type: Boolean,
      default: true
    },
    //默认类型值code
    radioCode: {
      type: String,
      default: ''
    },
    //默认类型名称
    radioName: {
      type: String,
      default: ''
    },
    //类型提示语
    typeTips: {
      type: String,
      default: 'Step2：请选择资源包类型'
    },
    //是否展示类型列表
    isShowTypeList: {
      type: Boolean,
      default: true
    },
    //是否展示专题
    isShowSpecial: {
      type: Boolean,
      default: false
    },
    //专题id
    specialId: {
      type: String,
      default: ''
    },
    //专题名称
    specialName: {
      type: String,
      default: ''
    },
    //是否展示tab页切换
    isShowTab: {
      type: Boolean,
      default: true
    },

    // 配置项
    configJson: {
      type: String,
      default: '{}'
    }

  },
  setup(props, context) {
    const store = useStore<IGlobalState>();
    const platformLabelRef = ref<any>(null);
    const state = reactive({
      //教材数据
      bookData: {
        gradeCode: '',
        gradeName: '',
        subjectCode: '',
        subjectName: '',
        editionCode: '',
        editionName: '',
        bookCode: '',
        bookName: '',
        catalogCode: '',
        catalogId: '',
        catalogName: '',
        catalogPathCode: [] as any[],
        catalogPathName: [] as any[],
        bookType:""
      },
      // 专题数据
      specialData: {
        id: props.specialId,
        title: props.specialName
      },
      //绑定的类型值
      typeCode: props.radioCode,
      //绑定的类型名称
      typeName: props.radioName,
      //类型数据
      shareTypeList: [] as any[],
      // 是否展示教材/专题选择弹窗
      isShowPlatformBookDialog: false,
      // 是否专题
      isSpecial: props.isShowSpecial,
      // 已选择树路径数据
      selectTreePathList: [] as any[],
      // 标签列表
      labelList: [] as any[]
    });

    /**
     * @name 选择类型
     */
    const chooseType = (item: any) => {
      state.typeCode = item.dict_code;
      state.typeName = item.dict_name;
    };
    /**
     * @name 获取标签
     */
    const getResTags = async () => {
      const params = {
        typecode: 313,
        limit: 50
      };
      const res = await getDictListAPI(params);
      if (res.code == 1) {
        state.shareTypeList = res.data.rows;
        state.shareTypeList.forEach((item: any) => {
          //资源类型为课件时，微课的选项为禁用
          item.disabled = false;
          if (props.type == '2' && item.dict_code == '31306') {
            item.disabled = true;
          } else {
            item.disabled = false;
          }
        });
        const index = state.shareTypeList.findIndex((sitem: any) => {
          return sitem.dict_code === props.radioCode;
        });
        if (index === -1) {
          state.typeCode = '';
          state.typeName = '';
        } else {
          state.typeCode = props.radioCode;
          state.typeName = props.radioName;
        }
      } else {
        ElMessage({
          message: res.msg,
          type: 'error'
        });
      }
    };
    /**
     * @name 获取当前切换的教材
     * @param item 当前教材
     */
    const getCurrentTextbook = (selectBookData: any) => {
      switchRegionBook(selectBookData);
    };

    /**
     * @name 获取公共缓存--获取默认的书本
     */
    const getCommonCatchList = async () => {
      let bookParams: any = localStorage.getItem(props.platFromId + '_selectBookInfo');
      if (bookParams) {
        bookParams = JSON.parse(bookParams);
        state.bookData = {
          ...state.bookData,
          gradeCode: bookParams.gradeCode,
          gradeName: bookParams.gradeName,
          subjectCode: bookParams.subjectCode,
          subjectName: bookParams.subjectName,
          editionCode: bookParams.editionCode,
          editionName: bookParams.editionName,
          bookCode: bookParams.bookCode,
          bookName: bookParams.bookName,
          bookType:bookParams.bookType
        };
        return;
      }
      const params = {
        appType: '122',
        uId: store.state.user.userId
      };
      const res = await getCommonCatchListAPI(params);
      if (res.code == 1) {
        if (res.data && res.data.catch_list) {
          //对数据的json格式处理
          const cacheData = JSON.parse(res.data.catch_list);
          const bookParams = JSON.parse(cacheData.bookParams);
          state.bookData = {
            ...state.bookData,
            gradeCode: bookParams.gradeCode,
            gradeName: bookParams.gradeName,
            subjectCode: bookParams.subjectCode,
            subjectName: bookParams.subjectName,
            editionCode: bookParams.editionCode,
            editionName: bookParams.editionName,
            bookCode: bookParams.bookCode,
            bookName: bookParams.bookName,
            bookType:bookParams.bookType
          };
        }
      } else {
        ElMessage({ message: res.msg, type: 'error' });
      }
    };
    /**
     * @name 选中书本目录
     */
    const switchCatalog = (v: any, selectTreePathList: any[]) => {
      state.selectTreePathList = selectTreePathList;
      state.bookData.catalogPathCode = [];
      state.bookData.catalogPathName = [];
      state.selectTreePathList.forEach((item: any) => {
        state.bookData.catalogPathCode.push(item.code);
        state.bookData.catalogPathName.push(item.title);
      });
      state.bookData = {
        ...state.bookData,
        catalogCode: v ? v.code : '',
        catalogId: v ? v.id : '',
        catalogName: v ? v.title : ''
      };
      insertCache();
    };
    /**
     *@name 取消
     */
    const closeDialog = () => {
      context.emit('close-dialog');
    };
    /**
     *@name 确定
     */
    const confirmDialog = () => {
      if (!state.isSpecial && state.bookData.bookCode == '') {
        ElMessage({ message: '请选择教材！', type: 'warning' });
        return;
      }
      //校验必选项
      if (!validateForm()) {
        return;
      }
      //资源类型
      if (!state.typeCode && props.isShowTypeList) {
        ElMessage({ message: '请选择资源类型！', type: 'warning' });
        return;
      }
      const configJson = getConfigJSON();
      let data = {
        configJson,
        labelList: JSON.stringify(state.labelList)
      };
      context.emit('sure-dialog', data);
    };
    /**
     * @name 校验表单
     */
    const validateForm = () => {
      if (!platformLabelRef.value.validateForm()) {
        return false;
      }
      return true;
    };
    /**
     * @name: 公共缓存
     */
    const insertCache = () => {
      const gradeId = 'grade' + state.bookData.gradeCode;
      const subjectId = 'subject' + state.bookData.subjectCode;
      const editionId = 'edition' + state.bookData.editionCode;
      const bookId = 'book' + state.bookData.bookCode;
      const bookParams = {
        gradeCode: state.bookData.gradeCode,
        gradeId,
        gradeName: state.bookData.gradeName,
        subjectCode: state.bookData.subjectCode,
        subjectId,
        subjectName: state.bookData.subjectName,
        editionCode: state.bookData.editionCode,
        editionId,
        editionName: state.bookData.editionName,
        bookCode: state.bookData.bookCode,
        bookId,
        bookName: state.bookData.bookName,
        bookType: state.bookData.bookType,
        // catalogCode: state.bookData.catalogCode,
        // catalogName: state.bookData.catalogName,
        // catalogId: state.bookData.catalogId,
        // catalogPathCode: state.bookData.catalogPathCode,
        // catalogPathName: state.bookData.catalogPathName,
        bookPick: [gradeId, subjectId, editionId, bookId]
      };
      const catalogSelectList =
        state.bookData.catalogId === '' ? [] : [state.bookData.catalogId];
      const catchData = {
        bookParams: JSON.stringify(bookParams),
        catalogSelectList: JSON.stringify(catalogSelectList)
      };
      localStorage.setItem(
        props.platFromId + '_selectBookInfo',
        JSON.stringify(bookParams)
      );
      insertCommonCatchAPI({
        appType: '122',
        uId: store.state.user.userId,
        catchList: JSON.stringify(catchData)
      });
    };
    /**
     * @name: 区域资源配置参数
     */
    const getConfigJSON = () => {
      let configJson;
      let catalogCode: any;
      let catalogName: any;
      catalogCode = state.bookData.catalogPathCode.length === 0 ? '' : [...state.bookData.catalogPathCode].pop();
      catalogName = state.bookData.catalogPathName.length === 0 ? '' : [...state.bookData.catalogPathName].pop();

      if (state.isSpecial) {
        configJson = {
          gradeCode: '',
          gradeName: '',
          subjectCode: '',
          subjectName: '',
          editionCode: '',
          editionName: '',
          bookCode: '',
          bookName: '',
          teamId: '',
          teamName: '',
          //活动类型
          teamType: -1, //3：集体备课，：4：听评课，5：活动评审
          // 目录
          catalogCode,
          catalogName,
          catalogPathCode: state.bookData.catalogPathCode,
          catalogPathName: state.bookData.catalogPathName,
          // 同步教材0, 专题:1
          type: 1,
          // type为0时,教材同步栏目id,教材同步栏目名称
          // type为1时,专题的id,专题名称
          id: state.specialData.id,
          name: state.specialData.title,
          // 资源包
          resourcePackage: false,
          typeCode: state.typeCode,
          typeName: state.typeName
        };
      } else {
        configJson = {
          gradeCode: state.bookData.gradeCode,
          gradeName: state.bookData.gradeName,
          subjectCode: state.bookData.subjectCode,
          subjectName: state.bookData.subjectName,
          editionCode: state.bookData.editionCode,
          editionName: state.bookData.editionName,
          bookCode: state.bookData.bookCode,
          bookName: state.bookData.bookName,
          teamId: '',
          teamName: '',
          //活动类型
          teamType: -1, //3：集体备课，：4：听评课，5：活动评审
          // 目录
          catalogCode,
          catalogName,
          catalogPathCode: state.bookData.catalogPathCode,
          catalogPathName: state.bookData.catalogPathName,
          // 同步教材0, 专题:1
          type: 0,
          // type为0时,教材同步栏目id,教材同步栏目名称
          // type为1时,专题的id,专题名称
          id:
            platformLabelRef && platformLabelRef.value
              ? platformLabelRef.value.columnData.id
              : '',
          name:
            platformLabelRef && platformLabelRef.value
              ? platformLabelRef.value.columnData.title
              : '',
          // 资源包
          resourcePackage: false,
          typeCode: state.typeCode,
          typeName: state.typeName,
          bookType:state.bookData.bookType
        };
      }
      return configJson;
    };
    /**
     * @name: 展示弹窗
     */
    const showDialog = () => {
      state.isShowPlatformBookDialog = true;
      state.selectTreePathList = [];
      //清空专题资源目录绑定值
      if (platformLabelRef) {
        platformLabelRef.value.specialCode = [];
      }
    };

    /**
     * @name 切换区域教材
     * @param item 当前教材
     */
    const switchRegionBook = (selectBookData: any) => {
      state.bookData = {
        catalogCode: '',
        catalogId: '',
        catalogName: '',
        catalogPathCode: [],
        catalogPathName: [],
        ...selectBookData
      };
      state.selectTreePathList = [];
      state.labelList = [];
      state.specialData = {
        id: '',
        title: ''
      };
      //清空教材同步目录绑定值和展开目录选中值
      if (platformLabelRef) {
        platformLabelRef.value.catalogCode = [];
        platformLabelRef.value.exCatalogId = '';
      }
      state.isSpecial = false;
      insertCache();
    };
    /**
     * @name: 切换专题
     * @param item 当前专题对象
     */
    const switchSpecial = (item: any) => {
      state.isSpecial = true;
      state.specialData = item;
      state.bookData.catalogCode = '';
      state.bookData.catalogId = '';
      state.bookData.catalogName = '';
      state.bookData.bookCode = '';
      state.bookData.catalogPathCode = [];
      state.bookData.catalogPathName = [];
      state.selectTreePathList = [];
      if (platformLabelRef.value) {
        platformLabelRef.value.exCatalogId = '';
      }
    };

    /**
     * @name: 切换标签
     * @param item 标签对象
     */
    const switchLabel = (item: any) => {
      const index = state.labelList.findIndex((sitem: any) => {
        return sitem.labelId === item.labelId;
      });
      if (index === -1) {
        state.labelList.push(item);
      } else {
        state.labelList.splice(index, 1, item);
      }
    };
    /**
     * @name: 切换知识点
     * @param knowledge 知识点列表
     */
    const switchKnowLedge = (knowledge: any[], selfResult: any[], curSubject: any, labelId: string) => {
      let optionCode: any[] = [];
      let optionName: any[] = [];
      let selfCode: any[] = [];
      let selfName: any[] = [];
      knowledge.forEach((item) => {
        optionCode.push(item.code);
        optionName.push(item.name);
      });
      selfResult.forEach((self) => {
        selfCode.push(self.code);
        selfName.push(self.name);
      });
      const label = {
        type: LABEL_TYPE.KNOWLEDGE,
        labelId,
        optionCode: optionCode.toString(),
        optionName: optionName.toString(),
        curSubject,
        selfCode: selfCode.toString(),
        selfName: selfName.toString()
      };
      switchLabel(label);
    };
    /**
     * @name: 切换自定义标签
     * @param customLabelList 自定义标签列表
     */
    const switchCustomLabel = (customLabelList: any) => {
      for (let i = 0; i < customLabelList.length; i++) {
        let item = customLabelList[i];
        const index = state.labelList.findIndex((sitem: any) => {
          return sitem.labelId === item.labelId;
        });
        if (index === -1) {
          state.labelList.push(item);
        } else {
          state.labelList.splice(index, 1, item);
        }
      }
    };

    onMounted(() => {
      getResTags();

      if( !['','{}'].includes(props.configJson) ){
        try {
          const configJson = JSON.parse(props.configJson)
          state.bookData = {
            gradeCode: configJson.gradeCode || '',
            gradeName: configJson.gradeName || '',
            subjectCode: configJson.subjectCode || '',
            subjectName: configJson.subjectName || '',
            editionCode: configJson.editionCode || '',
            editionName: configJson.editionName || '',
            bookCode: configJson.bookCode || '',
            bookName: configJson.bookName || '',
            catalogCode: configJson.catalogCode || '',
            catalogId: configJson.catalogId || '',
            catalogName: configJson.catalogName || '',
            catalogPathCode: configJson.catalogPathCode || [],
            catalogPathName: configJson.catalogPathName || [],
            bookType:configJson.bookType
          }
          state.specialData = {
            id: configJson.id || '',
            title: configJson.name || '',
          }
          state.typeCode = configJson.typeCode || '',
          state.typeName = configJson.typeName || ''
        }catch (e) {
        }
        return
      }
      getCommonCatchList();
    });

    return {
      ...toRefs(state),
      TAB_TYPE,
      switchCatalog,
      getCurrentTextbook,
      confirmDialog,
      chooseType,
      showDialog,
      switchRegionBook,
      switchSpecial,
      platformLabelRef,
      switchLabel,
      switchCustomLabel,
      switchKnowLedge,
      closeDialog
    };
  }
});
</script>

<style scoped lang="scss">
body {
  margin: 0;
}

.res-box {
  position: relative;
  height: 100%;

  .book_box {
    padding: 10px 0px;

    .clearfix {
      &:before,
      &:after {
        display: table;
        content: ' ';
      }

      &:after {
        clear: both;
      }
    }

    .currentBook {
      position: relative;
      font-size: 16px;
      max-width: 70%;
      line-height: 30px;
      cursor: pointer;
      padding-left: 30px;
      float: left;

      .iconfont {
        position: absolute;
        left: 0px;
        top: 55%;
        transform: translateY(-50%);
        width: 22px;
        height: 20px;
      }
    }

    .chanke_book {
      @include theme_color;
      position: relative;
      font-size: 16px;
      line-height: 30px;
      cursor: pointer;
      text-align: right;
      float: right;

      .iconfont {
        width: 19px;
        height: 18px;
        margin-right: 5px;
      }
    }
  }
  .res-tips {
    font-size: 15px;
    font-weight: bold;
    color: #4e5668;
    margin-top: 20px;
  }

  .dic_list {
    padding-top: 10px;
    font-size: 16px;
    display: flex;

    .dic_item {
      margin-bottom: 10px;
      margin-right: 20px;
      margin-left: 10px;

      ::v-deep(.el-radio__label) {
        padding-left: 8px;
        font-size: 16px;
      }
    }

    .dic_box {
      max-width: calc(100%);
    }
  }
  .tip{
    color: #999;
    .tooltip-icon{
      font-size: 16px;
    }
  }

  .el-form-item {
    text-align: right;
  }
  .book-menu {
    height: 285px;
    overflow: auto;
    position: relative;
    background: #f0f3f5;
    border-radius: 5px;
    padding: 15px;
  }

  .share-btn {
    text-align: center;
    cursor: pointer;
    margin: 10px auto 0px;
  }
}
</style>

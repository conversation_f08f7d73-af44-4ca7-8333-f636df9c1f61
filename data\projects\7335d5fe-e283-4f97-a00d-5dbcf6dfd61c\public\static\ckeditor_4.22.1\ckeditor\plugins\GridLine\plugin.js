/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2023-11-24 13:40:37
 * @LastEditors: l<PERSON><PERSON><PERSON> y<PERSON><PERSON>@class30.com
 * @LastEditTime: 2024-03-25 11:53:06
 */
/**
 * 自定义作文格
 */
CKEDITOR.plugins.add("GridLine", {
  requires: ["dialog"],
  init: function (editor) {
    editor.addCommand("GridLine", new CKEDITOR.dialogCommand("GridLine"));
    editor.ui.addButton("GridLine", {
      label: "插入作文格",
      command: "GridLine",
      cindex: 0,
      click: function (e) {
        editor.execCommand("GridLine");
      },
    });
    CKEDITOR.dialog.add("GridLine", this.path + "dialog/GridLine.js");
  },
});

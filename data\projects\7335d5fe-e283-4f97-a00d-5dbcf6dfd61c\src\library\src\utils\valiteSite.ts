﻿enum SITE_TYPE {
    // 上虞
    xzsy = 'xzsy',
    syjy = 'syjy',
    // 清徐
    qxjyy = 'qxjyy',
    qxzhjy = 'qxzhjy',
    // 成都高新区
    cdsgxq = 'cdsgxq',
    // 贵州剑河
    gzjh = 'gzjh',
    // 凉山中学
    lszx = 'lszx',
    // 金陵微校
    jlwx = 'jlwx',
    // 南京江宁区
    njjnq = 'njjnq',
    jn = 'jn',
    // 江宁教育
    jnjy = 'jnjy',
    // 绍兴越城区
    sxycq = 'sxycq',
    // 杭州临安
    hzla = 'hzla',
    // 东莞松山湖
    dgsshsyzx = 'dgsshsyzx',
    // 南充江东
    ncjdsyxx = 'ncjdsyxx',
    // 广州智慧教育公共服务平台
    gdjyy = 'gdjyy',
    //合肥八中
    hfbz = 'hfbz',
    // 合肥168
    hf168='hf168',
    // 临沧市第一中学
    ynlcyz='ynlcyz',
    //四川乐山五通桥
    sclswtq = 'sclswtq',
    // 吴兴(吴兴优学,吴兴e答)
    wx = 'wx',
    // 呼和浩特
    huhhotdyzx = 'huhhotdyzx',
    // 山西晋中和顺县
    hsjy = 'hsjy'
}

const getReg = (site: SITE_TYPE) => {
    return new RegExp(SITE_TYPE[site], 'g')
}

const getHost = (isHost = false) => {
    const path = isHost ? location.host : location.host.split('.')[0]
    let host = path.replace(/test|pre|prod|new/g, '')
    if (host === 'jlwxjyy') {
        host = 'jlwx'
    }
    return host
}

/**
 * @name: 是否测试环境
 */
const isTest = () => {
    return /dev|test/g.test(process.env.VUE_APP_MODE_ENV + '')
}
/**
 * @name: 是否上虞
 */
const isSY = () => {
    const host = getHost(true)
    return process.env.VUE_APP_CHANNEL == "sy" || getReg(SITE_TYPE.xzsy).test(host) || getReg(SITE_TYPE.syjy).test(host)
}
/**
 * @name: 是否清徐
 */
const isQX = () => {
    const host = getHost(true)
    return process.env.VUE_APP_CHANNEL == "qx" || getReg(SITE_TYPE.qxjyy).test(host) || getReg(SITE_TYPE.qxzhjy).test(host)
}
/**
 * @name: 是否成都
 */
const isCD = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.cdsgxq).test(host)
}
/**
 * @name: 是否贵州剑河
 */
const isGZJH = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.gzjh).test(host)
}
/**
 * @name: 是否凉山
 */
const isLS = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.lszx).test(host)
}
/**
 * @name: 是否金陵
 */
const isJLWX = () => {
    if (isTest()) {
        const host = getHost(true)
        return getReg(SITE_TYPE.jlwx).test(host)
    }
    return process.env.VUE_APP_CHANNEL == "jlwx"
}
/**
 * @name: 是否南京江宁区
 */
const isNJJNQ = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.njjnq).test(host) || getReg(SITE_TYPE.jn).test(host)
}
/**
 * @name: 是否江宁教育
 */
const isJNJY = () => {
    return getReg(SITE_TYPE.jnjy).test(location.host)
}
/**
 * @name: 是否绍兴越城
 */
const isSXYCQ = () => {
    const host = getHost(true)
    return process.env.VUE_APP_CHANNEL == SITE_TYPE.sxycq || getReg(SITE_TYPE.sxycq).test(host)
}
/**
 * @name: 是否临安
 */
const isHZLA = () => {
    const host = getHost(true)
    return process.env.VUE_APP_CHANNEL == SITE_TYPE.hzla || getReg(SITE_TYPE.hzla).test(host)
}
/**
 * @name: 是否东莞松山湖
 */
const isDGSSH = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.dgsshsyzx).test(host)
}
/**
 * @name: 是否南充江东
 */
const isNCJD = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.ncjdsyxx).test(host)
}
/**
 * @name: 是否乐山五通桥研修平台
 */
const isLSWTQ = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.sclswtq).test(host)
}
/**
 * @name: 是否广州平台
 */
const isGZ = () => {
    const host = getHost(true)
    // return true;
    return process.env.VUE_APP_CHANNEL == "guangzhou" || getReg(SITE_TYPE.gdjyy).test(host)
}
/**
 * @name: 是否是合肥八中
 */
const isHFBZ = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.hfbz).test(host) || getReg(SITE_TYPE.hf168).test(host)
}
/**
 * @name: 是否是临沧市第一中学
 */
const isYNLCYZ = () => {
  const host = getHost(true)
  return getReg(SITE_TYPE.ynlcyz).test(host)
}
/**
 * @name: 是否马边彝族自治县碧桂园职业中学
 */
const isMBZX = () => {
    const host = getHost(true)
    return /mb/.test(host)
}
/**
 * @name: 是否吴兴平台
 */
const isWXED = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.wx).test(host)
}
/**
 * @name: 是否呼和浩特平台
 */
const isHHHT = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.huhhotdyzx).test(host)
}
/**
 * @name: 是否和顺县平台
 */
const isHSX = () => {
    const host = getHost(true)
    return getReg(SITE_TYPE.hsjy).test(host)
}
/**
 * @name: 是否本地化
 */
const isLocal = () => {
    return isQX() || isSY() || isJLWX() || isSXYCQ() || isJNJY()
}


export {
    isSY,
    isQX,
    isCD,
    isGZJH,
    isLS,
    isJLWX,
    isNJJNQ,
    isJNJY,
    isSXYCQ,
    isHZLA,
    isDGSSH,
    isNCJD,
    isGZ,
    isHFBZ,
    isYNLCYZ,
    isMBZX,
    isWXED,
    isHHHT,
    isHSX,
    isLSWTQ,
    isLocal,
    isTest,
    getHost
}
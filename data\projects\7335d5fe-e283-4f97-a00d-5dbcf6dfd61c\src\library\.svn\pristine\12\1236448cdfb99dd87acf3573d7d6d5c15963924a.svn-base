<template>
  <div class="export-container">
    <el-page-header @back="goBack" :class="[{ hideback: !isShowBack }]">
      <template #content>
        <el-row type="flex" style="flex-wrap: nowrap" align="middle">
          <mk-search
            :keyWord="keyWord"
            :round="true"
            @search="searchList"
          ></mk-search>

          <p>
            第一步：下载栏显示"等待下载"文案,需要等待片刻后点击"刷新"按钮；
            <br />
            第二步：下载栏显示"下载统计报表"文案,即可下载。
          </p>
          <el-button type="primary" @click="refresh" class="refresh-btn"
            >刷新</el-button
          >
        </el-row>
      </template>
    </el-page-header>

    <el-table :data="exportList" style="width: 100%" v-loading="loading">
      <el-table-column prop="id" label="ID" align="center"></el-table-column>
      <el-table-column
        prop="title"
        label="名称"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        align="center"
      ></el-table-column>
      <el-table-column prop="state" label="导出状态" align="center">
        <template #default="scope">
          <span>{{ scope.row.state === 1 ? "统计成功" : "统计中" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="state" label="下载" align="center" width="400">
        <template #default="scope">

          <template v-if="scope.row.state">
            <template v-if="scope.row.urlList.length">
              <el-button
                  v-for="(item,index) in scope.row.urlList"
                  :key="index"
                  type="text"
                  @click="download(item)"
                  style="margin-right: 5px"
              >
                <template v-if="typeCode === EXPORT_TYPE.OBJECT">下载客观题报表</template>
                <template v-else-if="typeCode === EXPORT_TYPE.SUBJECT">下载主观题报表</template>
                <template v-else-if="typeCode === EXPORT_TYPE.NOTSUBMIT">下载未提交名单</template>
                <template v-else>{{ item.title }}</template>
              </el-button>
            </template>
            <template v-else>
              <span>等待下载</span>
            </template>
          </template>


          <span v-else>等待下载</span>
        </template>
      </el-table-column>
    </el-table>

    <mk-pagination
      :total="totalCount"
      :currentPage="page"
      @switch-page="switchPage"
    ></mk-pagination>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs, onMounted } from "vue";
import {
  commonExportRecord,
  getCommonExportRecord,
} from "../../src/service/API/export";
import { EXPORT_TYPE } from "../../src/utils/gloableEnum";
import MkPagination from "../mk-pagination";
import MkSearch from "../mk-search";
import {
  ElMessage,
  ElHeader,
  ElTable,
  ElTableColumn,
  ElButton,
} from "element-plus";
import {useRouter} from "vue-router";
import {addFsUrl} from "@/library/src/utils/globalFunction";

export default defineComponent({
  name: "mk-export-table",

  components: {
    MkPagination,
    MkSearch,
    ElHeader,
    ElTable,
    ElTableColumn,
    ElButton,
  },

  props: {
    // 主键id
    primaryKey: {
      type: String,
      required: true,
    },
    // 导出模块类别
    typeCode: {
      type: String,
      required: true,
    },
    // 用户id
    userId: {
      type: String,
      required: true,
    },
    // 用户真实名称
    realName: {
      type: String,
      required: true,
    },
    // 导出文件名称
    title: {
      type: String,
      default: "",
    },
    // 年级名称
    gradeId: {
      type: String,
      default: "",
    },
    // 班级名称
    classId: {
      type: String,
      default: "",
    },
    // 是否显示返回
    isShowBack: {
      type: Boolean,
      default: true,
    },
  },

  setup(props) {
    const router = useRouter()
    const state = reactive({
      // 关键词
      keyWord: ref(""),
      // 导出列表
      exportList: ref([]),
      // 页码
      page: 1,
      // 请求条目
      limit: 10,
      // 数据总数
      totalCount: 0,
      // 是否加载中
      loading: false,
    });

    /**
     * @name: 返回
     */
    const goBack = () => {
      router.back()
    };
    /**
     * @name: 刷新
     */
    const refresh = () => {
      getExportList();
    };

    /**
     * @name: 导出excel生成统计报表
     */
    const exportExcel = async () => {
      state.loading = true
      return commonExportRecord({
        type: props.typeCode,
        primaryKey: props.primaryKey,
        title: props.title,
        paramJson: JSON.stringify({
          gradeId: props.gradeId,
          classId: props.classId,
        }),
      });
    };

    /**
     * @name: 获取导出excel列表
     */
    const getExportList = async () => {
      state.loading = true
      const res = await getCommonExportRecord({
        type: props.typeCode,
        primaryKey: props.primaryKey,
        keyWord: state.keyWord,
        page: state.page,
        limit: state.limit,
        optUserId: props.userId,
        optRealName: props.realName,
      });
      if (res.code == 1) {
        state.exportList = res.data.rows.map((item:any)=>{
          try {
            item.urlList =  JSON.parse(item.urlJson)
          }catch (e) {
            item.urlList =  []
          }
          return item
        });
        state.totalCount = res.data.total_rows;
      } else {
        ElMessage.warning(res.msg);
      }
      state.loading = false
    };

    /**
     * @name: 查询导出列表
     * @params keyWork 关键词
     */
    const searchList = (keyWork: string) => {
      state.page = 1;
      state.keyWord = keyWork;
      getExportList();
    };

    /**
     * @name: 分页切换
     * @param page 页码
     */
    const switchPage = (page: number) => {
      state.page = page;
      getExportList();
    };

    /**
     * @name: 下载
     */
    const download = (item: any) => {
      const a: any = document.createElement("a");
      a.href = addFsUrl(item.url);
      a.download = item.title;
      a.click();
    };

    onMounted(async () => {
      await exportExcel();
      setTimeout(()=>{
        getExportList();
      },500)
    });

    return {
      ...toRefs(state),
      EXPORT_TYPE,
      goBack,
      refresh,
      switchPage,
      download,
      searchList,
    };
  },
});
</script>

<style lang="scss">
@import "../../css/_layout";
.export-container {
  margin: 0 auto 15px;
  width: 100%;
  max-width: $main-container-width;
  padding:0 20px;
  background-color: #fff;
  
  .el-page-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-bottom: 1px solid #e9eaec;

    &.hideback{
      .el-page-header__content{
        width: 100%;
      }
    }

    .el-page-header__content {
      width: calc(100% - 100px);
      .el-input {
        .el-input__inner {
          height: 40px !important;
        }
      }
      p {
        margin-left: 20px;
        font-size: 14px;
        color: $main-error-color;
      }
      .refresh-btn {
        position: absolute;
        right: 30px;
        top: 5px;
      }
    }
    &.hideback {
      .el-page-header__left {
        display: none !important;
      }
    }
  }
}
</style>

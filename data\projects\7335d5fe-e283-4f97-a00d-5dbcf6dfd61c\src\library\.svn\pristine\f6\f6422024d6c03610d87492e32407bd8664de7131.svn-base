<template>
  <!-- 已同步提示 -->
  <div class="flex-center" v-if="synchroStatus">
    <i class="res-icon el-icon-success"></i>
    <div>已成功同步至区域资源中心</div>
  </div>
  <div class="team-name">
    <span class="type-name" v-if="configJson.typeName">
      {{ configJson.typeName }}
    </span>
    <span
      v-if="teamTitle"
      @click="lookResDetail"
      :class="['ellipsis', synchroStatus ? 'team-title' : '']"
      :title="teamTitle"
      >{{ teamTitle }}</span
    >
  </div>
  <div class="resource-path">
    资源包同步路径：{{ configJson.type == 0 ? "同步教材" : "专题资源" }}
  </div>
  <!-- 资源目录 -->
  <div class="book-path">
    <!--同步资源名称-->
    <span v-if="configJson.bookName && configJson.type == 0">{{
      configJson.bookName
    }}</span>
    <!--专题资源名称-->
    <span v-if="configJson.name && configJson.type == 1">{{
      configJson.name
    }}</span>
    <span
      v-if="configJson.catalogPathName && configJson.catalogPathName.length > 0"
      >></span
    >
    <span>{{ getBookCatalogue(configJson.catalogPathName) }}</span>
  </div>
  <!-- 标签 -->
  <div v-for="(item, index) in labelList" :key="index">
    <span
      class="label-item"
      v-if="getLabelHtml(item)"
      v-html="getLabelHtml(item)"
    ></span>
  </div>
</template>
<script lang="ts">
import { defineComponent } from "vue";
import { LABEL_TYPE } from "@/library/ui/mk-share-res/modules/PlatformLabelItem.vue";
export default defineComponent({
  props: {
    configJson: {
      type: Object,
      default: function() {
        return {};
      }
    },
    labelList: {
      type: Array,
      default: function() {
        return [];
      }
    },
    teamTitle: {
      type: String,
      default: ""
    },
    synchroStatus: {
      type: Boolean,
      default: false
    },
    dataInfo: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  setup(props, ctx) {
    /**
     * @name:获取资源得标签文本
     */
    const getLabelHtml = (item: any) => {
      // 标签
      let html = "";
      //简介
      if (item.type == LABEL_TYPE.INTRO && item.optionName) {
        html += `<span>简介： ${item.optionName}</span>`;
      } else if (item.type == LABEL_TYPE.KNOWLEDGE && item.selfName) {
        //学科知识点展示真实选中的目录
        item.optionName = item.selfName.split(",").join(" ");
        html += `<span>知识点： ${item.optionName}</span>`;
      } else if (
        item.type == LABEL_TYPE.SOURCE &&
        (item.optionName || item.schoolName)
      ) {
        //作者来源
        html += `<span>作者来源： ${item.optionName.split(",").join(" ")}${
          item.schoolName
            ? (item.schoolName && item.optionName ? " | " : "") +
              item.schoolName
            : ""
        }</span>`;
      } else if (item.labelTitle && item.optionName) {
        html += `<span>${item.labelTitle}： ${item.optionName}</span>`;
      }
      return html;
    };
    /**
     * @name 获取书籍选中的目录
     */
    const getBookCatalogue = (list: any) => {
      if (list && list.length > 0) {
        return list
          .map((item: any) => {
            return item;
          })
          .join(">");
      } else {
        return "";
      }
    };
    /**
     * @name:查看资源详情预览页
     */
    const lookResDetail = () => {
      ctx.emit("look-res-detail");
    };
    return {
      getLabelHtml,
      lookResDetail,
      getBookCatalogue
    };
  }
});
</script>
<style lang='scss' scoped>
.res-icon {
  font-size: 24px;
  color: #07c29d;
  margin-right: 5px;
}
.team-name {
  display: flex;
  align-items: center;
  .type-name {
    display: inline-block;
    min-width: 14%;
    background: #f7f9ff;
    border-radius: 4px;
    margin-right: 10px;
    font-size: 14px;
    height: 32px;
    line-height: 32px;
    padding: 0 10px;
    @include theme_color();
    text-align: center;
  }
  .team-title {
    @include theme_color();
    word-break: break-all;
    cursor: pointer;
  }
}
.label-item {
  display: flex;
  align-items: center;
  margin-top: 8px;
}
.resource-path {
  margin: 8px 0;
}
</style>

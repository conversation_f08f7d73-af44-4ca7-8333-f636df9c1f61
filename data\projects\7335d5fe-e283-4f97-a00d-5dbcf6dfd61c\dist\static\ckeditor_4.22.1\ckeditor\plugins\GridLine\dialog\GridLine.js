/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2024-03-25 11:41:46
 * @LastEditors: l<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>@class30.com
 * @LastEditTime: 2024-03-25 16:22:14
 */
CKEDITOR.dialog.add("GridLine", function (editor) {
  var getSmallPage = function () {
    return editor.element.$.clientWidth < 600;
  };
  var getRowCell = function (count, type) {
    let isSmallPage = getSmallPage();
    let tds = 0;
    if (type == "small") {
      tds = isSmallPage ? 18 : 26;
    } else if (type == "big") {
      tds = isSmallPage ? 13 : 20;
    } else {
      tds = isSmallPage ? 15 : 23;
    }
    let rows = Math.ceil(count / tds);

    return { rows, tds };
  };
  var getHtml = function (count, type) {
    let { rows, tds } = getRowCell(count, type);
    let container = document.createElement("div");
    container.className = "writing-container dynamic";

    for (let i = 0; i < rows; i++) {
      let line = document.createElement("div");
      line.className = `writing-line split-tag writing-line-${i}`;
      container.appendChild(line);
      for (let j = 0; j < tds; j++) {
        let index = tds * i + (j + 1);
        let cell = document.createElement("div");
        cell.className = `writing-cell writing-cell-${type}`;
        cell.setAttribute("aria-colcount", index);
        if (index % 100 == 0) {
          cell.className += " writing-cell--mark";
        }
        line.appendChild(cell);
      }
    }
    return container.outerHTML;
  };
  return {
    title: "插入作文格", //对话框标题
    minWidth: 300, //对话框宽度
    minHeight: 200, //对话框高度
    contents: [
      {
        //对话框内容
        id: "GridLine",
        name: "GridLine",
        elements: [
          {
            label: "格子大小",
            id: "type",
            type: "radio",
            items: [
              ["小格", "small"],
              ["中格", "medium"],
              ["大格", "big"],
            ],
            default: "medium",
          },
          {
            type: "text",
            id: "count",
            label: "字数",
            default: 200,
            onShow: function (params) {
              // 添加输入验证
              var input = this.getInputElement();
              input.on("input", function () {
                var value = input.$.value.replace(/[^0-9]/g, "");
                if (value.length > 4) {
                  value = value.slice(0, 4);
                }
                if (value !== input.$.value) {
                  input.$.value = value;
                }
              });
            },
          },
        ],
      },
    ],
    onOk: function () {
      var type = this.getValueOf("GridLine", "type");
      var count = this.getValueOf("GridLine", "count");
      var html = getHtml(Number(count), type);
      try {
        let $ele = editor.getSelectedRanges()[0].endContainer.$
        let cName = $ele.className;
        let pName = $ele.parentElement.className;
        if (
          (cName && cName.includes("writing-")) ||
          (pName && pName.includes("writing-"))
        ) {
          const box =
            editor.element.$.getElementsByClassName("writing-container");
          box[box.length - 1].insertAdjacentHTML("afterend", html);
        } else {
          if (cName && cName.includes("subject-para-p")) {
            editor
            .getSelection()
            .getStartElement()
            .$.replaceWith(
              CKEDITOR.dom.element.createFromHtml(html + "<p><br/></p>").$
            );
          } else {
            const paras = Array.from(editor.element.$.getElementsByClassName("subject-para-p"));
            if (paras[0].contains($ele)) {
              // 跳过首个分数段落
              paras[0].insertAdjacentHTML("afterend", html);
            } else {
              editor.insertHtml(html + "<p><br/></p>");
            }
          }
        }
      } catch (e) {
        editor.insertHtml(html + "<p><br/></p>");
      }

      editor.fire('insertWritingCells')
    },
  };
});

@if $use-bounceOutLeft == true {

	@-webkit-keyframes bounceOutLeft {
		0% {
			-webkit-transform: translateX(0);
		}

		20% {
			opacity: 1;
			-webkit-transform: translateX($base-distance * 2);
		}

		100% {
			opacity: 0;
			-webkit-transform: translateX(-$base-distance-big * 2);
		}
	}

	@keyframes bounceOutLeft {
		0% {
			transform: translateX(0);
		}

		20% {
			opacity: 1;
			transform: translateX($base-distance * 2);
		}

		100% {
			opacity: 0;
			transform: translateX(-$base-distance-big * 2);
		}
	}

	.bounceOutLeft {
		@include animate-prefixer(animation-name, bounceOutLeft);
	}

}

﻿/**
 * @Name: 工作指令类
 * @Descripttion: 模块说明
 * @Author: g<PERSON><PERSON>
 * @Date: 2021/6/1 20:29
 * @LastEditors: gaohan
 * @LastEditTime: 2021/6/1
 */
import { DirectiveBinding } from "vue";
import { trimStr } from './globalFunction'

export default (app: any) => {
    // 输入去除前后空格,标签
    app.directive('triminput', {
        beforeMount: (el: HTMLElement) => {
            const textarea = el.getElementsByTagName('textarea')[0]
            const input = el.getElementsByTagName('input')[0]
            textarea && textarea.addEventListener('change', () => {
                textarea.value = trimStr(textarea.value)
            })
            input && input.addEventListener('change', () => {
                input.value = trimStr(input.value)
            })
        }
    })

    // 滚动加载更多
    app.directive('loadmore', {
        beforeMount(el: HTMLElement, binding: DirectiveBinding) {
            let timer:any = null
            el.addEventListener('scroll', () => {
                const isLoading: boolean = el.scrollHeight - el.scrollTop - 20 <= el.clientHeight;
                if( isLoading ){
                    clearTimeout(timer)
                    timer = setTimeout(()=>{
                        binding.value()
                    },300)
                }
            })
        }
    });
    // element下拉滚动加载更多
    app.directive('loadselect', {
        beforeMount(el:HTMLElement, binding:DirectiveBinding) {
            let timer:any = null
            const dom = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
            dom &&
            dom.addEventListener('scroll', () => {
                const isLoading: boolean = dom.scrollHeight - dom.scrollTop - 20 <= dom.clientHeight;
                if( isLoading ){
                    clearTimeout(timer)
                    timer = setTimeout(()=>{
                        binding.value()
                    },300)
                }
            });
        }
    });
}
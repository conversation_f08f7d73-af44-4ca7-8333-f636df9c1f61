<template>
  <div
      v-if="isShow"
      :class="['mk-float-ad',{link}]"
      ref="mkFloatAdRef"
      :style="{
         left: curLeft + 'px',
         top: curTop + 'px'
      }"
      @mouseover="onClear"
      @mouseout="onStart"
      @click.stop="onClick"

  >

    <div class="mk-float-ad-close flex-center" @click.prevent.stop="onClose"></div>
    <p v-if="text">{{ text }}</p>
    <img v-if="img" :src="img" width="100%" border="0"/>
  </div>
</template>

<script lang="ts">
import {defineComponent, reactive, ref, toRefs, onMounted} from 'vue'
import {addFsUrl} from "@/library/src/utils/globalFunction";


/**
 * @Name: 飘窗广告
 * @Descripttion: 根据json文件获取广告内容
 * @Author: gaohan
 * @Date: 2023/1/13 17:14
 * @LastEditors: gaohan
 * @LastEditTime: 2023/1/13
 */
export default defineComponent({
  name: 'mk-float-ad',

  props: {
    // 水平位置
    left: {
      type: Number,
      default: 0
    },
    // 垂直位置
    top: {
      type: Number,
      default: 0
    },
    // 步长
    step: {
      type: Number,
      default: 1
    },
    // 延迟
    delay: {
      type: Number,
      default: 30
    },
    // 宽度
    width: {
      type: Number,
      default: 200
    },
    // 高度
    height: {
      type: Number,
      default: 200
    },
    // 是否允许水平运动
    isMoreLeft: {
      type: Boolean,
      default: true
    },
    // 是否允许垂直运动
    isMoreTop: {
      type: Boolean,
      default: true
    },
    // json地址
    path: {
      type: String,
      default: addFsUrl(`aliba/resource/region/json/advert.json`)
    }
  },

  setup(props) {
    const mkFloatAdRef = ref<any>(null)
    const state = reactive({
      // 水平位置
      xPos: props.left,
      // 垂直位置
      yPos: props.top,
      // 垂直偏移量
      Woffset: 0,
      // 水平偏移量
      Hoffset: 0,
      // 是否到达水平边界
      xon: false,
      // 是否到达垂直边界
      yon: false,
      // 是否暂停
      pause: true,
      // 是否展示
      isShow: false,
      // 定时器
      timer: null as any,
      // 当前水平位置
      curLeft: props.left,
      // 当前垂直位置
      curTop: props.top,
      // 文案
      text: '',
      // 图片
      img: '',
      // 跳转地址
      link: ''
    })

    /**
     * @name: 移动
     */
    const onMove = () => {
      if( !mkFloatAdRef.value ){
          return
      }
      const width = document.documentElement.clientWidth;
      const height = document.documentElement.clientHeight;
      state.Hoffset = mkFloatAdRef.value.offsetHeight;
      state.Woffset = mkFloatAdRef.value.offsetWidth;

      // 滚动部分跟随屏幕滚动
      if (props.isMoreLeft) {
        state.curLeft = state.xPos + document.body.scrollLeft + document.documentElement.scrollLeft;
      }
      if (props.isMoreTop) {
        state.curTop = state.yPos + document.body.scrollTop + document.documentElement.scrollTop;
      }

      if (state.yon) {
        state.yPos = state.yPos + props.step;
      } else {
        state.yPos = state.yPos - props.step;
      }
      if (state.yPos < 0) {
        state.yon = true;
        state.yPos = 0;
      }
      if (state.yPos >= height - state.Hoffset) {
        state.yon = false;
        state.yPos = height - state.Hoffset;
      }

      if (state.xon) {
        state.xPos = state.xPos + props.step;
      } else {
        state.xPos = state.xPos - props.step;
      }
      if (state.xPos < 0) {
        state.xon = true;
        state.xPos = 0;
      }
      if (state.xPos >= width - state.Woffset) {
        state.xon = false;
        state.xPos = width - state.Woffset;
      }
    }
    /**
     * @name: 暂停
     */
    const onClear = () => {
      clearInterval(state.timer);
      state.timer = null
    }
    /**
     * @name: 开始
     */
    const onStart = () => {
      onClear()
      state.timer = setInterval(onMove, props.delay);
      state.isShow = true
    }
    /**
     * @name: 关闭
     */
    const onClose = () => {
      onClear()
      state.isShow = false
    }
    /**
     * @name: 点击跳转
     */
    const onClick = () => {

      if( state.link ){
          window.open(state.link)
      }
    }
    /**
     * @name: 获取广告信息
     */
    const getJSON =  (url:string,cb:Function)=> {
      const xhr:any = window.XMLHttpRequest ? new XMLHttpRequest() : new ActiveXObject("Microsoft.XMLHTTP");
      xhr.open("GET", url, true);
      xhr.send(null);
      xhr.onreadystatechange = function () {
        if (xhr.readyState == 4 && xhr.status == 200) {
          cb(JSON.parse(xhr.responseText));
        }
      };
    };
    /**
     * @name: 初始化
     */
    const init = ()=>{
      getJSON(`${props.path}?v=${Date.now()}`,(data:any)=>{
        const host = location.host
        const config = data[host]
        if( config && config.isshow ){
          state.text = config.text
          state.img = config.img
          state.link = config.link
          onStart()
        }else{
          onClose()
        }
      });
    }


    onMounted(() => {
      init()
    })

    return {
      ...toRefs(state),
      mkFloatAdRef,
      onClear,
      onStart,
      onClose,
      onClick,
    }
  }
})
</script>

<style lang="scss" scoped>
.mk-float-ad {
  position: absolute;
  background-color: #fff;
  z-index: 9999;
  min-width: 200px;
  min-height: 100px;
  max-height: 500px;
  max-width: 500px;
  border-radius: 8px;
  &.link{
    cursor: pointer;
  }

  .mk-float-ad-close{
    position: absolute;
    right: -20px;
    top: -20px;
    width: 23px;
    height: 23px;
    background: #fff url('./close.png') no-repeat center/100%;
    cursor: pointer;
    border-radius: 50%;
  }

  p{
    line-height: 1.5;
    padding: 0 10px;
  }
  img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
    -webkit-user-drag: none;
  }
}

</style>

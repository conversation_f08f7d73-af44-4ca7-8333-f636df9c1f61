﻿<template>
  <div class="depart-breadcrumb">
    <el-input
      placeholder="请输入教师名称搜索"
      v-model="departmentUserAPIParams.keyWord"
      clearable
      suffix-icon="el-icon-search"
      @change="searchTeacher"
      class="search-bar"
    >
    </el-input>
    <div style="margin: 5px 10px">
      <span v-for="(item, index) in breadcrumb" :key="index">
        <span
          :class="index === breadcrumb.length - 1 ? '' : 'active'"
          @click="switchBreadcrumb(item, index)"
        >
          {{ item.departmentName }}</span
        >

        <span v-if="index !== breadcrumb.length - 1"> ></span>
      </span>
    </div>
  </div>
  <div class="depart-tree-content" ref="departContentRef" v-loadmore="loadMoreTeacher">
    <el-tree
      v-show="
        breadcrumb.length === 1 ||
        (breadcrumb.length !== 1 && departmentList.length)
      "
      :data="departmentList"
      :show-checkbox="hasCheckedDepartmentPower"
      :icon-class="'depart-icon'"
      :props="props"
      :expand-on-click-node="false"
      :check-on-click-node="false"
      ref="departmentTreeRef"
      check-strictly
      node-key="id"
      @check="handleCheckDepartmentChange"
    >
      <template #default="{ node, data }">
        <span style="max-width: 75%; display: flex">
          <span class="ellipsis" :title="node.label">
            <el-avatar
              :src="data.avatar"
              style="vertical-align: middle; background-color: transparent"
              :size="20"
            >
              <img :src="require('../assets/department.png')" />
            </el-avatar>
            {{ node.label }}
          </span>
          <span style="margin-left: 10px"
            >({{ data.departmentNumberOfPeople }}人)</span
          >
        </span>
        <span
          :class="[
            'depart-next',
            {
              disabled: node.checked
            }
          ]"
          @click="nodeClick(data)"
          v-if="data.departmentNumberOfPeople > 0"
        >
          <img
            :src="require('../assets/dep_next_dis_icon.png')"
            v-if="node.checked"
          />
          <img :src="require('../assets/dep_next_icon.png')" v-else />
          下级
        </span>
      </template>
    </el-tree>

    <!--用户列表-->
    <div class="department-user-list" v-loading="departmentUserOptions.loading">
      <div class="depart-user-checkall">
        <el-checkbox
          style="line-height: 40px"
          :indeterminate="isIndeterminate"
          v-model="isCheckAll"
          @change="handleCheckAllChange"
          :disabled="disabledDepart"
        >
          全选
        </el-checkbox>
      </div>
      <el-tree
        v-if="departmentUserList.length"
        :data="departmentUserList"
        :props="{
          label: 'realName',
          disabled: (data) => {
            return disabledKeys.includes(data.userId) || disabledDepart;
          }
        }"
        node-key="userId"
        :class="[
          'user-tree',
          departmentUserAPIParams.departmentId === '-1' ? 'all' : ''
        ]"
        ref="userTreeRef"
        show-checkbox
        :check-on-click-node="true"
        @check="handleCheckItemChange"
      >
        <template #default="{ node, data }">
          <div class="user-item">
            <el-avatar
              :src="data.avatar"
              style="vertical-align: middle"
              :size="30"
            >
              <img :src="addFsUrl('aliba/avatar/default/default.png')" />
            </el-avatar>
            {{ data.realName || data.userName }}
          </div>
        </template>
      </el-tree>
      <mk-no-data v-else></mk-no-data>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  onMounted,
  nextTick,
  watch,
  computed
} from 'vue';
import { PLATFORM_TYPE, REGION_TYPE } from '@/library/src/utils/gloableEnum';
import {
  getDepartmentListAPI,
  getDepartmentUserListAPI
} from '@/library/src/service/API/base';
import { IGlobalState } from '@/library/src/store';
import { useStore } from 'vuex';
import {
  unique,
  groupByKey,
  addFsUrl
} from '@/library/src/utils/globalFunction';

export default defineComponent({
  name: 'department-user',

  emits: [
    'switch-department',
    'switch-teacher',
    'switchDepartment',
    'switchTeacher'
  ],

  props: {
    // 是否有选择部门的权限
    hasCheckedDepartmentPower: {
      type: Boolean,
      default: true
    },
    // 已选用户列表
    userList: {
      type: Array,
      default: () => []
    },
    // 已选部门列表
    choosedDepartmentList: {
      type: Array,
      default: () => []
    },
    // 禁用列表
    disabledKeys: {
      type: Array,
      default: () => []
    }
  },

  setup(props, ctx) {
    const userTreeRef = ref<any>(null);
    const departContentRef = ref<any>(null);
    const departmentTreeRef = ref<any>(null);
    const store = useStore<IGlobalState>();
    const state = reactive({
      // 当前部门
      curDepart: null as any,
      // 部门列表
      departmentList: [] as any[],
      // 当前已选部门列表
      curDepartmentList: props.choosedDepartmentList,
      // 局端用户列表
      departmentUserList: [] as any[],
      // 当前已选用户列表
      curDepartmentUserList: [] as any[],
      // 局端用户查询请求参数
      departmentUserAPIParams: {
        departmentId: '-1',
        keyWord: '',
        page: 1,
        limit: 10
      },
      // 局端用户查询配置参数
      departmentUserOptions: {
        timer: null as any,
        loading: false,
        total: 0,
        pageCount: 0
      },
      // 局端树配置项
      props: {
        children: 'children',
        label: 'departmentName',
        isLeaf: (data: any) => {
          return !data.children || data.children.length === 0;
        },
        disabled: (data: any) => {
          return (
            props.disabledKeys.includes(data.id) ||
            data.departmentNumberOfPeople === 0
          );
        }
      },
      // 部门用户Map列表
      departmentUserMap: new Map(),
      // 面包屑列表
      breadcrumb: [] as any[],
      // 是否全选
      isCheckAll: false,
      // 全选状态
      isIndeterminate: true,
      //取消选中的数据
      deleteUserList: [] as any[]
    });

    // 是否禁用部门
    const disabledDepart = computed(() => {
      return state.curDepart && props.disabledKeys.includes(state.curDepart.id);
    });

    /**
     * @name: 获取局端列表
     */
    const getDepartmentList = async (parentId: string = '-1') => {
      const orgType =
        REGION_TYPE.SCHOOL === store.state.app.platFormInfo.platformType
          ? PLATFORM_TYPE.SCHOOL
          : PLATFORM_TYPE.REGION;
      const res = await getDepartmentListAPI({
        orgId:
          orgType == PLATFORM_TYPE.SCHOOL
            ? store.state.user.schoolId
            : store.state.app.platFormInfo.id,
        orgType,
        parentId: parentId === '-1' ? '' : parentId
      });
      if (res.code === 1) {
        state.departmentList = arrayToTree(res.data);

        // 全区域
        if (parentId === '-1') {
          let departmentNumberOfPeople: number = 0;
          state.departmentList.forEach((item: any) => {
            departmentNumberOfPeople += item.departmentNumberOfPeople;
          });
          state.curDepart = departmentNumberOfPeople;
        }
      } else {
        state.departmentList = [];
      }
    };
    /**
     * @name: 获取局端用户列表
     */
    const getDepartmentUserList = async () => {
      state.departmentUserOptions.loading = true;
      const departmentId = state.departmentUserAPIParams.departmentId;

      const res = await getDepartmentUserListAPI({
        orgId: store.state.app.platFormInfo.id,
        orgType: 1,
        ...state.departmentUserAPIParams,
        containChildUser: departmentId === '-1' ? 1 : 0
      });
      if (res.code === 1) {
        const departmentUserList = res.data.rows.map((item: any) => {
          item.schoolName =
            departmentId === '-1'
              ? store.state.app.platFormInfo.platformName
              : item.departmentName;
          // 区域人员的学校id就是区域id
          item.schoolId = store.state.app.platFormInfo.id;
          return item;
        });
        state.departmentUserList =
          res.code === 1
            ? [...state.departmentUserList, ...departmentUserList]
            : [];
      } else {
        state.departmentUserList = [];
      }
      state.departmentUserOptions.total = res.data.total_rows || 0;
      state.departmentUserOptions.pageCount = res.data.page_count || 0;
      state.departmentUserMap.set(departmentId, state.departmentUserList);
      state.departmentUserOptions.loading = false;

      nextTick(() => {
        let userList: any[] = [];
        state.curDepartmentList.forEach((item: any) => {
          if (state.departmentUserMap.has(item.id)) {
            let list = state.departmentUserMap.get(item.id);
            userList = [...userList, ...list];
          }
        });
        state.curDepartmentUserList = unique(
          [...state.curDepartmentUserList, ...userList],
          'userId'
        );
        setScrollPosition()
      });
    };

    /**
     * @name: 滚动定位
     */
    const setScrollPosition = () => {
      if (departContentRef.value && state.departmentUserAPIParams.keyWord) {
        const top = departmentTreeRef.value.$el ? departmentTreeRef.value.$el.clientHeight - 20 : 0;
        departContentRef.value.scrollTop = top;
      }
    };

    /**
     * @name: 查询教师
     * @param keyWord 关键词
     */
    const searchTeacher = async (keyWord: string = '') => {
      clearTimeout(state.departmentUserOptions.timer);
      state.departmentUserOptions.timer = setTimeout(() => {
        state.departmentUserList = [];
        state.departmentUserAPIParams.keyWord = keyWord;
        state.departmentUserAPIParams.page = 1;
        getDepartmentUserList();
      }, 300);
    };
    /**
     * @name: 下拉加载更多
     */
    const loadMoreTeacher = async () => {
      state.departmentUserAPIParams.page++;
      if (state.departmentUserAPIParams.page > state.departmentUserOptions.pageCount) {
        return;
      }
      getDepartmentUserList();
    };
    /**
     * @name: 树结构转换
     * @param list 部门集合
     */
    const arrayToTree = (list: any[]) => {
      // 递归子级节点
      let children = groupByKey(list, 'parentId');
      let rootData = children.filter((item: any) => {
        return item.data === '-1';
      });
      let childData = children.filter((item: any) => {
        return item.data !== '-1';
      });
      let root = rootData.length ? rootData[0].list : [];
      const mergeData = mergeChild(childData);
      const result = insertChild(mergeData, root);
      return result.length ? result : mergeData;
    };
    /**
     * @name: 合并子级
     * @param children 子级集合
     */
    const mergeChild = (children: any[]) => {
      let dest = [];
      for (let i = 0; i < children.length; i++) {
        let { list } = children[i];
        for (let j = 0; j < list.length; j++) {
          let item = list[j];
          item.children = [];
          let index = dest.findIndex((sitem) => {
            return sitem.id === item.parentId;
          });
          if (index === -1) {
            dest.push(item);
          } else {
            dest[index].children.push(item);
          }
        }
      }
      return dest;
    };
    /**
     * @name: 插入子级到根节点
     * @param list 子级集合
     * @param rootList 根节点结合
     */
    const insertChild = (list: any, rootList: any) => {
      let _list = Object.assign([], list);
      for (let i = 0; i < _list.length; i++) {
        let item = _list[i];
        for (let j = 0; j < rootList.length; j++) {
          let sitem = rootList[j];
          sitem.children = sitem.children || [];
          if (item.parentId === sitem.id) {
            sitem.children = [...sitem.children, item];
          }
        }
      }
      return rootList;
    };
    /**
     * @name: 移除子级部门
     * @param list 节点集合
     */
    const removeChild = (list: any) => {
      let ids: any[] = [];
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (item.children && item.children.length) {
          const childrenids = [...item.children].map((item: any) => {
            return item.id;
          });
          ids = [...ids, ...childrenids];
        }
      }
      for (let i = 0; i < list.length; i++) {
        const item = list[i];
        if (ids.includes(item.id)) {
          list.splice(i, 1);
          i--;
        }
      }
      return list;
    };
    /**
     * @name: 点击部门
     * @param data 当前部门对象
     */
    const nodeClick = async (data: any) => {
      state.curDepart = data;
      state.departmentUserAPIParams.departmentId = data.id;
      state.departmentUserAPIParams.page = 1;
      state.departmentUserList = [];
      state.breadcrumb.push({
        departmentName: data.departmentName,
        id: data.id
      });
      await getDepartmentList(data.id);
      await getDepartmentUserList();
    };
    /**
     * @name: 选择教师
     */
    const handleCheckItemChange = (value: any, data: any) => {
      let curTeacherList = [
        ...state.curDepartmentUserList,
        ...data.checkedNodes
      ];
      let deleteList: any = [];
      if (value) {
        const currentNode = userTreeRef.value.getNode(value);
        if (currentNode && !currentNode.checked) {
          deleteList.push(value);
          curTeacherList = curTeacherList.filter((item: any) => {
            return item.userId !== value.userId;
          });
        }
      } else {
        if (data.checkedNodes.length == 0) {
          deleteList = state.deleteUserList;
        }
      }
      state.curDepartmentUserList = unique(curTeacherList, 'userId');
      ctx.emit('switch-teacher', state.curDepartmentUserList, deleteList);
    };
    /**
     * @name: 选择部门
     */
    const handleCheckDepartmentChange = async (value: any, data: any) => {
      if (props.hasCheckedDepartmentPower) {
        let checked = true;
        if (value) {
          checked = departmentTreeRef.value.getNode(value)?.checked;
        }
        let list = unique(
          [...props.choosedDepartmentList, ...data.checkedNodes],
          'id'
        );
        // 删除当前节点
        if (!checked) {
          list = list.filter((item: any) => {
            return item.id !== value.id;
          });
        }
        // 去除子级部门
        list = removeChild(list);
        state.curDepartmentList = list;
        uniqueUserList();
        ctx.emit('switch-department', state.curDepartmentList);
      } else {
        let userList: any[] = [];
        if (value) {
          await nodeClick(value);
          state.curDepartmentList.forEach((item: any) => {
            if (state.departmentUserMap.has(item.id)) {
              let list = state.departmentUserMap.get(item.id);
              userList = [...userList, ...list];
            }
          });
        }
        uniqueUserList();
        ctx.emit('switch-teacher', state.curDepartmentUserList);
      }
    };
    /**
     * @name: 群组人员去重
     */
    const uniqueUserList = () => {
      let curDepartmentUserList = unique(
        [...state.curDepartmentUserList],
        'userId'
      );
      const departmentIds = [...state.curDepartmentList].map((item: any) => {
        return item.id;
      });
      for (let i = 0; i < curDepartmentUserList.length; i++) {
        const departmentId = curDepartmentUserList[i].departmentId;
        if (departmentId && !departmentIds.includes(departmentId)) {
          curDepartmentUserList.splice(i, 1);
          i--;
        }
      }
      state.curDepartmentUserList = curDepartmentUserList;
    };
    /**
     * @name: 全选
     */
    const handleCheckAllChange = (isCheckAll: any) => {
      let checkedNodes = [];
      if (isCheckAll) {
        checkedNodes = state.departmentUserList;
      } else {
        checkedNodes = [];

        state.curDepartmentUserList = state.curDepartmentUserList.filter(
          (item: any) => {
            if (
              state.departmentUserList.some((sitem) => {
                return sitem.userId === item.userId;
              })
            ) {
              //获取当前需要删除的用户
              state.deleteUserList.push(item);
            }
            return !state.departmentUserList.some((sitem) => {
              return sitem.userId === item.userId;
            });
          }
        );
      }
      handleCheckItemChange(null, { checkedNodes });
    };
    /**
     * @name: 清空已选
     */
    const clear = () => {
      if (userTreeRef.value) {
        userTreeRef.value.setCheckedKeys([]);
        state.curDepartmentUserList = [];
        handleCheckItemChange(null, { checkedNodes: [] });
      }
      if (departmentTreeRef.value) {
        departmentTreeRef.value.setCheckedKeys([]);
        state.curDepartmentList = [];
        handleCheckDepartmentChange(null, { checkedNodes: [] });
      }
    };
    /**
     * @name: 删除已选教师
     * @param item 当前已选对象
     */
    const deleteUser = (item: any) => {
      if (!userTreeRef.value) {
        return;
      }
      userTreeRef.value.setChecked(item.userId, false);
      state.curDepartmentUserList = state.curDepartmentUserList.filter(
        (sitem: any) => {
          return sitem.userId !== item.userId;
        }
      );
      const checkedNodes = userTreeRef.value.getCheckedNodes();
      handleCheckItemChange(item, { checkedNodes });
    };
    /**
     * @name: 删除部门
     * @param item 对于部门对象
     */
    const deleteDepartment = (item: any) => {
      if (!departmentTreeRef.value) {
        return;
      }
      departmentTreeRef.value.setChecked(item.id, false);
      state.curDepartmentList = state.curDepartmentList.filter((sitem: any) => {
        return sitem.id !== item.id;
      });
      const checkedNodes = userTreeRef.value.getCheckedNodes();
      handleCheckItemChange(item, { checkedNodes });
    };
    /**
     * @name: 重置已选部门
     * @param ids 已选部门id列表
     */
    const resetDepartment = (ids?: any[]) => {
      if (!departmentTreeRef.value) {
        return;
      }
      let checkedKeys: any[] = ids
        ? ids
        : [...state.curDepartmentList].map((item: any) => {
            return item.id;
          });
      departmentTreeRef.value.setCheckedKeys(checkedKeys);
    };
    /**
     * @name: 重置已选部门人员
     * @param ids 已选部门人员列表
     */
    const resetDepartmentUser = () => {
      if (!userTreeRef.value) {
        return;
      }
      let checkedKeys: any[] = [...state.curDepartmentUserList].map(
        (item: any) => {
          return item.userId;
        }
      );
      userTreeRef.value.setCheckedKeys(checkedKeys);
    };
    /**
     * @name: 重置全选状态
     */
    const resetCheckAll = () => {
      if (!userTreeRef.value) {
        return;
      }
      const userCheckedNodes = userTreeRef.value.getCheckedNodes();
      if (userCheckedNodes) {
        let checkedCount = userCheckedNodes.length;
        state.isCheckAll =
          state.departmentUserList.length > 0 &&
          checkedCount === state.departmentUserList.length;
        state.isIndeterminate =
          checkedCount > 0 && checkedCount < state.departmentUserList.length;
      }
    };
    /**
     * @name: 面包屑切换
     * @param item 当前面包屑
     * @param index 当前索引
     */
    const switchBreadcrumb = (item: any, index: number) => {
      state.breadcrumb.length = index;
      nodeClick(item);
    };

    onMounted(() => {
      state.curDepartmentUserList = props.userList;
      state.curDepartmentList = props.choosedDepartmentList;
      nodeClick({
        departmentName: store.state.app.platFormInfo.platformName,
        id: '-1',
        departmentNumberOfPeople: 0
      });
    });

    watch(
      () => state.curDepartmentUserList,
      () => {
        // 避免数据持续更新影响性能,延时处理
        setTimeout(() => {
          resetDepartmentUser();
          resetDepartment();
          resetCheckAll();
        }, 300);
      }
    );

    return {
      ...toRefs(state),
      departContentRef,
      userTreeRef,
      departmentTreeRef,
      disabledDepart,
      resetDepartment,
      nodeClick,
      handleCheckItemChange,
      handleCheckDepartmentChange,
      handleCheckAllChange,
      searchTeacher,
      loadMoreTeacher,
      clear,
      deleteUser,
      deleteDepartment,
      switchBreadcrumb,
      addFsUrl
    };
  }
});
</script>

<style lang="scss" scoped>
.depart-tree-content {
  max-height: 360px;
  overflow-y: auto;
  .depart-icon {
    display: none;
  }
  .depart-next {
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    color: #38adff;
    &.disabled {
      color: #bbb;
      pointer-events: none;
      cursor: default;
    }
  }
}
.depart-breadcrumb {
  span.active {
    color: #0f57d9;
    cursor: pointer;
  }
  .search-bar {
    margin: 10px;
    width: calc(100% - 20px);
    .el-input__inner {
      border-radius: 17px;
    }
  }
}
.department-user-list {
  .user-tree.all {
    margin-left: -12px;
  }
  .depart-user-checkall {
    margin: 10px 8px;
    padding: 0 6px;
    background-color: #eff0f1;
    border-radius: 8px;
  }
}
</style>
<style lang="scss">
.user-tree {
  .el-tree-node {
    margin: 10px 0;
  }
}
</style>
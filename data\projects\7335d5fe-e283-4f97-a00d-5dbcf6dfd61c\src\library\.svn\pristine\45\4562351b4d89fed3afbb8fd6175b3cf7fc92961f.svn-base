<template>
  <span>{{ rollingNumber.toLocaleString() }}</span>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, watch } from "vue";

export default defineComponent({
  name: "mk-rolling-number",

  props: {
    // 数值
    number: {
      type: Number,
      default: 0,
    },
    // 速度,单位ms
    speed: {
      type: Number,
      default: 1000,
    },
  },
  setup(props) {
    const state = reactive({
      rollingNumber: 0,
      rAF: 0,
      start: 0,
    });

    const rollNumber = (timestamp: number) => {
      if (!state.start) {
        state.start = timestamp;
      }
      const elapsed = timestamp - state.start;
      const progress = (elapsed / props.speed) * props.number;

      if (state.rollingNumber >= props.number) {
        clearRAF();
      } else {
        state.rollingNumber = Math.min(~~progress, props.number);
        state.rAF = requestAnimationFrame(rollNumber);
      }
    };

    /**
     * @name: 清除动画
     */
    const clearRAF = () => {
      state.rAF = 0;
      state.start = 0;
      cancelAnimationFrame(state.rAF);
    };

    onMounted(() => {
      state.rAF = requestAnimationFrame(rollNumber);
    });

    watch(
      () => props.number,
      () => {
        clearRAF();
        state.rollingNumber = 0;
        state.rAF = requestAnimationFrame(rollNumber);
      }
    );

    return {
      ...toRefs(state),
    };
  },
});
</script>

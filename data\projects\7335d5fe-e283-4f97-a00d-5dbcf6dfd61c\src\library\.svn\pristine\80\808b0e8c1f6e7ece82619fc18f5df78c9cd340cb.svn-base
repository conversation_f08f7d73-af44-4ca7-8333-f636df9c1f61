/*
 * @Descripttion: 阿里云上传工具类
 * @Author: l<PERSON>yu<PERSON>
 * @Date: 2020-06-09 19:05:04
 * @LastEditors: liuyue
 * @LastEditTime: 2020-06-09 19:47:53
 */
import OSS from "ali-oss";
import md5 from "js-md5";
import { getSTSTokenAPI } from "../service/API/export";
class OSSUpload {
  private client: any; //oss对象
  private accessKeyId: string = ""; //授权id
  private accessKeySecret: string = ""; //授权秘钥
  private stsToken: string = ""; //临时token
  private region: string = ""; //区域
  private bucket: string = ""; //
  private stsKey: string = "m0b1ycd2ef3g4hi5jk6lm7op8d9fg1"; //加密key
  private timer: any;
  private initConfig(): void {
    //使用STS临时授权数据初始化OSS对象
    this.client = new OSS({
      accessKeyId: this.accessKeyId,
      accessKeySecret: this.accessKeySecret,
      stsToken: this.stsToken,
      region: this.region,
      bucket: this.bucket,
    });
  }
  /**
   * @name: 获取STS授权数据信息
   * @param path 授权地址
   */
  getSTSToken(path: string): void {
    let timestamp = new Date().getTime().toString();
    let key = md5(this.stsKey + path + timestamp);
    let params = {
      path: path,
      timestamp: timestamp,
      secure: key.toUpperCase(),
    };
    getSTSTokenAPI(params)
      .then((res: any) => {
        if (res.code == 1) {
          let data = res.data;
          this.accessKeyId = data.accessKeyId;
          this.accessKeySecret = data.accessKeySecret;
          this.stsToken = data.securityToken;
          this.region = data.region;
          this.bucket = data.bucket;
          this.initConfig();
          this.timer && clearTimeout(this.timer);
          this.timer = setTimeout(() => {
            this.getSTSToken(path);
          }, 1000 * 60 * 30); //避免失效，半小时请求一次权限
        } else {
          throw new Error("");
        }
      })
      .catch(() => {
        console.error("OSSUpload getSTSTokenAPI fail");
      });
  }
  /**
   * @name: 取消上传
   */
  async cancelUpload() {
    this.client.cancel();
    this.client = null;
  }
  /**
   * @name: 上传文件
   * @param file 文件对象
   * @param filePath 文件地址
   * @param cb 进度回调函数
   */
  uploadFile(file: File, filePath: string, cb?: Function): Promise<any> {
    return new Promise((resolve, reject) => {
      console.log("[oss] : 开始上传,文件地址:" + filePath);
      if (!this.client) {
        this.initConfig();
      }
      //调用OSS的上传方法，将file上传到res.filepath
      this.client
        .multipartUpload(filePath, file, {
          progress: async function (progress: any) {
            //上传进度回调;
            console.log("上传文件:" + progress);
            cb && cb(progress, file);
          },
        })
        .then((res: any) => {
          //上传完成;
          console.log("[oss] :上传成功,res:" + res);
          resolve(res);
        })
        .catch((err: any) => {
          //上传失败
          console.error("[oss] :上传失败,res:" + err);
          reject(err);
        });
    });
  }
  /**
   * @name: 上传文件
   * @param base64 文件base64
   * @param filePath 文件地址
   * @param fileName 文件名称
   */
  uploadBase64File(
    base64: string,
    filePath: string,
    fileName: string
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      if (filePath.indexOf("?") >= 0) {
        filePath = filePath.substring(0, filePath.indexOf("?"));
      }
      let file = this.blobToFile(this.dataURLtoBlob(base64), fileName);
      this.uploadFile(file, filePath)
        .then((res: any) => {
          resolve(res);
        })
        .catch((err: any) => {
          reject(err);
        });
    });
  }
  /**
   * @name: base64转blob
   * @param dataurl base64数据
   */
  dataURLtoBlob(dataurl: string): Blob {
    let arr: any = dataurl.split(","),
      mime = arr[0].match(/:(.*?);/)[1],
      bstr = atob(arr[1]),
      n = bstr.length,
      u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
  }
  /**
   * @name: blob转file
   * @param theBlob blob数据
   * @param fileName 文件名称
   */
  blobToFile(theBlob: Blob, fileName: string): File {
    // theBlob.lastModifiedDate = new Date();
    // theBlob.name = fileName;
    // return theBlob;
    return new File([theBlob], fileName);
  }

  /*
   * @name 多文件上传
   * @params list Array<File>
   * @description file.path需要在外部手动拼接
   * */
  uploadListFile(list: Array<any>, cb?: Function): Promise<any> {
    if (list.length == 0) {
      cb && cb(1);
      return Promise.resolve();
    }
    let self: any = this;
    let progressMap: Map<string, number> = new Map();
    for (let i: number = 0, length = list.length; i < length; i++) {
      const key: string = list[i].name + list[i].size;
      progressMap.set(key, list[i]);
    }
    // 设置进度
    const setProgress = (progress: number, file: any) => {
      console.log("上传进度:" + progress);
      file.progress = progress;
      progressMap.set(file.name + file.size, file);
      cb && cb([...progressMap.values()]);
    };
    let promise: Promise<any> = Promise.all(
      list.map((file: any) => {
        return self.uploadFile(file, file.path, setProgress);
      })
    );
    return promise
      .then((res: any) => {
        return Promise.resolve(res);
      })
      .catch((err: Error) => {
        return Promise.reject(err);
      });
  }
}
export default new OSSUpload();

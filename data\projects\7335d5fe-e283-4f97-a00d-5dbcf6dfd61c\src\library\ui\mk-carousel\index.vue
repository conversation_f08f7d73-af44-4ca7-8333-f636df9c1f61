﻿<template>
  <div
    :class="['mk-carousel', dir, { autoplay, showBtn: isShowSwitchBtn }]"
    :style="{
      '--delay': delay + 's',
      '--width': width + 'px',
      '--height': height + 'px',
    }"
  >
    <div class="mk-carousel-list">
      <slot name="prepend"></slot>
      <div
        class="mk-carousel-item"
        v-for="(item, index) in data"
        :key="index"
        @click.native="clickItem(item, index)"
      >
        <slot name="content" :data="item" :index="index">
          <div class="mk-carousel-content">
            {{ item.title }}
          </div>
        </slot>
      </div>
      <slot name="append"></slot>
    </div>
    <div class="mk-carousel-list" v-if="autoplay">
      <slot name="prepend"></slot>
      <div
        class="mk-carousel-item"
        v-for="(item, index) in data"
        :key="index"
        @click.native="clickItem(item, index)"
      >
        <slot name="content" :data="item" :index="index">
          <div class="mk-carousel-content">
            {{ item.title }}
          </div>
        </slot>
      </div>
      <slot name="append"></slot>
    </div>
    <template v-if="isShowSwitchBtn">
      <div
        class="mk-carousel-button-prev mk-carousel-button"
        @click="switchPrev"
      >
        <slot name="prev">
          <i class="icon iconfont iconicon-test21"></i>
        </slot>
      </div>
      <div
        class="mk-carousel-button-next mk-carousel-button"
        @click="switchNext"
      >
        <slot name="next">
          <i class="icon iconfont iconicon-test23"></i>
        </slot>
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, PropType } from "vue";

/**
 * @Name: 走马灯
 * @Author: gaohan
 * @Date: 2021/12/30 19:14
 * @LastEditors: gaohan
 * @LastEditTime: 2021/12/30
 * @slot: prepend 前置内容内容
 * @slot: content 内容
 * @slot: append 后置内容
 * @slot: prev 向前切换按钮
 * @slot: next 后切换按钮
 */

export default defineComponent({
  name: "mk-carousel",

  emits: ["on-click"],

  props: {
    // 自动轮播方向
    dir: {
      type: String as PropType<"left" | "right">,
      default: "left",
    },
    // 自动轮播
    autoplay: {
      type: Boolean,
      default: true,
    },
    // 是否展示切换按钮
    isShowSwitchBtn: {
      type: Boolean,
      default: true,
    },
    // 数据集合
    data: {
      type: Array,
      required: true,
    },
    // 宽度
    width: {
      type: Number,
      default: 350,
    },
    // 高度
    height: {
      type: Number,
      default: 188,
    },
  },

  setup(props, ctx) {
    const state = reactive({
      // 动画延时
      delay: 3,
    });

    /**
     * @name: 向前切换
     */
    const switchPrev = () => {
      let delay = state.delay;
      delay -= 3;
      if (delay <= 3) {
        delay = 3;
      }
      state.delay -= delay;
    };
    /**
     * @name: 向后切换
     */
    const switchNext = () => {
      state.delay += 3;
    };
    /**
     * @name: 点击事件
     */
    const clickItem = (item: any, index: number) => {
      ctx.emit("on-click", item, index);
    };

    return {
      ...toRefs(state),
      switchPrev,
      switchNext,
      clickItem,
    };
  },
});
</script>

<style lang="scss" scoped>
@import "../../css/install";
@keyframes moveLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}
@keyframes moveRight {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
.mk-carousel {
  white-space: nowrap;
  display: inline-flex;
  position: relative;
  width: $main-container-width;
  &.showBtn {
    margin: 0 260px;
  }
  &.autoplay {
    &.left {
      .mk-carousel-list {
        animation: moveLeft linear 60s infinite var(--delay);
      }
    }
    &.right {
      .mk-carousel-list {
        animation: moveRight linear 60s infinite var(--delay);
      }
    }
  }
  &:hover {
    .mk-carousel-list {
      animation-play-state: paused;
    }
    .mk-carousel-button {
      display: block;
    }
  }

  .mk-carousel-list {
    display: inline-flex;
    align-items: center;
  }
  .mk-carousel-item {
    display: inline-block;
    text-align: left;
    cursor: pointer;
    width: var(--width);
    height: var(--height);
    background-color: #fff;
    border-radius: 10px;
    margin: 0 20px;
  }
  .mk-carousel-button {
    position: absolute;
    z-index: 1;
    top: 50%;
    height: 60px;
    margin-top: -10px;
    line-height: 60px;
    text-align: center;
    cursor: pointer;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 21px;
    display: none;
    .iconfont {
      font-size: 36px;
      color: #fff;
    }
    &:hover {
      .iconfont {
        @include theme_color();
      }
    }
    &.mk-carousel-button-prev {
      left: 10px;
    }
    &.mk-carousel-button-next {
      right: 10px;
    }
  }
}
</style>

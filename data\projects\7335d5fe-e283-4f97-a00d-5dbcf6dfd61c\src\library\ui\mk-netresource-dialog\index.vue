<template>
  <mk-dialog
    ref="mkModalRef"
    :isShowDialog="true"
    :dialogWidth="'48%'"
    :title="title"
    :sureBtnLoading="sureBtnLoading"
    :isCustomFooter="true"
    @click-sure="sure"
    @click-cancel="cancel"
    :customClass="'netres-box'"
  >
    <!--弹框内容-->
    <template v-slot:customSlot>
      <div class="lesson-list">
        <div class="resource-top flex-between">
          <!--tab页切换-->
          <ul class="type-box">
            <li
              @click="changeCourseInfoType(TAB_TYPE.folder)"
              class="type-item"
              :class="activeName == TAB_TYPE.folder ? 'is-active' : ''"
            >
              文件夹
            </li>
            <li
              @click="changeCourseInfoType(TAB_TYPE.course)"
              class="type-item"
              :class="activeName == TAB_TYPE.course ? 'is-active' : ''"
              v-if="isShowCourseTab"
            >
              课件
            </li>
            <li
              @click="changeCourseInfoType(TAB_TYPE.micro)"
              class="type-item"
              :class="activeName == TAB_TYPE.micro ? 'is-active' : ''"
              v-if="isShowMicroTab"
            >
              微课
            </li>
          </ul>
          <el-input
            class="input-search"
            :placeholder="
              activeName == TAB_TYPE.folder
                ? '请输入名称关键字进行搜索'
                : activeName == TAB_TYPE.course
                ? '请输入课件资源名称进行搜索'
                : '请输入微课资源名称进行搜索'
            "
            suffix-icon="el-icon-search"
            v-model.trim="queryParams.keyWord"
            @change="handleSearch"
            clearable
          >
          </el-input>
        </div>
        <!--文件夹列表-->
        <div v-if="activeName == TAB_TYPE.folder" class="type-table">
          <!--文件夹面包屑-->
          <div
            class="breadcrumb-folder"
            v-if="breadcrumbList.length && breadcrumbList.length > 0"
          >
            <el-breadcrumb separator-class="el-icon-arrow-right">
              <el-breadcrumb-item
                class="breadcrumb-item"
                @click.native="gotoCurrentFolder(null, -1)"
                >全部文件夹
              </el-breadcrumb-item>
              <template v-for="(item, index) in breadcrumbList">
                <el-breadcrumb-item
                  class="breadcrumb-item"
                  v-if="index < breadcrumbList.length - 1"
                  @click.native="gotoCurrentFolder(item, index)"
                  >{{ item.title }}
                </el-breadcrumb-item>
                <el-breadcrumb-item v-else>{{ item.title }}</el-breadcrumb-item>
              </template>
            </el-breadcrumb>
          </div>
          <el-row
            class="course-evaluate-list"
            v-if="folderList.length > 0"
            v-loadmore="loadMore"
          >
            <el-row
              v-for="(item, index) in folderList"
              :key="index"
              type="flex"
              align="middle"
              class="course-evaluate-item"
              @click="clickFileInfo(item)"
            >
              <template v-if="item.isFolder == FOLDER_TYPE.FOLDER">
                <el-col :span="2" style="text-align: start; margin-right: 5px">
                  <img
                    :src="
                      require('@/library/ui/mk-netresource-dialog/assets/folder.png')
                    "
                    class="courser-item-left"
                  />
                </el-col>

                <el-col :span="21">
                  <span class="course-evaluate-name" :title="item.title">{{
                    item.title
                  }}</span>
                </el-col>
              </template>
              <template v-else>
                <el-col :span="2" style="text-align: start">
                  <svg class="icon" aria-hidden="true">
                    <use
                      :xlink:href="
                        '#' + getFileIconByType(item.fileType, item.fileUrl)
                      "
                    ></use>
                  </svg>
                </el-col>
                <el-col :span="isShowTypeSelect && item.checked ? 15 : 20">
                  <span class="course-evaluate-name" :title="item.title">{{
                    item.title + '.' + item.fileExt
                  }}</span>
                  <span
                    class="course-evaluate-content"
                    v-if="item.fileType == '2'"
                  >
                    {{ timeToSize(item.timeLength) }}
                  </span>
                  <span class="course-evaluate-content" v-else>
                    {{ fileToSize(item.fileSize) }}
                  </span>
                </el-col>
                <el-col :span="5" v-if="isShowTypeSelect && item.checked">
                  <el-select
                    :disabled="typeDisabled"
                    v-model="item.typeCode"
                    @change="changeFileType($event, item)"
                  >
                    <el-option
                      v-for="val in fileTypeList"
                      :key="val.dict_code"
                      :label="val.dict_name"
                      :value="val.dict_code"
                    >
                    </el-option>
                  </el-select>
                </el-col>
                <el-col :span="2" style="text-align: center" @click.stop>
                  <el-checkbox
                    v-model="item.checked"
                    @change="changeCheckBox(item)"
                  ></el-checkbox>
                </el-col>
              </template>
            </el-row>
          </el-row>
          <!--无数据缺省-->
          <mk-no-data v-else></mk-no-data>
        </div>
        <!-- 课件列表 -->
        <div v-if="activeName == TAB_TYPE.course" class="type-table">
          <el-row
            class="course-evaluate-list"
            v-if="courseList.length > 0"
            v-loadmore="loadMore"
          >
            <el-row
              v-for="(item, index) in courseList"
              :key="index"
              type="flex"
              align="middle"
              class="course-evaluate-item"
              @click="clickFileInfo(item)"
            >
              <el-col :span="2" style="text-align: start">
                <svg class="icon" aria-hidden="true">
                  <use
                    :xlink:href="
                      '#' + getFileIconByType(item.fileType, item.fileUrl)
                    "
                  ></use>
                </svg>
              </el-col>

              <el-col :span="isShowTypeSelect && item.checked ? 15 : 20">
                <span class="course-evaluate-name" :title="item.title">{{
                  item.title + '.' + item.fileExt
                }}</span>
                <span class="course-evaluate-content">
                  {{ fileToSize(item.fileSize) }}
                </span>
              </el-col>
              <el-col :span="5" v-if="isShowTypeSelect && item.checked">
                <el-select
                  :disabled="typeDisabled"
                  v-model="item.typeCode"
                  @change="changeFileType($event, item)"
                >
                  <el-option
                    v-for="val in fileTypeList"
                    :key="val.dict_code"
                    :label="val.dict_name"
                    :value="val.dict_code"
                  >
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="2" style="text-align: center" @click.stop>
                <el-checkbox
                  v-model="item.checked"
                  @change="changeCheckBox(item)"
                ></el-checkbox>
              </el-col>
            </el-row>
          </el-row>
          <!--无数据缺省-->
          <mk-no-data v-else></mk-no-data>
        </div>
        <!-- 微课列表 -->
        <div v-if="activeName == TAB_TYPE.micro" class="type-table">
          <el-row
            class="course-evaluate-list"
            v-if="microList.length > 0"
            v-loadmore="loadMore"
          >
            <el-row
              v-for="(item, index) in microList"
              :key="index"
              type="flex"
              align="middle"
              class="course-evaluate-item"
              @click="clickFileInfo(item)"
            >
              <el-col :span="2">
                <svg class="icon" aria-hidden="true">
                  <use
                    :xlink:href="
                      '#' + getFileIconByType(item.fileType, item.fileUrl)
                    "
                  ></use>
                </svg>
              </el-col>

              <el-col :span="isShowTypeSelect && item.checked ? 15 : 20">
                <span class="course-evaluate-name" :title="item.title">{{
                  item.title + '.' + item.fileExt
                }}</span>
                <span class="course-evaluate-content">
                  {{ timeToSize(item.timeLength) }}
                </span>
              </el-col>
              <el-col :span="5" v-if="isShowTypeSelect && item.checked">
                <el-select
                  :disabled="typeDisabled"
                  v-model="item.typeCode"
                  @change="changeFileType($event, item)"
                >
                  <el-option
                    v-for="val in fileTypeList"
                    :key="val.dict_code"
                    :label="val.dict_name"
                    :value="val.dict_code"
                  >
                  </el-option>
                </el-select>
              </el-col>
              <el-col :span="2" style="text-align: center" @click.stop>
                <el-checkbox
                  v-model="item.checked"
                  @change="changeCheckBox(item)"
                ></el-checkbox>
              </el-col>
            </el-row>
          </el-row>
          <!--无数据缺省-->
          <mk-no-data v-else></mk-no-data>
        </div>
      </div>
    </template>
    <template v-slot:customFooterSlot>
      <div style="text-align: end">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="sure" :loading="sureBtnLoading"
          >确定
        </el-button>
      </div>
    </template>
  </mk-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, onMounted } from 'vue';
import { useStore } from 'vuex';
import { IGlobalState } from '@/library/src/store';
import {
  getTeacherResourceAPI,
  getResInfoByIds
} from '@/library/src/service/API/resource';
import { ElMessage } from 'element-plus';
import { getDictListAPI } from '@/library/src/service/API/base';
import { get_file_type } from "@/library/src/utils/globalFunction";
import {
  bytesToSize,
  durationTrans,
  getFileIconByType
} from '@/library/src/utils/globalFunction';
/**tab类型*/
export enum TAB_TYPE {
  /** 文件夹 */
  folder = 'folder',
  /** 课件 */
  course = 'course',
  /** 微课 */
  micro = 'micro'
}
/**文件夹类型 */
export enum FOLDER_TYPE {
  //是否文件夹（-1全部，0 否（默认），1 是）
  ALL = -1,
  RES = 0,
  FOLDER = 1
}
export default defineComponent({
  name: 'mk-netresource-dialog',

  props: {
    //弹框标题
    title: {
      type: String,
      default: ''
    },
    //确定按钮的load
    sureBtnLoading: {
      type: Boolean,
      default: false
    },
    //是否展示课件tab页切换
    isShowCourseTab: {
      type: Boolean,
      default: true
    },
    //是否展示微课tab页切换
    isShowMicroTab: {
      type: Boolean,
      default: true
    },
    //默认选择得tab
    tabName: {
      type: String,
      default: TAB_TYPE.folder
    },
    //是否禁选类型筛选
    typeDisabled: {
      type: Boolean,
      default: false
    },
    //是否展示类型筛选
    isShowTypeSelect: {
      type: Boolean,
      default: false
    },
    //最大数量
    maxLimit: {
      type: Number,
      default: 10
    },
    //是否展示默认类型
    isShowDefaultType: {
      type: Boolean,
      default: false
    },
    //是否展示自定义类型
    isSelfType: {
      type: Boolean,
      default: false
    },
    //默认类型名称
    typeName: {
      type: String,
      default: ''
    },
    //默认类型code
    typeCode: {
      type: String,
      default: ''
    },
    resourceTypeList: {
      type: Array,
      default: []
    },
    suffixTypeList: {
      type: Array,
      default: []
    }
  },

  setup(props, context) {
    const uploadRef = ref<any>(null);
    const store = useStore<IGlobalState>();
    const state = reactive({
      //确认按钮加载中
      sureBtnLoading: false,
      pageCount: 0,
      //文件夹列表
      folderList: [] as any[],
      //课件列表
      courseList: [] as any[],
      //微课列表
      microList: [] as any[],
      //公共参数
      queryParams: {
        schoolId: store.state.user.schoolId,
        userId: store.state.user.userId,
        keyWord: '',
        page: 1,
        //文件后缀类型
        suffix: 'all'
      },
      //文件夹参数
      queryFolder: {
        appType: -1,
        parentId: '0',
        //是否文件夹（-1全部，0 否（默认），1 是）
        isFolder: FOLDER_TYPE.FOLDER,
        limit: 999
      },
      //课件参数
      queryCourse: {
        appType: -1,
        parentId: '0',
        notContainSuffix: 'video',
        limit: 10
      },
      //微课参数
      queryMicro: {
        appType: 0,
        parentId: '0',
        //文件类型(0:其它1:文档 2:微课 3:音频 4:图片)(非必传)
        fileType: '2',
        limit: 10
      },
      // 当前tab
      activeName: props.tabName as keyof typeof TAB_TYPE,
      //勾选状态
      checked: false,
      list: [] as any[],
      //类型列表
      fileTypeList: [] as any,
      //面包屑导航
      breadcrumbList: [] as any[]
    });
    /**
     * @name 文件转换
     * @param value
     */
    const fileToSize = (value: any) => {
      if (value != '') {
        return bytesToSize(value);
      }
    };
    /**
     * @name 时长转换
     * @param value
     */
    const timeToSize = (value: any) => {
      if (value != '') {
        return durationTrans(value);
      }
    };
    /**
     * @name 初始化数据
     */
    const initData = () => {
      state.queryParams.page = 1;
      state.queryParams.keyWord = '';
    };
    /**
     * @name 获取文件夹和资源列表
     */
    const getFolderResList = async () => {
      await getFolderList();
      await getResourceList(TAB_TYPE.folder);
    };
    /**
     * @name 获取文件夹列表
     */
    const getFolderList = async () => {
      let params: any = {
        ...state.queryParams,
        ...state.queryFolder
      };
      const res = await getTeacherResourceAPI(params);
      if (res.code == 1) {
        state.folderList = res.data.rows;
      } else {
        ElMessage({
          message: res.msg,
          type: 'error'
        });
      }
    };
    /**
     * @name 获取资源列表
     * @param type TAB_TYPE.folder：文件夹 TAB_TYPE.course：课件 TAB_TYPE.micro：微课
     */
    const getResourceList = async (type: any) => {
      let params: any = {};
      if (type == TAB_TYPE.folder) {
        params = {
          ...state.queryParams,
          appType: state.queryFolder.appType,
          parentId: state.queryFolder.parentId,
          limit: 10
        };
        if (!props.isShowCourseTab) {
          //只展示微课 文件夹内屏蔽除视频资源以外的文件
          params.appType = 0;
          params.fileType = '2';
        } else if (!props.isShowMicroTab) {
          //只展示课件 文件夹内屏蔽视频资源
          params.notContainSuffix = 'video';
        }
      } else if (type == TAB_TYPE.course) {
        params = {
          ...state.queryParams,
          ...state.queryCourse
        };
      } else {
        params = {
          ...state.queryParams,
          ...state.queryMicro
        };
      }
      const res = await getTeacherResourceAPI(params);
      if (res.code == 1) {
        //资源默认课例类型
        res.data.rows.forEach((item: any) => {
          item.typeCode = props.typeDisabled ? '31308' : '';
          item.typeName = props.typeDisabled ? '课例' : '';
        });
        state.pageCount = res.data.page_count;
        if (type == TAB_TYPE.folder) {
          state.folderList = [...state.folderList, ...res.data.rows];
        } else if (type == TAB_TYPE.course) {
          state.courseList = [...state.courseList, ...res.data.rows];
        } else {
          state.microList = [...state.microList, ...res.data.rows];
          // 名师工作室默认类型是微课
          if (props.isShowDefaultType) {
            state.microList.map((item: any) => {
              item.typeName = props.typeName;
              item.typeCode = props.typeCode;
            });
          }
        }
      } else {
        ElMessage({
          message: res.msg,
          type: 'error'
        });
      }
    };
    /**
     * @name 获取文件
     */
    const clickFileInfo = (item: any) => {
      initData();
      if (item.isFolder == FOLDER_TYPE.FOLDER) {
        updateBreadcrumbList(item.nodePath);
      } else {
        item.checked = !item.checked;
        changeCheckBox(item);
      }
    };
    /**
     * @name 刷新面包屑
     */
    const updateBreadcrumbList = (nodePath: any) => {
      if (nodePath != '0') {
        let folderIdStr = '';
        if (nodePath.indexOf('/') > -1) {
          const folderIdArr = nodePath.split('/');
          folderIdStr = folderIdArr.join(',');
        } else {
          folderIdStr = nodePath;
        }
        findNodeFolderList(folderIdStr);
      } else {
        state.queryFolder.parentId = nodePath;
        getFolderResList();
      }
    };
    /**
     * @name 根据nodePath查找文件夹集合
     */
    const findNodeFolderList = async (folderIdStr: any) => {
      const params = {
        ids: folderIdStr
      };
      const res = await getResInfoByIds(params);
      if (res.code == 1) {
        if (Object.keys(res.data).length === 0) {
          //文件夹集合为空时返回顶级
          state.queryFolder.parentId = '0';
          getFolderList();
          return;
        }
        if (res && parseInt(res.code) === 1) {
          state.breadcrumbList = [];
          for (let i = 0; i < res.data.length; i++) {
            const breadcrumbObj = {
              id: '',
              title: '',
              nodePath: ''
            };
            breadcrumbObj.id = res.data[i].id;
            breadcrumbObj.title = res.data[i].title;
            breadcrumbObj.nodePath = res.data[i].node_path;
            state.breadcrumbList.push(breadcrumbObj);
          }
          state.queryFolder.parentId = res.data[res.data.length - 1].id;
          getFolderResList();
        }
      } else {
        ElMessage({
          message: '文件夹列表请求出现异常',
          type: 'error'
        });
      }
    };
    /**
     * @name 去到当前选中的面包屑文件夹下
     */
    const gotoCurrentFolder = (breadObj: any, index: number) => {
      initData();
      state.folderList = [];
      if (index == -1) {
        //全部文件回到首页
        state.queryFolder.parentId = '0';
        state.breadcrumbList = [];
        getFolderList();
      } else {
        state.queryFolder.parentId = breadObj.id;
        //移除当前选中面包屑后面的元素
        state.breadcrumbList.splice(
          index + 1,
          state.breadcrumbList.length - 1 - index
        );
        getFolderResList();
      }
    };
    /**
     * @name 获取资源类型
     */
    const getResourcesType = async () => {
      let params = {
        typecode: 313,
        page: 1,
        limit: 10
      };
      const res = await getDictListAPI(params);
      if (res && parseInt(res.code) === 1) {
        state.fileTypeList = res.data.rows;
      }
    };
    /**
     * @name 切换备课信息
     * @param name 当前tab
     * @param page 页数
     */
    const changeCourseInfoType = (name: keyof typeof TAB_TYPE) => {
      state.activeName = name;
      initData();
      state.queryFolder.parentId = '0';
      state.queryFolder.isFolder = FOLDER_TYPE.FOLDER;
      state.breadcrumbList = [];
      state.folderList = [];
      state.courseList = [];
      state.microList = [];
      state.list = [];
      switch (state.activeName) {
        //获取文件夹列表数据
        case TAB_TYPE.folder:
          getFolderList();
          break;
        //获取课程列表数据
        case TAB_TYPE.course:
          getResourceList(TAB_TYPE.course);
          break;
        //获取微课列表数据
        case TAB_TYPE.micro:
          getResourceList(TAB_TYPE.micro);
          break;
      }
    };
    /**
     * @name 搜索
     */
    const handleSearch = () => {
      state.queryParams.page = 1;
      if (state.activeName == TAB_TYPE.folder) {
        //文件夹搜索 支持搜索当前层级的文件，不包含文件夹内部资源
        state.folderList = [];
        if (
          state.queryFolder.parentId == '' ||
          state.queryFolder.parentId == '0'
        ) {
          //一级文件夹只搜索文件夹
          getFolderList();
        } else {
          //二级及以上搜索当前文件夹和资源
          getFolderResList();
        }
      } else if (state.activeName == TAB_TYPE.course) {
        //课件搜索
        state.courseList = [];
        getResourceList(TAB_TYPE.course);
      } else {
        //微课搜索
        state.microList = [];
        getResourceList(TAB_TYPE.micro);
      }
    };
    /**
     * @name 滚动加载
     */
    const loadMore = () => {
      //文件夹不支持滚动加载，文件夹内的资源支持滚动加载
      if (
        state.activeName == TAB_TYPE.folder &&
        (state.queryFolder.parentId == '0' || !state.queryFolder.parentId)
      ) {
        return;
      }
      state.queryParams.page++;
      if (state.queryParams.page > state.pageCount) {
        return;
      }
      getResourceList(state.activeName);
    };
    /**
     * @name 改变选中框
     */
    const changeCheckBox = (item: any) => {
      if (
        props.suffixTypeList.length>0&&props.suffixTypeList.indexOf(get_file_type("***." + item.fileExt)) <= -1
      ) {
        ElMessage({
          message: "暂不支持上传该类型的资源",
          type: "warning",
        });
        item.checked = !item.checked;
      }
      state.checked = !item.checked;
      if (item.checked) {
        state.list.push(item);
      } else {
        state.list = state.list.filter((value: any) => {
          return value != item;
        });
      }
    };
    /**
     * @name 点击确认
     */
    const sure = () => {
      state.sureBtnLoading = true;
      uploadResource();
    };

    /**
     * @name:点击取消
     */
    const cancel = () => {
      state.list = [];
      state.checked = false;
      state.sureBtnLoading = false;
      context.emit('cancle');
    };
    /**
     * @name 批量上传资源
     */
    const uploadResource = () => {
      if (state.list.length <= 0) {
        ElMessage({
          message: '请选择要上传的资源',
          type: 'warning'
        });
        state.sureBtnLoading = false;
        return;
      }
      if (state.list.length > props.maxLimit) {
        ElMessage.closeAll();
        ElMessage({
          message: `操作失败，文件个数超出限制，最多只能上传${props.maxLimit}个文件`,
          type: 'warning'
        });
        state.sureBtnLoading = false;
        return false;
      }
      if (props.isShowTypeSelect) {
        //判断类型筛选
        for (let i = 0; i < state.list.length; i++) {
          if (state.list[i].typeCode == '') {
            ElMessage({
              message: '操作失败，请选择文件类型',
              type: 'warning'
            });
            state.sureBtnLoading = false;
            return false;
          }
        }
      }
      saveResource();
    };
    /**
     * @name 保存资源
     */
    const saveResource = () => {
      context.emit('save-rescourse', state.list);
    };
    /**
     * @name：切换类型
     */
    const changeFileType = (value: any, item: any) => {
      for (let i = 0; i < state.fileTypeList.length; i++) {
        if (state.fileTypeList[i].dict_code == value) {
          item.typeName = state.fileTypeList[i].dict_name;
          break;
        }
      }
    };
    /**
     * @name 页面一开始加载
     */
    onMounted(() => {
      changeCourseInfoType(state.activeName);
      //获取资源类型
      if (props.isShowTypeSelect) {
        if (props.isSelfType) {
          state.fileTypeList = props.resourceTypeList;
        } else {
          getResourcesType();
        }
      }
    });
    return {
      ...toRefs(state),
      uploadRef,
      TAB_TYPE,
      FOLDER_TYPE,
      cancel,
      sure,
      handleSearch,
      changeCourseInfoType,
      loadMore,
      changeCheckBox,
      uploadResource,
      fileToSize,
      timeToSize,
      getFileIconByType,
      changeFileType,
      clickFileInfo,
      gotoCurrentFolder
    };
  }
});
</script>

<style lang="scss" scoped>
@import '~@/library/css/variables';
.lesson-list {
  background-color: #fff;
  .resource-top {
    border-bottom: 1px solid #e2e9ed;
    position: relative;
    .type-box {
      text-align: start;
      padding: 0;
      margin: 0;
      line-height:32px;
      .type-item {
        padding: 5px 15px;
        margin-right: 45px;
        display: inline-block;
        font-size: 16px;
        color: #4e5668;
        cursor: pointer;
        &:hover {
          color: $main-link-color;
        }
        &.is-active {
          color: $main-link-color;
          border-bottom: 4px solid $main-link-color;
          border-radius: 2px;
        }
      }
    }
  }
}
.course-evaluate-list {
  max-height: 380px;
  overflow: auto;
  .course-evaluate-item {
    width: 100%;
    padding: 15px 0;
    border-bottom: 1px solid #e6e6e6;
    cursor: pointer;
    .course-evaluate-name {
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      line-height: 30px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      display: block;
    }
    .course-evaluate-content {
      font-size: 14px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #bbbbbb;
      line-height: 30px;
    }
    .active {
      color: #f84b4b;
    }
    .icon {
      width: 40px;
      height: 40px;
    }
  }
  .course-evaluate-item:hover {
    border-color: #d5e1ff;
    background-color: #f7f9ff;
  }
}
</style>
<style lang="scss">
.lesson-list {
  .input-search {
    width: 300px;
    margin-bottom: 8px;
    .el-input__inner {
      font-size: 14px;
      border: none;
      background-color: #f5f7fa;
      border-radius: 17px !important;
      height: 36px !important;
      line-height: 36px !important;
    }
  }
  .breadcrumb-folder {
    margin-top: 20px;
    .el-breadcrumb {
      border-bottom: 0 !important;
      margin-bottom: 0 !important;
      cursor: pointer;
    }
    .el-breadcrumb__inner {
      color: #409eff;
    }
  }
}
.netres-box {
  > .el-dialog__body {
    padding-top: 10px !important;
  }
}
</style>

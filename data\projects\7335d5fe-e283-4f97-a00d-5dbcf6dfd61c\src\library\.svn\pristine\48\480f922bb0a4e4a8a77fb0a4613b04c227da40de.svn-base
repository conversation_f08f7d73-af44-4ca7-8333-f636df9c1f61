<template>
  <!-- 添加个人教材 -->
  <div class="add-userbook-content">
    <!--级联选择-->
    <el-cascader popper-class="add-userbook-cascader" v-model="checkBookValue" clearable :props="props"
      @change="bookCascaderChange" @expand-change="expandChange" placeholder="请选择教材" :key="cascaderKey" empty="暂无数据">
      <template #default="{ node, data }">
        <span :title="data.label">{{ data.label }}</span>
      </template>
    </el-cascader>
    <!--封面图-->
    <div class="cover-content">
      <img v-if="currentSelectBook && currentSelectBook.cover" :src="currentSelectBook.cover" class="cover-image" />
      <img v-if="!currentSelectBook || !currentSelectBook.cover"
        :src="require('@/library/ui/mk-share-res/assets/book_empty.png')" class="cover-image textbook-img" alt="" />
    </div>
    <div class="opt-btn-footer">
      <el-button @click="cancelAdd" class="cancel-btn"> 取 消 </el-button>
      <el-button type="primary" @click="commitAdd">确 定</el-button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue';
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';
import { IGlobalState } from '@/library/src/store';
import {
  gradeListAPI,
  subjectListAPI,
  editionListAPI,
  bookListAPI,
} from '@/library/src/service/API/resource';
import {
  getRegionBookGradeListAPI,
  getRegionBookSubjectListAPI,
  getRegionBookEditionListAPI,
  regionBookListAPI
} from '@/library/src/service/API/tearesearch';
/**年级列表数据接口类 */
export interface IGrade {
  /**年级名称 */
  name: string;
  /**年级编码 */
  code: string;
  /**下级数量 */
  children: number;
}
/**学科列表数据接口类 */
export interface ISubject {
  /**学科名称 */
  name: string;
  /**学科编码 */
  code: string;
  /**下级数量 */
  children: number;
}
/**版本列表数据接口类 */
export interface IEdition {
  /**版本名称 */
  name: string;
  /**版本编码 */
  code: string;
  /**下级数量 */
  children: number;
}
/**教材列表数据接口类 */
export interface IBook {
  /**教材名称 */
  name: string;
  /**年级编码 */
  grade: string;
  /**学科编码 */
  subject: string;
  /**版本编码*/
  edition: string;
  /**学段编码 */
  phase: string;
  /**教材编码 */
  code: string;
  /**封面 */
  cover: string;
  /**是否绑定电子书（1绑定，0未绑定） */
  isbind: number;
}
/** 分享类型 */
export enum SHARE_TYPE {
  /** 校本资源 */
  school = 'school',
  /** 区域资源 */
  platform = 'platform'
}

export default defineComponent({
  name: 'textbook-condition',
  props: {
    //书本编码
    bookCode: {
      type: String,
      default: ''
    },
    //平台id
    platFromId: {
      type: String,
      default: ''
    },
    //是否开通校级平台
    isSchoolPlat: {
      type: Boolean,
      default: false
    },
    //统一教材的配置类型
    schoolRadioType: {
      type: Number,
      default: 0
    },
    //分享类型
    shareType: {
      type: String,
      default: ''
    }
  },
  setup(props, context) {
    const store = useStore<IGlobalState>();
    const state = reactive({
      // 年级列表
      gradeList: [] as IGrade[],
      // 学科列表
      subjectList: [] as ISubject[],
      // 版本列表
      editionList: [] as IEdition[],
      // 教材的列表
      bookList: [] as IBook[],
      // 年级编码
      gradeCode: '',
      // 学科编码
      subjectCode: '',
      // 版本编码
      editionCode: '',
      // 年级名称
      gradeName: '',
      // 学科名称
      subjectName: '',
      // 版本名称
      editionName: '',
      checkBookValue: [] as any[],
      // 当前选中的书
      currentSelectBook: {
        code: '0801010101-950',
        name: '',
        id: '',
        cover: ''
      } as any,
      // 筛选条件
      textbookFilter: {
        gradeInfo: [],
        subjectInfo: [],
        editionInfo: []
      },
      //级联key
      cascaderKey: 0,
      //配置项
      props: {
        lazy: true,
        lazyLoad: (node: any, resolve: any) => {
          const { level } = node;
          switch (level) {
            // 年级
            case 0:
              getGradeData(level, resolve);
              break;
            // 学科
            case 1:
              getSubjectData(node, level, resolve);
              break;
            // 版本
            case 2:
              getEditionData(node, level, resolve);
              break;
            // 教材
            case 3:
              getBookData(node, level, resolve);
              break;
          }
        }
      }
    });
    /**
     * @name 选中教材
     */
    const selectTextbook = (item: any) => {
      if (!item || item.id === props.bookCode) {
        closeModal();
        return;
      }
      context.emit('switchTextbook', item, state.textbookFilter);
      closeModal();
    };


    /**
     * @name 根据gradeCode、subjectCode、editionCode、userid获取教材列表
     */
    const getBookList = async (
      gradeCode: string,
      subjectCode: string,
      editionCode: string
    ) => {
      let fn = null;
      let bookApi = {}
      if (props.shareType == SHARE_TYPE.school) {
        //state.schoolRadioType：3 获取管理员手动添加：系统统一教材
        fn = props.schoolRadioType == 3 ? regionBookListAPI : bookListAPI;
        bookApi = props.schoolRadioType == 3 ? {
          regionId: store.state.app.platFormInfo.id,
          bookType: 1
        } : {
          phase: props.schoolRadioType == 1 ? '' : store.state.user.phase
        }
      } else {
        fn = bookListAPI
      }
      const params = {
        gradeCode: gradeCode,
        subjectCode: subjectCode,
        editionCode: editionCode,
        ...bookApi
      };
      const res = await fn(params);
      if (res.code == 1) {
        const bookList = res.data.map((item: any) => {
          return {
            code: props.schoolRadioType == 3 ? item.bookCode : item.code,
            name: props.schoolRadioType == 3 ? item.bookName : item.name,
            cover: item.cover,
            id: item.id,
          }

        });
        return bookList;
      } else {
        ElMessage({
          message: res.msg,
          type: 'error'
        });
      }
    };
    /**
     * @name 获取教材相关数据
     */
    const getBookData = async (node: any, level: any, resolve: any) => {
      state.editionCode = node.value;
      state.editionName = node.label;
      state.bookList = await getBookList(
        state.gradeCode,
        state.subjectCode,
        state.editionCode
      );
      try {
        const nodes = await state.bookList.map((item: any) => ({
          value: item.code,
          label: item.name,
          leaf: level >= 3
        }));
        resolve(nodes);
      } catch (error) {
        resolve();
        console.table(error);
      }
    };
    /**
     * @name 获取版本相关数据
     */
    const getEditionData = async (node: any, level: any, resolve: any) => {
      state.subjectCode = node.value;
      state.subjectName = node.label;
      state.editionList = await getEditionList(
        state.gradeCode,
        state.subjectCode
      );
      const nodes = await state.editionList.map((item: any) => ({
        value: props.schoolRadioType == 3 ? item.editionCode : item.code,
        label: props.schoolRadioType == 3 ? item.editionName : item.name,
        leaf: level >= 3
      }));
      resolve(nodes);
    };
    /**
    * @name 根据gradeCode、subjectCode获取版本列表
    */
    const getEditionList = async (gradeCode: string, subjectCode: string) => {
      //state.schoolRadioType：3 获取管理员手动添加：系统统一教材
      let fn = props.schoolRadioType == 3 ? getRegionBookEditionListAPI : editionListAPI;
      const params = {
        gradeCode: gradeCode,
        subjectCode: subjectCode
      };
      const res = await fn(params);
      if (res.code == 1) {
        const editionList = res.data;
        state.textbookFilter.editionInfo = editionList;
        return editionList;
      } else {
        ElMessage({
          message: res.msg,
          type: 'error'
        });
      }
    };
    /**
     * @name 获取学科相关数据
     */
    const getSubjectData = async (node: any, level: any, resolve: any) => {
      state.gradeCode = node.value;
      state.gradeName = node.label;
      const res = await getSubjectList(state.gradeCode);
      state.subjectList = res.data;
      const nodes = await state.subjectList.map((item: any) => ({
        value: props.schoolRadioType == 3 ? item.subjectCode : item.code,
        label: props.schoolRadioType == 3 ? item.subjectName : item.name,
        leaf: level >= 3
      }));
      resolve(nodes);
    };
    /**
     * @name 获取学科列表
     */
    const getSubjectList = async (gradeCode: string) => {
      //state.schoolRadioType：3 获取管理员手动添加：系统统一教材
      let fn = props.schoolRadioType == 3 ? getRegionBookSubjectListAPI : subjectListAPI;
      const subjectList = await fn({ gradeCode: gradeCode });
      state.textbookFilter.subjectInfo = subjectList;
      state.textbookFilter.editionInfo = [];
      return subjectList;
    };
    /**
     * @name 获取年级相关数据
     */
    const getGradeData = async (level: any, resolve: any) => {
      const res = await getGradeList();
      state.gradeList = res.data;
      const nodes = await state.gradeList.map((item: any) => ({
        value: props.schoolRadioType == 3 ? item.gradeCode : item.code,
        label: props.schoolRadioType == 3 ? item.gradeName : item.name,
        leaf: level >= 3
      }));
      resolve(nodes);
    };
    /**
      * @name 获取年级列表
      */
    const getGradeList = async () => {
      //state.schoolRadioType：3 获取管理员手动添加：系统统一教材
      let fn = props.schoolRadioType == 3 ? getRegionBookGradeListAPI : gradeListAPI;
      const gradeList = await fn({
        phase: props.schoolRadioType == 2 ? store.state.user.phase : ''
      });
      state.textbookFilter.gradeInfo = gradeList;
      state.textbookFilter.subjectInfo = [];
      state.textbookFilter.editionInfo = [];
      return gradeList;
    };
    /**
     * @name 当级联选择发生改变时
     */
    const bookCascaderChange = (value: any) => {
      // 获取当前选中的书
      if (!value || value.length < 4) {
        return;
      }
      for (let i = 0; i < state.bookList.length; i++) {
        let item = state.bookList[i];
        if (item.code === value[3]) {
          state.currentSelectBook = item;
          break;
        }
      }
      context.emit('select-book-synch', state.currentSelectBook);
    };
    /**
     * @name 当展开节点发生变化时触发
     */
    const expandChange = async (value: any) => {
      if (value.length === 1) {
        state.subjectList = await getSubjectList(value[0]);
        return;
      }
      if (value.length === 2) {
        state.editionList = await getEditionList(value[0], value[1]);
        return;
      }
      if (value.length === 3) {
        state.bookList = await getBookList(value[0], value[1], value[2]);
        return;
      }
    };
    /**
     * @name 取消添加
     */
    const cancelAdd = () => {
      context.emit('close-choose');
    };
    /**
     * @name 确定添加
     */
    const commitAdd = () => {
      if (state.checkBookValue.length > 0) {
        let selectBookData = {
          gradeCode: state.gradeCode,
          gradeName: state.gradeName,
          subjectCode: state.subjectCode,
          subjectName: state.subjectName,
          editionCode: state.editionCode,
          editionName: state.editionName,
          bookCode: state.currentSelectBook.code,
          bookName: state.currentSelectBook.name,
          bookId: state.currentSelectBook.id
        };

        cancelAdd();
        context.emit('sure-add', selectBookData);
      } else {
        ElMessage({
          message: '请选择教材',
          type: 'warning'
        });
      }
      state.checkBookValue = [];
      state.subjectList = state.editionList = state.bookList = [];
      state.textbookFilter = {
        gradeInfo: [],
        subjectInfo: [],
        editionInfo: []
      };
      state.cascaderKey = Math.random();
    };
    /**
     * @name 关闭对话框
     */
    const closeModal = () => {
      context.emit('textbookModalVis', false);
    };

    return {
      ...toRefs(state),
      bookCascaderChange,
      commitAdd,
      expandChange,
      cancelAdd,
      closeModal,
      selectTextbook
    };
  }
});
</script>

<style lang="scss">
.el-popper__arrow {
  display: none;
}

/*添加教材*/
.add-userbook-content {
  padding: 0 20px;

  .el-cascader {
    width: 100%;
    line-height: 36px;
  }

  .cover-content {
    margin: 45px 0px;
    text-align: center;
    height: 165px;

    .cover-image {
      width: 122px;
      height: 172px;
      margin: 0 auto;
      border: 1px solid #e6eef1;
    }

    .textbook-img {
      border: none;
    }
  }

  .add-userbook-cascader {
    .el-cascader-menu {
      width: 150px;
      height: 173px;
      min-width: 150px;

      &:first-child {
        min-width: 110px;
        width: 110px;
      }

      &:nth-child(2) {
        min-width: 120px;
        width: 120px;
      }

      &:nth-child(4) {
        min-width: 260px;
        width: 260px;
      }
    }

    .el-cascader-node {
      padding: 10px 8px 10px 18px;
      text-align: center;
      height: auto !important;
      line-height: normal !important;
    }

    .el-cascader-menu__list {
      padding: 0;
      min-height: 100px;
    }

  }

  .opt-btn-footer {
    text-align: center;
    margin-top: 30px;

    .cancel-btn {
      margin-right: 50px;
    }
  }
}
</style>

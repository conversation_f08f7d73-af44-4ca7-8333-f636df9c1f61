﻿/* 向上移动5px */
.transtop{
  &:hover{
    transition: 0.3s;
    transform: translateY(-5px);
  }
}
/* 无限滚动 */
.infinite-y{
  animation: infinitevertical 30s linear infinite;
  &:hover {
    animation-play-state: paused;
  }
}

.infinite-x{
  animation: infinitehorizontal 30s linear infinite;
  &:hover {
    animation-play-state: paused;
  }
}


/* 无限滚动垂直 */
@keyframes infinitevertical {
  0% {
    transform: translateY(0%);
  }
  100% {
    transform: translateY(-50%);
  }
}
/* 无限滚动水平 */
@keyframes infinitehorizontal {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}
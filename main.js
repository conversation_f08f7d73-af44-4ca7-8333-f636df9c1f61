const express = require('express');
const http = require('http');
const path = require('path');
const bodyParser = require('body-parser');
const socketIo = require('socket.io');
const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');
const dotenv = require('dotenv');
const winston = require('winston');

// 加载环境变量
dotenv.config();

// 导入路由
const projectRoutes = require('./routes/projects');
const deployRoutes = require('./routes/deploy');
const serverRoutes = require('./routes/servers');
const userRoutes = require('./routes/users');
const buildQueueRoutes = require('./routes/buildQueue');
const nodeVersionRoutes = require('./routes/nodeVersions');
const vcsCredentialRoutes = require('./routes/vcsCredentials');

// 创建Express应用
const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// 配置日志记录器
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' }),
    new winston.transports.Console({ format: winston.format.simple() })
  ],
});

// 确保日志目录存在
fs.ensureDirSync('logs');

// 确保数据目录存在
fs.ensureDirSync('data/projects');
fs.ensureDirSync('data/builds');
fs.ensureDirSync('data/configs');
fs.ensureDirSync('data/users');
fs.ensureDirSync('data/queue');

// 中间件
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Socket.io连接处理
io.on('connection', (socket) => {
  logger.info('客户端已连接');
  
  // 记录用户连接信息
  socket.on('user_connected', (userData) => {
    logger.info(`用户 ${userData.username} 已连接`);
    socket.join(`user_${userData.userId}`);
  });
  
  socket.on('disconnect', () => {
    logger.info('客户端已断开连接');
  });
});

// 使io对象在路由中可访问
app.use((req, res, next) => {
  req.io = io;
  req.logger = logger;
  next();
});

// 路由
app.use('/api/projects', projectRoutes);
app.use('/api/deploy', deployRoutes);
app.use('/api/servers', serverRoutes);
app.use('/api/users', userRoutes);
app.use('/api/queue', buildQueueRoutes);
app.use('/api/node-versions', nodeVersionRoutes);
app.use('/api/vcs-credentials', vcsCredentialRoutes);

// 首页路由
app.get('/', (req, res) => {
  res.render('index');
});

// 登录页面
app.get('/login', (req, res) => {
  res.render('login');
});

// 注册页面
app.get('/register', (req, res) => {
  res.render('register');
});

// 构建队列状态页面
app.get('/queue', (req, res) => {
  res.render('queue');
});

// VCS凭据管理页面
app.get('/vcs-credentials', (req, res) => {
  res.render('vcs-credentials');
});

// 错误处理
app.use((err, req, res, next) => {
  logger.error(`错误: ${err.message}`);
  res.status(500).json({ error: err.message });
});

// 启动服务器
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  logger.info(`服务器运行在端口 ${PORT}`);
  console.log(`服务器运行在端口 ${PORT}`);
});

// 导出应用实例，供测试和其他模块使用
module.exports = { app, io, logger }; 
<template>
  <div class="mk-time-range flex-start">
    <el-select v-model="timeType" @change="switchType" class="type-select">
      <el-option
        v-for="item in typeList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
    <!--按学年-->
    <el-select
      v-model="value"
      v-if="timeType === TIME_TYPE.year"
      class="time-box"
    >
      <el-option
        v-for="item in yearList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        @click.native="switchYear(item)"
      >
      </el-option>
    </el-select>
    <div class="time-box" v-else-if="timeType === TIME_TYPE.month">
      <!--按月-->
      <el-date-picker
        v-model="value"
        :default-value="[startDate, endDate]"
        :editable="false"
        :clearable="false"
        range-separator="至"
        start-placeholder="开始月份"
        end-placeholder="结束月份"
        type="monthrange"
        placeholder="选择月份"
        @change="switchCustom"
        :popper-append-to-body="false"
      ></el-date-picker>
    </div>
    <div class="time-box" v-else-if="timeType === TIME_TYPE.custom">
      <!--自定义-->
      <el-date-picker
        v-model="value"
        type="daterange"
        :default-value="[startDate, endDate]"
        :editable="false"
        :clearable="false"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="switchCustom"
        :popper-append-to-body="false"
      >
      </el-date-picker>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, PropType, onMounted } from 'vue';

/** 时间类型 */
export enum TIME_TYPE {
  /** 全部时间 */
  all = 'all',
  /** 按学年 */
  year = 'year',
  /** 按月 */
  month = 'month',
  /** 自定义 */
  custom = 'custom'
}

export default defineComponent({
  name: 'mk-time-range',

  emits: ['on-change'],

  props: {
    // 时间类型
    timeType: {
      type: String as PropType<TIME_TYPE>,
      default: TIME_TYPE.all
    },
    // 最大长度
    maxLength: {
      type: Number,
      default: 0
    }
  },

  setup(props, ctx) {
    const state = reactive({
      // 时间类型列表
      typeList: [
        { value: TIME_TYPE.all, label: '全部时间' },
        { value: TIME_TYPE.year, label: '按学年' },
        { value: TIME_TYPE.month, label: '按月' },
        { value: TIME_TYPE.custom, label: '自定义' }
      ],
      // 当前时间类型
      timeType: props.timeType,
      // 学年列表
      yearList: [] as any[],
      // 当前值 (全部时间,按学年时为字符串,按月,自定义时为数组)
      value: '' as any,
      // 当前开始时间
      startDate: new Date(),
      // 当前结束时间
      endDate: new Date()
    });

    /**
     * @name: 时间格式化
     * @param date 日期
     * @param isEnd 是否结束日期
     * @description: 默认时间格式(HH:mm:ss)返回 00:00:00, 若isEnd为true, 则返回23:59:59
     * @return: YYYY-MM-DD HH:mm:ss
     */
    const formatTime = (date: Date, isEnd: boolean = false) => {
      const newDate = new Date(date);
      if (isEnd) {
        newDate.setHours(23);
        newDate.setMinutes(59);
        newDate.setSeconds(59);
      } else {
        newDate.setHours(0);
        newDate.setMinutes(0);
        newDate.setSeconds(0);
      }
      const YYYY = newDate.getFullYear();
      const MM = newDate.getMonth() + 1;
      const DD = newDate.getDate();
      const HH = newDate.getHours();
      const mm = newDate.getMinutes();
      const ss = newDate.getSeconds();
      const resultDate = [YYYY, MM, DD]
        .map((item) => (item + '').padStart(2, '0'))
        .join('-');
      const time = [HH, mm, ss]
        .map((item) => (item + '').padStart(2, '0'))
        .join(':');
      return `${resultDate} ${time}`;
    };
    /**
     * @name: 设置学年列表
     * @description: 若maxLength>0,取近maxLength年,否则取近三年
     * @description: 学年: 跨自然年,以8月10号为界,8月10之前为上一学年,8月10号之后为下一学年
     */
    const setYearsList = () => {
      let maxLength = props.maxLength || 3;
      let year = new Date().getFullYear();
      if (maxLength >= year) {
        maxLength = year - 3;
      }
      const today = new Date().getTime();
      const limitDay = new Date(year, 7, 9, 23, 59, 59).getTime();
      if (today > limitDay) {
        year += 1;
      }
      let yearList: any[] = [];
      for (let i = year - maxLength; i < year; i++) {
        const startDate = new Date(i, 7, 10, 0, 0, 0);
        const endDate = new Date(i + 1, 7, 9, 23, 59, 59);
        const value = `${i}-${i + 1}`;
        yearList.unshift({ value, label: `${value}学年`, startDate, endDate });
      }
      state.yearList = yearList;
    };
    /**
     * @name: 设置月份默认值
     * @description: 若maxLength>0,取近maxLength月,否则取近6月
     */
    const setDefaultMonth = () => {
      let maxLength = props.maxLength || 6;
      const startDate = new Date();
      const endDate = new Date();
      const month = startDate.getMonth() + 1;
      if (maxLength >= 12) {
        maxLength = 6;
      }
      startDate.setMonth(month - maxLength);
      startDate.setDate(1);
      switchCustom([startDate, endDate]);
    };
    /**
     * @name: 设置自定义默认值
     * @description: 若maxLength>0,取近maxLength天,否则取近14天
     */
    const setDefaultCustom = () => {
      let maxLength = props.maxLength || 14;
      const startDate = new Date();
      const endDate = new Date();
      const stateDay = startDate.getDate() - maxLength;
      startDate.setDate(stateDay);
      switchCustom([startDate, endDate]);
    };
    /**
     * @name: 时间变化
     */
    const onChange = () => {
      let startTime, endTime;
      if (state.timeType === TIME_TYPE.all) {
        startTime = '';
        endTime = '';
      } else {
        startTime = formatTime(state.startDate);
        endTime = formatTime(state.endDate, true);
      }
      ctx.emit('on-change', { startTime, endTime, timeType: state.timeType });
    };
    /**
     * @name: 切换时间类型
     * @param type 当前时间类型
     */
    const switchType = (type: TIME_TYPE) => {
      switch (type) {
        case TIME_TYPE.all:
          switchYear({ value: '', startDate: '', endDate: '' });
          break;
        case TIME_TYPE.year:
          switchYear(state.yearList[0]);
          break;
        case TIME_TYPE.month:
          setDefaultMonth();
          break;
        case TIME_TYPE.custom:
          setDefaultCustom();
          break;
      }
    };
    /**
     * @name: 切换学年
     * @param item 当前学年对象
     */
    const switchYear = (item: any) => {
      state.value = item.value;
      state.startDate = item.startDate;
      state.endDate = item.endDate;
      onChange();
    };
    /**
     * @name: 切换自定义时间
     * @param value 当前值
     */
    const switchCustom = (value: any) => {
      state.startDate = value[0];
      if (state.timeType === TIME_TYPE.month) {
        let endDay = new Date(value[1]); // 获取时间选择器的结束日
        // 判断是否为当前月 当前月就传当前所选择日期
        if (endDay.getMonth() + 1 === new Date().getMonth() + 1) {
          state.endDate = new Date();
        } else {
          // 非当前月传 所选月的最后一天
          endDay.setMonth(endDay.getMonth() + 1);
          endDay.setDate(0);
          state.endDate = endDay;
        }
      } else {
        state.endDate = value[1];
      }
      state.value = [state.startDate, state.endDate];
      onChange();
    };
    /**
     * @name: 初始化
     */
    const init = () => {
      setYearsList();
      switchType(props.timeType);
    };

    onMounted(() => {
      init();
    });

    return {
      ...toRefs(state),
      TIME_TYPE,
      switchType,
      switchYear,
      switchCustom
    };
  }
});
</script>

<style lang="scss" scoped>
.mk-time-range {
  .type-select {
    width: 130px;
  }
}
</style>
<style lang="scss">
.mk-time-range {
  .time-box {
    margin-left: 14px;
    .el-range-separator {
      padding: 0;
    }
  }
}
</style>

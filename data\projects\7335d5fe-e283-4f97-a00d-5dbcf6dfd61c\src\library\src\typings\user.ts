/**
 * @name 用户类型
*/
export interface IUserState {
  /** 用户id */
  userId: string;
  /** 用户名称 */
  userName: string;
  /** 手机号 */
  mobile: string;
  /** 邮箱 */
  email: string;
  /** 头像 */
  avatar: string;
  /** 真实名称 */
  realName: string;
  /** 性别 */
  sex: string;
  /** 用户类型 */
  userType: number;
  /** 管理员类型 */
  adminType: number;
  /** 学校id */
  schoolId: string;
  /** 学校名称 */
  schoolName: string;
  /** 学科id */
  subjectId: string;
  /** 学科名称 */
  subjectName: string;
  /** 年级 */
  phase: string;
  /** token */
  token: string;
  /** 火花token */
  huohuaToken?: string;
  /** 火花id */
  huohuaId: string;
  /** 用户角色 */
  roleList?: Array<String>;
  /** 用户学段 */
  userPhase: string;
  userInfoList: any[];
  /**c30在线用户token */
  Authorization?: string;
}

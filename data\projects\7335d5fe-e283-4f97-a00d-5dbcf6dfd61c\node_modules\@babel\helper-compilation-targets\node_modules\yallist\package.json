{"_args": [["yallist@3.1.1", "D:\\代码\\datedu-hw\\deploy-dev\\data\\projects\\7335d5fe-e283-4f97-a00d-5dbcf6dfd61c"]], "_development": true, "_from": "yallist@3.1.1", "_id": "yallist@3.1.1", "_inBundle": false, "_integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "_location": "/@babel/helper-compilation-targets/yallist", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "yallist@3.1.1", "name": "yallist", "escapedName": "yallist", "rawSpec": "3.1.1", "saveSpec": null, "fetchSpec": "3.1.1"}, "_requiredBy": ["/@babel/helper-compilation-targets/lru-cache"], "_resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "_spec": "3.1.1", "_where": "D:\\代码\\datedu-hw\\deploy-dev\\data\\projects\\7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blog.izs.me/"}, "bugs": {"url": "https://github.com/isaacs/yallist/issues"}, "dependencies": {}, "description": "Yet Another Linked List", "devDependencies": {"tap": "^12.1.0"}, "directories": {"test": "test"}, "files": ["yallist.js", "iterator.js"], "homepage": "https://github.com/isaacs/yallist#readme", "license": "ISC", "main": "yallist.js", "name": "yallist", "repository": {"type": "git", "url": "git+https://github.com/isaacs/yallist.git"}, "scripts": {"postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "test": "tap test/*.js --100"}, "version": "3.1.1"}
﻿import { getHost } from "@/library/src/utils/valiteSite";
import { PLATFORM_TYPE } from "@/library/src/utils/gloableEnum";

/**
 * 修改站点图标
 * @param {string} src 图标地址
 */
function setFavicon(src: string) {
  if( !src ){
      return
  }
  let oldLink = document.getElementById("dynamic-favicon");
  let link = document.createElement("link");
  link.id = "dynamic-favicon";
  link.rel = "shortcut icon";
  link.href = src;
  if (oldLink) {
    document.head.removeChild(oldLink);
  }
  document.head.appendChild(link);
}
/**
 * 修改站点名称
 * @param {string} src 站点名称
 */
function setTitle(title: string) {
  document.title = title;
}
/**
 * 修改主题色
 * @param {string} theme 主题色
 */
function setTheme(theme: string,themeColor?:string) {
  const target = window.document.documentElement
  target.setAttribute("data-theme", theme);
  // try {
  //   require(`@/library/css/theme/element-variables-yellow.scss`)
  // }catch (e) {
      require(`@/library/css/theme/element-variables.scss`)
  // }
  if( themeColor ){
    target.style.setProperty('--theme-color',themeColor)
    target.style.setProperty('--swiper-theme-color',themeColor)
  }


}
/**
 * 根据域名配置样式
 */
function setChannel() {
  let channel = process.env.VUE_APP_CHANNEL;
  let host = channel && channel !== "undefined" ? channel : getHost();
  host = host.replace("new", "");
  if (host == "qx") {
    host = "qxjyy";
  } else if (host == "sy") {
    host = "xzsy";
  }
  window.document.documentElement.setAttribute("data-channel", host);
}
/**
 * 根据平台类型配置样式
 */
function setPlatFormType(platformType: string = PLATFORM_TYPE.SCHOOL) {
  window.document.documentElement.setAttribute(
    "data-platformtype",
    platformType
  );
}

/**
 * 页面宽度
 */
function setMainWidth(width:number|string){
  const target = document.documentElement
  let mainWidth = width
  const winWidth = target.clientWidth - 10
  if( typeof width === "number"){
    let minWidth = winWidth >= mainWidth ? winWidth : mainWidth
    target.style.setProperty('--mainWidth',`${mainWidth}px`)
    target.style.setProperty('--minWidth',`${minWidth}px`)
  }else{
    let _mainWidth:any = (mainWidth+'').toLowerCase().replace(/%|px|rem|em|vw/,'')
    _mainWidth = parseInt(_mainWidth)
    let minWidth = winWidth >= _mainWidth ? winWidth : _mainWidth
    target.style.setProperty('--mainWidth',`${mainWidth}`)
    target.style.setProperty('--minWidth',`${minWidth}px`)
  }
}

export { setFavicon, setTitle, setTheme, setChannel, setPlatFormType,setMainWidth };

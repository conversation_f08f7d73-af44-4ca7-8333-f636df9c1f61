// VCS凭据管理JavaScript

document.addEventListener('DOMContentLoaded', function() {
  // 初始化模态框
  const addCredentialModal = new bootstrap.Modal(document.getElementById('addCredentialModal'));
  const testConnectionModal = new bootstrap.Modal(document.getElementById('testConnectionModal'));
  
  let currentTestCredentialId = null;

  // 页面加载时获取凭据列表
  loadCredentials();

  // 添加凭据按钮事件
  document.getElementById('add-credential-button').addEventListener('click', () => {
    resetCredentialForm();
    addCredentialModal.show();
  });

  // VCS类型选择变化事件
  document.getElementById('credential-type').addEventListener('change', function() {
    const emailField = document.getElementById('email-field');
    if (this.value === 'git') {
      emailField.style.display = 'block';
    } else {
      emailField.style.display = 'none';
    }
  });

  // 保存凭据按钮事件
  document.getElementById('save-credential-btn').addEventListener('click', saveCredential);

  // 测试连接按钮事件
  document.getElementById('test-connection-btn').addEventListener('click', testConnection);

  /**
   * 加载凭据列表
   */
  function loadCredentials() {
    fetch('/api/vcs-credentials')
      .then(response => response.json())
      .then(credentials => {
        displayCredentials(credentials);
      })
      .catch(error => {
        console.error('加载VCS凭据失败:', error);
        showAlert('加载VCS凭据失败', 'danger');
      });
  }

  /**
   * 显示凭据列表
   */
  function displayCredentials(credentials) {
    const container = document.getElementById('credentials-container');
    
    if (credentials.length === 0) {
      container.innerHTML = `
        <div class="text-center py-5">
          <i class="bi bi-key" style="font-size: 3rem; color: #6c757d;"></i>
          <h4 class="mt-3 text-muted">暂无VCS凭据</h4>
          <p class="text-muted">点击上方"添加凭据"按钮创建第一个VCS凭据</p>
        </div>
      `;
      return;
    }

    let html = '';
    credentials.forEach(credential => {
      const typeColor = credential.type === 'git' ? 'success' : 'info';
      const typeIcon = credential.type === 'git' ? 'git' : 'archive';
      
      html += `
        <div class="credential-card">
          <div class="d-flex justify-content-between align-items-start">
            <div class="flex-grow-1">
              <div class="d-flex align-items-center mb-2">
                <i class="bi bi-${typeIcon} me-2"></i>
                <h5 class="mb-0">${credential.name}</h5>
                <span class="badge bg-${typeColor} credential-type-badge ms-2">${credential.type.toUpperCase()}</span>
              </div>
              <p class="text-muted mb-1">
                <i class="bi bi-person me-1"></i>
                用户名: ${credential.username}
              </p>
              ${credential.email ? `
                <p class="text-muted mb-1">
                  <i class="bi bi-envelope me-1"></i>
                  邮箱: ${credential.email}
                </p>
              ` : ''}
              ${credential.description ? `
                <p class="text-muted mb-1">
                  <i class="bi bi-info-circle me-1"></i>
                  ${credential.description}
                </p>
              ` : ''}
              <small class="text-muted">
                创建时间: ${new Date(credential.createdAt).toLocaleString()}
              </small>
            </div>
            <div class="btn-group" role="group">
              <button class="btn btn-sm btn-outline-primary" onclick="testCredential('${credential.id}', '${credential.name}')">
                <i class="bi bi-wifi"></i> 测试
              </button>
              <button class="btn btn-sm btn-outline-secondary" onclick="editCredential('${credential.id}')">
                <i class="bi bi-pencil"></i> 编辑
              </button>
              <button class="btn btn-sm btn-outline-danger" onclick="deleteCredential('${credential.id}', '${credential.name}')">
                <i class="bi bi-trash"></i> 删除
              </button>
            </div>
          </div>
        </div>
      `;
    });

    container.innerHTML = html;
  }

  /**
   * 重置凭据表单
   */
  function resetCredentialForm() {
    document.getElementById('add-credential-form').reset();
    document.getElementById('addCredentialModalLabel').textContent = '添加VCS凭据';
    document.getElementById('save-credential-btn').textContent = '保存凭据';
    document.getElementById('save-credential-btn').removeAttribute('data-credential-id');
    document.getElementById('email-field').style.display = 'none';
  }

  /**
   * 保存凭据
   */
  function saveCredential() {
    const name = document.getElementById('credential-name').value;
    const type = document.getElementById('credential-type').value;
    const username = document.getElementById('credential-username').value;
    const password = document.getElementById('credential-password').value;
    const email = document.getElementById('credential-email').value;
    const description = document.getElementById('credential-description').value;
    const credentialId = document.getElementById('save-credential-btn').getAttribute('data-credential-id');

    if (!name || !type || !username) {
      showAlert('凭据名称、类型和用户名为必填项', 'danger');
      return;
    }

    // 编辑模式下密码可以为空
    if (!credentialId && !password) {
      showAlert('创建凭据时密码为必填项', 'danger');
      return;
    }

    const isEdit = !!credentialId;
    const url = isEdit ? `/api/vcs-credentials/${credentialId}` : '/api/vcs-credentials';
    const method = isEdit ? 'PUT' : 'POST';

    const requestData = {
      name,
      type,
      username,
      email,
      description
    };

    // 只有在密码不为空时才包含密码字段
    if (password) {
      requestData.password = password;
    }

    fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })
      .then(response => response.json())
      .then(data => {
        if (data.error) {
          showAlert(data.error, 'danger');
          return;
        }
        
        addCredentialModal.hide();
        loadCredentials();
        showAlert(isEdit ? 'VCS凭据更新成功' : 'VCS凭据创建成功', 'success');
      })
      .catch(error => {
        console.error(`${isEdit ? '更新' : '创建'}VCS凭据失败:`, error);
        showAlert(`${isEdit ? '更新' : '创建'}VCS凭据失败`, 'danger');
      });
  }

  /**
   * 编辑凭据
   */
  window.editCredential = function(credentialId) {
    fetch(`/api/vcs-credentials/${credentialId}`)
      .then(response => response.json())
      .then(credential => {
        if (credential.error) {
          showAlert(credential.error, 'danger');
          return;
        }

        // 填充表单
        document.getElementById('credential-name').value = credential.name;
        document.getElementById('credential-type').value = credential.type;
        document.getElementById('credential-username').value = credential.username;
        document.getElementById('credential-password').value = ''; // 出于安全考虑不显示密码
        document.getElementById('credential-email').value = credential.email || '';
        document.getElementById('credential-description').value = credential.description || '';

        // 显示邮箱字段（如果是Git）
        if (credential.type === 'git') {
          document.getElementById('email-field').style.display = 'block';
        }

        // 设置编辑模式
        document.getElementById('addCredentialModalLabel').textContent = '编辑VCS凭据';
        document.getElementById('save-credential-btn').textContent = '更新凭据';
        document.getElementById('save-credential-btn').setAttribute('data-credential-id', credentialId);

        addCredentialModal.show();
      })
      .catch(error => {
        console.error('获取VCS凭据详情失败:', error);
        showAlert('获取VCS凭据详情失败', 'danger');
      });
  };

  /**
   * 删除凭据
   */
  window.deleteCredential = function(credentialId, credentialName) {
    if (confirm(`确定要删除凭据"${credentialName}"吗？此操作不可撤销。`)) {
      fetch(`/api/vcs-credentials/${credentialId}`, {
        method: 'DELETE'
      })
        .then(response => response.json())
        .then(data => {
          if (data.error) {
            showAlert(data.error, 'danger');
            return;
          }
          
          loadCredentials();
          showAlert('VCS凭据删除成功', 'success');
        })
        .catch(error => {
          console.error('删除VCS凭据失败:', error);
          showAlert('删除VCS凭据失败', 'danger');
        });
    }
  };

  /**
   * 测试凭据连接
   */
  window.testCredential = function(credentialId, credentialName) {
    currentTestCredentialId = credentialId;
    document.getElementById('testConnectionModalLabel').textContent = `测试连接 - ${credentialName}`;
    document.getElementById('test-repository-url').value = '';
    document.getElementById('test-result').style.display = 'none';
    testConnectionModal.show();
  };

  /**
   * 执行连接测试
   */
  function testConnection() {
    const repositoryUrl = document.getElementById('test-repository-url').value;
    const resultDiv = document.getElementById('test-result');

    if (!repositoryUrl) {
      showAlert('请输入仓库URL', 'danger');
      return;
    }

    if (!currentTestCredentialId) {
      showAlert('未选择测试凭据', 'danger');
      return;
    }

    // 显示加载状态
    resultDiv.innerHTML = `
      <div class="alert alert-info">
        <i class="bi bi-hourglass-split me-2"></i>
        正在测试连接...
      </div>
    `;
    resultDiv.style.display = 'block';

    fetch(`/api/vcs-credentials/${currentTestCredentialId}/test`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ repositoryUrl })
    })
      .then(response => response.json())
      .then(data => {
        if (data.success) {
          resultDiv.innerHTML = `
            <div class="alert alert-success">
              <i class="bi bi-check-circle me-2"></i>
              ${data.message}
            </div>
          `;
        } else {
          resultDiv.innerHTML = `
            <div class="alert alert-danger">
              <i class="bi bi-x-circle me-2"></i>
              连接测试失败: ${data.error || '未知错误'}
            </div>
          `;
        }
      })
      .catch(error => {
        console.error('测试VCS连接失败:', error);
        resultDiv.innerHTML = `
          <div class="alert alert-danger">
            <i class="bi bi-x-circle me-2"></i>
            连接测试失败: 网络错误
          </div>
        `;
      });
  }

  /**
   * 显示提示信息
   */
  function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    alertDiv.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动移除
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.parentNode.removeChild(alertDiv);
      }
    }, 3000);
  }
});

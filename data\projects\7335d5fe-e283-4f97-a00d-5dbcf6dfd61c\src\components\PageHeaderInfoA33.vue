<template>
  <div style="
      font-size: 4.8mm;
      line-height: 4mm;
      font-family: 宋体, <PERSON>m<PERSON><PERSON>, Times New Roman;
    ">
    <div :class="[Paper.isEdit ? 'uneditstyle' : '']">
      <div :id="'name-txt' + page" class="name-txt" :style="{
          fontSize: nameSize + ' !important',
          fontWeight: nameFont ? 'bold !important' : 'unset !important',
          maxHeight: '12mm !important',
          lineHeight: lineHeight + ' !important',
        }" contenteditable="true" v-html="Paper.nameHtml.html"></div>
      <div class="nameset nosave">
        <el-checkbox v-model="nameFont" label="加粗" size="small" />
        <el-select v-model="nameSize" style="width: 80px" :teleported="false" placeholder="Select" size="small">
          <el-option v-for="item in fontSizeList" :key="item.value" :label="item.text" :value="item.value" />
        </el-select>
      </div>
    </div>
    <div class="header-table">
      <template v-if="
          Paper.numberLayout == INUMBERLAYOUT.BARCODE || Paper.stuNoLength < 11
        ">
        <div class="headerinfo" style="width: 65mm; margin-right: 5mm" :style="{
            paddingTop: Paper.correctType == ICorrectType.WEB ? 0 : '5mm',
          }">
          <div class="header-info" style="width: fit-content; margin: auto">
            <div :id="'qrcode' + page" class="qrcode" :page="page"
              style="width: 20mm; height: 20mm; display: inline-block">
              <img class="nosave" v-if="QRCodeUrl" :src="QRCodeUrl" style="width: 20mm; height: 20mm" />
            </div>
            <div v-if="!Paper.isSealLine" class="stu-info-wrap m-b-4" name="sealLine">
              <div style="display: inline-block">
                <span style="width: 10mm">班级:</span>
                <div class="card-color-info"></div>
              </div>
              <div id="stu-name" style="display: inline-block; margin-top: 6mm" :style="{
                  marginTop:
                    Paper.correctType == ICorrectType.WEB ? '5mm' : '10mm',
                }">
                <span style="width: 10mm">姓名:</span>
                <div class="card-color-info"></div>
              </div>
              <div style="display: inline-block; margin-top: 6mm" v-if="Paper.correctType == ICorrectType.WEB">
                <span style="width: 10mm">考场号:</span>
                <div class="card-color-info"></div>
              </div>
              <div style="display: inline-block; margin-top: 6mm" v-if="Paper.correctType == ICorrectType.WEB">
                <span style="width: 10mm">座位号:</span>
                <div class="card-color-info"></div>
              </div>
            </div>
          </div>
          <div id="notice" v-if="Paper.numberLayout == INUMBERLAYOUT.TICKET">
            <NoticeVue></NoticeVue>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="headerinfo">
          <div :class="[
              'header-info',
              'long-stu-no',
              Paper.correctType == ICorrectType.HAND ? 'hand' : '',
            ]" style="width: fit-content; margin: auto">
            <div v-if="!Paper.isSealLine" class="stu-info-wrap m-b-4" name="sealLine">
              <div style="display: inline-block">
                <span style="width: 10mm">班级:</span>
                <div class="card-color-info"></div>
              </div>
              <div style="display: inline-block; margin-top: 5mm">
                <span style="width: 10mm">姓名:</span>
                <div class="card-color-info"></div>
              </div>
              <div style="display: inline-block; margin-top: 5mm" v-if="Paper.correctType == ICorrectType.WEB">
                <span style="width: 10mm">考场:</span>
                <div class="card-color-info"></div>
              </div>
            </div>
            <div id="notice" v-if="Paper.numberLayout == INUMBERLAYOUT.TICKET">
              <NoticeVue :show-right="false"></NoticeVue>
            </div>
            <div :id="'qrcode' + page" class="qrcode" :page="page"
              style="width: 20mm; height: 20mm; display: inline-block">
              <img class="nosave" v-if="QRCodeUrl" :src="QRCodeUrl" style="width: 20mm; height: 20mm" />
            </div>
          </div>
        </div>
      </template>
      <div class="headerinfo">
        <StuNumberFillCard number-block="bracket" v-if="Paper.numberLayout == INUMBERLAYOUT.TICKET"/>
        
        <div class="barcode-wrapper" v-if="Paper.numberLayout == INUMBERLAYOUT.BARCODE">
          <div class="stu-bar-code-container" id="stu-no-three-tr">
            <div class="stu-bar-code">
              <div class="text text--top"><span>贴条形码区</span></div>
              <div class="text text--bottom">
                <span>(正面朝上，切勿贴出虚线方框)</span>
              </div>
            </div>
          </div>
          <div id="notice">
            <NoticeVue class="full-wrap"></NoticeVue>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  ref,
  nextTick,
  watch,
} from "vue";
import Paper from "../views/paper";
import QRCode from "qrcode";
import { INUMBERLAYOUT, ICorrectType } from "@/typings/card";
import NoticeVue from "./Notice.vue";
import StuNumberFillCard from "./stuNumberFillCard.vue";

export default defineComponent({
  props: {
    id: {
      type: String,
      default: "",
    },
    page: {
      type: Number,
      default: 1,
    },
  },
  components: {
    NoticeVue,
    StuNumberFillCard,
  },
  setup(props: any, ctx: any) {
    const state = reactive({
      Paper: Paper,
      INUMBERLAYOUT,
      ICorrectType,
      qrValue: "",
      QRCodeUrl: "",
      //字号集合
      fontSizeList: [
        {
          id: 1,
          text: "小五",
          value: "3.2mm",
        },
        {
          id: 2,
          text: "五号",
          value: "3.5mm",
        },
        {
          id: 3,
          text: "小四",
          value: "3.7mm",
        },
        {
          id: 4,
          text: "四号",
          value: "4mm",
        },
        {
          id: 5,
          text: "小三",
          value: "4.2mm",
        },
        {
          id: 6,
          text: "三号",
          value: "4.4mm",
        },
        {
          id: 7,
          text: "小二",
          value: "4.8mm",
        },
      ],
      nameSize: "4.8mm",
      nameFont: true,
      lineHeight: "4.8mm",
    });
    watch(
      () => [state.nameSize, state.nameFont],
      () => {
        Paper.nameHtml.fontBold = state.nameFont;
        Paper.nameHtml.size = state.nameSize;
        if (Number(state.nameSize.replace("mm", "")) > 4) {
          state.lineHeight = "4.8mm";
        } else {
          state.lineHeight = "4mm";
        }
      }
    );

    /**
     * 页面一开始加载
     */
    onMounted(async () => {
      state.nameFont = Paper.nameHtml.fontBold;
      state.nameSize = Paper.nameHtml.size;
      if (Number(state.nameSize.replace("mm", "")) > 4) {
        state.lineHeight = "4.8mm";
      } else {
        state.lineHeight = "4mm";
      }
      state.qrValue = Paper.paperId + props.page.toString().padStart(2, "0");
      let opts = {
        errorCorrectionLevel: "M",
        type: "image/jpeg",
        width: "20mm",
        height: "20mm",
        quality: 0.8,
        margin: 0,
        color: {
          dark: "#020202",
          light: "#fff",
        },
      };
      state.QRCodeUrl = await QRCode.toDataURL(state.qrValue, opts);
      nextTick(() => {
        const contenteditableDiv = document.getElementById(
          "name-txt" + props.page.toString()
        );
        contenteditableDiv?.addEventListener("paste", function (e: any) {
          e.preventDefault();
          document.execCommand(
            "insertHTML",
            false,
            e?.clipboardData.getData("text")
          );
        });
        contenteditableDiv?.addEventListener("blur", function (e: any) {
          e.target.innerHTML = e.target.innerHTML.replace(
            /(style)="[^"]*"/g,
            ""
          );
          Paper.name = e.target.textContent;
          Paper.nameHtml = {
            html: e.target.innerHTML,
            size: state.nameSize,
            fontBold: state.nameFont,
          };
          Array.from(document.getElementsByClassName("name-txt")).forEach(
            (el: any) => {
              // el.innerText = Paper.name;
              el.innerHTML = Paper.nameHtml.html;
            }
          );
          // $(".name-txt").text(Paper.name)
        });
      });
    });
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang="scss" scoped>
.header {
  padding: 0;
  margin: 0;
  margin-bottom: 1mm;
  border-top: 0.1mm solid #fff;
  border-bottom: 0.1mm solid #000;
  position: relative;
  width: 100%;
  font-family: 宋体, SimSun, Times New Roman;
}

.header-table {
  width: 100%;
  // height: 57mm;
  display: flex;

  .headerinfo {
    display: inline-block;
    position: relative;
  }

  tr {
    width: 100%;
  }

  td {
    vertical-align: inherit;
  }

  .header-info {
    display: flex;
    margin-top: 2mm;
    align-items: center;
    .card-color-info {
      display: inline-block;
      width: 18mm;
      border-bottom: 0.1mm solid;
    }
    &.long-stu-no {
      display: block;
      align-items: start;
      margin-top: 0mm;
      &.hand {
        margin-top: 5mm;
        .stu-info-wrap {
          margin-bottom: 5mm;
        }
      }
      .stu-info-wrap {
        max-width: 30mm;
        text-align: left;
        padding-top: 0mm;
      }
      .card-color-info {
        width: 15mm;
      }
      #notice {
        .full-wrap {
          width: 26mm;
          margin-top: 1mm;
          margin-bottom: 1mm;
        }
      }
    }
  }
}

.header-name {
  margin: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 100%;
  -ms-flex-pack: distribute;
  justify-content: space-around;
  max-height: 21mm;
  width: 100%;
}

.name-txt {
  // display: block;
  font-size: 4.3mm;
  max-width: 100%;
  margin: 0 auto;
  font-weight: 700;
  word-break: break-all;
  text-align: center;
  overflow: hidden;
  -webkit-line-clamp: 1;
  line-height: 6.5mm;
  min-height: 6.5mm;
  font-family: 宋体, SimSun, Times New Roman;

  &:hover {
    outline: 1px solid #000;
  }

  &:hover ~ .nameset {
    visibility: inherit;
  }
}

.nameset {
  visibility: hidden;
  position: absolute;
  top: 13px;
  right: 0;

  &:hover {
    visibility: inherit;
  }
}

.flex-end {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.flex-center {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.barcode-wrapper {
  width: 65mm;
}

.stu-bar-code-container {
  width: 100%;
  height: 30mm;
  padding: 5mm;
  border: 1px dashed #000;
}

.stu-bar-code {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
  height: 100%;
  background-color: #e4e4e4;

  .text {
    color: #a5a5a5;
  }

  .text--top {
    font-size: 20px;
  }

  .text--bottom {
    font-size: 12px;
  }
}

.td-flex {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.table-left-td {
  height: 100%;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.table-notice-div {
  width: 100%;
}

.table-left-td,
.table-notice-div {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.stu-info-wrap {
  padding-top: 2mm;
  text-align: center;
  position: relative;
  font-size: 3.5mm !important;
}

.notice-info {
  display: inline-block;
  width: 100%;
  max-width: 90mm;
  text-align: left;
  border: 0.1mm solid #000;
  line-height: 4mm;
  padding: 0;
  word-break: break-all;
}

.notice-title {
  text-align: center;
  font-size: 3.5mm;
  margin: 0;
  padding: 0;
  color: #000;
}

.notice-infop {
  margin: 0 1mm;
  padding: 0;
  color: #000;
}

.card-color {
  color: #000;
}

.full-wrap {
  margin-top: 5mm;
  max-width: 90mm;
  width: 100%;
  line-height: 7mm;
  font-size: 3mm !important;
  border: 0.1mm solid #000;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.full-item {
  vertical-align: middle;

  span {
    margin-right: 1mm;
  }
}

.full-sec {
  display: inline-block;
  border-width: 1.5mm 2.5mm;
  border-style: solid;
  border-color: #000;
  width: 0;
  height: 0;
}

.full-example {
  margin: 0;
  padding: 0;
  width: 5mm;
  height: 3mm;
  border: 0.1mm solid #000;
  font-size: 5mm;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}
</style>
<style lang="scss">
.headerinfo {
  .el-input__inner {
    height: 30px !important;
    line-height: 30px !important;
    // padding: 1.2mm 1mm !important;
  }
}

.ques-list {
  .el-input__inner {
    // text-align: center;
  }
}

.preview .qrcode {
  background: url(../assets/qrcode.png) 100% 100%;
  background-size: cover;
  // background: url('../assets/qrcode.png') no-repeat;
}
</style>

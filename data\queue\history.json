{"history": [{"id": "30f24d9a-3569-4520-8303-14adf1ce51ef", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "success", "queuedAt": "2025-07-19T01:12:18.454Z", "startedAt": "2025-07-19T01:12:18.457Z", "completedAt": "2025-07-19T01:13:13.146Z", "logs": [{"time": "2025-07-19T01:12:18.457Z", "message": "开始构建"}, {"time": "2025-07-19T01:13:13.146Z", "message": "构建成功"}]}, {"id": "f1a0e700-6b66-422c-be26-ffde38185bad", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "success", "queuedAt": "2025-07-19T01:02:25.919Z", "startedAt": "2025-07-19T01:02:25.924Z", "completedAt": "2025-07-19T01:03:59.635Z", "logs": [{"time": "2025-07-19T01:02:25.924Z", "message": "开始构建"}, {"time": "2025-07-19T01:03:59.635Z", "message": "构建成功"}]}, {"id": "d3ba764a-c2bd-4f7c-a5c2-7cb31d1de121", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "failed", "queuedAt": "2025-07-19T00:54:24.130Z", "startedAt": "2025-07-19T00:54:24.135Z", "completedAt": "2025-07-19T00:57:18.316Z", "logs": [{"time": "2025-07-19T00:54:24.135Z", "message": "开始构建"}, {"time": "2025-07-19T00:57:18.316Z", "message": "构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752886636849.bat\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nnpm error Missing script: \"build:test\"\nnpm error\nnpm error To see a list of scripts, run:\nnpm error   npm run\n\nnpm error A complete log of this run can be found in: C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-19T00_57_18_035Z-debug-0.log\n"}]}, {"id": "8954f111-915a-4c19-9344-d56940049b83", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "failed", "queuedAt": "2025-07-16T05:52:06.716Z", "startedAt": "2025-07-16T05:52:06.724Z", "completedAt": "2025-07-16T05:52:16.860Z", "logs": [{"time": "2025-07-16T05:52:06.724Z", "message": "开始构建"}, {"time": "2025-07-16T05:52:16.860Z", "message": "构建失败: 部署失败: 安装依赖失败: 安装依赖失败: Command failed: cmd.exe /c D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\npm_install_1752645136714.bat\n'nvs' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n"}]}, {"id": "2151b12a-11a5-4b7f-8551-740db731b92c", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "failed", "queuedAt": "2025-07-16T03:49:46.696Z", "startedAt": "2025-07-16T03:49:46.704Z", "completedAt": "2025-07-16T03:50:24.778Z", "logs": [{"time": "2025-07-16T03:49:46.704Z", "message": "开始构建"}, {"time": "2025-07-16T03:50:24.778Z", "message": "构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752637811569.bat\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nDebugger attached.\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T03_50_16_595Z-debug-0.log\nWaiting for the debugger to disconnect...\r\n"}]}, {"id": "66194889-625a-4f37-99c0-ed35ded68a76", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "failed", "queuedAt": "2025-07-16T03:24:37.647Z", "startedAt": "2025-07-16T03:24:37.650Z", "completedAt": "2025-07-16T03:26:18.007Z", "logs": [{"time": "2025-07-16T03:24:37.650Z", "message": "开始构建"}, {"time": "2025-07-16T03:26:18.007Z", "message": "构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c \"D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752636370057.bat\"\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nDebugger attached.\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T03_26_15_439Z-debug-0.log\nWaiting for the debugger to disconnect...\r\n"}]}, {"id": "fc9e50c1-907c-4ef8-a6a1-fb9968136574", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "failed", "queuedAt": "2025-07-16T03:21:36.131Z", "startedAt": "2025-07-16T03:21:36.138Z", "completedAt": "2025-07-16T03:22:07.782Z", "logs": [{"time": "2025-07-16T03:21:36.138Z", "message": "开始构建"}, {"time": "2025-07-16T03:22:07.782Z", "message": "构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c \"D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752636123612.bat\"\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nDebugger attached.\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T03_22_06_362Z-debug-0.log\nWaiting for the debugger to disconnect...\r\n"}]}, {"id": "398f6f53-866f-42ee-8c2d-dc11a2e6a583", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "failed", "queuedAt": "2025-07-16T03:19:39.461Z", "startedAt": "2025-07-16T03:19:39.465Z", "completedAt": "2025-07-16T03:19:53.995Z", "logs": [{"time": "2025-07-16T03:19:39.465Z", "message": "开始构建"}, {"time": "2025-07-16T03:19:53.995Z", "message": "构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c \"D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752635992036.bat\"\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T03_19_53_915Z-debug-0.log\n"}]}, {"id": "79eed6a4-11fa-4c74-b898-178e67efed17", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "failed", "queuedAt": "2025-07-16T02:17:19.891Z", "startedAt": "2025-07-16T02:17:19.897Z", "completedAt": "2025-07-16T02:20:27.139Z", "logs": [{"time": "2025-07-16T02:17:19.897Z", "message": "开始构建"}, {"time": "2025-07-16T02:20:27.139Z", "message": "构建失败: 部署失败: 构建项目失败: Command failed: D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752632261897.bat\n'l' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nDebugger attached.\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T02_17_51_908Z-debug-0.log\nWaiting for the debugger to disconnect...\r\n"}]}, {"id": "d870babf-d66e-481d-8105-2afa0ab68911", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "success", "queuedAt": "2025-07-16T02:12:52.051Z", "startedAt": "2025-07-16T02:12:52.055Z", "completedAt": "2025-07-16T02:14:53.245Z", "logs": [{"time": "2025-07-16T02:12:52.055Z", "message": "开始构建"}, {"time": "2025-07-16T02:14:53.245Z", "message": "构建成功"}]}, {"id": "5d20f0ee-ea38-4a10-ba3e-e588a0c934a1", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "success", "queuedAt": "2025-07-16T02:09:11.704Z", "startedAt": "2025-07-16T02:09:11.710Z", "completedAt": "2025-07-16T02:10:06.943Z", "logs": [{"time": "2025-07-16T02:09:11.710Z", "message": "开始构建"}, {"time": "2025-07-16T02:10:06.943Z", "message": "构建成功"}]}, {"id": "63ccc00e-0c43-40da-a7cf-3bf926f42b3c", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "success", "queuedAt": "2025-07-16T02:02:26.926Z", "startedAt": "2025-07-16T02:02:26.933Z", "completedAt": "2025-07-16T02:03:58.473Z", "logs": [{"time": "2025-07-16T02:02:26.933Z", "message": "开始构建"}, {"time": "2025-07-16T02:03:58.473Z", "message": "构建成功"}]}, {"id": "47cce972-33ab-413a-bedf-8917ae57ae40", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "success", "queuedAt": "2025-07-16T01:50:40.630Z", "startedAt": "2025-07-16T01:50:40.661Z", "completedAt": "2025-07-16T01:51:28.598Z", "logs": [{"time": "2025-07-16T01:50:40.661Z", "message": "开始构建"}, {"time": "2025-07-16T01:51:28.598Z", "message": "构建成功"}]}, {"id": "e09f5b21-1eb2-4228-8c20-9dbe07b972c5", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "success", "queuedAt": "2025-07-16T01:48:17.702Z", "startedAt": "2025-07-16T01:48:17.709Z", "completedAt": "2025-07-16T01:50:42.347Z", "logs": [{"time": "2025-07-16T01:48:17.709Z", "message": "开始构建"}, {"time": "2025-07-16T01:50:42.347Z", "message": "构建成功"}]}, {"id": "f55a5cb6-2a42-47bc-8184-b0040bed4960", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "de03a7df-e7cb-4c43-9294-ad309433a870", "deployPath": "/var/datedu/web/makeCard/"}], "status": "success", "queuedAt": "2025-07-16T01:41:44.177Z", "startedAt": "2025-07-16T01:41:44.183Z", "completedAt": "2025-07-16T01:45:39.952Z", "logs": [{"time": "2025-07-16T01:41:44.183Z", "message": "开始构建"}, {"time": "2025-07-16T01:45:39.952Z", "message": "构建成功"}]}, {"id": "b0af17a9-df24-4e27-b4b1-d7a52db7d98f", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": ["de03a7df-e7cb-4c43-9294-ad309433a870"], "status": "success", "queuedAt": "2025-07-16T01:39:28.798Z", "startedAt": "2025-07-16T01:39:28.803Z", "completedAt": "2025-07-16T01:39:41.318Z", "logs": [{"time": "2025-07-16T01:39:28.803Z", "message": "开始构建"}, {"time": "2025-07-16T01:39:41.318Z", "message": "构建成功"}]}, {"id": "2dac8c32-2128-408f-b7c8-1702d837462b", "projectId": "7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "projectName": "制卡", "branch": "main", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "b79d6eee-17ab-45c2-b8d5-1a6a6da567c9", "deployPath": "/var/datedu/web/makeCard/"}], "status": "success", "queuedAt": "2025-07-16T01:05:44.494Z", "startedAt": "2025-07-16T01:05:44.496Z", "completedAt": "2025-07-16T01:05:58.657Z", "logs": [{"time": "2025-07-16T01:05:44.496Z", "message": "开始构建"}, {"time": "2025-07-16T01:05:58.657Z", "message": "构建成功"}]}, {"id": "6839dafe-d7b5-4884-b8b6-816a8db20184", "projectId": "87289dd2-e559-4fc3-90ee-affb81755879", "projectName": "bigdata", "branch": "master", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "b79d6eee-17ab-45c2-b8d5-1a6a6da567c9", "deployPath": "/var/datedu/web/big-data/"}], "status": "success", "queuedAt": "2025-07-16T00:59:41.484Z", "startedAt": "2025-07-16T00:59:41.488Z", "completedAt": "2025-07-16T00:59:55.325Z", "logs": [{"time": "2025-07-16T00:59:41.488Z", "message": "开始构建"}, {"time": "2025-07-16T00:59:55.325Z", "message": "构建成功"}]}, {"id": "ad664aba-0172-4e9f-916f-e6d03cc8ec19", "projectId": "87289dd2-e559-4fc3-90ee-affb81755879", "projectName": "bigdata", "branch": "master", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "b79d6eee-17ab-45c2-b8d5-1a6a6da567c9", "deployPath": "/var/datedu/web/big-data/"}], "status": "failed", "queuedAt": "2025-07-16T00:58:06.091Z", "startedAt": "2025-07-16T00:58:06.102Z", "completedAt": "2025-07-16T00:58:19.552Z", "logs": [{"time": "2025-07-16T00:58:06.102Z", "message": "开始构建"}, {"time": "2025-07-16T00:58:19.552Z", "message": "构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c \"D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752627497894.bat\"\n'use' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T00_58_19_476Z-debug-0.log\n"}]}, {"id": "2d1150d0-c4c7-4122-97c4-14059b674cbd", "projectId": "87289dd2-e559-4fc3-90ee-affb81755879", "projectName": "bigdata", "branch": "master", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "b79d6eee-17ab-45c2-b8d5-1a6a6da567c9", "deployPath": "/var/datedu/web/big-data/"}], "status": "failed", "queuedAt": "2025-07-16T00:56:30.177Z", "startedAt": "2025-07-16T00:56:30.184Z", "completedAt": "2025-07-16T00:56:53.412Z", "logs": [{"time": "2025-07-16T00:56:30.184Z", "message": "开始构建"}, {"time": "2025-07-16T00:56:53.412Z", "message": "构建失败: 部署失败: 构建项目失败: Cannot read properties of undefined (reading 'replace')"}]}, {"id": "5e3bc6de-8c54-430f-8329-b9a67a6a3bf9", "projectId": "87289dd2-e559-4fc3-90ee-affb81755879", "projectName": "bigdata", "branch": "master", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "b79d6eee-17ab-45c2-b8d5-1a6a6da567c9", "deployPath": "/var/datedu/web/big-data/"}], "status": "failed", "queuedAt": "2025-07-16T00:55:17.494Z", "startedAt": "2025-07-16T00:55:17.498Z", "completedAt": "2025-07-16T00:55:27.540Z", "logs": [{"time": "2025-07-16T00:55:17.498Z", "message": "开始构建"}, {"time": "2025-07-16T00:55:27.540Z", "message": "构建失败: 部署失败: 构建项目失败: Cannot read properties of undefined (reading 'replace')"}]}, {"id": "8b6496fb-0a71-45d3-8b5e-c416512f9ddb", "projectId": "87289dd2-e559-4fc3-90ee-affb81755879", "projectName": "bigdata", "branch": "master", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "b79d6eee-17ab-45c2-b8d5-1a6a6da567c9", "deployPath": "/var/datedu/web/big-data/"}], "status": "failed", "queuedAt": "2025-07-16T00:47:00.477Z", "startedAt": "2025-07-16T00:47:00.482Z", "completedAt": "2025-07-16T00:49:42.877Z", "logs": [{"time": "2025-07-16T00:47:00.482Z", "message": "开始构建"}, {"time": "2025-07-16T00:49:42.877Z", "message": "构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c \"D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752626906969.bat\"\n'use' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nDebugger attached.\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T00_49_09_315Z-debug-0.log\nWaiting for the debugger to disconnect...\r\n"}]}, {"id": "b1f8c6a9-7383-418a-aabd-22187147406c", "projectId": "87289dd2-e559-4fc3-90ee-affb81755879", "projectName": "bigdata", "branch": "master", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "b79d6eee-17ab-45c2-b8d5-1a6a6da567c9", "deployPath": "/var/datedu/web/big-data/"}], "status": "failed", "queuedAt": "2025-07-16T00:39:35.175Z", "startedAt": "2025-07-16T00:39:35.179Z", "completedAt": "2025-07-16T00:39:48.277Z", "logs": [{"time": "2025-07-16T00:39:35.179Z", "message": "开始构建"}, {"time": "2025-07-16T00:39:48.276Z", "message": "构建失败: 部署失败: 构建项目失败: Command failed: cmd.exe /c \"D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\build_command_1752626386696.bat\"\n'use' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n'/d' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\nnpm ERR! Missing script: \"build:test\"\nnpm ERR! \nnpm ERR! To see a list of scripts, run:\nnpm ERR!   npm run\n\nnpm ERR! A complete log of this run can be found in:\nnpm ERR!     C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-16T00_39_48_212Z-debug-0.log\n"}]}, {"id": "a94c61ed-acfb-4d35-83e7-df3e59055d14", "projectId": "87289dd2-e559-4fc3-90ee-affb81755879", "projectName": "bigdata", "branch": "master", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "b79d6eee-17ab-45c2-b8d5-1a6a6da567c9", "deployPath": "/var/datedu/web/big-data/"}], "status": "failed", "queuedAt": "2025-07-16T00:29:38.783Z", "startedAt": "2025-07-16T00:29:38.788Z", "completedAt": "2025-07-16T00:29:44.004Z", "logs": [{"time": "2025-07-16T00:29:38.788Z", "message": "开始构建"}, {"time": "2025-07-16T00:29:44.004Z", "message": "构建失败: 部署失败: 切换Node.js版本失败: Command failed: cmd.exe /c \"D:\\代码\\datedu-hw\\deploy-dev\\temp\\scripts\\nvs_switch_1752625783874.bat\"\n'nvs' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n"}]}, {"id": "5e52a45a-20a3-4509-b5e8-db9fb58ebfb6", "projectId": "87289dd2-e559-4fc3-90ee-affb81755879", "projectName": "bigdata", "branch": "master", "userId": "621922ab-5770-4a8d-a03c-ac521e06d803", "username": "liu<PERSON>e", "servers": [{"serverId": "b79d6eee-17ab-45c2-b8d5-1a6a6da567c9", "deployPath": "/var/datedu/web/big-data/"}], "status": "failed", "queuedAt": "2025-07-16T00:26:19.576Z", "startedAt": "2025-07-16T00:26:19.581Z", "completedAt": "2025-07-16T00:26:25.052Z", "logs": [{"time": "2025-07-16T00:26:19.581Z", "message": "开始构建"}, {"time": "2025-07-16T00:26:25.052Z", "message": "构建失败: 部署失败: 切换Node.js版本失败: 安装Node.js版本失败: Command failed: nvs add 14.17.0\n'nvs' �����ڲ����ⲿ���Ҳ���ǿ����еĳ���\r\n���������ļ���\r\n"}]}]}
/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image2', 'lt', {
	alt: 'Alternatyvus Tekstas',
	btnUpload: 'Siųsti į serverį',
	captioned: 'Captioned image', // MISSING
	captionPlaceholder: 'Caption', // MISSING
	infoTab: 'Vaizdo informacija',
	lockRatio: 'I<PERSON><PERSON><PERSON>ti proporciją',
	menu: 'Vaizdo savybės',
	pathName: 'image', // MISSING
	pathNameCaption: 'caption', // MISSING
	resetSize: 'Atstatyti dydį',
	resizer: 'Click and drag to resize', // MISSING
	title: 'Vaizdo savybės',
	uploadTab: 'Si<PERSON>sti',
	urlMissing: 'Paveiksliuko nuorod<PERSON> nėra.',
	altMissing: 'Alternative text is missing.' // MISSING
} );

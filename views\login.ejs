<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>前端部署工具 - 登录</title>
  <link href="/bootstrap/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/bootstrap/bootstrap-icons.css">
  <link rel="stylesheet" href="/css/style.css">
  <style>
    body {
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      padding-top: 40px;
      padding-bottom: 40px;
      height: 100vh;
    }
    .form-signin {
      width: 100%;
      max-width: 400px;
      padding: 15px;
      margin: auto;
    }
    .form-signin .form-floating:focus-within {
      z-index: 2;
    }
    .form-signin input[type="text"] {
      margin-bottom: -1px;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
    }
    .form-signin input[type="password"] {
      margin-bottom: 10px;
      border-top-left-radius: 0;
      border-top-right-radius: 0;
    }
    .login-logo {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
      color: #0d6efd;
    }
  </style>
</head>
<body>
  <main class="form-signin">
    <div class="text-center mb-4">
      <div class="login-logo">
        <i class="bi bi-cloud-upload"></i>
      </div>
      <h1 class="h3 mb-3 fw-normal">前端部署工具</h1>
      <p class="text-muted">请登录以继续</p>
    </div>
    
    <div class="card">
      <div class="card-body">
        <form id="login-form">
          <div class="alert alert-danger d-none" id="login-error"></div>
          
          <div class="form-floating mb-3">
            <input type="text" class="form-control" id="username" placeholder="用户名" required>
            <label for="username">用户名</label>
          </div>
          
          <div class="form-floating mb-3">
            <input type="password" class="form-control" id="password" placeholder="密码" required>
            <label for="password">密码</label>
          </div>
          
          <button class="w-100 btn btn-lg btn-primary" type="submit">
            <i class="bi bi-box-arrow-in-right"></i> 登录
          </button>
        </form>
      </div>
    </div>
    
    <div class="text-center mt-3">
      <p>还没有账号？ <a href="/register">注册</a></p>
    </div>
  </main>
  
  <script src="/bootstrap/bootstrap.bundle.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const loginForm = document.getElementById('login-form');
      const loginError = document.getElementById('login-error');
      
      loginForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        try {
          loginError.classList.add('d-none');
          
          const response = await fetch('/api/users/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
          });
          
          const data = await response.json();
          
          if (!response.ok) {
            // 根据状态码和错误信息提供更详细的提示
            let errorMessage = data.error || '登录失败';

            if (response.status === 403 && data.status) {
              switch (data.status) {
                case 'pending':
                  errorMessage = '您的账户正在等待管理员审核，请耐心等待。';
                  break;
                case 'disabled':
                  errorMessage = '您的账户已被禁用，请联系管理员。';
                  break;
              }
            }

            throw new Error(errorMessage);
          }
          
          // 保存用户信息和令牌到本地存储
          localStorage.setItem('user', JSON.stringify(data.user));
          localStorage.setItem('token', data.token);
          
          // 跳转到首页
          window.location.href = '/';
        } catch (error) {
          loginError.textContent = error.message;
          loginError.classList.remove('d-none');
        }
      });
    });
  </script>
</body>
</html> 
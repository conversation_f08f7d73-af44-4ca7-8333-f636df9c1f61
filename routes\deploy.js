const express = require('express');
const router = express.Router();
const DeployService = require('../services/DeployService');
const Project = require('../models/Project');

// 开始部署
router.post('/', async (req, res) => {
  try {
    const { projectId, branch } = req.body;
    
    if (!projectId || !branch) {
      return res.status(400).json({ error: '项目ID和分支名称为必填项' });
    }
    
    // 检查项目是否存在
    const project = await Project.getById(projectId);
    if (!project) {
      return res.status(404).json({ error: '项目不存在' });
    }
    
    // 在后台开始部署
    const buildId = require('uuid').v4();
    DeployService.deploy(projectId, branch, req.io, buildId)
      .then(result => {
        req.logger.info(`部署完成: ${JSON.stringify(result)}`);

        // 发送成功事件
        req.io.emit('build-completed', {
          buildId: result.buildId,
          projectId: result.projectId,
          status: 'success',
          message: '部署成功'
        });
      })
      .catch(error => {
        req.logger.error(`部署错误: ${error.message}`);

        // 发送失败事件到前端
        req.io.emit('build-completed', {
          buildId: buildId,
          projectId: projectId,
          status: 'failed',
          message: `部署失败: ${error.message}`,
          error: {
            message: error.message,
            stack: error.stack
          }
        });

        // 发送全局错误事件
        req.io.emit('build-error', {
          buildId: buildId,
          projectId: projectId,
          message: `部署失败: ${error.message}`,
          time: new Date().toISOString()
        });
      });
    
    // 立即返回构建ID
    res.json({
      success: true,
      message: '部署已开始',
      buildId,
      project: {
        id: project.id,
        name: project.name
      },
      branch
    });
  } catch (error) {
    req.logger.error(`启动部署失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取项目分支
router.get('/branches/:projectId', async (req, res) => {
  try {
    const branches = await DeployService.getBranches(req.params.projectId);
    res.json(branches);
  } catch (error) {
    req.logger.error(`获取分支列表失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router; 
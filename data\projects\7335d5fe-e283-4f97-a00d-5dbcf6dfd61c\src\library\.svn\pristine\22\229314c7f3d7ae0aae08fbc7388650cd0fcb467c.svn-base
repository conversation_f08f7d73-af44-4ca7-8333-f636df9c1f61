<template>
    <el-row
            class="mk-school-card"
            type="flex" align="middle" justify="space-between"  :gutter="18"
    >
        <el-col
                v-for="(item, index) in dataList"
                :key="index"
                :span="8"
                class="mk-school-card-list"
                @click="emitClick(item)"
        >
            <el-row class="mk-school-card-item transtop" type="flex" align="middle" justify="space-between">
                <div class="school-img">
                    <img :src="item.icon"/>
                </div>
                <el-row class="mk-school-info" type="flex" align="middle" justify="space-between">
                    <div class="mk-school-info-name ellipsis" :title="item.schoolName">{{item.schoolName}}</div>
                    <div class="mk-school-info-area">{{item.province}} {{item.area}}</div>
                    <div>
                        <span>课程  {{item.count}}</span>
                        <span class="mk-school-info-views">访问量  {{item.views}}</span>
                    </div>
                </el-row>
            </el-row>
        </el-col>
    </el-row>
</template>
<script lang="ts">
    import {defineComponent, PropType} from "vue";

    export interface ISchoolCard {
        /** 学校图片 */
        icon: string;
        /** 学校名称 */
        schoolName: string;
        /** 省份 */
        province: string;
        /** 地区 */
        area: string;
        /** 课程数 */
        count: number;
        /** 预览量 */
        views: number;
    }

    export default defineComponent({
        name: "mk-school-card",

        emits: ['click-school-card'],

        props: {
            dataList: {
                type: [] as PropType<ISchoolCard[]>,
                required: true
            }
        },

        setup(props, ctx){
          /**
           * @name: 事件传递
           * @param item 学校卡片对象
           */
          const  emitClick = (item: ISchoolCard)=>{
            ctx.emit('click-school-card', item)
          }

          return {
            emitClick
          }
        }
    });
</script>

<style lang="scss" scoped>

    .mk-school-card-item {
        height: 140px;
        background-color: #fff;
        flex-wrap: nowrap;
        padding: 30px 20px;
        margin-bottom: 15px;
        border-radius: 4px;
        cursor: pointer;
        .school-img {
            width: 80px;
            height: 80px;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .mk-school-info {
            width: calc(100% - 60px);
            div {
                width: 100%;
                text-indent: 20px;
                font-size: 12px;
                color: #999;
                &.mk-school-info-name {
                    font-weight: bold;
                    color: #333333;
                    line-height: 30px;
                    font-size: 16px;
                    margin-bottom: 14px;
                }
                .mk-school-info-views {
                    margin-left: 40px;
                }
            }
            .mk-school-info-area{
                margin-bottom: 16px;
            }
        }
    }
</style>
<template>
  <el-row
          type="flex"
          align="middle"
          justify="center"
          class="mk-c30-cloud-platform-content-tabs"
  >
    <div class="mk-c30-cloud-platform-content-tabs-title" v-if="src || title">
      <img v-if="src" :src="src"/>
      <span v-if="title">{{ title }}</span>
    </div>
    <template v-for="(item, index) in tabList">
      <div
              :class="[
          'mk-c30-cloud-platform-content-tabs-item',
          {
            'title-active-item':
              tabKey === '' ? tabList[0].key === item.key : tabKey == item.key,
          },
        ]"
              @click="changeTab(item.key)"
      >
        <span class="text">{{ item.value }}</span>
        <slot name="slot-item" :data="item" :index="index"></slot>
      </div>
    </template>
  </el-row>
</template>

<script lang="ts">
    import {defineComponent, reactive, toRefs, watch} from "vue";

    export default defineComponent({
        name: "mk-tabs",

        props: {
            //页面标题
            title: {
                type: String,
                default: "",
            },
            //图标地址
            imgSrc: {
                type: String,
                default: "",
            },
            //默认选中的标签页
            tabKeyDefault: {
                type: String,
                default: "",
            },
            //标签页集合
            tabList: {
                type: Array,
                required: true,
            },
        },

        emits: ["change-tab"],

        setup(props, context) {
            const state = reactive({
                tabKey: props.tabKeyDefault,
                src:
                    props.imgSrc === "" ? "" : `${require(`@/assets/${props.imgSrc}.png`)}`,
            });

            /**
             * 切换标签页
             * @param {string} key
             */
            function changeTab(key: string) {
                state.tabKey = key;
                context.emit("change-tab", key);
            }

            watch(
                () => props.tabKeyDefault,
                (newValue: string) => {
                    state.tabKey = newValue;
                }
            );
            return {
                ...toRefs(state),
                changeTab,
            };
        },
    });
</script>

<style lang="scss" scoped>
  .mk-c30-cloud-platform-content-tabs {
    position: relative;
    height: 64px;
    background: #fff;
    border-radius: 4px;
    margin-bottom: 10px;
    text-align: center;
    -moz-user-select: none; /*火狐*/
    -webkit-user-select: none; /*webkit浏览器*/
    -ms-user-select: none; /*IE10*/
    -khtml-user-select: none; /*早期浏览器*/
    user-select: none;
    &-title {
      position: absolute;
      left: 24px;
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: bold;
      color: #333;
      img {
        width: 36px;
        height: 36px;
        margin-right: 12px;
      }
    }
    &-item {
      min-width: 115px;
      display: inline-block;
      margin-right: 10px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      font-weight: bold;
      color: #333333;
    }
    .title-active-item .text {
      padding: 11px 22px;
      background: #eef2fb;
      border: 1px solid #3e73f6;
      border-radius: 40px;
      color: #3e73f6;
    }
  }
</style>

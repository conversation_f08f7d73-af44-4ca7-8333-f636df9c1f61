<template>
  <el-tooltip :disabled="disabled" :content="content" :placement="placement" effect="light" class="toolitip-box">
    <template #content v-if="content==''">
      <slot name="custom-tooltip-content"></slot>
    </template>
    <slot name="tooltip-content"></slot>
  </el-tooltip>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "mk-tooltip",

  props: {
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    //内容
    content: {
      type: String,
      default: ''
    },
    //方位
    placement: {
      type: String,
      default: "bottom"
    }
  }
});
</script>

<style lang="scss">
 .el-popper{
    max-width: 800px;
    line-height: 2;
  }
</style>
﻿<template>
  <el-select
    v-model="curSubject"
    value-key="id"
    class="subject-select-div"
    @change="switchSubject"
    :disabled="disabled"
    :popper-append-to-body="false"
    :title="`${curSubject.name}`"
  >
    <template v-if="subjectList.length">
      <el-option
        v-for="item in subjectList"
        :key="item.id"
        :label="item.name"
        :value="item"
        :title="item.name"
        class="subject-select-item"
      ></el-option>
    </template>
  </el-select>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, watch, PropType } from 'vue';

export interface ISubjectModel {
  /** id */
  id: string;
  /** 学科名称 */
  name: string;
  /** 学段id */
  phaseId: string;
  /** 学科id */
  subjectId: string;
}

export default defineComponent({
  name: 'subject-select',

  emits: ['switch-subject'],

  props: {
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    //当前学科
    curSubObj: {
      type: Object as PropType<ISubjectModel>,
      default: () => {
        return {
          phaseId: '1',
          subjectId: '24',
          id: '24',
          name: '小学语文'
        };
      }
    }
  },

  setup(props, ctx) {
    const subjectList = require('@/library/ui/mk-share-res/SubjectSelect/subjectList.json');
    const state = reactive({
      /** 学科列表 */
      subjectList: subjectList as ISubjectModel[],
      // 当前学科
      curSubject: props.curSubObj as ISubjectModel
    });
    /**
     * @name: 切换学科
     * @param subjectInfo 当前学科对象
     */
    const switchSubject = (subjectInfo: ISubjectModel) => {
      state.curSubject = {
        phaseId: subjectInfo.phaseId,
        subjectId: subjectInfo.subjectId,
        id: subjectInfo.id,
        name: subjectInfo.name
      };
    };

    watch(
      () => state.curSubject,
      () => {
        ctx.emit('switch-subject', state.curSubject);
      }
    );

    return {
      ...toRefs(state),
      switchSubject
    };
  }
});
</script>

<style lang="scss" scoped>
.subject-select-div{
  .subject-select-item{
    font-size: 14px !important;
    &.selected{
      @include theme_color();
    }
  }
  ::v-deep(.el-input.is-focus .el-input__inner),
  ::v-deep(.el-input__inner:focus){
    @include theme_border-color();
  }
}
</style>

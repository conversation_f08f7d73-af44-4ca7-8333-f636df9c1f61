/*
 * @Description:
 * @Author: liuyue <EMAIL>
 * @Date: 2025-03-20 09:23:42
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-04-03 10:00:40
 */
CKEDITOR.plugins.add("protectedelements", {
  init: function (editor) {
    // 默认配置：保护带有 data-protected 属性的元素
    var config = editor.config.protectedElements || [""];
    // 核心保护逻辑
    protectElements(editor, config);
  },
});
function protectElements(editor, selectors) {
  // 使用CKEditor事件系统监听事件
  editor.on("key", function (evt) {
    var domEvent = evt;
    onKeyDown(domEvent);
  });

  editor.on("paste", function (evt) {
    var domEvent = evt;
    onPaste(domEvent);
  });

  // 监听输入法事件
  editor.on("contentDom", function () {
    var editable = editor.editable();
    editable.attachListener(editable, "compositionstart", function (evt) {
      onComposition(evt.data.$);
    });
    editable.attachListener(editable, "compositionend", function (evt) {
      onComposition(evt.data.$);
    });
    editable.attachListener(editable, "beforeinput", function (evt) {
      onBeforeInput(evt.data.$);
    });
  });

  // 处理粘贴事件
  function onPaste(evt) {
    var selection = editor.getSelection();
    var startElement = selection.getStartElement();

    // if (isProtected(startElement)) {
    //   evt.preventDefault();
    //   showWarning(editor);
    // }
  }

  // 处理输入法事件
  function onComposition(evt) {
    var selection = editor.getSelection();
    var startElement = selection.getStartElement();
    console.log("onComposition", startElement);
    // if (isProtected(startElement)) {
    //   evt.preventDefault();
    //   showWarning(editor);
    // }
  }

  // 处理输入前事件
  function onBeforeInput(evt) {
    var selection = editor.getSelection();
    var startElement = selection.getStartElement();

    // if (isProtected(startElement)) {
    //   evt.preventDefault();
    //   showWarning(editor);
    // }
  }

  // 处理键盘事件（Delete/Backspace）
  function onKeyDown(evt) {
    var selection = editor.getSelection();
    var startElement = selection.getStartElement();
    var ranges = selection.getRanges();

    if (evt.data.keyCode == 46) {
      let nextElement = getNextElement(ranges);
      // Delete键
      // 检查当前选区的开始位置和结束位置
      if (isProtected(nextElement) || hasTagInSelection(selection)) {
        evt.cancel();
        showWarning(editor);
        return;
      }
    } else if (
      evt.data.keyCode === 8 || // Backspace
      (evt.data.ctrlKey && evt.data.keyCode == 88) // ctrl+x
    ) {
      let previousElement = getPrevElement(ranges);
      if (isProtected(previousElement) || hasTagInSelection(selection)) {
        evt.cancel();
        showWarning(editor);
        return;
      }
    } else if (
      evt.data.ctrlKey &&
      (evt.data.keyCode === 67 || evt.data.keyCode === 86)
    ) {
      // ctrl+c or ctrl+v
      // 允许复制，但阻止粘贴到保护元素中
      if (evt.data.keyCode === 86 && isProtected(startElement)) {
        evt.cancel();
        showWarning(editor);
      }
    } else {
      // 其他按键输入，检查是否在保护元素内
      // let nextElement = getNextElement(ranges);
      // let previousElement = getPrevElement(ranges);
      // if (!(isProtected(previousElement) || isProtected(nextElement))) {
      //   evt.cancel();
      //   showWarning(editor);
      //   return;
      // }
    }
  }

  function getNextElement(ranges) {
    if (ranges.length === 0) return null;

    var range = ranges[0].clone();
    range.collapse(true); // 折叠到开始位置
    var startContainer = range.startContainer;
    var startOffset = range.startOffset;
    var nextElement = null;

    // 处理文本节点
    if (startContainer.type === CKEDITOR.NODE_TEXT) {
      // 如果在文本节点的末尾
      if (startOffset === startContainer.getText().length) {
        var node = startContainer;
        while (node) {
          var nextNode = node.getNext();
          if (nextNode && nextNode.type === CKEDITOR.NODE_ELEMENT) {
            nextElement = nextNode;
            break;
          }
          node = node.getParent();
          if (node && node.type === CKEDITOR.NODE_ELEMENT) {
            var nextSibling = node.getNext();
            if (nextSibling) {
              nextElement = nextSibling;
              break;
            }
          }
        }
      }
    }
    // 处理元素节点
    else if (startContainer.type === CKEDITOR.NODE_ELEMENT) {
      var child = startContainer.getChild(startOffset);
      if (child) {
        if (child.type === CKEDITOR.NODE_ELEMENT) {
          nextElement = child;
        } else {
          // 查找下一个元素节点
          while (child && child.type !== CKEDITOR.NODE_ELEMENT) {
            child = child.getNext();
          }
          if (child) {
            nextElement = child;
          }
        }
      } else {
        // 如果没有子节点，检查下一个兄弟节点
        nextElement = startContainer.getNext();
      }
    }

    return nextElement;
  }

  function getPrevElement(ranges) {
    if (ranges.length === 0) return null;

    var range = ranges[0].clone();
    range.collapse(true); // 折叠到光标起始位置

    var startContainer = range.startContainer;
    var startOffset = range.startOffset;
    var previousElement = null;

    // 处理文本节点
    if (startContainer.type === CKEDITOR.NODE_TEXT) {
      if (startOffset === 0) {
        // 查找前一个兄弟元素节点
        var node = startContainer;
        while (node) {
          var prevNode = node.getPrevious();
          if (prevNode && prevNode.type === CKEDITOR.NODE_ELEMENT) {
            previousElement = prevNode;
            break;
          }
          node = node.getParent();
          if (node && node.type === CKEDITOR.NODE_ELEMENT) {
            previousElement = node;
            break;
          }
        }
      }else if(startOffset >= 10){
        previousElement = startContainer.getParent()
      }
    }
    // 处理元素节点
    else if (startContainer.type === CKEDITOR.NODE_ELEMENT) {
      if (startOffset > 0) {
        var child = startContainer.getChild(startOffset - 1);
        if (child) {
          if (child.type === CKEDITOR.NODE_ELEMENT) {
            previousElement = child;
          } else {
            // 查找前一个元素节点
            while (child && child.type !== CKEDITOR.NODE_ELEMENT) {
              child = child.getPrevious();
            }
            if (child) {
              previousElement = child;
            }
          }
        }
      } else {
        // 如果startOffset为0，检查父元素
        previousElement = startContainer;
      }
    }

    return previousElement;
  }

  // 判断元素是否受保护
  function isProtected(element) {
    if (!element || !element.$ || !element.$.classList) {
      return false;
    }
    return selectors.some(function (selector) {
      return element.$.classList.contains(selector);
    });
  }

  function hasTagInSelection(selection) {
    if (!selection) return false;

    var ranges = selection.getRanges();
    if (ranges.length === 0) return false;

    var range = ranges[0];
    var startNode = range.startContainer;
    var endNode = range.endContainer;
    var commonAncestor = range.getCommonAncestor();

    // 检查选区的开始和结束节点是否在受保护元素内
    var startProtected = isNodeInProtectedElement(startNode);
    var endProtected = isNodeInProtectedElement(endNode);

    if (startProtected || endProtected) {
      return true;
    }

    // 检查选区是否跨越了受保护元素
    var walker = new CKEDITOR.dom.walker(range);
    var node;

    while ((node = walker.next())) {
      if (node.type === CKEDITOR.NODE_ELEMENT && isProtected(node)) {
        return true;
      }
    }

    return false;
  }

  // 检查节点是否在受保护元素内
  function isNodeInProtectedElement(node) {
    while (node) {
      if (node.type === CKEDITOR.NODE_ELEMENT && isProtected(node)) {
        return true;
      }
      node = node.getParent();
    }
    return false;
  }
}

// 显示警告提示
function showWarning(editor) {
  editor.showNotification("该元素受保护，不可删除", "warning", 1000);
}

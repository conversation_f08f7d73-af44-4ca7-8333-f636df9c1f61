<template>
  <el-breadcrumb
      separator-class="el-icon-arrow-right"
      v-if="breadcrumb.length"
      class="mk-bread-crumb"
  >
    <template v-if="isShowBack">
      <span @click="goBack(-1,null)" class="mk-bread-crumb-back">
      <i class="icon iconfont iconfanhui"></i>
      返回
    </span>

    </template>


    <slot name="prepend"></slot>

    <el-breadcrumb-item
        v-for="(item, index) in breadcrumb"
        :key="index"
        :class="{
        isClick: index !== breadcrumb.length - 1,
        disabled: index === breadcrumb.length - 1
      }"
        @click="routerGo(index + 1 - breadcrumb.length,item)"
    >{{ item.title }}</el-breadcrumb-item
    >
  </el-breadcrumb>
</template>

<script lang="ts">
import {defineComponent, PropType} from "vue";
import { ElBreadcrumb, ElBreadcrumbItem } from "element-plus";
import { useRouter } from "vue-router";

export declare interface IBreadCrumb {
  // 路由path地址
  path: string;
  // 路由名称
  title: string;
  /** 路由参数 */
  query: object;
}

export default defineComponent({
  name: "mk-bread-crumb",

  components: { ElBreadcrumb, ElBreadcrumbItem },

  props: {
    // 面包屑集合
    breadcrumb: {
      type: Array as PropType<IBreadCrumb[]>,
      default: () => {
        return [];
      }
    },
    // 是否根据参数path跳转
    usePathGo:{
      type:Boolean,
      default:false,
    },
    // 是否展示返回
    isShowBack:{
      type:Boolean,
      default: true
    }
  },

  setup(props) {
    const router = useRouter();
    /**
     * @name: 返回
     * @param backIndex 返回索引 0当前路由 -1 前一个路由 1 下一个路由
     */
    const goBack = (backIndex: number)=>{
      const referrer = document.referrer
      if( history.length === 1 ){
        // 是否来自中转页面
        const isFromTransPage = /(fs|sso|presso|ssotest)\.iclass30\.com/.test(referrer)
        if(  referrer === '' || isFromTransPage ){
          window.close()
        }else{
          location.href = referrer
        }
      }else{
        router.go(backIndex);
      }
    }

    /**
     * @name: 路由跳转
     * @param backIndex 返回索引 0当前路由 -1 前一个路由 1 下一个路由
     * @description: 方法说明
     */
    const routerGo = (backIndex: number,item: any) => {
      if(props.usePathGo){
        history.pushState(null,'',`/region${item.path}`)
        return;
      }
      goBack(backIndex)
    }

    return {
      routerGo,
      goBack
    };
  }
});
</script>

<style lang="scss" scoped>
.mk-bread-crumb {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 0 !important;
  width: 100%;

  .mk-bread-crumb-back{
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    color: #606266;
    .iconfanhui{
      margin-right: 5px;
    }
    &:hover{
      @include theme_color();
    }
  }
  ::v-deep(.isClick) {
    font-weight: 600;
    cursor: pointer;
    &:hover{
      .el-breadcrumb__inner{
        @include theme_color(true);
      }
    }
  }
  ::v-deep(.disabled) {
    pointer-events: none;
  }
}
</style>
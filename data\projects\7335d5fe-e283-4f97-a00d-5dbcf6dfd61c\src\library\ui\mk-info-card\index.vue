<template>
  <el-row type="flex" align="middle" justify="space-between" class="mk-info-card">
    <el-col
        :span="8"
        v-for="(item,index) in dataList"
        :key="index"
        class="mk-info-card-item"
        :style="{background: item.bg ? `url(${item.bg}) no-repeat left top/ cover` : 'linear-gradient(223deg, #9492FF 0%, #4384FF 100%)'}"
    >
      <p class="mk-info-card-title">{{item.name}}</p>
      <el-row type="flex" align="middle" justify="space-between">
        <el-col
          :span="7"
          v-for="(subItem,subIndex) in item.list"
          :key="subIndex"
        >
          <p class="mk-info-card-item-count">{{subItem.count}}</p>
          <p>{{subItem.name}}</p>
        </el-col>
      </el-row>
    </el-col>
  </el-row>
</template>
<script lang="ts">
import { defineComponent, PropType } from "vue";

export interface IInfo{
  /** 名称 */
  name: string;
  /** 计数 */
  count: string;
}
export default defineComponent({
  name: "mk-info-card",

  props: {
    // 数据列表
    dataList: {
      type: [] as PropType<{
        /** 名称 */
        name: string;
        /** 背景图 */
        bg: string
        /** 数据集合 */
        list: IInfo[];
      }[]>,
      required: true
    }
  }
});
</script>

<style lang="scss" scoped>
.mk-info-card{
  margin-top: 20px;
  text-align: center;
  .mk-info-card-item{
    flex: 0 0 32.2%;
    border-radius: 6px;
    padding: 22px 10px 30px 10px;
    color: #fff;
    font-size: 16px;
    .mk-info-card-item-count{
      margin-bottom: 10px;
      font-weight: bold;
    }
    .mk-info-card-title{
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 30px;
    }
  }
}
</style>
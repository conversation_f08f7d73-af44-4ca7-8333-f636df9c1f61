/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2023-11-24 13:40:37
 * @LastEditors: l<PERSON><PERSON><PERSON> y<PERSON><PERSON>@class30.com
 * @LastEditTime: 2024-03-25 11:53:06
 */
/**
 * 自定义作文格
 */
CKEDITOR.plugins.add("TianziLine", {
  requires: ["dialog"],
  init: function (editor) {
    editor.addCommand("TianziLine", new CKEDITOR.dialogCommand("TianziLine"));
    editor.ui.addButton("TianziLine", {
      label: "插入田字格",
      command: "TianziLine",
      cindex: 0,
      click: function (e) {
        editor.execCommand("TianziLine");
      },
    });
    CKEDITOR.dialog.add("TianziLine", this.path + "dialog/TianziLine.js");
  },
});

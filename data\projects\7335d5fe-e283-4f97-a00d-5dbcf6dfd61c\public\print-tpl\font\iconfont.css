@font-face {
  font-family: "iconfont"; /* Project id 4260570 */
  src: url('iconfont.woff2?t=1695193538752') format('woff2'),
       url('iconfont.woff?t=1695193538752') format('woff'),
       url('iconfont.ttf?t=1695193538752') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 12px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-duicuo:before {
  content: "\e653";
}

.icon-a-duicuocuo:before {
  content: "\e952";
}

.icon-a-duicuodui:before {
  content: "\e953";
}


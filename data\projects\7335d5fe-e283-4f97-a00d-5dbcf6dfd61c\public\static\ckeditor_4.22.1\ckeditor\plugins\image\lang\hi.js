/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'hi', {
	alt: 'वैकल्पिक टेक्स्ट',
	border: 'बॉर्डर',
	btnUpload: 'इसे सर्वर को भेजें',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: 'हॉरिज़ॉन्टल स्पेस',
	img2Button: 'Do you want to transform the selected image on a image button?', // MISSING
	infoTab: 'तस्वीर की जानकारी',
	linkTab: 'लिंक',
	lockRatio: 'लॉक अनुपात',
	menu: 'तस्वीर प्रॉपर्टीज़',
	resetSize: 'रीसॅट साइज़',
	title: 'तस्वीर प्रॉपर्टीज़',
	titleButton: 'तस्वीर बटन प्रॉपर्टीज़',
	upload: 'अपलोड',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: 'वर्टिकल स्पेस',
	validateBorder: 'Border must be a whole number.', // MISSING
	validateHSpace: 'HSpace must be a whole number.', // MISSING
	validateVSpace: 'VSpace must be a whole number.' // MISSING
} );

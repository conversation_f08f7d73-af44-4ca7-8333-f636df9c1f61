/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'id', {
	alt: 'Teks alternatif',
	border: 'Batas',
	btnUpload: 'Kirim ke Server',
	button2Img: 'Do you want to transform the selected image button on a simple image?', // MISSING
	hSpace: 'HSpace', // MISSING
	img2Button: 'Apakah anda ingin mengubah gambar yang dipilih pada tombol gambar?',
	infoTab: 'Info Gambar',
	linkTab: 'Tautan',
	lockRatio: 'Lock Ratio', // MISSING
	menu: 'Image Properties', // MISSING
	resetSize: 'Atur Ulang Ukuran',
	title: 'Image Properties', // MISSING
	titleButton: 'Image Button Properties', // MISSING
	upload: 'Unggah',
	urlMissing: 'Image source URL is missing.', // MISSING
	vSpace: 'VSpace',
	validateBorder: 'Border harus berupa angka',
	validateHSpace: 'HSpace harus berupa angka',
	validateVSpace: 'VSpace must be a whole number.' // MISSING
} );

﻿<template>
  <div
    :class="
      source == 'admin'
        ? 'admin-tree'
        : isJLWX()
        ? 'jlwx-knowledge-tree'
        : 'knowledge-tree'
    "
  >
    <el-tree
      v-show="treeData.length"
      v-loading="loading"
      ref="knowledgeTreeRef"
      :data="treeData"
      :highlight-current="true"
      :show-checkbox="showCheckbox"
      :node-key="nodeKey"
      :default-expanded-keys="checkedKeys"
      :default-checked-keys="checkedKeys"
      :empty-text="emptyText"
      :check-on-click-node="!showCheckbox"
      :check-strictly="true"
      :current-node-key="checkedKey"
      accordion
      :props="{ label: 'name' }"
      :style="{
        height: maxHeight,
        overflowY: 'auto',
        backgroundColor: backgroundColor
      }"
      @check="checkChange"
    >
      <template #default="{ node, data }" v-if="isCustomTree">
        <div
          class="custom-tree-node"
          :class="data.code === checkedKey ? 'custom-active' : ''"
        >
          <div class="custom-tree-node-item">
            <div
              class="live-big-box flex-between"
              :class="data.code === checkedKey ? 'catalog-active' : ''"
            >
              <span class="label-box" :title="node.label">
                {{ node.label }}
              </span>
              <!-- icon图标 -->
              <div class="labelicon-box">
                <i
                  v-if="
                    (data.childCatalogues && data.childCatalogues.length > 0) ||
                    (data.children && data.children.length > 0)
                  "
                  :class="[
                    'labelicon iconfont',
                    !node.expanded ? 'iconicon-test22' : 'iconicon-test20 ',
                    data.code === checkedKey ? 'labelicon-active' : ''
                  ]"
                ></i>
              </div>
            </div>
          </div>
        </div>
      </template>
    </el-tree>
    <mk-no-data
      v-if="!loading && treeData.length === 0"
      :noDataTipTex="'暂无知识点'"
    ></mk-no-data>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  watch,
  ref,
  onMounted,
  nextTick
} from 'vue';
import { findPointAPI } from '@/library/src/service/API/kkl';
import { useStore } from 'vuex';
import { IGlobalState } from '@/library/src/store';
import { isJLWX } from '@/library/src/utils/valiteSite';
export default defineComponent({
  name: 'knowledge-tree',

  emits: ['checked-change'],

  props: {
    // 是否显示选择框
    showCheckbox: {
      type: Boolean,
      default: true
    },
    // 唯一键
    nodeKey: {
      type: String,
      default: 'code'
    },
    // 默认已选节点
    checkedKeys: {
      type: Array,
      default: []
    },
    // 无数据文本
    emptyText: {
      type: String,
      default: '暂无知识点'
    },
    //最大高度
    maxHeight: {
      type: String,
      default: ''
    },
    // 学科id
    subjectId: {
      type: String,
      default: ''
    },
    // 学段
    phaseId: {
      type: String,
      default: ''
    },
    //是否展示自定义样式
    isCustomTree: {
      type: Boolean,
      default: true
    },
    // 来源 前台:front,空间:admin
    source: {
      type: String,
      default: 'front'
    },
    //背景色
    backgroundColor: {
      type: String,
      default: ''
    }
  },

  setup(props, ctx) {
    const knowledgeTreeRef = ref<any>(null);
    const store = useStore<IGlobalState>();
    const state = reactive({
      // 目录树数据
      treeData: [] as any[],
      // 是否加载中
      loading: true,
      //当前选中得节点
      checkedKey: props.checkedKeys[props.checkedKeys.length-1]
    });

    /**
     * @name: 初始化
     */
    const init = () => {
      state.treeData = [];
      state.loading = true;
      findPointAPI({
        subjectId: props.subjectId,
        phaseId: props.phaseId
      })
        .then((res: any) => {
          if (res.code === 1) {
            state.treeData = res.data;
            if (!props.showCheckbox) {
              state.treeData.unshift({
                code: '',
                name: '全部'
              });
            }
          } else {
            state.treeData = [];
          }

          nextTick(() => {
            if (props.checkedKeys.length) {
              const checkedNodes = knowledgeTreeRef.value.getCheckedNodes();
              checkChange(null, { checkedNodes });
            }
          });
          state.loading = false;
        })
        .catch(() => {
          state.treeData = [];
          state.loading = false;
        });
    };
    /**
     * @name: 选择改变
     * @param node 树节点
     * @param data 树选择数据
     */
    const checkChange = (node: any, data: any) => {
      if (node !== null) {
        state.checkedKey = node.code;
      }
      let result: any = [];
      data.checkedNodes.forEach((item: any) => {
        //层级大于1，点击的是子节点
        if (item.depth > 1) {
          //获取所有的父节点的code
          let parentCode = item.path.split('/');
          parentCode.forEach((val: string) => {
            //判断是否添加过该父节点
            let index = result.findIndex((value: any) => value.code == val);
            //获取所有的父节点
            let parentNode = knowledgeTreeRef.value.getNode(val);
            if (index == -1) {
              //添加父节点
              result.push({
                //获取父节点的名称
                name: parentNode.label,
                code: val
              });
            }
          });
          //添加本节点
          result.push({
            name: item.name,
            code: item.code
          });
        } else {
          //判断是否添加过该父节点
          let index = result.findIndex((value: any) => value.code == item.code);
          if (index == -1) {
            result.push({
              name: item.name,
              code: item.code
            });
          }
        }
      });
      const selfResult = data.checkedNodes.map((item: any) => {
        return {
          name: item.name,
          code: item.code
        };
      });
      ctx.emit('checked-change', node, result, selfResult);
    };
    /**
     * @name: 清空已选
     */
    const clearKnowLedge = () => {
      if (!knowledgeTreeRef.value) {
        return;
      }
      knowledgeTreeRef.value.setCheckedKeys([]);
      checkChange(null, { checkedNodes: [] });
    };
    /**
     * @name: 删除已选
     * @param item 当前已选对象
     */
    const deleteKnowLedge = (item: any) => {
      if (!knowledgeTreeRef.value) {
        return;
      }
      knowledgeTreeRef.value.setChecked(item.code, false);
      const checkedNodes = knowledgeTreeRef.value.getCheckedNodes();
      checkChange(null, { checkedNodes });
    };

    watch(
      () => props.subjectId,
      () => {
        init();
      }
    );
    watch(
      () => props.phaseId,
      () => {
        init();
      }
    );
    onMounted(() => {
      init();
    });

    return {
      ...toRefs(state),
      checkChange,
      knowledgeTreeRef,
      clearKnowLedge,
      deleteKnowLedge,
      isJLWX
    };
  }
});
</script>
<style lang="scss" scoped>
.knowledge-tree {
  width: 100%;
  margin-top: 5px;
  background: white;
  padding: 0 10px;
  border-radius: 10px;
}
</style>
<style lang="scss">
.admin-tree {
  .el-checkbox__input.is-checked .el-checkbox__inner {
    @include theme_background-color();
    @include theme_border-color();
  }
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: #f0f7ff;
    @include theme_color();
  }
}
.knowledge-tree {
  .el-checkbox__input.is-checked .el-checkbox__inner {
    @include theme_background-color();
    @include theme_border-color();
  }
  // 直接一级目录
  .el-tree {
    // 点击目录章节激活样式
    .catalog-active {
      @include theme_color(true);
    }
    & > .el-tree-node {
      & > .el-tree-node__content {
        color: #333;
        border-bottom: 1px solid #e5e5e5;
        padding-left: 15px !important;
        position: relative;
        // 展开图标
        .el-tree-node__expand-icon {
          width: 12px;
          height: 12px;
          @include theme_background-color();
          opacity: 0.2;
          border-radius: 50%;
          position: absolute;
        }
        &::after {
          display: inline-block;
          content: '';
          clear: both;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          @include theme_background-color();
          position: absolute;
          left: 17px;
          top: 15px;
        }
        .custom-tree-node {
          margin-left: 22px;
          width: 90%;
          .custom-tree-node-item {
            font-size: 16px;
            .labelicon-box {
              .labelicon {
                font-size: 24px;
              }
            }
          }
        }
      }
      // 直接子级
      & > .el-tree-node__children {
        & > div:first-child {
          padding-top: 10px;
        }
        & > div:last-child {
          padding-bottom: 10px;
          border-bottom: 1px solid #e5e5e5;
        }
        .el-tree-node__content {
          &:hover,
          &:active,
          &:focus {
            background-color: #eef7ff;
            @include theme_color();
          }
          .custom-tree-node {
            width: 90%;
          }
        }
      }
    }
  }
  // 一级目录展开
  .el-tree-node__expand-icon.expanded {
    transform: rotate(0deg);
  }
  // 二级目录的类型图片
  .custom-tree-node-item {
    font-size: 14px;
  }
  // 子级目录鼠标经过
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: transparent;
  }
  .live-big-box {
    width: 100%;
    .label-box {
      width: 75%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .labelicon-box {
      .labelicon {
        font-size: 22px;
        color: #999;
        cursor: pointer;
      }
    }
  }
  .el-tree-node__label {
    font-size: 16px;
  }
  .el-tree-node__content {
    height: 40px !important;
  }
  .el-icon-caret-right:before {
    content: '';
  }
}
/* jlwx目录样式 */
.jlwx-knowledge-tree {
  width: 100%;
  margin-top: 5px;
  background: white;
  padding: 0 10px;
  border-radius: 10px;
  // 去除系统自带样式
  .el-tree--highlight-current
    .el-tree-node.is-current
    > .el-tree-node__content {
    background-color: transparent;
  }
  // 点击目录章节激活样式
  .catalog-active {
    @include theme_color(true);
  }
  .live-big-box {
    width: 100%;
    color: #8a8a8a;
    .label-box {
      font-family: PingFangSC-Regular;
      font-size: 16px;
      width: 75%;
      line-height: 27px;
    }
    .labelicon-box {
      .labelicon {
        font-size: 20px;
        color: #999;
        cursor: pointer;
      }
      .labelicon-active {
        @include theme_color();
      }
    }
  }
  .el-tree-node__content {
    color: #8a8a8a;
    padding: 10px 0;
    height: 48px !important;
    margin-bottom: 5px;
    &:hover,
    &:active,
    &:focus {
      background-color: transparent;
    }
    .el-tree-node__expand-icon {
      display: none;
    }
    .custom-tree-node {
      width: calc(100% - 10px);
      font-family: PingFangSC-Regular;
      font-size: 16px;
      padding: 10px 5px 10px 20px;
      border-radius: 12px;
      &:hover,
      &:active,
      &:focus {
        background: #f7f7f7;
      }
      &.custom-active {
        background: #e7f1fd;
      }
    }
  }
  // 直接一级目录
  .el-tree {
    & > .el-tree-node {
      // 当前目录
      & > .el-tree-node__content {
        .custom-tree-node {
          font-family: PingFangSC-Medium;
          .labelicon {
            font-size: 24px;
          }
        }
      }
      // 当前目录子级
      & > .el-tree-node__children {
        margin-left: 10px;
        & > div:first-child {
          padding-top: 10px;
        }
        & > div:last-child {
          padding-bottom: 10px;
        }
      }
    }
  }
}
</style>
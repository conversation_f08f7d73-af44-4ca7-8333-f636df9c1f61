﻿/**
 * @description 公共方法，跳转不同地址
 * 教师空间地址：/teachermain/
 * 后台管理地址：/console/
 * 区域站点地址：/region/
 * 名师工作室地址：/teacherstudio/
 * 用户空间地址：/userspace/
 * 公共详情页地址：/common/
 */

import store from "@/store";
import {addFsUrl} from "@/library/src/utils/globalFunction";

declare const _MK_:any

const windOpen = (url:string,target:string)=>{
  if( target === '_blank' ){
    const a:any = document.createElement('a')
    a.href = url
    a.target = '_blank'
    a.click()
  }else{
    window.open(url, target);
  }
}

const getConsolePath = (pathname: string, target: string) => {
  // 小窗空间
  let url = `${addFsUrl('aliba/resource/region/html/console.html')}?v=${Date.now()}&userId=${
      store.state.user.userId
  }&toPath=${encodeURIComponent(pathname)}`;
  windOpen(url, target);
};

const getUrlObj:any = () =>{
  // 是否乾坤模式下打开
  const isQianKunModel = true

  const urlConfig:any =  {
    development: {
      teaSpaceUrl: "http://localhost:7702/teachermain/",
      consoleUrl: "http://localhost:5500/console/",
      platformconsoleUrl: "http://localhost:9902/platformconsole/",
      regionUrl: "http://localhost:7802",
      teacherStudioUrl: "http://localhost:7705/teacherstudio/",
      userSpaceUrl: "http://localhost:7706/userspace/",
      commonUrl: isQianKunModel
          ? `${location.origin}/${store.getters.entry}/common/`
          : "http://localhost:7713/common/#/",
      regionConsoleUrl: "http://localhost:7805/console/#/",
      iframeUrl: "http://localhost:7802/region/iframe/",
      JLWXLoginUrl: "http://jlwxssotest.iclass30.com/index.html",
      JLWXLogoutUrl: "http://jlwxssotest.iclass30.com/common_sso_logout_test.html",

    },
    production: {
      teaSpaceUrl: location.origin + "/teachermain/",
      consoleUrl: location.origin + "/console/",
      platformconsoleUrl: location.origin + "/platformconsole/",
      regionUrl: location.origin,
      teacherStudioUrl: location.origin + "/teacherstudio/",
      userSpaceUrl: location.origin + "/userspace/",
      commonUrl: isQianKunModel
          ? `${location.origin}/${store.getters.entry}/common/`
          : location.origin + "/common/",
      regionConsoleUrl: location.origin + "/console/",
      iframeUrl: location.origin + "/iframe/",
      JLWXLoginUrl: "https://jlwx.jsnje.cn/login/common_sso_login.html",
      JLWXLogoutUrl: "https://jlwx.jsnje.cn/login/common_sso_logout.html",
    },
  };
  return urlConfig[process.env.NODE_ENV || "production"];
}

const convertPath = (path: string, source?: string) => {
  if (!source) {
    return path;
  }
  let parmas = `source=${source}`;
  if (path.indexOf(parmas) !== -1) {
    return path;
  }
  if (/\?/g.test(path)) {
    path = path + `&${parmas}`;
  } else {
    path = path + `?${parmas}`;
  }
  return path;
};

/**
 * @name: 获取当前菜单索引
 */
const getMenuIndex = ()=>{
  try {
    let meunIndex = _MK_.store.app.headerMenuIndex || sessionStorage.getItem('mk_menu_index')
    if( typeof meunIndex === 'undefined' || meunIndex === null){
      return null
    }
    return meunIndex
  }catch (e) {
    return null
  }
}


/**
 * @name: 跳转管理平台
 */
export const goManage = ()=>{
  location.href = '//yun.xckj.net'
}

/**
 * @name 跳转教师空间地址
 * @param pathname:子级路由
 * @param target:打开方式，默认新窗口打开
 */
export const goTeaSpace = (
    pathname: string = "",
    source?: string,
    target?: string
) => {
  const urlObj = getUrlObj();
  if (urlObj) {
    if (!target) {
      target = source || "_blank";
      source = "region";
    }
    let url = urlObj.teaSpaceUrl;
    url = convertPath(`${url}${pathname}`, source);
    windOpen(url, target);
  }
};
/**
 * @name 跳转后台管理地址
 * @param pathname:子级路由
 * @param target:打开方式，默认新窗口打开
 */
export const goConsole = (pathname: string = "", target: string = "_blank") => {
  const urlObj = getUrlObj();
  if (urlObj) {
    let url = urlObj.consoleUrl;

    return getConsolePath(url + pathname, target);
  }
};
/**
 * @name: 跳转中控后台地址
 * @param pathname:子级路由
 * @param target:打开方式，默认新窗口打开
 */
export const goPlatformConsole = (
    pathname: string = "",
    target: string = "_blank"
) => {
  const urlObj = getUrlObj();
  if (urlObj) {
    let url = urlObj.platformconsoleUrl;
    windOpen(url + pathname, target);
  }
};
/**
 * @name 跳转平台地址
 * @param pathname:子级路由
 * @param source:平台来源
 * @param target:打开方式，默认新窗口打开
 */
export const goRegion = (
    pathname: string = "regionside/",
    source?: string,
    target?: string
) => {
  const urlObj = getUrlObj();
  if (urlObj) {
    if (!target) {
      target = source || "_blank";
      source = "region";
    }
    let url = `${urlObj.regionUrl}/${source}/`;
    return getConsolePath(url + pathname, target);
  }
};
/**
 * @name 跳转名师工作室地址
 * @param pathname:子级路由
 * @param target:打开方式，默认新窗口打开
 */
export const goTeacherStudio = (
    pathname: string = "",
    source?: string,
    target?: string
) => {
  const urlObj = getUrlObj();
  if (urlObj) {
    if (!target) {
      target = source || "_blank";
      source = "region";
    }
    let url = urlObj.teacherStudioUrl;
    url = convertPath(`${url}${pathname}`, source);
    windOpen(url, target);
  }
};
/**
 * @name 跳转用户空间地址
 * @param pathname:子级路由
 * @param target:打开方式，默认新窗口打开
 */
export const goUserSpace = (
    pathname: string = "",
    source?: string,
    target?: string
) => {
  const urlObj = getUrlObj();
  if (urlObj) {
    if (!target) {
      target = source || "_blank";
      source = "region";
    }
    let url = urlObj.userSpaceUrl;
    url = convertPath(`${url}${pathname}`, source);
    windOpen(url, target);
  }
};
/**
 * @name 跳转公共详情页地址
 * @param pathname:子级路由
 * @param target:打开方式，默认新窗口打开
 */
export const goCommonDetail = (
    pathname: string = "",
    target: string = "_blank"
) => {
  const urlObj = getUrlObj();
  if (urlObj) {
    let url = urlObj.commonUrl;
    url = url + pathname
    const menuIndex = getMenuIndex()
    if( menuIndex !== null ){
      if (/\?/g.test(url)) {
        url = url + `&menuindex=${menuIndex}`;
      } else {
        url = url + `?menuindex=${menuIndex}`;
      }
    }
    windOpen(url, target);
  }
};
/**
 * @name 跳转区域console地址
 * @param pathname:子级路由
 * @param target:打开方式，默认新窗口打开
 */
export const goRegionConsole = (
    pathname: string = "",
    target: string = "_blank"
) => {
  const urlObj = getUrlObj();
  if (urlObj) {
    let url = urlObj.regionConsoleUrl;
    return getConsolePath(url + pathname, target);
  }
};
/**
 * @name 跳转iframe地址
 * @param pathname:子级路由
 * @param target:打开方式，默认新窗口打开
 */
export const goIframe = (pathname: string = "", target: string = "_self") => {
  const urlObj = getUrlObj();
  if (urlObj) {
    let url = urlObj.iframeUrl;
    windOpen(url + pathname, target);
  }
};
/**
 * @name: 金陵微校登录
 */
export const goJLWXLogin = (
    returnUrl: string = location.origin,
    isTest: boolean = false,
    target: string = "_self"
) => {
  const urlObj = getUrlObj();
  if (urlObj) {
    let url = urlObj.JLWXLoginUrl;
    windOpen(`${url}?returnUrl=${returnUrl}`, target);
  }
};
/**
 * @name: 金陵微校登出
 */
export const goJLWXLogout = (
    returnUrl: string = location.origin,
    isTest: boolean = false,
    target: string = "_self"
) => {
  const urlObj = getUrlObj();
  if (urlObj) {
    let url = urlObj.JLWXLogoutUrl;
    windOpen(`${url}?returnUrl=${returnUrl}`, target);
  }
};

@import "./variables.scss";

// 混合方法
@mixin theme_color($important: true) {
  @if ($important) {
    color: $mainColor-red !important;
    [data-theme="red"] & {
      color: $mainColor-red !important;
    }
    [data-theme="cyan"] & {
      color: $mainColor-cyan !important;
    }
    [data-theme="yellow"] & {
      color: $mainColor-yellow !important;
    }
    [data-theme="blue"] & {
      color: $mainColor-blue !important;
    }
    [data-theme="green"] & {
      color: $mainColor-green !important;
    }
    [data-theme="purple"] & {
      color: $mainColor-purple !important;
    }
    [data-theme="seablue"] & {
      color: $mainColor-seablue !important;
    }
    [data-theme="weekblue"] & {
      color: $mainColor-weekblue !important;
    }
    [data-theme="darkreen"] & {
      color: $mainColor-darkreen !important;
    }
    [data-theme="deepblue"] & {
      color: $mainColor-deepblue !important;
    }
  }
  @else {
    color: $mainColor-red;
    [data-theme="red"] & {
      color: $mainColor-red;
    }
    [data-theme="cyan"] & {
      color: $mainColor-cyan;
    }
    [data-theme="yellow"] & {
      color: $mainColor-yellow;
    }
    [data-theme="blue"] & {
      color: $mainColor-blue;
    }
    [data-theme="green"] & {
      color: $mainColor-green;
    }
    [data-theme="purple"] & {
      color: $mainColor-purple;
    }
    [data-theme="seablue"] & {
      color: $mainColor-seablue;
    }
    [data-theme="weekblue"] & {
      color: $mainColor-weekblue;
    }
    [data-theme="darkreen"] & {
      color: $mainColor-darkreen;
    }
    [data-theme="deepblue"] & {
      color: $mainColor-deepblue;
    }
  }
}

@mixin theme_border-color {
  border-color: $mainColor-red;
  [data-theme="red"] & {
    border-color: $mainColor-red;
  }
  [data-theme="cyan"] & {
    border-color: $mainColor-cyan;
  }
  [data-theme="yellow"] & {
    border-color: $mainColor-yellow;
  }
  [data-theme="blue"] & {
    border-color: $mainColor-blue;
  }
  [data-theme="green"] & {
    border-color: $mainColor-green;
  }
  [data-theme="purple"] & {
    border-color: $mainColor-purple;
  }
  [data-theme="seablue"] & {
    border-color: $mainColor-seablue;
  }
  [data-theme="weekblue"] & {
    border-color: $mainColor-weekblue;
  }
  [data-theme="darkreen"] & {
    border-color: $mainColor-darkreen;
  }
  [data-theme="deepblue"] & {
    border-color: $mainColor-deepblue;
  }
}

@mixin theme_background-color() {
  background-color: $mainColor-red;
  [data-theme="red"] & {
    background-color: $mainColor-red;
  }
  [data-theme="cyan"] & {
    background-color: $mainColor-cyan;
  }
  [data-theme="yellow"] & {
    background-color: $mainColor-yellow;
  }
  [data-theme="blue"] & {
    background-color: $mainColor-blue;
  }
  [data-theme="green"] & {
    background-color: $mainColor-green;
  }
  [data-theme="purple"] & {
    background-color: $mainColor-purple;
  }
  [data-theme="seablue"] & {
    background-color: $mainColor-seablue;
  }
  [data-theme="weekblue"] & {
    background-color: $mainColor-weekblue;
  }
  [data-theme="darkreen"] & {
    background-color: $mainColor-darkreen;
  }
  [data-theme="deepblue"] & {
    background-color: $mainColor-deepblue;
  }
}

@mixin theme_hover_background-color() {
  background-color: $mainColor-hover-red;
  [data-theme="red"] & {
    background-color: $mainColor-hover-red;
  }
  [data-theme="cyan"] & {
    background-color: $mainColor-hover-cyan;
  }
  [data-theme="yellow"] & {
    background-color: $mainColor-hover-yellow;
  }
  [data-theme="blue"] & {
    background-color: $mainColor-hover-blue;
  }
  [data-theme="green"] & {
    background-color: $mainColor-hover-green;
  }
  [data-theme="purple"] & {
    background-color: $mainColor-hover-purple;
  }
  [data-theme="seablue"] & {
    background-color: $mainColor-hover-seablue;
  }
  [data-theme="weekblue"] & {
    background-color: $mainColor-hover-weekblue;
  }
  [data-theme="darkreen"] & {
    background-color: $mainColor-hover-darkreen;
  }
  [data-theme="deepblue"] & {
    background-color: $mainColor-hover-deepblue;
  }
}

@mixin theme_border-bottom-color() {
  border-bottom-color: $mainColor-red;
  [data-theme="red"] & {
    border-bottom-color: $mainColor-red;
  }
  [data-theme="cyan"] & {
    border-bottom-color: $mainColor-cyan;
  }
  [data-theme="yellow"] & {
    border-bottom-color: $mainColor-yellow;
  }
  [data-theme="blue"] & {
    border-bottom-color: $mainColor-blue;
  }
  [data-theme="green"] & {
    border-bottom-color: $mainColor-green;
  }
  [data-theme="purple"] & {
    border-bottom-color: $mainColor-purple;
  }
  [data-theme="seablue"] & {
    border-bottom-color: $mainColor-seablue;
  }
  [data-theme="weekblue"] & {
    border-bottom-color: $mainColor-weekblue;
  }
  [data-theme="darkreen"] & {
    border-bottom-color: $mainColor-darkreen;
  }
  [data-theme="deepblue"] & {
    border-bottom-color: $mainColor-deepblue;
  }
}

@mixin theme_border-top-color() {
  border-top-color: $mainColor-red;
  [data-theme="red"] & {
    border-top-color: $mainColor-red;
  }
  [data-theme="cyan"] & {
    border-top-color: $mainColor-cyan;
  }
  [data-theme="yellow"] & {
    border-top-color: $mainColor-yellow;
  }
  [data-theme="blue"] & {
    border-top-color: $mainColor-blue;
  }
  [data-theme="green"] & {
    border-top-color: $mainColor-green;
  }
  [data-theme="purple"] & {
    border-top-color: $mainColor-purple;
  }
  [data-theme="seablue"] & {
    border-top-color: $mainColor-seablue;
  }
  [data-theme="weekblue"] & {
    border-top-color: $mainColor-weekblue;
  }
  [data-theme="darkreen"] & {
    border-top-color: $mainColor-darkreen;
  }
  [data-theme="deepblue"] & {
    border-top-color: $mainColor-deepblue;
  }
}

@mixin theme_border($width) {
  border: $width solid $mainColor-red;
  [data-theme="red"] & {
    border: $width solid $mainColor-red;
  }
  [data-theme="cyan"] & {
    border: $width solid $mainColor-cyan;
  }
  [data-theme="yellow"] & {
    border: $width solid $mainColor-yellow;
  }
  [data-theme="blue"] & {
    border: $width solid $mainColor-blue;
  }
  [data-theme="green"] & {
    border: $width solid $mainColor-green;
  }
  [data-theme="purple"] & {
    border: $width solid $mainColor-purple;
  }
  [data-theme="seablue"] & {
    border: $width solid $mainColor-seablue;
  }
  [data-theme="weekblue"] & {
    border: $width solid $mainColor-weekblue;
  }
  [data-theme="darkreen"] & {
    border: $width solid $mainColor-darkreen;
  }
  [data-theme="deepblue"] & {
    border: $width solid $mainColor-deepblue;
  }
}

@mixin theme_border-top($width) {
  border-top: $width solid $mainColor-red;
  [data-theme="red"] & {
    border-top: $width solid $mainColor-red;
  }
  [data-theme="cyan"] & {
    border-top: $width solid $mainColor-cyan;
  }
  [data-theme="yellow"] & {
    border-top: $width solid $mainColor-yellow;
  }
  [data-theme="blue"] & {
    border-top: $width solid $mainColor-blue;
  }
  [data-theme="green"] & {
    border-top: $width solid $mainColor-green;
  }
  [data-theme="purple"] & {
    border-top: $width solid $mainColor-purple;
  }
  [data-theme="seablue"] & {
    border-top: $width solid $mainColor-seablue;
  }
  [data-theme="weekblue"] & {
    border-top: $width solid $mainColor-weekblue;
  }
  [data-theme="darkreen"] & {
    border-top: $width solid $mainColor-darkreen;
  }
  [data-theme="deepblue"] & {
    border-top: $width solid $mainColor-deepblue;
  }
}

@mixin theme_border-bottom($width) {
  border-bottom: $width solid $mainColor-red;
  [data-theme="red"] & {
    border-bottom: $width solid $mainColor-red;
  }
  [data-theme="cyan"] & {
    border-bottom: $width solid $mainColor-cyan;
  }
  [data-theme="yellow"] & {
    border-bottom: $width solid $mainColor-yellow;
  }
  [data-theme="blue"] & {
    border-bottom: $width solid $mainColor-blue;
  }
  [data-theme="green"] & {
    border-bottom: $width solid $mainColor-green;
  }
  [data-theme="purple"] & {
    border-bottom: $width solid $mainColor-purple;
  }
  [data-theme="seablue"] & {
    border-bottom: $width solid $mainColor-seablue;
  }
  [data-theme="weekblue"] & {
    border-bottom: $width solid $mainColor-weekblue;
  }
  [data-theme="darkreen"] & {
    border-bottom: $width solid $mainColor-darkreen;
  }
  [data-theme="deepblue"] & {
    border-bottom: $width solid $mainColor-deepblue;
  }
}

@mixin theme_border-left($width) {
  border-left: $width solid $mainColor-red;
  [data-theme="red"] & {
    border-left: $width solid $mainColor-red;
  }
  [data-theme="cyan"] & {
    border-left: $width solid $mainColor-cyan;
  }
  [data-theme="yellow"] & {
    border-left: $width solid $mainColor-yellow;
  }
  [data-theme="blue"] & {
    border-left: $width solid $mainColor-blue;
  }
  [data-theme="green"] & {
    border-left: $width solid $mainColor-green;
  }
  [data-theme="purple"] & {
    border-left: $width solid $mainColor-purple;
  }
  [data-theme="seablue"] & {
    border-left: $width solid $mainColor-seablue;
  }
  [data-theme="weekblue"] & {
    border-left: $width solid $mainColor-weekblue;
  }
  [data-theme="darkreen"] & {
    border-left: $width solid $mainColor-darkreen;
  }
  [data-theme="deepblue"] & {
    border-left: $width solid $mainColor-deepblue;
  }
}

@mixin theme_border-right($width) {
  border-right: $width solid $mainColor-red;
  [data-theme="red"] & {
    border-right: $width solid $mainColor-red;
  }
  [data-theme="cyan"] & {
    border-right: $width solid $mainColor-cyan;
  }
  [data-theme="yellow"] & {
    border-right: $width solid $mainColor-yellow;
  }
  [data-theme="blue"] & {
    border-right: $width solid $mainColor-blue;
  }
  [data-theme="green"] & {
    border-right: $width solid $mainColor-green;
  }
  [data-theme="purple"] & {
    border-right: $width solid $mainColor-purple;
  }
  [data-theme="seablue"] & {
    border-right: $width solid $mainColor-seablue;
  }
  [data-theme="weekblue"] & {
    border-right: $width solid $mainColor-weekblue;
  }
  [data-theme="darkreen"] & {
    border-right: $width solid $mainColor-darkreen;
  }
  [data-theme="deepblue"] & {
    border-right: $width solid $mainColor-deepblue;
  }
}
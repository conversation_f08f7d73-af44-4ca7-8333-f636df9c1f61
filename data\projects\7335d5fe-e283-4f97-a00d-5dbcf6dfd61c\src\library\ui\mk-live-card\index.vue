<template>
  <el-row class="mk-live-card" :gutter="16">
    <el-col
      class="mk-live-card-item"
      v-for="(item, index) in dataList"
      :key="index"
      :span="6"
    >
      <!-- 直播信息 -->
      <div class="mk-live-card-info">
        <!--直播名称-->
        <p class="mk-live-card-title ellipsis">{{ item.title }}</p>

        <!--教师以及学校名称-->
        <div
          class="mk-live-card-name"
          v-if="item.teacherName || item.schoolName"
        >
          <span class="teacher-name" v-if="item.teacherName">{{
            item.teacherName
          }}</span>
          <template v-if="item.schoolName">
            <span class="split-line">|</span>
            <span class="school-name">{{ item.schoolName }}</span>
          </template>
        </div>

        <el-row type="flex" align="middle" justify="space-between">
          <div class="mk-live-card-subject">{{ item.subjectName }}</div>
          <div class="mk-live-card-time">
            <span class="mk-live-card-dot"></span>{{ item.subInfo }}开始
          </div>
        </el-row>
      </div>
    </el-col>
  </el-row>
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue';

export interface ILive {
  /** 直播名称 */
  title: string;
  /** 教师名称 */
  teacherName: string;
  /** 学校名称 */
  schoolName: string;
  /** 学科名称 */
  subjectName: string;
  /** 开始时间 */
  subInfo: string;
}

export default defineComponent({
  name: 'mk-live-card',

  emits: ['goto-more'],

  props: {
    // 数据列表
    dataList: {
      type: [] as PropType<ILive[]>,
      required: true
    }
  }
});
</script>

<style lang="scss" scoped>
.mk-live-card {
  .mk-live-card-item {
    width: 288px;
    margin-bottom: 15px;
    cursor: pointer;
    .mk-live-card-info {
      width: 100%;
      padding: 22px 30px;
      background: #fff;
      border-radius: 4px;
      .mk-live-card-title {
        font-weight: bold;
        color: #333;
        font-size: 16px;
        line-height: 30px;
      }
      .mk-live-card-name {
        margin: 10px 0;
        .teacher-name {
          font-size: 14px;
          color: #333;
        }
        .split-line {
          color: #c6c6c6;
          margin: 0 12px;
        }
        .school-name {
          font-size: 12px;
          color: #666;
        }
      }
      .mk-live-card-subject {
        font-size: 12px;
        font-weight: 400;
        color: #ffffff;
        padding: 4px 6px;
        background: #ccc;
        border-radius: 3px;
        margin-top: 5px;
      }
      .mk-live-card-time {
        background-color: #f5f5f5;
        padding: 4px 6px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        margin-top: 5px;
        .mk-live-card-dot {
          width: 5px;
          height: 5px;
          border-radius: 50%;
          background-color: #999;
          margin-right: 5px;
        }
      }
    }
  }
}
</style>
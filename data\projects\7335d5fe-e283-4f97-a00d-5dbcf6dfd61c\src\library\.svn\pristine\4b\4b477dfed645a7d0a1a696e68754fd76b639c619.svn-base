﻿<template>
  <div class="group-user-list">
    <el-button
      style="margin-left: 10px"
      type="text"
      @click="showEditDialog(null)"
      v-if="groupList.length < 20"
      >+创建群组</el-button
    >
    <ul v-if="groupList.length" class="group-content">
      <el-row v-for="item in groupList" :key="item.groupId">
        <el-col :span="22">
          <el-checkbox-group v-model="checkList" @change="switchGroup">
            <el-checkbox
              class="group-check-item"
              :label="item"
              :disabled="disabledKeys.includes(item.groupId)"
            >
              <li class="group-item">
                <el-row type="flex" align="middle" justify="between">
                  <el-row
                    type="flex"
                    align="middle"
                    justify="start"
                    style="flex-wrap: nowrap"
                  >
                    <el-avatar
                      :src="require('../assets/group.png')"
                      shape="square"
                      style="margin-right: 5px;background-color:#fff;"
                    ></el-avatar>

                    <div class="group-name">
                      <p :title="item.groupName">
                        {{ item.groupName }}
                        <span style="color:#999">({{ item.peopleNum }}人)</span>
                      </p>
                      <p>{{ item.createDate }}</p>
                    </div>
                  </el-row>
                </el-row>
              </li>
            </el-checkbox>
          </el-checkbox-group>
        </el-col>
        <el-col :span="2">
          <el-dropdown style="margin-top: 15px">
            <span class="el-dropdown-link" style="cursor: pointer">
              <i class="icon iconfont icongengduo"></i>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click.native="showEditDialog(item)"
                  >编辑</el-dropdown-item
                >
                <el-dropdown-item @click.native="deleteGroup(item)"
                  >删除</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </el-col>
      </el-row>
    </ul>
    <mk-no-data v-else></mk-no-data>
    <mk-confirm-dialog v-if="isShowDeleteDialog" @on-confirm="confirmDelete" @on-cancle="isShowDeleteDialog = false; "
      :title="'提示'">
      <template #customTipSlot>
        <div>确定删除该群组吗?</div>
      </template>
    </mk-confirm-dialog>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import { IGlobalState } from "@/library/src/store";
import { useStore } from "vuex";
import {
  delRegionGroupAPI,
  editRegionGroupAPI,
  getRegionGroupRelationListAPI,
  getUserRegionGroupListAPI
} from "@/library/src/service/API/tearesearch";
import { ElMessage } from "element-plus";

export default defineComponent({
  name: "group-user",

  emits: ["on-edit-group", "switch-group", "updata-group"],

  props: {
    // 已选部门列表
    group: {
      type: Array,
      default: () => []
    },
    // 禁用列表
    disabledKeys: {
      type: Array,
      default: () => []
    }
  },

  setup(props, ctx) {
    const store = useStore<IGlobalState>();
    const state = reactive({
      // 群组列表
      groupList: [] as any[],
      // 当前群组
      defaultGroupOptions: {
        isShow: false,
        groupName: "",
        groupId: ""
      },
      // 已选列表
      checkList: [] as any[],

      defaultGroupUserAPIParams: {
        keyWord: "",
        page: 1,
        limit: 10000
      },
      // 已选群组用户集合
      groupUserMap: new Map(),
      //是否展示删除弹窗
      isShowDeleteDialog:false,
      groupId:''
    });

    /**
     * @name: 获取群组列表
     */
    const getGroupList = async () => {
      const res = await getUserRegionGroupListAPI({
        userId: store.state.user.userId,
        keyWord: ""
      });
      state.groupList = res.code === 1 ? res.data : [];
      //更新群组
      ctx.emit("updata-group");
      // 更新已选群组
      let checkList: any[] = [];
      if (props.group.length) {
        props.group.forEach((item: any) => {
          const group = state.groupList.find((sitem: any) => {
            return item.groupId === sitem.groupId;
          });
          if (group) {
            checkList.push(group);
          }
        });
      }
      state.checkList = checkList;
    };
    /**
     * @name: 展示编辑弹窗
     * @param item 分组对象
     */
    const showEditDialog = async (item: any) => {
      let groupUserList: any[] = [];
      if (item !== null) {
        if (state.groupUserMap.has(item.groupId)) {
          groupUserList = state.groupUserMap.get(item.groupId);
        } else {
          groupUserList = await getGroupUserList(item.groupId);
        }
      }
      ctx.emit("on-edit-group", item, groupUserList);
    };
    /**
     * @name: 重命名
     */
    const renameGroup = async () => {
      const groupName = state.defaultGroupOptions.groupName.trim();

      if (groupName === "") {
        ElMessage.warning("请输入群组名称!");
        return;
      }
      const res = await editRegionGroupAPI({
        groupName: state.defaultGroupOptions.groupName,
        groupId: state.defaultGroupOptions.groupId
      });
      if (res.code === 1) {
        await getGroupList();
      }
    };
    /**
     * @name: 删除群组
     */
    const deleteGroup = (item: any) => {
      state.groupId=item.groupId;
      state.isShowDeleteDialog=true
    };
    /**
     * @name 确认删除
     */
    const confirmDelete=async ()=>{
      const res = await delRegionGroupAPI({
          groupId: state.groupId
        });
        if (res.code === 1) {
          await getGroupList();
        }
        state.isShowDeleteDialog=false;
    }
    /**
     * @name: 选择群组
     * @param checkList 已选择群组列表
     */
    const switchGroup = (checkList: any[]) => {
      ctx.emit("switch-group", checkList);
    };
    /**
     * @name: 重置已选群组
     * @param ids 已选群组id列表
     */
    const resetGroup = (ids: any[]) => {
      state.checkList =
        ids.length === 0
          ? []
          : state.checkList.filter((item: any) => {
              return !ids.includes(item.groupId);
            });
    };
    /**
     * @name: 获取群组用户
     */
    const getGroupUserList = async (groupId: string) => {
      if (state.groupUserMap.has(groupId)) {
        return state.groupUserMap.get(groupId);
      }
      const res = await getRegionGroupRelationListAPI({
        groupId,
        ...state.defaultGroupUserAPIParams
      });
      if (res.code === 1) {
        const userList = res.data.rows.map((item: any) => {
          item.realName = item.userName;
          item.groupId = groupId;
          return item;
        });
        state.groupUserMap.set(groupId, userList);
        return userList;
      } else {
        return [];
      }
    };
    /**
     * @name: 清空
     */
    const clear = () => {
      state.groupUserMap.clear();
      state.checkList = [];
    };
    /**
     * @name: 删除用户
     * @param item 当前用户
     */
    const deleteUser = (item: any) => {
      let userList = state.groupUserMap.get(item.groupId);
      if (userList) {
        userList = userList.filter((sitem: any) => {
          return sitem.userId !== item.userId;
        });
        if (userList.length === 0) {
          state.groupUserMap.delete(item.groupId);
          state.checkList = state.checkList.filter((sitem: any) => {
            return sitem.groupId !== item.groupId;
          });
        } else {
          state.groupUserMap.set(item.groupId, userList);
        }
      }
    };

    onMounted(() => {
      getGroupList();
    });
    return {
      ...toRefs(state),
      showEditDialog,
      getGroupList,
      renameGroup,
      deleteGroup,
      switchGroup,
      resetGroup,
      clear,
      deleteUser,
      confirmDelete
    };
  }
});
</script>

<style lang="scss" scoped>
.group-user-list {
  border: 1px solid #e6e8ed;
  border-radius: 4px;
  margin: 10px 0;
  .user-tree {
    height: 270px;
    overflow: auto;
  }
}
.group-content {
  height: 380px;
  overflow-y: auto;
}

.group-item {
  .group-name {
    display: inline-block;
    p {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 280px;
    }
  }
  span {
    display: inline-block;
    max-width: 95%;
  }
  &:hover {
    background-color: #f5f7fa;
  }
  .iconguanbi {
    font-size: 12px;
    cursor: pointer;
    &:hover {
      color: red;
    }
  }
}
</style>
<style lang="scss" >
.group-check-item {
  padding: 10px;
  width: calc(100% - 30px);
  display: flex;
  align-items: center;
  justify-content: flex-start;
  .el-checkbox__label {
    width: 100%;
  }
}
</style>

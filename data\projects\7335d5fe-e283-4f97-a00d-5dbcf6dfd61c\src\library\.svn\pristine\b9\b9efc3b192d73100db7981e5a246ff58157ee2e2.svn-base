@import "./animate/animate";

.previewSlider {
  /* 预览弹窗 */
  .preview-swiper, .preview-swiper-cover {
    position : fixed;
    left     : 0;
    right    : 0;
    z-index  : 101;
  }
  .preview-swiper-cover {
    top              : 0;
    bottom           : 0;
    z-index          : 100;
    background-color : rgba(0, 0, 0, .5);
  }
  .preview-swiper {
    &.gallery-top {
      top    : 0;
      height : 82%;
      width  : 100%;
      .swiper-slide {
        img {
          max-height : 70%;
          max-width  : 85%;
          z-index:2
        }
      }
      .swiper-rorate-box {
        transition-duration : 300ms;
        width               : 100%;
        height              : 100%;
        justify-content     : center;
        align-items         : center;
        display             : flex;
      }
      .swiper-tanslate-box {
        width  : 100%;
        height : 100%;
      }
    }
    &.gallery-thumbs {
      bottom           : 0;
      height           : 18%;
      box-sizing       : border-box;
      padding          : 10px 0;
      background-color : rgba(0, 0, 0, .5);
      .swiper-slide {
        height           : 100%;
        background-color : #ccc;
        cursor           : pointer;
        .thumb-box {
          background-size     : cover;
          background-position : center;
          width               : 100%;
          height              : 100%;
          opacity             : 0.25;
        }
      }
      .swiper-slide-active {
        .thumb-box {
          opacity : 1;
          border  : 2px solid #0199ff;
        }
      }
    }
    .swiper-pagination {
      color : #fff;
    }
  }
  .swiper-imageTool {
    position          : fixed;
    left              : 50%;
    bottom            : 19%;
    background-color  : rgba(0, 0, 0, .5);
    border-radius     : 50px;
    padding           : 0 5PX;
    -webkit-transform : translateX(-50%);
    -moz-transform    : translateX(-50%);
    -ms-transform     : translateX(-50%);
    -o-transform      : translateX(-50%);
    transform         : translateX(-50%);
    overflow          : hidden;
    z-index           : 1000;
    .preview-pagination {
      float       : left;
      line-height : 40PX;
      color       : #fff;
      padding     : 0 15PX;
      width       : auto;
      bottom      : 0;
      font-size   : 18PX;
    }
    .icon-imageTools {
      float  : left;
      width  : 46PX;
      height : 40PX;
      cursor : pointer;
      &:active {
        opacity : .4;
      }
      &.swiper-button-disabled {
        opacity : .2;
        cursor  : default;
      }
      &.icon-pre {
        background-position : -85PX 11PX;
        border-right        : 1px solid rgba(255, 255, 255, .17);
      }
      &.icon-zoomin {
        background-position : 14PX 11PX;
      }
      &.icon-zoomout {
        background-position : -35PX 11PX;
      }
      &.icon-recover {
        background-position : -140PX 11PX;
      }
      &.icon-close {
        background-position : -346PX 11PX;
      }
      &.icon-rotateL {
        background-position : -246PX 11PX;
      }
      &.icon-rotateR {
        background-position : -295PX 11PX;
      }
      &.icon-next {
        background-position : -195PX 11PX;
        border-left         : 1px solid rgba(255, 255, 255, .17);
      }
    }
  }
  /* 弹窗组件 */
  .modal-cover {
    position         : fixed;
    top              : 0;
    left             : 0;
    z-index          : 100;
    width            : 100%;
    height           : 100%;
    background-color : rgba(0, 0, 0, .4);
  }
  .down-modal-box, .video-modal-box {
    position      : fixed;
    top           : 50%;
    left          : 50%;
    border-radius : 5px;
    box-shadow    : 0 1px 0 rgba(0, 0, 0, .1);
    z-index       : 101;
  }
  .down-modal-box {
    width            : 800px;
    height           : 520px;
    margin           : -260px 0 0 -400px;
    background-color : #fafafa;
  }
  .video-modal-box {
    width             : 60%;
    -webkit-transform : translate(-50%, -50%);
    transform         : translate(-50%, -50%);
    video {
      display          : block;
      width            : 100%;
      max-height       : 800px;
      background-color : #000;
      border-radius    : 5px;

      @media (max-height : 800px) {
        max-height : 600px;
      }
      @media (max-height : 680px) {
        max-height : 500px;
      }
      @media (max-height : 500px) {
        max-height : 320px;
      }
    }
    .playback-rate-select {
      opacity: 0;
      transition:opacity 1s;
      position: absolute;
      bottom: 39px;
      right: 146px;
      background: transparent;
      border: 1px solid #fff;
      color: #fff;
      border-radius: 4px;
      .playback-rate-item {
        color: #333;
      }
    }
  }
  .video-modal-box:hover .video-playback-rate{
    display: block;
  }
  .video-close-btn {
    position : absolute;
    left     : 100%;
    bottom   : 26px;
    width    : 64PX;
    height   : 64PX;
    cursor   : pointer;
  }
}

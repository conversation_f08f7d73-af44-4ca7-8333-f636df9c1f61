@charset "UTF-8";
/*!
Animate.css - http://daneden.me/animate
Licensed under the MIT license - http://opensource.org/licenses/MIT
Copyright (c) 2015 <PERSON>
*/

body {
	-webkit-backface-visibility: hidden; // Addresses a small issue in webkit: https://css-tricks.com/almanac/properties/b/backface-visibility/
}

.animated {
  	@include animate-prefixer(animation-duration, $base-duration);
  	@include animate-prefixer(animation-fill-mode, $base-fill-mode);

	&.infinite {
	  animation-iteration-count: $base-iteration-count;
	}

	&.hinge {
	  @include animate-prefixer(animation-duration, $base-duration * 2);
	}

	&.bounceIn,
	&.bounceOut {
	  @include animate-prefixer(animation-duration, $base-duration * 0.75);
	}

	&.flipOutX,
	&.flipOutY {
	  @include animate-prefixer(animation-duration, $base-duration * 0.75);
	}
}
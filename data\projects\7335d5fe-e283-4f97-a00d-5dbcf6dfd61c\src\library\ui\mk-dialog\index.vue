<template>
  <el-dialog
    :title="title"
    :modal="modal"
    :model-value="isShowDialog"
    :width="dialogWidth"
    :fullscreen="fullScreenState"
    :show-close="showClose"
    :custom-class="customClass"
    :append-to-body="appendTobody"
    :close-on-click-modal="clickDialogClose"
    destroy-on-close
    :before-close="closeDialog"
  >
    <!--弹框头部-->
    <template #title>
      <div class="dialog-header">
        <div class="header-left">
          <slot name="title">
            <span>{{ title }}</span>
          </slot>
        </div>
        <div class="header-right">
          <i
            v-if="isFull"
            class="mk-dialog-full-screen-icon"
            :class="
              fullScreenState
                ? 'mk-dialog-full-screen-small-icon'
                : 'mk-dialog-full-screen-big-icon'
            "
            :title="fullScreenState ? '点击取消全屏' : '点击全屏'"
            v-on:click="closeFullScreenState(!fullScreenState)"
          ></i>
          <i v-if="isClose" class="mk-dialog-close-icon" v-on:click="closeDialog"></i>
        </div>
      </div>
    </template>
    <!--弹框内容-->
    <slot name="customSlot"></slot>
    <!--弹框底部-->
    <template #footer v-if="isShowFooter">
      <div class="content-footer">
        <!--是否自定义底部按钮-->
        <template v-if="!isCustomFooter">
          <span class="dialog-footer">
            <el-button @click="closeDialog">
              <slot name="cancleText">取 消</slot>
            </el-button>
            <el-button
              type="primary"
              @click="sureClick"
              :loading="sureBtnLoading"
            >
              <slot name="confirmText">确 定</slot>
            </el-button>
          </span>
        </template>
        <template v-else>
          <slot name="customFooterSlot"></slot>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  watch,
  nextTick,
  onMounted
} from 'vue';
import DragDrop from '@/library/ui/mk-dialog/DragDrop';
export default defineComponent({
  name: 'mk-dialog',

  props: {
    //是否显示全屏图标
    isFull: {
      type: Boolean,
      default: true
    },
    isClose: {
      type: Boolean,
      default: true
    },
    // 是否显示弹框
    isShowDialog: {
      type: Boolean,
      default: false
    },
    // 是否显示底部
    isShowFooter: {
      type: Boolean,
      default: true
    },
    // 是否显示遮罩
    modal: {
      type: Boolean,
      default: true
    },
    //弹框标题
    title: {
      type: String,
      default: ''
    },
    // 自定义类名
    customClass: {
      type: String,
      default: ''
    },
    //弹框宽度
    dialogWidth: {
      type: String,
      default: '30%'
    },
    //确定按钮的load
    sureBtnLoading: {
      type: Boolean,
      default: false
    },
    //弹框是否全屏-通过参数默认
    fullScreen: {
      type: Boolean,
      default: false
    },
    //是否自定义底部按钮
    isCustomFooter: {
      type: Boolean,
      default: false
    },
    // 自身是否插入至 body 元素上
    appendTobody: {
      type: Boolean,
      default: false
    },
    clickDialogClose: {
      type: Boolean,
      default: false
    },
    //是否允许拖拽
    isDrag: {
      type: Boolean,
      default: false
    }
  },

  emits: ['click-cancel', 'click-sure'],

  setup(props, context) {
    const state = reactive({
      //弹框是否全屏-通过点击事件改变
      fullScreenState: false,
      //是否显示弹框关闭图标
      showClose: false
    });
    /**
     * 初始化弹框参数
     */
    function initDialogData() {
      if (props.isShowDialog) {
        state.fullScreenState = props.fullScreen;
      }
    }
    /**
     * 关闭弹框
     */
    function closeDialog() {
      context.emit('click-cancel');
    }
    /**
     * 点击确认
     */
    function sureClick() {
      context.emit('click-sure');
    }
    /**
     * 改变全屏状态
     */
    function closeFullScreenState(fullScreenState: boolean) {
      state.fullScreenState = fullScreenState;
    }
    onMounted(() => {
      if (props.isShowDialog) {
        nextTick(() => {
          //是否拖拽
          if (props.isDrag) {
            new DragDrop({
              // 是否锁定,锁定状态下不可拖动
              locked: !props.isDrag,
              // 拖动目标元素
              target: '.el-dialog__header',
              // 拖动目标元素父级
              parent: '.el-dialog'
            });
          }
        });
      }
    });

    /**
     * 监听 isShowDialog
     */
    watch(
      () => props.isShowDialog,
      (newVal) => {
        if (newVal) {
          initDialogData();
        }
      }
    );
    return {
      ...toRefs(state),
      closeFullScreenState,
      closeDialog,
      sureClick,
      initDialogData
    };
  }
});
</script>

<style lang="scss" scoped>
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .header-left {
    display: flex;
    align-items: center;
    font-size: 18px;
    color: #07111b;
  }

  .header-right {
    cursor: pointer;
    display: inline-flex;
  }
}

.mk-dialog-close-icon {
  width: 14px;
  height: 14px;
  cursor: pointer;
  background: url('./assets/error.png') no-repeat;
  display: block;
  margin-top: 2px;
  background-size: 100% 100%;
}

.mk-dialog-full-screen-icon {
  width: 16px;
  height: 16px;
  cursor: pointer;
  display: block;
  margin-top: 2px;
  margin-right: 20px;
}

.mk-dialog-full-screen-small-icon {
  background: url('./assets/full_screen_small.png') no-repeat;
  background-size: 100% 100%;
}

.mk-dialog-full-screen-big-icon {
  background: url('./assets/full_screen_big.png') no-repeat;
  background-size: 100% 100%;
}
</style>
<style lang="scss">
.el-dialog {
  border-radius: 10px !important;
  padding-top: 0;

  .el-dialog__header {
    padding: 12px 24px 12px !important;
    border-bottom: 1px solid #ebebeb;
    border-radius: 10px 10px 0 0;
    background-color: rgba(232, 235, 255, 0.4);
    font-size: 16px;
    color: #4e5668;
    line-height: 1.5;
  }

  .el-dialog__footer {
    text-align: right !important;

    .el-button {
      margin-right: 24px;
    }

    .el-button + .el-button {
      margin-left: 0 !important;
    }
  }
}
</style>

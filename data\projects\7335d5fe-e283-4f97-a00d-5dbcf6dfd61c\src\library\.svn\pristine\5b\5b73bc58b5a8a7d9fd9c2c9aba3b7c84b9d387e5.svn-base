<template>
  <mk-dialog :isShowDialog="true" :isCustomFooter="false" :appendTobody="true" :title="title"
    :customClass="'mk-tip-dialog'" @click-cancel="closeDialog" @click-sure="confirm" :width="'27%'">
    <template #customSlot>
      <div class="mk-tip-dialog-title">{{ title }}</div>
      <slot name="customTipSlot"></slot>
    </template>
  </mk-dialog>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import MkDialog from "../mk-dialog";

export default defineComponent({
  name: "mk-confirm-dialog",

  emits: ["on-cancle", "on-confirm"],

  props: {
    title: {
      type: String,
      default: '提示'
    }
  },

  components: {
    [MkDialog.name]: MkDialog,
  },

  setup(props, ctx) {
    /**
     * @name 关闭弹窗
     */
    const closeDialog = () => {
      ctx.emit("on-cancle");
    };
    /**
     * @name 确定
     */
    const confirm = () => {
      ctx.emit("on-confirm");
      closeDialog();
    };

    return {
      closeDialog,
      confirm
    };
  },
});
</script>

<style lang="scss">
.mk-tip-dialog {
  padding-bottom: 10px;
  border-radius: 4px;
  border: 1px solid #EBEEF5;
  font-size: 18px;
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
  text-align: left;
  overflow: hidden;

  .el-dialog__header {
    display: none !important;
  }

  .el-dialog__body {
    font-size: 16px;
    line-height: 2;
  }

  .mk-tip-dialog-title {
    font-size: 18px;
    color: #303133;
  }
}
</style>

@import "./variables.scss";
@import "./mixins-theme.scss";
@import "./mixins.scss";
* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}

.list-none {
  margin: 0 auto;
  padding: 0;
  list-style: none;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.pull-left {
  float: left;
}

.pull-right {
  float: right;
}

.dis-table {
  display: table;
  &.ver-mid {
    > .dis-tab-cell {
      vertical-align: middle;
    }
  }
  &.ver-top {
    > .dis-tab-cell {
      vertical-align: top;
    }
  }
  &.ver-bot {
    > .dis-tab-cell {
      vertical-align: bottom;
    }
  }
}

.ver-mid {
  vertical-align: middle;
}

.ver-top {
  vertical-align: top;
}

//图片与文字水平居中
.img-ver-align {
  vertical-align: text-bottom;
}

.dis-tab-header-group {
  display: table-header-group;
}

.dis-tab-group {
  display: table-row-group;
}

.dis-tab-row {
  display: table-row;
}

.dis-tab-cell {
  display: table-cell;
}

// 点击元素，添加点击透明效果
.click-element {
  cursor: pointer;
  &:active {
    opacity: 0.6;
  }
}

// 移动元素
.move-element {
  cursor: move;
}

/* 禁止鼠标选中文本 */
.noselect {
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Chrome/Safari/Opera */
  -khtml-user-select: none; /* Konqueror */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none;
}

// 页面穿透
.nopointer {
  pointer-events: none;
}

.clearfix {
  &:before,
  &:after {
    display: table;
    content: " ";
  }
  &:after {
    clear: both;
  }
}

.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

body {
  margin: 0;
  padding: 0;
  font       : {
    family: $fontFamily;
    size: $fontSize;
  }
  line-height: $lineHeight;
  color: $main-font-color;
  background-color: #f3f4fc;
}

button,
.el-input__inner,
.el-textarea__inner {
  font-family: $fontFamily;
}

.layout-line {
  background-color: #ccc;
  border: none;
  height: 1px;
}

.img-container {
  background   : {
    size: cover;
    position: center center;
    repeat: no-repeat;
  }
}

.play-icon:hover {
  .playicon {
    position: relative;
    &:before {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      content: "";
      z-index: 1;
      background: url("https://fs.iclass30.com/aliba/region/default/play.png")
        no-repeat center;
    }
  }
}

.transparent {
  opacity: 0;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
// symbol 统一样式
.icon {
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
// 鼠标经过
.bgLightBlue {
  cursor: pointer;
  &:hover {
    @include theme_hover_background-color();
  }
}

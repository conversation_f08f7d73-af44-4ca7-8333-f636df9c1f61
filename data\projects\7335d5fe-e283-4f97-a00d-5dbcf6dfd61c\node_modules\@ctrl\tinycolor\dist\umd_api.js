"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var css_color_names_js_1 = require("./css-color-names.js");
var format_input_js_1 = require("./format-input.js");
var from_ratio_js_1 = require("./from-ratio.js");
var index_js_1 = require("./index.js");
var random_js_1 = require("./random.js");
var readability_js_1 = require("./readability.js");
var to_ms_filter_js_1 = require("./to-ms-filter.js");
var tinycolorumd = index_js_1.tinycolor;
tinycolorumd.TinyColor = index_js_1.TinyColor;
tinycolorumd.readability = readability_js_1.readability;
tinycolorumd.mostReadable = readability_js_1.mostReadable;
tinycolorumd.random = random_js_1.random;
tinycolorumd.names = css_color_names_js_1.names;
tinycolorumd.fromRatio = from_ratio_js_1.fromRatio;
tinycolorumd.legacyRandom = from_ratio_js_1.legacyRandom;
tinycolorumd.toMsFilter = to_ms_filter_js_1.toMsFilter;
tinycolorumd.inputToRGB = format_input_js_1.inputToRGB;
tinycolorumd.stringInputToObject = format_input_js_1.stringInputToObject;
tinycolorumd.isValidCSSUnit = format_input_js_1.isValidCSSUnit;
exports.default = tinycolorumd;

<template>
  <el-upload
    ref="uploadRef"
    action="#"
    :accept="accept"
    :format="format"
    :multiple="multiple"
    :drag="drag"
    :disabled="disabled"
    :show-file-list="isShowFileList"
    :auto-upload="isAutoUpload"
    :limit="fileLimit"
    :before-upload="beforeUpload"
    :http-request="uploadFile"
    :on-exceed="onExceed"
    :on-change="fileChange"
  >
    <slot name="uploadSlot"></slot>
  </el-upload>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import SparkMD5 from 'spark-md5';
import {
  generateUUID,
  get_suffix,
  getDateForPath
} from '@/library/src/utils/globalFunction';
import MkDialog from '../mk-dialog/index.vue';
import { isTest } from '@/library/src/utils/valiteSite';
import store from '@/store';

export default defineComponent({
  name: 'mk-upload',

  components: {
    MkDialog
  },

  props: {
    //是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    //是否允许拖拽
    drag: {
      type: Boolean,
      default: false
    },
    //是否支持多个上传
    multiple: {
      type: Boolean,
      default: false
    },
    //接受上传的文件类型
    accept: {
      type: String,
      default: ''
    },
    //是否显示文件列表
    isShowFileList: {
      type: Boolean,
      default: false
    },
    //是否自动上传
    isAutoUpload: {
      type: Boolean,
      default: false
    },
    //是否自动上传OSS
    isAutoUplodOss: {
      type: Boolean,
      default: true
    },
    //文件上传限制
    fileLimit: {
      type: Number,
      default: 1
    },
    format: {
      type: Array,
      default: []
    },
    uploadPath: {
      type: String,
      default: ''
    },
    //文件上传前的校验回调方法
    beforeUploadCallback: {
      type: Function,
      default: () => {}
    }
  },

  setup(props, context) {
    const uploadRef = ref<any>(null);
    const state = reactive({
      fileList: [],
      md5Count: 0,
      isLoading: false
    });
    /**
     * @name: 上传前校验
     * @param file 文件对象
     * @return 校验是否通过
     */
    const beforeUpload = (file: File): boolean => {
      cancelUpload();
      return props.beforeUploadCallback(file);
    };
    /**
     * @name: 添加文件
     * @param file 文件对象
     */
    const fileChange = (file: any) => {
      uploadRef.value.submit();
    };
    /**
     * @name: 获取文件md5
     * @param {*} file
     * @return {*}
     */
    const doIncrementalTest = (file: any) => {
      state.md5Count += 1;
      state.isLoading = true;
      context.emit('is-loading', state.isLoading);
      let running = false; //running用于判断是否正在计算md5
      //这里假设直接将文件选择框的dom引用传入
      if (running) {
        return;
      }
      //这里需要用到File的slice( )方法，以下是兼容写法
      const blobSlice: Function = File.prototype.slice;
      const chunkSize = 2097152; // 以每片2MB大小来逐次读取
      const chunks: number = Math.ceil(file.size / chunkSize);
      let currentChunk = 0;
      const spark = new SparkMD5(); //创建SparkMD5的实例
      const fileReader: any = new FileReader();
      fileReader.onload = function (e: any) {
        console.log('Read chunk number (currentChunk + 1) of  chunks ');
        spark.appendBinary(e.target.result); // append array buffer
        currentChunk += 1;
        if (currentChunk < chunks) {
          loadNext();
        } else {
          state.md5Count -= 1;
          running = false;
          console.log('Finished loading!');
          const str: any = spark.end();
          console.log('str', str);
          //获取文件对象
          context.emit('get-file-obj', file, str);
          if (state.md5Count == 0) {
            state.isLoading = false;
            context.emit('is-loading', state.isLoading);
          }
          return spark.end(); // 完成计算，返回结果
        }
      };
      fileReader.onerror = function () {
        running = false;
        console.log('something went wrong');
      };

      function loadNext() {
        const start = currentChunk * chunkSize,
          end = start + chunkSize >= file.size ? file.size : start + chunkSize;
        fileReader.readAsBinaryString(blobSlice.call(file, start, end));
      }

      running = true;
      loadNext();
    };
    /**
     * @name: 取消上传文件
     * @param file 文件对象
     */
    const cancelUpload = () => {
      uploadRef.value.clearFiles();
    };
    /**
     * @name: 文件上传个数超出限制
     */
    const onExceed = () => {
      ElMessage({
        message: '文件上传个数超出限制',
        type: 'warning'
      });
    };
    /**
     * @name: 上传文件
     */
    const uploadFile = (file: any) => {
      const fileItem = file.file;
      if (!props.isAutoUplodOss) {
        doIncrementalTest(fileItem);
      } else {
        let filePath = handleUploadPath(fileItem.name);
        minioUpload.uploadFile(file.file, filePath).then((res: any) => {
          context.emit('confirm-upload', res);
          uploadRef.value.clearFiles();
        });
      }
    };
    /**
     * @name oss上传资源列表
     */
    const ossUploadResourceList = (fileList: any) => {
      const resList = [...fileList].map((item: any) => {
        return {
          file: item,
          filePath: item.path || item.filePath
        };
      });
      minioUpload
        .uploadListFile(resList, (list: any) => {
          context.emit('get-upload-list-file', list);
        })
        .then((res: any) => {
          context.emit('confirm-upload', res);
        });
    };

    /**
     * 处理上传资源地址
     */
    function handleUploadPath(fileName: string) {
      let schoolId = store.state.user.schoolId;
      let userId = store.state.user.userId;
      let filePath = props.uploadPath + getDateForPath(schoolId, userId);
      let uuid = generateUUID();
      filePath = filePath + uuid + '/f' + get_suffix(fileName);
      return filePath;
    }

    onMounted(() => {
      minioUpload.initConfig(
        isTest(),
        store.state.user.schoolId,
        'datedu',
        store.state.app.platFormInfo.id
      );
    });
    return {
      uploadRef,
      ...toRefs(state),
      cancelUpload,
      onExceed,
      beforeUpload,
      uploadFile,
      fileChange,
      ossUploadResourceList
    };
  }
});
</script>

<style lang="scss" scoped>
.logo-img {
  width: 228px;
  height: 32px;
  margin: 30px 0;
  img {
    width: 100%;
    height: 100%;
  }
}

.remind-text {
  color: #fd032f;
}
</style>
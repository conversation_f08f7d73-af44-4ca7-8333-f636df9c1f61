﻿<template>
  <div
    class="mk-banner"
    :style="{ '--height': height + 'px', '--width': width }"
  >
    <template v-if="bannerList.length">
      <div class="swiper-container" :class="className">
        <div class="swiper-wrapper">
          <div
            v-for="(item, index) in bannerList"
            :key="index"
            :class="['swiper-slide', { isLink: item.isLink }]"
          >
            <img
              :data-item="JSON.stringify(item)"
              :src="item.src"
              :alt="item.title || ''"
              :onerror="onError"
            />
            <p
              :data-item="JSON.stringify(item)"
              :title="item.title"
              v-if="isShowTitle && item.title"
              class="banner-title"
            >
              {{ item.title }}
            </p>
          </div>
        </div>
        <template v-if="bannerList.length > 1">
          <div :class="['swiper-button-prev', { bothIcon }]">
            <slot name="prevIcon">
              <p class="allow-left"><i class="iconfont iconicon-test21"></i></p>
            </slot>
          </div>
          <div :class="['swiper-button-next', { bothIcon }]">
            <slot name="nextIcon">
              <p class="allow-right">
                <i class="iconfont iconicon-test23"></i>
              </p>
            </slot>
          </div>
        </template>
        <div class="swiper-pagination" v-if="isShowPage"></div>
      </div>
    </template>
    <slot name="slot" :swiper="mySwiper"></slot>
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  toRefs,
  reactive,
  onMounted,
  PropType,
  ref,
  watch,
  nextTick
} from "vue";
import Swiper, { Controller, Navigation, Autoplay } from "swiper";
import "swiper/swiper.scss";
import { addFsUrl } from "@/library/src/utils/globalFunction";

Swiper.use([Controller, Navigation, Autoplay]);

/** banner接口类 */
export interface IBanner {
  /** 是否链接 */
  isLink: boolean;
  /** 链接地址 */
  link: string;
  /** 图片地址 */
  src: string;
  /** 链接名称 */
  title?: string;
}

export default defineComponent({
  name: "mk-banner",

  emits: ["slide-change", "on-click"],

  props: {
    // banner列表
    banner: {
      type: [] as PropType<IBanner[]>,
      required: true
    },
    // 高度
    height: {
      type: Number,
      default: 360
    },
    // 宽度
    width: {
      type: String,
      default: "100%"
    },
    // 是否开启自动轮播
    autoplay: {
      type: Boolean,
      default: true
    },
    // 是否开启循环轮播
    loop: {
      type: Boolean,
      default: true
    },
    // 左右切换按钮是否靠边显示
    bothIcon: {
      type: Boolean,
      default: false
    },
    // 是否显示分页器
    isShowPage: {
      type: Boolean,
      default: false
    },
    // 是否显示图片标题
    isShowTitle: {
      type: Boolean,
      default: false
    }
  },

  setup(props, ctx) {
    const state = reactive({
      // 轮播图列表
      bannerList: props.banner,
      // 轮播实例
      mySwiper: ref<any>(null),
      // 类名
      className: ""
    });

    /**
     * @name: 轮播图的初始化
     */
    const initSwiper = () => {
      if (props.banner.length <= 1) {
        return;
      }
      state.className = "swiper-container" + Date.now();
      nextTick(() => {
        // 等待视图元素构建完成再初始化,否则找不到root标签
        // 避免多组件调用时swiper事件冲突,设置命名空间隔离事件
        const autoplay =
          props.autoplay && props.banner.length > 1
            ? {
                delay: 5000,
                stopOnLastSlide: false,
                // 滑动之后,重新自动播放
                disableOnInteraction: false
              }
            : false;
        state.mySwiper = null;
        state.mySwiper = new Swiper("." + state.className, {
          direction: "horizontal", // 水平切换选项
          loop: props.loop, // 循环模式选项
          autoplay,
          // 前进后退按钮
          navigation: {
            prevEl: "." + state.className + " .swiper-button-prev",
            nextEl: "." + state.className + " .swiper-button-next"
          },
          pagination: {
            el: "." + state.className + " .swiper-pagination",
            clickable: true
          },
          on: {
            slideChangeTransitionStart: (e: any) => {
              ctx.emit("slide-change", e.activeIndex);
            },
            click: (swiper: Swiper, e: any) => {
              // loop会导致vue@绑定事件失效,故使用Swiper的事件监听绑定事件
              // clicked为自定义参数,避免多次点击
              if (e.target.clicked) {
                e.target.clicked = false;
                return;
              }
              const dataset = e.target.dataset;
              if (dataset.item) {
                e.target.clicked = true;
                goLink(JSON.parse(dataset.item));
              }
            }
          }
        });
      });
    };

    /**
     * @name: 页面跳转
     */
    const goLink = (item: IBanner) => {
      if (item.isLink && item.link) {
        window.open(item.link, "_blank");
      }
      ctx.emit("on-click", item);
    };

    /**
     * @name: 图片加载失败错误处理
     * @param event 图片对象
     */
    const onError = (event: any) => {
      const img = event.srcElement;
      img.src = addFsUrl("aliba/region/default/res_default.png");
      img.onerror = null;
    };

    onMounted(() => {
      initSwiper();
    });

    watch(
      () => props.banner,
      () => {
        state.bannerList = props.banner;
        initSwiper();
      }
    );

    return {
      ...toRefs(state),
      goLink,
      onError
    };
  }
});
</script>

<style lang="scss" scoped>
.mk-banner {
  position: relative;
  width: var(--width);
  height: var(--height);
  margin: 0 auto;
  .swiper-container {
    width: 100%;
    height: 100%;
    &:hover .swiper-button-prev {
      display: block;
    }
    &:hover .swiper-button-next {
      display: block;
    }
    .swiper-wrapper {
      .swiper-slide {
        width: var(--width);
        &.isLink {
          cursor: pointer;
        }
        img {
          width: 100%;
          height: 100%;
        }
      }
    }
    .swiper-button-prev {
      left: 5%;
      &.bothIcon {
        left: 0;
      }
      ::v-deep(.allow-left) {
        position: absolute;
        left: 0;
      }
    }
    .swiper-button-next {
      right: 5%;
      &.bothIcon {
        right: 0;
      }
      ::v-deep(.allow-right) {
        position: absolute;
        right: 0;
      }
    }
    .swiper-button-prev,
    .swiper-button-next {
      background-image: none;
      display: none;
      p {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: center;
        align-items: center;
        .iconfont {
          font-size: 42px;
        }
      }
    }
    .banner-title {
      position: absolute;
      bottom: 0;
      left: 0;
      height: 45px;
      width: 100%;
      line-height: 45px;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      text-indent: 20px;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
    }
  }
}
</style>

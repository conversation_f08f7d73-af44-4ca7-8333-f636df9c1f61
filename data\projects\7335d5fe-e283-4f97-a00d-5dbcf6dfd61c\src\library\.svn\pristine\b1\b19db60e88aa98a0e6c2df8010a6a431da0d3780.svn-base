<template>
  <div class="mk-login-dingding">
    <div class="mk-login-select-school" :style="isOpen?'border-radius:10px 10px 0px 0px':''">
      <!-- 展示学校名称 -->
      <div class="mk-show-school" style="text-align:left" @click="updateClass">
        <i class="iconfont iconxuexiao4" style="padding-left:12px"></i>
        <span class="mk-show-school-name">{{selectSchoolName}}</span>
        <i :class="isOpen?'el-select__caret el-input__icon el-icon-arrow-up':'el-select__caret el-input__icon el-icon-arrow-up is-reverse'"
          style="position: absolute;right: 15px;color:#DCDFE6"></i>
      </div>
      <!-- 搜索框 -->
      <div class="mk-select-input" v-if="isOpen">
        <el-input v-model="keyWord" placeholder="请输入关键字(小学,初中,高中,大学等)" size="mini" @input="querySchool"></el-input>
        <div class="mk-select-input-text">{{inputText}}</div>
      </div>
      <!-- 学校列表 -->
      <div class="mk-select-list" v-if="keyWord.length>=2 && isOpen && showSchoolList.length>0">
        <div class="mk-select-list-item" v-for="item in showSchoolList" @click="changeSchool(item)">
          {{item.text}}
        </div>
      </div>

    </div>
    <div style="margin-top:17%">
      <!-- 钉钉二维码 -->
      <div id="qr-code" class="qr-code">
        <div v-loading="loading">
          <p id='qr-invalid' v-if="invalid"
            @click="changeSchool({id:schoolId,text:selectSchoolName});invalid = false;text='请使用钉钉扫码'">
            点击刷新</p>
          <img id='qr-img' />
        </div>
        <p id='tip' style="margin-top:5px">{{text}}
          <slot name="dropdown"></slot>
        </p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
} from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import Types from "@/library/src/store/action-types";
import { IGlobalState } from "@/library/src/store/index";
import {
  getUnitListAPI,
  getQrCodeAPI,
  getScanStatusAPI,
  shangYuDingScanCodeLoginAPI,
} from "@/library/src/service/API/base";

declare let ssoClient: any;
export default defineComponent({
  name: "ding-ding-model",
  props: {
    loginTypeCode: {
      type: Array,
      default: function () {
        return [];
      },
    },
  },

  setup(props, contex) {
    const store = useStore<IGlobalState>();
    const state = reactive({
      //选中的学校名称
      selectSchoolName: "",
      //关键字
      keyWord: "",
      //下拉状态
      isOpen: false,
      //全部学校集合
      schoolList: [] as any,
      //展示的学校集合
      showSchoolList: [] as any,
      //状态id
      scanId: "",
      //定时器
      scanInterval: null as any,
      //是否显示过期提示
      invalid: false,
      //选中的学校id
      schoolId: "",
      //加载动画
      loading: false,
      //提示语
      text: "请使用钉钉扫码",
      //提示语
      inputText: "请在输入至少2个字符",
    });
    onMounted(() => {
      getSchoolList();
    });
    /**
     * 获取学校
     */
    const getSchoolList = () => {
      getUnitListAPI({}).then((res: any) => {
        state.schoolList = res.data;
        changeSchool(res.data[0]);
      });
    };
    function changeLoginType(type: string) {
      contex.emit("change-login-type", type);
    }
    /**
     * 搜索学校
     */
    function querySchool(keyWord: any) {
      if (keyWord.length >= 2) {
        state.showSchoolList = [];
        state.showSchoolList = state.schoolList.filter((i: any) =>
          i.text.includes(keyWord)
        );
        state.inputText = state.showSchoolList.length == 0 ? "未找到结果" : "";
      } else {
        state.inputText = `请再输入至少${2 - keyWord.length}个字符`;
      }
    }
    /**
     * 获取二维码
     */
    function changeSchool(item: any) {
      state.schoolId = item.id;
      state.loading = true;
      getQrCodeAPI({ type: "pc", domainId: item.id })
        .then((res: any) => {
          state.scanId = res.data.scanId;
          let doc: any = document.getElementById("qr-img");
          doc.setAttribute("src", res.data.fileUrl);
          state.loading = false;
          // 定时器每间隔3秒检查一下二维码扫描状态
          if (state.scanInterval) {
            clearInterval(state.scanInterval);
          }
          state.scanInterval = setInterval(getScanStatus, 3000);
        })
        .catch((error: any) => {
          state.loading = false;
        });

      state.keyWord = "";
      state.isOpen = false;
      state.selectSchoolName = item.text;
      state.inputText = "请在输入至少2个字符";
    }

    /**
     * 获取二维码状态
     */
    const getScanStatus = () => {
      getScanStatusAPI({ scanId: state.scanId }).then((res: any) => {
        let now = new Date().getTime();
        let generateTime = res.data.generateTime;
        // 二维码10分钟过期
        if (now - generateTime > 10 * 60 * 1000) {
          //   ElMessage.warning("二维码已过期");
          state.invalid = true;
          state.text = "二维码已过期";
          if (state.scanInterval) {
            clearInterval(state.scanInterval);
          }
        }
        // type 0:未扫描 1:扫过未确认 2:确认登录(返回ddId)
        if (res.data.type == 2) {
          clearInterval(state.scanInterval);
          getLoginByDdId(res.data.ddId);
        }
      });
    };

    /**
     * 登录
     */
    function getLoginByDdId(ddId: any) {
      shangYuDingScanCodeLoginAPI({ ddId: ddId }).then((res: any) => {
        if (res.code == 1) {
          let data = res.data;
          ssoClient && ssoClient.setCookie("mk-sso-ticket", data.token);
          store.commit(`user/${Types.SET_USER_INFO}`, data);
          ElMessage({ message: "登录成功！", type: "success" });
          window.location.reload();
        } else {
          ElMessage.warning("登录失败!");
        }
      });
    }
    /**
     * 修改样式
     */
    function updateClass() {
      state.isOpen = !state.isOpen;
    }

    return {
      ...toRefs(state),
      getSchoolList,
      changeSchool,
      getScanStatus,
      getLoginByDdId,
      updateClass,
      querySchool,
      changeLoginType,
    };
  },
});
</script>

<style lang="scss" scoped>
.mk-login-dingding {
  position: absolute;
  width: 100%;
  top: 20%;
  left: 0px;
  .mk-login-select-school-title {
    font-size: 18px;
    text-align: center;
    font-weight: bold;
    margin-bottom: 10px;
  }
  .qr-code {
    width: 60%;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 10px;
    text-align: center;
  }
  .mk-login-select-school {
    text-align: center;
    border: 1px solid #dcdfe6;
    border-radius: 10px;
    width: 80%;
    margin: 0 auto;
    position: absolute;
    font-size: 16px;
    background-color: #f4f7f6;
    z-index: 100;
    left: 10%;
    .mk-show-school {
      height: 40px;
      position: relative;
      .mk-show-school-name {
        line-height: 40px;
        padding-left: 5px;
      }
    }
    .is-reverse {
      transform: rotateX(180deg); /* 垂直镜像翻转 */
      transition: 0.1s;
    }
    .mk-select-input {
      width: 95%;
      margin: 0 auto;
      padding-bottom: 5px;
      .mk-select-input-text {
        text-align: left;
        margin-bottom: 5px;
        margin-top: 5px;
      }
    }
    .mk-select-list {
      text-align: left;
      max-height: 100px;
      overflow: scroll;
    }
    .mk-select-list-item {
      padding-left: 12px;
    }
    .mk-select-list-item:hover {
      background-color: #2e8bff;
      color: #fff;
    }
  }
  #qr-invalid {
    width: 36%;
    line-height: 139px;
    text-align: center;
    position: absolute;
    background: hsla(0,0%,100%,.97);
    cursor: pointer;
    left: 32%;
  }
  #qr-img {
    width: 60%;
  }
}
</style>

<style lang="scss" >
.mk-login-dingding {
  .el-input__inner {
    height: 38px !important;
    background-color: #f4f7f6;
  }
  .el-loading-mask {
    width: 140px;
    height: 140px;
    margin: 0 auto;
  }
}
</style>
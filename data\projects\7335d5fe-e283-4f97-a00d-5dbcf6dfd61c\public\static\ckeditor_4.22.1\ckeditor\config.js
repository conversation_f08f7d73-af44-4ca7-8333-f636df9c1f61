/**
 * @Description: ckeditor自定义配置
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2023-11-21 09:21:50
 * @LastEditors: l<PERSON><PERSON><PERSON> y<PERSON><PERSON>@class30.com
 * @LastEditTime: 2024-05-08 14:39:02
 */
/**
 * @license Copyright (c) 2003-2018, CKSource - <PERSON><PERSON>. All rights reserved.
 * For licensing, see https://ckeditor.com/legal/ckeditor-oss-license
 */
window.CKEDITOR.editorConfig = function(config) {
  config.language = "zh-cn";
  config.toolbar = [
    { name: "fontline", items: ["Font","FontSize", "lineheight"] },
    {
      name: "basicstyles",
      items: [
        "Bold",
        "Italic",
        "Underline",
        "SolidUnderline",
        "UnderDot",
        "SolidWaveline",
        "Subscript",
        "Superscript",
        "RemoveFormat",
      ],
    },
    {
      name: "justify",
      items: [
        "JustifyLeft",
        "JustifyCenter",
        "JustifyRight",
        "JustifyBlock",
      ],
    },
    { name: "custom", items: ["SpaceLine","ChoiceOpts"] },
    { name: "insert", items: ["uploadImg", "Table", "SpecialChar"] },
    { name: "wiris", items: ["ckeditor_wiris_formulaEditor"] },
    { name: 'more', items: ['more'] },
    { name: "save", items: ["Undo", "Redo"] },
  ];
  // config.pasteFromWordRemoveStyles = true;
  // config.pasteFromWordRemoveFontStyles = true;
  config.pasteFromWordRemoveFontStyles = false;
  config.pasteFromWordRemoveStyles = false;
  config.forcePasteAsPlainText = false;
  config.formatMathTexMK = true;

  config.enterMode = window.CKEDITOR.ENTER_P;
  config.shiftEnterMode = window.CKEDITOR.ENTER_P;

  config.fillEmptyBlocks = false;

  config.disableObjectResizing = false;

  config.fontSize_sizes =
    "小五/3.2mm;五号/3.5mm;小四/3.7mm;四号/4mm;小三/4.2mm;三号/4.4mm;小二/4.8mm;";
  config.font_names = '宋体/宋体;黑体/黑体;仿宋/仿宋;楷体/楷体;隶书/隶书;幼圆/幼圆;微软雅黑/微软雅黑;' + config.font_names;

  config.filebrowserUploadUrl =
    "https://service.iclass30.com/testbank/testBank/uploadEditorImgNew?htmlurl=" +
    location.href +
    "&_csrf=" +
    $("meta[name='_csrf_token']").attr("content");
  config.extraPlugins =
    "font,lineheight,ckeditor_wiris,tableresize,SolidUnderline,GridLine,FourLine,TianziLine,ChoiceOpts,UnderDot,more";
  config.removePlugins = "stylescombo, magicline";

  config.specialChars = [
    "&#9312;",
    "&#9313;",
    "&#9314;",
    "&#9315;",
    "&#9316;",
    "&#9317;",
    "&#9318;",
    "&#9319;",
    "&#9320;",
    "&#9321;",
    "&quot;",
    "#",
    "$",
    "%",
    "&amp;",
    "'",
    "*",
    "&lt;",
    "=",
    "&gt;",
    "@",
    "^",
    "_",
    "`",
    "{",
    "|",
    "}",
    "~",
    "&euro;",
    "&lsquo;",
    "&rsquo;",
    "&ldquo;",
    "&rdquo;",
    "&ndash;",
    "&mdash;",
    "&iexcl;",
    "&cent;",
    "&pound;",
    "&curren;",
    "&yen;",
    "&brvbar;",
    "&sect;",
    "&uml;",
    "&copy;",
    "&ordf;",
    "&laquo;",
    "&not;",
    "&reg;",
    "&macr;",
    "&deg;",
    "&sup2;",
    "&sup3;",
    "&acute;",
    "&micro;",
    "&para;",
    "&middot;",
    "&cedil;",
    "&sup1;",
    "&ordm;",
    "&raquo;",
    "&frac14;",
    "&frac12;",
    "&frac34;",
    "&iquest;",
    "&Agrave;",
    "&Aacute;",
    "&Acirc;",
    "&Atilde;",
    "&Auml;",
    "&Aring;",
    "&AElig;",
    "&Ccedil;",
    "&Egrave;",
    "&Eacute;",
    "&Ecirc;",
    "&Euml;",
    "&Igrave;",
    "&Iacute;",
    "&Icirc;",
    "&Iuml;",
    "&ETH;",
    "&Ntilde;",
    "&Ograve;",
    "&Oacute;",
    "&Ocirc;",
    "&Otilde;",
    "&Ouml;",
    "&times;",
    "&Oslash;",
    "&Ugrave;",
    "&Uacute;",
    "&Ucirc;",
    "&Uuml;",
    "&Yacute;",
    "&THORN;",
    "&szlig;",
    "&agrave;",
    "&aacute;",
    "&acirc;",
    "&atilde;",
    "&auml;",
    "&aring;",
    "&aelig;",
    "&ccedil;",
    "&egrave;",
    "&eacute;",
    "&ecirc;",
    "&euml;",
    "&igrave;",
    "&iacute;",
    "&icirc;",
    "&iuml;",
    "&eth;",
    "&ntilde;",
    "&ograve;",
    "&oacute;",
    "&ocirc;",
    "&otilde;",
    "&ouml;",
    "&divide;",
    "&oslash;",
    "&ugrave;",
    "&uacute;",
    "&ucirc;",
    "&uuml;",
    "&yacute;",
    "&thorn;",
    "&yuml;",
    "&OElig;",
    "&oelig;",
    "&#372;",
    "&#374",
    "&#373",
    "&#375;",
    "&sbquo;",
    "&#8219;",
    "&bdquo;",
    "&hellip;",
    "&trade;",
    "&#9658;",
    "&bull;",
    "&rarr;",
    "&rArr;",
    "&hArr;",
    "&diams;",
    "&asymp;",
  ];
  config.image_previewText = " ";
  config.FILLING_CHAR_SEQUENCE = "";
  config.allowedContent = true;
  config.startupFocus = false;
  config.protectedElements = [ // 自定义选择器规则
    // 'ocr-pos',
    // 'opts-write'       // 类选择器
  ]
};

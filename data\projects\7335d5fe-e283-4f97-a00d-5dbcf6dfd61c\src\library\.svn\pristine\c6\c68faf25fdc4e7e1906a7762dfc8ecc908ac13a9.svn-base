// Color
$mainColor-seablue:#1988f4;
$mainColor-red: #fd032f;
$mainColor-cyan: #14d6dc;
$mainColor-yellow: #fea43e;
$mainColor-blue: #0f57d9;
$mainColor-green: #0fa764;
$mainColor-purple: #939fff;
$mainColor-weekblue: #409EFF;
$mainColor-darkreen: #487446;
$mainColor-deepblue: #2d8cf0;

$mainColor-hover-seablue:#E8F3FE;
$mainColor-hover-red: #F8E8EB;
$mainColor-hover-cyan: #EFFBF5;
$mainColor-hover-yellow: #FFF0E0;
$mainColor-hover-blue: #E8F3FE;
$mainColor-hover-green: #E5F6EE;
$mainColor-hover-purple: #EAECFF;
$mainColor-hover-weekblue: #E8F3FE;
$mainColor-hover-darkreen: #E6F7E5;
$mainColor-hover-deepblue: #57a3f3;

$main-primary-color: #009bff;
$main-info-color: #2db7f5;
$main-success-color: #19be6b;
$main-warning-color: #f90;
$main-error-color: #f56353;
$main-normal-color: #e6ebf1;
$main-link-color: $mainColor-deepblue;
$main-link-hover-color: tint($mainColor-deepblue, 20%);
$main-link-active-color: shade($mainColor-deepblue, 5%);
$main-selected-color: fade($mainColor-deepblue, 90%);
$main-tooltip-color: #fff;
$main-subsidiary-color: #808695;
$main-rate-star-color: #f5a623;
$main-white: #ffffff;
$main-black: #000;
$main-font-color: #666666;
$main-font-normal-color: #545454; //用于未选中的默认颜色
$main-input-placeholder-color: #727c8f; //用于搜索框、输入框内提示性文字

// Border color
$border-color-base: rgba(238, 238, 238, 1); // outside
$border-color-split: #e8eaec; // inside
$border-width-base: 1px; // width of the border for a component
$border-style-base: solid; // style of a components border

//font
$fontFamily: 微软雅黑,
Microsoft YaHei,
Verdana,
Arial,
Helvetica,
sans-serif;
$fontSize: vw(16);
$lineHeight: 1.125;
$font: $fontSize/$lineHeight $fontFamily;

//主视图区查询栏高度
$main-searchbar-height: 40px;
$main-searchbar-btn-color: $main-primary-color;

//主页头部高度
$main-header-height: 80px;
$main-footer-height: 120px;
$main-banner-height: 360px;
$main-container-width: 1400px;
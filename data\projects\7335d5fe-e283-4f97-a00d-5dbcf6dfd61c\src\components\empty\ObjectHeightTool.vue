<template>
  <!-- 客观题调整高度工具 -->
  <div class="pop down nosave">
    <div class="pop-icon" @click="setHeightFull(true)" title="点击调整至页面底部">
      <!-- v-if="splitNodes.length <= 1"> -->
      <span class="bottom"></span>
    </div>
    <div class="pop-icon pop-icon-free" @pointerdown="startDrag" title="拖动鼠标调整高度">
      <span class="free"></span>
    </div>
  </div>
</template>

<script lang="ts">
import bus from '@/utils/bus';
import { footerKeepHeight, pageHeight } from '@/utils/config';
import {
  getFullHeight,
  getAccHeight,
  getNameElement,
  pxConversionMm,
  getObjectiveMaxHeight,
} from '@/utils/dom';
import { defineComponent, nextTick } from 'vue';
export default defineComponent({
  emits: ['setQuesCardHeight'],
  props: {
    qId: {
      type: String,
      default: '',
    },
    refreshKey: {
      type: Number,
      default: '',
    },
    splitNodes: {
      type: Object,
      default: [],
    },
  },
  setup(props: any, { emit }) {
    const setHeightFull = async (render: boolean = false) => {
      const splitNode = props.splitNodes[props.splitNodes.length - 1];
      const pDom = document.getElementById(props.qId);
      let scoreH = 0;
      try {
        let $title: any = getNameElement(pDom);
        if ($title) {
          scoreH += pxConversionMm($title.offsetHeight);
        }
      } catch (error) {}

      let offsetTop = pxConversionMm(pDom.offsetTop);
      let height = 0;

      if (props.splitNodes.length > 1) {
        height = pageHeight - footerKeepHeight * 2;
      } else {
        height =
          pageHeight * Math.ceil(offsetTop / pageHeight) - footerKeepHeight - offsetTop - scoreH;
      }
      if (splitNode.height === `${height}mm`) return;

      splitNode.height = `${height}mm`;
      props.refreshKey++;

      if (render) {
        await nextTick();
        emit('setQuesCardHeight');
        bus.emit('renderAllCard');
      }
    };

    let startY = 0;
    let startHeight = 0;
    let startSpIndex = 0;
    let $dragDom = null;
    let dragDomBoxHeight = 0;
    const startDrag = (event: MouseEvent) => {
      startSpIndex = props.splitNodes.length - 1;
      startY = event.clientY;
      let splitNode = props.splitNodes[startSpIndex];
      $dragDom = document.querySelectorAll(`[data-id="${splitNode.id}"]`)[0];
      let $dragDomBox: any = document.querySelectorAll(`[data-id="${splitNode.id}"] >.ques-box`)[0];
      if ($dragDomBox) {
        dragDomBoxHeight = getFullHeight($dragDomBox);
      } else {
        let dragDomBoxs = document.querySelectorAll(`[data-id="${splitNode.id}"] .ques-item-wrap`);
        dragDomBoxHeight = getObjectiveMaxHeight(Array.from(dragDomBoxs));
      }
      // if (startSpIndex > 2) dragDomBoxHeight = 0;

      startHeight = getAccHeight($dragDom);
      document.addEventListener('pointermove', handleDrag);
      document.addEventListener('pointerup', stopDrag);
    };

    let newHeight = 0;
    let isDrag = false;
    function handleDrag(e: MouseEvent) {
      const deltaY = e.clientY - startY;
      newHeight = startHeight + deltaY;
      newHeight = newHeight <= 2 ? 0 : newHeight;
      newHeight = Math.max(dragDomBoxHeight, newHeight);
      $dragDom.style.height = newHeight + 'px';
      isDrag = Math.abs(deltaY) > 2;
    }

    async function stopDrag() {
      console.log('-> stopDrag');

      document.removeEventListener('pointermove', handleDrag);
      document.removeEventListener('pointerup', stopDrag);

      dragDomBoxHeight = 0;
      if (!isDrag) {
        $dragDom.style.height = startHeight + 'px';
        return;
      }

      isDrag = false;
      props.splitNodes[startSpIndex].height = `${pxConversionMm(newHeight)}mm`;

      emit('setQuesCardHeight');
      await nextTick();
      bus.emit('renderAllCard');
      startSpIndex = 0;
    }

    return {
      setHeightFull,
      startDrag,
    };
  },
});
</script>
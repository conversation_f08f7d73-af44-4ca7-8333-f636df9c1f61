<!--
 * @Description: 
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2024-04-02 10:54:18
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-05-21 10:12:51
-->
<template>
    <template v-if="Paper.isSealLine">
        <div style="top:0mm;left:2mm;width:10mm;height:269mm;border-right:1px dashed #000;position:absolute;z-index:0">
        </div>
        <span
            style="width: 30px;letter-spacing:0px;display: block;position: absolute;left:8mm;top: 250mm;transform: rotate(-90deg); white-space: nowrap;"><span
                style="padding:0 30mm;"></span>密<span style="padding:0 30mm;"></span>封<span
                style="padding:0 30mm;"></span>线<span style="padding:0 30mm;"></span></span>
        <div style="z-index: 1;top:0mm;left:1mm;width:10mm;height:269mm;position:absolute;font-size:3.7mm;">
            <table style="table-layout:fixed;border-collapse:collapse;width:100%;height:90%;margin-top: 20mm;"
                cellpadding="0" cellspacing="0">
                <tbody>
                    <tr>
                        <td style="border: none;word-break: keep-all;font-weight:bold;">
                            <span v-if="Paper.numberLayout != INUMBERLAYOUT.WRITE" style=" display: block;height: 30mm;border-left: 1px solid #333;margin-left:32px;margin-bottom: 10px;"></span>
                            <table v-else style="display: inline-block;margin-left: 3mm;">
                                <tr v-for="item in Paper.stuNoLength">
                                <td style="width: 6mm;height: 6mm;border: 1px solid;" class="write-box" :style="{
                                    borderBottom: item === Paper.stuNoLength ? '1px solid' : '1px dashed',
                                    borderTop: item === 1 ? '1px solid' : '1px dashed',
                                    borderRight: '1px solid',
                                    borderLeft: '1px solid'
                                }">
                                </td>
                                </tr>
                            </table>
                            <span style="display:block;-webkit-transform: rotate(-90deg);transform: rotate(-90deg);white-space: nowrap;width:50px;">考号：</span>
                        </td>
                    </tr>
                    <tr>
                        <td style="border: none;word-break: keep-all;font-weight:bold;" id="stu-name"><span
                                style=" display: block;height: 30mm;border-left: 1px solid #333;margin-left: 32px;margin-bottom:10px;"></span><span
                                style="display:block;-webkit-transform: rotate(-90deg);transform: rotate(-90deg);white-space: nowrap;width: 50px;">姓名：</span>
                        </td>
                    </tr>
                    <tr>
                        <td style="border: none;word-break: keep-all;font-weight:bold;"><span
                                style=" display: block;height: 30mm;border-left: 1px solid #333;margin-left: 32px;margin-bottom:100px;"></span><span
                                style="display:inline-block;-webkit-transform: rotate(-90deg);transform: rotate(-90deg);white-space: nowrap;width: 50px;">&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;班级：</span>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </template>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs } from 'vue'
import Paper from '@/views/paper';
import { INUMBERLAYOUT } from '@/typings/card';
export default defineComponent({
    setup() {
        const state = reactive({
            Paper: Paper,
            INUMBERLAYOUT
        })
        return {
            ...toRefs(state),
        };
    },
})
</script>
<style lang="scss" scoped></style>
{"_args": [["lru-cache@5.1.1", "D:\\代码\\datedu-hw\\deploy-dev\\data\\projects\\7335d5fe-e283-4f97-a00d-5dbcf6dfd61c"]], "_development": true, "_from": "lru-cache@5.1.1", "_id": "lru-cache@5.1.1", "_inBundle": false, "_integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "_location": "/@babel/helper-compilation-targets/lru-cache", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "lru-cache@5.1.1", "name": "lru-cache", "escapedName": "lru-cache", "rawSpec": "5.1.1", "saveSpec": null, "fetchSpec": "5.1.1"}, "_requiredBy": ["/@babel/helper-compilation-targets"], "_resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "_spec": "5.1.1", "_where": "D:\\代码\\datedu-hw\\deploy-dev\\data\\projects\\7335d5fe-e283-4f97-a00d-5dbcf6dfd61c", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/isaacs/node-lru-cache/issues"}, "dependencies": {"yallist": "^3.0.2"}, "description": "A cache object that deletes the least-recently-used items.", "devDependencies": {"benchmark": "^2.1.4", "tap": "^12.1.0"}, "files": ["index.js"], "homepage": "https://github.com/isaacs/node-lru-cache#readme", "keywords": ["mru", "lru", "cache"], "license": "ISC", "main": "index.js", "name": "lru-cache", "repository": {"type": "git", "url": "git://github.com/isaacs/node-lru-cache.git"}, "scripts": {"coveragerport": "tap --coverage-report=html", "postpublish": "git push origin --all; git push origin --tags", "postversion": "npm publish", "preversion": "npm test", "snap": "TAP_SNAPSHOT=1 tap test/*.js -J", "test": "tap test/*.js --100 -J"}, "version": "5.1.1"}
<!-- 大题标题 -->
<template>
  <div ref="bigNameRef" class="ques-content-name"
    :style="{ minHeight: Paper.space, lineHeight: Paper.space, fontFamily: Paper.titleFontFamily }">
    <div class="ques-name-wrap" contenteditable="true" @paste="handlePasteText"
      @focus="handleTitleFocus" @blur="handleTitleBlur" v-html="item.fullname" v-if="item.fullname">
    </div>
    <div class="ques-name-wrap" contenteditable="true" @paste="handlePasteText"
      @focus="handleTitleFocus" @blur="handleTitleBlur" v-else>
      {{ item.name }}
      (共<span class="total-score">{{ Number(item.score) }}</span>分)
    </div>
    <div v-if="item?.doQuesList?.length">
      请将所选题目对应的题号涂黑,每个答题区域只允许选择一题,如果多做,则按所做的前一题计分。
    </div>
    <slot></slot>
  </div>
</template>

<script lang="ts">
import bus from '@/utils/bus';
import { defineComponent, nextTick, reactive, ref, toRefs } from 'vue';
import Paper from '@/views/paper';

export default defineComponent({
  props: {
    qId: {
      type: String,
      default: '',
    },
    // 题干信息
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  setup(props: any, ctx: any) {
    const bigNameRef = ref<HTMLElement | any>(null);
    const state = reactive({
      Paper: Paper,
    });

    /* 获取源数据中的大题对象 */
    const getBigquesFromOrigin = () => {
      let ques = props.item;
      let qId = ques.parentId || ques.id;
      if (!props.item.mixinMode) qId = ques.id;
      let index = Paper.findBigIndexByQuesId(qId);
      return {
        qId,
        bigQues: Paper.getQuesInfos()[index],
      };
    };

    let lastTitle = '';
    /* 处理标题修改聚焦，缓存标题 */
    const handleTitleFocus = (event: any) => {
      lastTitle = event.target.innerText;
    };

    const syncTitle = (innerText: string) => {
      let { bigQues } = getBigquesFromOrigin();
      bigQues.fullname = innerText;
      innerText = Paper.convertQuesName(innerText);
      bigQues.name = innerText;
      Paper.notifyUpdateData('card');
      bus.emit('renderAllCard');
    };

    /* 处理标题修改失焦 */
    const handleTitleBlur = (event: any) => {
      let innerText = event.target.innerText;
      if (lastTitle === innerText) return;

      // 标题已修改
      syncTitle(innerText);
    };

    /* 处理并格式化文本 */
    const handlePasteText = (event: any) => {
      event.preventDefault();

      let innerText = event.clipboardData.getData('text');
      document.execCommand('insertHTML', false, innerText);
    };

    return {
      ...toRefs(state),
      bigNameRef,
      handleTitleFocus,
      handlePasteText,
      handleTitleBlur,
    };
  },
});
</script>

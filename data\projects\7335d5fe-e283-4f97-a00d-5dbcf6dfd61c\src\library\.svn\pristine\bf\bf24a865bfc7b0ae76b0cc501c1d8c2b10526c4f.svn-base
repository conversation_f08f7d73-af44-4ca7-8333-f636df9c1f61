<template>
  <div class="mk-login-card">
    <template v-if="!userId || isQX">
      <!--头部区域-->
      <div class="mk-login-card-header">
        <img :src="require(`@/library/ui/mk-login/assets/wechat-login.png`)" class="mk-login-card-type-img"
          v-if="isShowQRimg" @click="selectLoginType(1)" />
        <img v-else-if="isShowLoginImg()" :src="require(`@/library/ui/mk-login/assets/count-login.png`)"
          class="mk-login-card-type-img" @click="selectLoginType(2)" />
        <div class="mk-login-card-header-title flex-start">
          <span v-if="loginTypeCode.includes(LOGIN_TYPE.count) &&
            !qrLoginList.includes(loginType)
            " style="margin-right: 22px" :class="loginType == LOGIN_TYPE.count ? 'active-title-li' : ''"
            @click="changeLoginType('count')">账号登录</span>
          <span v-if="loginTypeCode.includes(LOGIN_TYPE.message) &&
            !qrLoginList.includes(loginType)
            " :class="loginType == LOGIN_TYPE.message ? 'active-title-li' : ''"
            @click="changeLoginType('message')">短信登录</span>
          <span v-else-if="loginType == LOGIN_TYPE.weChat"
            :class="loginType == LOGIN_TYPE.weChat ? 'active-title-li' : ''">微信登录
          </span>
          <span v-else-if="loginType == LOGIN_TYPE.syDingDing"
            :class="loginType == LOGIN_TYPE.syDingDing ? 'active-title-li' : ''">请选择学校</span>
        </div>
      </div>
      <!-- 内容区域 -->
      <div>
        <!--账号登录-->
        <template v-if="loginType == LOGIN_TYPE.count">
          <div class="input-login">
            <div class="input-item">
              <el-input v-model="userName" @blur="inputUserName" placeholder="手机号/用户名"
                prefix-icon="el-icon-mobile-phone" />
            </div>
            <div class="input-item">
              <el-input v-model="passWord" show-password placeholder="请输入密码" prefix-icon="el-icon-lock"
                @keyup.enter.native="login" />
            </div>
            <div class="password-box">
              <el-checkbox v-model="isRememberPassword">记住密码</el-checkbox>
              <span class="forget-password" @click="retrievePassword">忘记密码？</span>
            </div>
            <div class="password-btn">
              <el-button type="primary" class="login-btn" :loading="isLoginLoding" @click="login">登录
              </el-button>
            </div>
            <p class="register-account">
              <span @click="registerAccount">没有账号？立即注册</span>
            </p>
          </div>
        </template>

        <!--短信登录-->
        <template v-if="loginType == LOGIN_TYPE.message">
          <div class="input-login">
            <div class="input-item">
              <el-input v-model="phone" @blur="validateMobilePhone" placeholder="手机号"
                prefix-icon="el-icon-mobile-phone" />
            </div>
            <div class="input-item">
              <el-input v-model="code" @blur="checkMobileCode" placeholder="请输入验证码" prefix-icon="el-icon-lock" />
              <div class="verify-code-btn" @click="getVerifyCode" :style="reSendTime != 120 && reSendTime != 0
                  ? 'cursor: not-allowed'
                  : 'cursor: pointer'
                ">
                {{
                  reSendTime == 120 || reSendTime == 0
                  ? "获取验证码"
                  : "重新获取（" + reSendTime + "）"
                }}
              </div>
            </div>
            <div class="btn">
              <el-button type="primary" class="login-btn" :loading="isLoginLoding" @click="messageLogin">登录
              </el-button>
            </div>
            <p class="register-account">
              <span @click="registerAccount">没有账号？立即注册</span>
            </p>
          </div>
        </template>

        <!--微信登录-->
        <template v-if="loginType == LOGIN_TYPE.weChat">
          <template v-if="isWxBind">
            <div class="input-item">
              <el-input v-model="userName" placeholder="手机号" prefix-icon="el-icon-mobile-phone" />
            </div>
            <div class="input-item">
              <el-input v-model="passWord" show-password placeholder="请输入密码" prefix-icon="el-icon-lock" />
            </div>
            <div class="btn">
              <el-button type="primary" class="login-btn" @click="accountLogin">绑定并登录
              </el-button>
            </div>
          </template>
          <template v-else>
            <div id="wxlogin" class="qrcode-container"></div>
            <div class="text">
              微信扫码登录
              <el-dropdown v-if="isQrCode()">
                <span class="el-dropdown-link">
                  <i class="iconfont iconqiehuan" style="cursor: pointer; font-size: 10px"></i>
                  <el-icon class="el-icon--right" name="arrow-down"> </el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="loginTypeCode.includes('weChat')" @click.native="changeLoginType('weChat')">
                      <img :src="require('@/library/ui/mk-login/assets/wx.png')" />
                    </el-dropdown-item>
                    <el-dropdown-item v-if="loginTypeCode.includes('syDingDing')"
                      @click.native="changeLoginType('syDingDing')">
                      <img :src="require('@/library/ui/mk-login/assets/dingding.png')
                          " />
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </template>

        <!-- 上虞钉钉登录 -->
        <template v-if="loginType == LOGIN_TYPE.syDingDing">
          <ding-ding-model @change-login-type="changeLoginType" v-if="loginType == LOGIN_TYPE.syDingDing">
            <template v-slot:dropdown>
              <el-dropdown v-if="isQrCode()">
                <span class="el-dropdown-link">
                  <i class="iconfont iconqiehuan" style="cursor: pointer; font-size: 10px"></i>
                  <el-icon class="el-icon--right" name="arrow-down"> </el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="loginTypeCode.includes('weChat')" @click.native="changeLoginType('weChat')">
                      <img :src="require('@/library/ui/mk-login/assets/wx.png')" />
                    </el-dropdown-item>
                    <el-dropdown-item v-if="loginTypeCode.includes('syDingDing')"
                      @click.native="changeLoginType('syDingDing')">
                      <img :src="require('@/library/ui/mk-login/assets/dingding.png')
                          " />
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </ding-ding-model>
        </template>
      </div>
      <i class="el-icon-error close-modal-icon" @click="closeModal" v-if="isShowClose"></i>
    </template>

    <div class="type-content" v-else-if="!isQX && userId">
      <div class="login-out" @click="loginOut">退出</div>
      <img :src="avatar" class="avatar-icon" />
      <p class="user-real-name">{{ name }}</p>
      <div class="btn flex-c flex-center " @click="gotoZoomByUserType">
        <img :src="require(`@/library/ui/mk-login/assets/space-bg.png`)" />
        <span>{{ roleName }}</span>
      </div>
      <template v-if="roleList.length > 1">
        <i class="icon" :class="isShowRoleSelect ? 'el-icon-caret-top' : 'el-icon-caret-bottom'
          " @click="isShowRoleSelect = !isShowRoleSelect" />
        <div class="role-type-list" v-show="isShowRoleSelect">
          <div class="role-type-li" :class="roleType == item.type ? 'role-type-li-active' : ''"
            v-for="(item, index) in roleList" :key="index" @click="selectRoleSpace(item)">
            {{ item.name }}
          </div>
        </div>
      </template>
    </div>
    <!--确认退出登录弹框-->
    <mk-logout v-if="logoutDialogVisible" :loginTypeCode="loginTypeCode" @on-logout="logout"
      @on-cancle="logoutDialogVisible = false"></mk-logout>
  </div>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  computed,
  nextTick,
  onBeforeMount,
  onMounted,
} from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import Types from "@/library/src/store/action-types";
import { IGlobalState } from "@/library/src/store/index";
import md5 from "js-md5";
import {
  goCommonDetail, goManage,
} from "@/library/src/utils/goPageView";
import { encryptByDES, decryptByDES } from "@/library/src/utils/desUtil";
import {
  getUserAPI,
  bindQuickLoginAPI,
  findAppId,
  findsafetyid,
  findsmscodebymobile,
  checkMobileCodeAPI,
  loginByMobileCodeAPI,
  adminLoginAPI,
  teacherLoginAPI,
} from "@/library/src/service/API/base";
import DingDingModel from "./modules/DingDingModel.vue";
import { isQX,isHSX } from "@/library/src/utils/valiteSite";
import {addFsUrl} from "@/library/src/utils/globalFunction";

declare let TencentCaptcha: any;
declare let WxLogin: any;
declare let ssoClient: any;
declare let Base64: any;
declare let AtlCtrlForRockeyArm: any;

export default defineComponent({
  name: "mk-login",

  components: {
    DingDingModel,
  },
  props: {
    userType: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
    //配置是否展示那些登录方式
    loginTypeCode: {
      type: Array,
      default: ["count", "message", "weChat"],
    },
    // 是否展示关闭图标
    isShowClose: {
      type: Boolean,
      default: false,
    },
  },

  setup(props, context) {
    // tab类型
    enum LOGIN_TYPE {
      /** 账号 */
      count = "count",
      /** 短信 */
      message = "message",
      /** 微信 */
      weChat = "weChat",
      /** 上虞钉钉扫码 */
      syDingDing = "syDingDing",
    }
    const store = useStore<IGlobalState>();
    const state = reactive({
      userName: "",
      passWord: "",
      wxUnionid: "",
      phone: "",
      code: "",
      appId: "",
      guid: "",
      // 验证码发送时间间隔
      reSendTime: 120,
      captcha: null as any,
      roleType: "",
      roleName: "",
      roleList: computed(() => {
        let userInfoList: any = store.state.user.userInfoList;
        if (userInfoList.length > 0) {
          for (let i = 0; i < userInfoList.length; i++) {
            userInfoList[i].userType = userInfoList[i].user_type;
            switch (userInfoList[i].userType) {
              case 6:
                userInfoList[i].name = "行政空间";
                userInfoList[i].type = "1";
                break;
              case 1:
                userInfoList[i].name = "教师空间";
                userInfoList[i].type = "2";
                break;
              case 2:
                userInfoList[i].name = "学生空间";
                userInfoList[i].type = "3";
                break;
              case 3:
                userInfoList[i].name = "家长空间";
                userInfoList[i].type = "4";
                break;
            }
          }
        }
        return userInfoList;
      }),
      userId: computed(() => store.state.user.userId),
      userType: computed(() => store.state.user.userType),
      token: computed(() => store.state.user.token),
      avatar: computed(() => store.state.user.avatar),
      name: computed(() => store.state.user.realName),
      //是否显示右上角二维码登录图标
      isShowQRimg: false,
      //全部二维码登录类型
      qrLoginList: ["weChat", "syDingDing"],
      //全部普通登录类型
      loginAllType: ["count", "message"],
      //当前选择的登录方式
      loginType: LOGIN_TYPE.count as any,
      //是否记住密码
      isRememberPassword: false,
      isWxBind: false,
      // 清徐
      isQX: computed(() => isQX()),
      // 和顺
      isHSX: computed(() => isHSX()),
      logoutDialogVisible: false,
      isShowRoleSelect: false,
      ctrl: null as any,
      websock: true,
      ArmHandle: null as any,
      FileAttr: null as any,
      ExeFile: null as any,
      //手机验证码验证通过
      validMobileCode: true,
      isLoginLoding: false,
    });
    onMounted(() => {
      /**
       * @name 监听iframe消息
       */
      window.addEventListener(
        "message",
        function (event) {
          if (event.origin.indexOf("iclass30") > -1) {
            try {
              let msg = JSON.parse(event.data);
              //如果是微信绑定页消息 刷新token
              if (msg.type == "wxLogin") {
                ssoClient &&
                  ssoClient.setCookie("mk-sso-ticket", msg.data.token);
                if (state.isQX) {
                  //如果是管理员
                  if (props.type == "5") {
                    Arm_Enum();
                  } else {
                    closeModal();
                    gotoZoomByUserType();
                  }
                } else {
                  window.location.reload();
                }
              } else if (msg.type == "wxBind") {
                state.wxUnionid = msg.data.wxUnionid;
                state.isWxBind = true;
              }
            } catch { }
          }
        },
        false
      );
      if (!state.isQX) {
        setUserRoleNameType();
      }
      if (window.location.host.indexOf("xzsy") != -1) {
        //上虞新增钉钉扫码登录
        props.loginTypeCode.push("syDingDing");
      }
      isShowQRimgMethod();
      state.loginType = props.loginTypeCode[0];
      changeLoginType(state.loginType);
    });

    onBeforeMount(() => {
      if (state.isQX && props.type == "5") {
        initRockeyArm();
      }
    });

    /**
     * @name 清徐项目管理员登录
     */
    function initRockeyArm() {
      try {
        state.ctrl = new AtlCtrlForRockeyArm(
          "{33020048-3E6B-40BE-A1D4-35577F57BF14}"
        );
      } catch (e) {
        state.ctrl = null;
        state.websock = false;
        console.log(e);
        ElMessage({
          message: "请先安装Ukey服务后刷新页面重试",
          type: "error",
        });
      }
    }

    /**
     * @name 查找RockeyArm
     */
    function Arm_Enum() {
      return new Promise<any>((resolve, reject) => {
        state.ctrl.Arm_Enum(function (result: any, response: any) {
          if (!result) {
            console.log("Arm_Enum error. " + response);
            ElMessage({ message: "获取Ukey授权失败", type: "error" });
            reject();
          } else {
            Arm_Open()
              .then((obj: any) => {
                resolve(obj);
              })
              .catch(() => {
                reject();
              });
            console.log("找到锁的个数为：" + response);
          }
        });
      });
    }

    /**
     * @name 打开RockeyArm
     */
    function Arm_Open() {
      return new Promise<any>((resolve, reject) => {
        state.ctrl.Arm_Open(0, function (result: any, response: any) {
          if (!result) {
            console.log("Arm_Open error. " + response);
            ElMessage({ message: "获取Ukey授权失败", type: "error" });
            reject();
          } else {
            state.ArmHandle = response;
            Arm_VerifyPIN()
              .then((obj: any) => {
                resolve(obj);
              })
              .catch(() => {
                reject();
              });
            console.log("加密锁句柄为：" + state.ArmHandle);
          }
        });
      });
    }

    /**
     * @name 验证PIN码
     */
    function Arm_VerifyPIN() {
      return new Promise<any>((resolve, reject) => {
        state.ctrl.Arm_VerifyPIN(state.ArmHandle, 0, "mukun2021", function (
          result: any,
          response: any
        ) {
          if (!result) {
            console.log("Arm_VerifyPIN error. " + response);
            ElMessage({ message: "获取Ukey授权失败", type: "error" });
            reject();
          } else {
            if (0 == response) {
              Arm_ReadFile()
                .then((obj: any) => {
                  resolve(obj);
                })
                .catch(() => {
                  reject();
                });
            } else {
              console.log(response);
              ElMessage({ message: "获取Ukey授权失败", type: "error" });
              reject();
            }
          }
        });
      });
    }

    /**
     * @name 读文件
     */
    function Arm_ReadFile() {
      return new Promise<any>((resolve, reject) => {
        state.ctrl.Arm_ReadFile(state.ArmHandle, "0001", 0, 1024, function (
          result: any,
          response: any
        ) {
          if (!result) {
            ElMessage({
              message: "Arm_ReadFile error. " + response,
              type: "error",
            });
            reject();
          } else {
            const b = new Base64();
            let userid = b.decode(response).replace(/[^0-9|a-z]/gi, "");
            console.log("读取数据为：" + userid);
            Arm_Close();
            let params = { userid: userid };
            resolve(params);
          }
        });
      });
    }

    /**
     * @name 关闭RockeyArm
     */
    function Arm_Close() {
      state.ctrl.Arm_Close(state.ArmHandle, function (
        result: any,
        response: any
      ) {
        if (!result) {
          alert("Arm_Close error. " + response);
        } else {
          if (response == 0) {
          } else {
            alert(response);
          }
        }
      });
    }

    /**
     * @name 切换登录方式
     * @params type 登录方式
     */
    const changeLoginType = (type: string) => {
      state.userName = "";
      state.passWord = "";
      switch (type) {
        case "count":
          state.loginType = LOGIN_TYPE.count;
          break;
        case "message":
          state.loginType = LOGIN_TYPE.message;
          //获取腾讯验证码
          getTxAppId();
          break;
        case "weChat":
          state.loginType = LOGIN_TYPE.weChat;
          nextTick(() => {
            wxQrcodeCreate();
          });
          break;
        case "syDingDing":
          state.loginType = LOGIN_TYPE.syDingDing;
          break;
      }
      isShowQRimgMethod();
    };

    /**
     * @name 动态选中登录方式
     * @param type 1 二维码登录 2普通登录
     */
    function selectLoginType(type: number) {
      if (type == 1) {
        for (let index = 0; index < state.qrLoginList.length; index++) {
          const element = state.qrLoginList[index];
          if (props.loginTypeCode.indexOf(element) != -1) {
            return changeLoginType(element);
          }
        }
      } else {
        for (let index = 0; index < state.loginAllType.length; index++) {
          const element = state.loginAllType[index];
          if (props.loginTypeCode.indexOf(element) != -1) {
            return changeLoginType(element);
          }
        }
      }
    }
    /**
     * @name 展示右上角普通登录的图片
     */
    function isShowLoginImg() {
      let allQr = false,
        allLogin = false;
      for (let index = 0; index < state.qrLoginList.length; index++) {
        const element = state.qrLoginList[index];
        if (props.loginTypeCode.indexOf(element) != -1) {
          allQr = true;
          break;
        }
      }
      for (let index = 0; index < state.loginAllType.length; index++) {
        const element = state.loginAllType[index];
        if (props.loginTypeCode.indexOf(element) != -1) {
          allLogin = true;
          break;
        }
      }
      // 存在至少一个二维码登录和至少一个普通登录就显示右上角普通登录的图片
      return allQr && allLogin;
    }

    /**
     * @name 是否显示右上角切换二维码登录图片
     */
    const isShowQRimgMethod = () => {
      //是否存在二维码登录
      let isQR = false,
        //是否选中二维码登录方式
        isSelectLoginType = true;
      for (let index = 0; index < state.qrLoginList.length; index++) {
        const element = state.qrLoginList[index];
        if (props.loginTypeCode.indexOf(element) != -1) {
          isQR = true;
        }
        if (state.loginType == element) {
          isSelectLoginType = false;
          break;
        }
      }
      //存在二维码但不选中就显示右上角切换二维码登录图片
      state.isShowQRimg = isQR && isSelectLoginType;
    };

    /**
     * @name 校验手机号
     */
    const validateMobilePhone = () => {
      let mobileRight = true;
      let reg = new RegExp("^\\d+$");
      let reg2 = new RegExp("^((-\\d+)|(0+))$");
      if (
        state.phone !== "" &&
        !reg.test(state.phone) &&
        !reg2.test(state.phone)
      ) {
        mobileRight = false;
        ElMessage({ message: "手机号格式不正确！", type: "error" });
      } else if (state.phone.length != 11) {
        mobileRight = false;
        ElMessage({ message: "手机号格式不正确！", type: "error" });
      }
      return mobileRight;
    };
    /**
     * @name 获取验证码
     */
    const getVerifyCode = () => {
      if (state.reSendTime == 120 || state.reSendTime == 0) {
        if (state.phone == "") {
          ElMessage({ message: "请先输入手机号码！", type: "error" });
        } else {
          if (validateMobilePhone()) {
            if (state.captcha) {
              // 滑块显示
              state.captcha.show();
            }
          }
        }
      }
    };
    /**
     * @name 获取腾讯验证码应用id
     */
    const getTxAppId = () => {
      findAppId({})
        .then(function (res: any) {
          if (res.code == 1) {
            state.appId = res.data;
            state.captcha = new TencentCaptcha(
              state.appId.toString(),
              function (res: any) {
                if (res.ret === 0) {
                  queryMobieQuid(res.ticket, res.randstr);
                }
              },
              { needFeedBack: false }
            );
          }
        })
        .catch(function (error: any) {
          ElMessage({ message: error.msg, type: "error" });
        });
    };
    /**
     *@name 发送手机验证码
     */
    const queryMobieQuid = (ticket: any, randstr: any) => {
      findsafetyid({})
        .then(function (res: any) {
          //获取安全码
          state.guid = res.data;
          if (state.guid) {
            getSmsCodeByMobile(state.guid, ticket, randstr);
          }
        })
        .catch(function (error: any) {
          ElMessage({ message: error.msg, type: "error" });
        });
    };
    /**
     * @name 获取验证码
     */
    const getSmsCodeByMobile = (guid: any, ticket: any, randstr: any) => {
      mukunSettime();
      let params = {
        guid: guid,
        mobile: state.phone,
        userType: props.userType,
        isRegister: 0,
        verificationCode: ticket,
        randstr: randstr,
        guidEncrypted: md5(guid + "datedu6738b274c68384f3").toUpperCase(),
      };
      findsmscodebymobile(params)
        .then(function (res: any) {
          if (res.code === 1) {
            ElMessage({
              message: "验证码已发送，请注意查收！",
              type: "success",
            });
          }
        })
        .catch(function (error: any) {
          ElMessage({ message: error.msg, type: "error" });
        });
    };
    /**
     * @name 重置验证码发送时间
     */
    const mukunSettime = () => {
      if (state.reSendTime == 0) {
        state.reSendTime = 120;
      } else {
        state.reSendTime--;
        setTimeout(function () {
          mukunSettime();
        }, 1000);
      }
    };
    /**
     * @name 校验验证码
     */
    const checkMobileCode = () => {
      let params = {
        guid: state.guid,
        mobile: state.phone,
        mobilecode: state.code,
      };
      checkMobileCodeAPI(params)
        .then(function (res: any) {
          if (res.code == 1) {
            state.validMobileCode = true;
          }
        })
        .catch(function (error: any) {
          state.validMobileCode = false;
        });
    };

    /**
     * @name 输入用户名
     */
    const inputUserName = () => {
      let passwordText = localStorage.getItem(state.userName);
      if (passwordText) {
        state.isRememberPassword = true;
        state.passWord = decryptByDES(passwordText);
      }
    };
    /**
     * @name 是否存储用户密码
     */
    const isLocalUserPassword = () => {
      if (state.isRememberPassword) {
        let passwordText = encryptByDES(state.passWord.trim());
        localStorage.setItem(state.userName, passwordText);
      } else {
        if (localStorage.getItem(state.userName)) {
          localStorage.removeItem(state.userName);
        }
      }
    };
    /**
     * @name 根据用户名获取用户id(清徐ukey登录需要)
     */
    const getUserIdByUserName = () => {
      if (state.userName == "" || state.passWord == "") {
        ElMessage({ message: "请输入账号或密码！", type: "error" });
        return;
      }
      let params: any = {
        keyvalue: state.userName,
        limitUserTypes: state.isQX ? (props.type == "5" ? "1,6" : props.userType) : "",
      };
      if (/^1[0-9]{10}/.test(state.userName)) {
        params.key = "mobile";
      } else {
        params.key = "user_name";
      }
      return getUserAPI(params);
    };
    /**
     * @name 找回密码
     */
    const retrievePassword = () => {
      let url = "forgetpassWord";
      goCommonDetail(url);
    };
    /**
     * @name 注册密码
     */
    const registerAccount = () => {
      let url = "registeraccount";
      goCommonDetail(url);
    };

    /**
     * @name 清徐登录
     */
    const login = async () => {
      if (state.isQX && props.type == "5") {
        let res = await getUserIdByUserName();
        let armRes = await Arm_Enum();
        if (res.data.id == armRes.userid) {
          accountLogin();
        }
      } else {
        accountLogin();
      }
    };
    /**
     * @name 获取登录方法
     */
    const getLoginFunc = () => {
      if (state.isQX && props.type == "5") {
        //清徐 校管 区管登录
        let param = {
          account: state.userName,
          passWord: state.passWord,
          logintype: "22",
          adminTypes: "2,4",
        };
        return adminLoginAPI(param);
      } else if (state.isQX && props.type == "2") {
        //清徐 仅教师登录 不包含校管
        let param = {
          account: state.userName,
          passWord: state.passWord,
          logintype: "22",
        };
        return teacherLoginAPI(param);
      } else {
        let param = {
          account: state.userName,
          passWord: state.passWord,
          logintype: "22",
          userType: state.isQX ? (props.type == "5" ? "" : props.userType) : "",
          customLoginType: state.isQX ? (props.type == "1" ? 1 : -1) : -1, //针对清徐项目 自定义登录类型 -1:不启用自定义登录(默认) 1:行政人员登录
          customLoginSupportUserType: state.isQX
            ? props.type == "1"
              ? 6
              : undefined
            : undefined, //customLoginType为1时有效
        };
        return store.dispatch(`user/${Types.PLATFORM_LOGIN}`, param);
      }
    };
    /**
     * @name 账号登录
     */
    const accountLogin = () => {
      if (state.userName == "" || state.passWord == "") {
        ElMessage({ message: "请输入账号或密码！", type: "error" });
        return;
      }
      state.isLoginLoding = true;
      getLoginFunc()
        .then((res: any) => {
          state.isLoginLoding = false;
          if (res.code == 1) {
            let data = res.data[0] || res.data
            ssoClient && ssoClient.setCookie(data.token);
            store.commit(`user/${Types.SET_USER_INFO}`, data);
            if (state.isWxBind) {
              bindWxAccount(data.id);
            } else {
              //是否存储用户密码
              isLocalUserPassword();
              if (state.isQX) {
                // if (props.type == "5") {
                //   Arm_Enum();
                // } else {
                closeModal();
                gotoZoomByUserType();
                // }
              } else {
                if( isHSX() ){
                 location.href = `https://yun.xckj.net/xcoffice/daiTe/ssoLogin?tokenId=${store.state.user.token}`
                }else{
                  window.location.reload();
                }
              }
            }
          } else {
            state.isLoginLoding = false;
            ElMessage({ message: res.msg, type: "error" });
          }
        })
        .catch((error: any) => {
          state.isLoginLoding = false;
          ElMessage({ message: error, type: "error" });
        });
    };
    /**
     * @name 短信登录
     */
    const messageLogin = () => {
      if (!state.validMobileCode) {
        ElMessage({ message: "手机验证码输入错误，请验证！", type: "error" });
        return;
      }
      if (state.code == "") {
        ElMessage({ message: "请输入手机验证码！", type: "error" });
        return;
      }
      state.isLoginLoding = true;
      let params = {
        mobile: state.phone,
        safId: state.guid,
        mobileCode: state.code,
        userType: state.isQX ? (props.type == "5" ? "" : props.userType) : "",
        loginType: 5,
      };
      loginByMobileCodeAPI(params)
        .then(function (res: any) {
          state.isLoginLoding = false;
          if (res.code == 1) {
            let data = res.data;
            ssoClient && ssoClient.setCookie(data.token);
            store.commit(`user/${Types.SET_USER_INFO}`, data);
            ElMessage({ message: "登录成功！", type: "success" });
            if (state.isQX) {
              if (props.type == "5") {
                Arm_Enum();
              } else {
                closeModal();
                gotoZoomByUserType();
              }
            } else {
              window.location.reload();
            }
          } else if (res.code == 2) {
            let data = res.data;
            store
              .dispatch(`user/${Types.GET_USER_INFO}`, { userId: data[0].id })
              .then((res) => {
                if (state.isQX) {
                  if (props.type == "5") {
                    Arm_Enum();
                  } else {
                    closeModal();
                    gotoZoomByUserType();
                  }
                } else {
                  window.location.reload();
                }
              });
          } else {
            ElMessage({ message: res.msg, type: "error" });
          }
        })
        .catch(function (error: any) {
          state.isLoginLoding = false;
          ElMessage({ message: error.msg, type: "error" });
        });
    };
    /**
     * @name 是否存在二维码登录
     */
    function isQrCode() {
      let num = 0;
      for (let index = 0; index < state.qrLoginList.length; index++) {
        const element = state.qrLoginList[index];
        if (props.loginTypeCode.includes(element)) {
          num++;
        }
      }
      return num >= 2;
    }
    /**
     * @name 构建微信二维码
     */
    const wxQrcodeCreate = () => {
      let obj = new WxLogin({
        self_redirect: true,
        id: "wxlogin",
        appid: "wx867e51edfaa2386f",
        scope: "snsapi_login",
        redirect_uri: "https://cloud.iclass30.com/wxlogin",
        state: "",
        style: "white",
        href: addFsUrl('aliba/plug/sso/wx.css'),
      });
      let id = document.getElementById("wxlogin");
      let iframe = id?.getElementsByTagName("iframe")[0];
      if (iframe) {
        iframe.width = id?.clientWidth + "px";
        iframe.height = id?.clientHeight + "px";
      }
    };
    /**
     * @name 绑定微信
     */
    const bindWxAccount = async (userid: string) => {
      let params = {
        userid: userid,
        unionid: state.wxUnionid,
        type: 1, //1：微信2：企业微信 3：钉钉 4：学科网 5：腾讯智启 6：钉钉
      };
      let res = await bindQuickLoginAPI(params);
      if (res.code == 1) {
        ssoClient && ssoClient.setCookie("mk-sso-ticket", res.data.token);
        if (state.isQX) {
          gotoZoomByUserType();
        } else {
          window.location.reload();
        }
      } else if (res.code == 2) {
        window.location.href = `?token=${res.data[0].token}`;
      } else {
        ElMessage({ message: "账号绑定失败，请重试", type: "error" });
      }
    };
    /**
     * @name 设置用户的角色名称和类型
     */
    const setUserRoleNameType = () => {
      switch (store.state.user.userType) {
        case 6:
          state.roleName = "行政空间";
          state.roleType = "1";
          break;
        case 1:
          state.roleName = "教师空间";
          state.roleType = "2";
          break;
        case 2:
          state.roleName = "学生空间";
          state.roleType = "3";
          break;
        case 3:
          state.roleName = "家长空间";
          state.roleType = "4";
          break;
      }
    };

    /**
     * @name 根据用户类型进入不同的空间
     */
    const gotoZoomByUserType = () => {
      context.emit("goto-zoom-by-user-type", state.roleType);
    };
    /**
     * @name 选择角色空间
     * @params item
     */
    const selectRoleSpace = async (item: any) => {
      state.roleType = item.type;
      state.roleName = item.name;
      state.isShowRoleSelect = false;
      store.dispatch(`user/${Types.GET_USER_INFO}`, { userId: item.id }).then((res) => {
          window.location.reload();
        });
    };
    /**
     * @name 退出登录
     */
    const loginOut = () => {
      state.logoutDialogVisible = true;
    };
    /**
     * @name 关闭弹框
     */
    const closeModal = () => {
      context.emit("close-modal");
    };

    /*
     * 确认退出
     */
    function logout() {
      ssoClient.logout(`${location.origin + "/region/regionside/home"}`);
      state.logoutDialogVisible = false;
    }

    return {
      ...toRefs(state),
      LOGIN_TYPE,
      selectLoginType,
      inputUserName,
      isLocalUserPassword,
      retrievePassword,
      accountLogin,
      isQrCode,
      validateMobilePhone,
      getVerifyCode,
      checkMobileCode,
      registerAccount,
      isShowLoginImg,
      messageLogin,
      login,
      loginOut,
      logout,
      selectRoleSpace,
      changeLoginType,
      gotoZoomByUserType,
      closeModal,
    };
  },
});
</script>

<style lang="scss" scoped>
.mk-login-card {
  padding: 10px 30px;
  border-radius: 10px;
  background: #f4f7f6;
  width: 386px;
  height: 325px;

  .mk-login-card-header {
    height: 55px;
    margin-bottom: 10px;
  }

  .mk-login-card-type-img {
    position: absolute;
    top: 15px;
    right: 15px;
    cursor: pointer;
  }

  .mk-login-card-header-title {
    height: 100%;
    cursor: pointer;
    font-size: 18px;
    color: #666;

    .active-title-li {
      font-size: 24px;
      color: #333;
    }
  }

  .close-icon {
    position: absolute;
    top: 25px;
    right: 35px;
    cursor: pointer;
    font-size: 14px;
    color: #999;
  }

  .input-item {
    position: relative;
    margin-bottom: 18px;

    .input-name {
      font-size: 14px;
      color: #999;
    }

    .verify-code-btn {
      position: absolute;
      right: 12px;
      top: 50%;
      transform: translate(0, -50%);
      font-size: 14px;
      color: #2e8bff;
    }

    .el-input__icon {
      font-size: 18px;
      margin-top: 2px;
    }
  }

  .btn {
    width: 100%;
    margin-top: 46px;
    text-align: center;
  }

  .password-btn {
    width: 100%;
    text-align: center;
  }

  .login-btn {
    width: 100%;
    height: 48px;
    text-align: center;
    background: #2e8bff;
    border-radius: 6px;
    font-size: 19px;
    color: #fff;
  }

  .qrcode-container {
    height: 225px;
    margin-top: -20px;
  }

  .text {
    text-align: center;
  }
}

.forget-password {
  float: right;
  // margin-top: 18px;
  font-size: 12px;
  color: #2e8bff;
  cursor: pointer;
}

.register-account {
  margin-top: 13px;
  font-size: 12px;
  color: #2e8bff;
  cursor: pointer;
  text-align: center;
  text-decoration-line: underline;
}

.password-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;
}

.type-content {
  position: relative;
  margin-top: 15px;
  text-align: center;

  .login-out {
    position: absolute;
    top: 2px;
    right: 8px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
  }

  .avatar-icon {
    width: 103px;
    height: 103px;
    border-radius: 50%;
  }

  .user-real-name {
    margin-top: 15px;
    font-size: 24px;
    color: #2a3034;
  }

  .btn {
    position: relative;
    width: 100%;
    height: 50px;
    line-height: 50px;
    border-radius: 4px;
    font-size: 22px;
    font-weight: bold;
    color: #fff;
    cursor: pointer;

    img {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
    }

    span {
      position: absolute;
    }
  }

  .icon {
    margin-top: 10px;
    font-size: 18px;
    color: #bbb;
    cursor: pointer;
  }

  .role-type-list {
    width: 200px;
    margin: 0 auto;
    padding: 14px 0;
    background: #fff;
    box-shadow: 0 3px 10px 0 rgba(181, 181, 181, 0.29);
    border-radius: 5px;

    .role-type-li {
      height: 47px;
      line-height: 47px;
      cursor: pointer;
    }

    .role-type-li:hover {
      background: rgba(209, 230, 255, 0.28);
    }

    .role-type-li-active {
      background: rgba(209, 230, 255, 0.28);
    }
  }
}

.close-modal-icon {
  position: absolute;
  right: -18px;
  bottom: -10px;
  color: #4d4d4d;
  font-size: 28px;
  cursor: pointer;
}
</style>
<style lang="scss">
.mk-login-card {
  .input-login {
    .el-input__inner {
      height: 50px !important;
      line-height: 50px !important;
      border: 0;
      border-radius: 0 !important;
    }

    .el-checkbox__label {
      font-size: 14px;
      color: #999;
    }

    .input-item {
      .el-input__icon {
        font-size: 22px;
        margin: 2px 9px 0 18px;
      }

      .el-input--prefix .el-input__inner {
        padding-left: 52px;
      }
    }
  }
}
</style>

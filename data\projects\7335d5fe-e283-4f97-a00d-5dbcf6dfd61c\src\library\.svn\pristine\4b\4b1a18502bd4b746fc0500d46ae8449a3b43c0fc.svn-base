import { API } from "../index";

/****************************** resource *****************************************/
/**
 * @name 获取教师资源列表
 * @param keyWord 搜索关键字
 * @param schoolId 学校Id
 * @param userId 用户Id
 * @param suffix 文件后缀类型(包括all、ppt、word、image、music、video、pdf、logs、excel)(是类型不是文件后缀,例如:传word,查的是doc,docx等后缀的文件)(非必传,多个逗号分隔)
 * @param notContainSuffix 不包含的文件后缀类型(参数值如suffix,这里是扩展字段,用来排查指定后缀的资源)(非必传)
 * @param fileType 文件类型(0:其它1:文档 2:微课 3:音频 4:图片)(非必传)
 * @param appType 应用类型(0:个人网盘1:备课课堂活动 2:备课)(默认-1,获取全部资源中排除appType=2和微课资源)
 * @param bkType 备课课堂活动类型(banked_cloze:选词填空guess:猜词游戏 lineing:连线题)(非必传)
 * @param bookCode 教材code(非必传,获取备课资源时需要传)
 * @param catalogCode 目录code(非必传,获取备课指定目录下资源时需要传)
 * @param isFolder 否文件夹（-1全部，0 否（默认），1 是）
 * @param parentId 可不传（不传取全部）
 * @param page 页码
 * @param limit (-1为不分页)
 **/
export const getTeacherResourceAPI = (params: {
  keyWord?: string;
  schoolId: string;
  userId: string;
  suffix?: string;
  notContainSuffix?: string;
  fileType?: string;
  appType?: number;
  bkType?: string;
  bookCode?: string;
  catalogCode?: string;
  isFolder?: number;
  page?: number;
  limit?: number;
  parentId?: string;
}) => {
  return API.GET("/resource/center/getResourceList", params);
};
/**
 * @name  获取文件，文件夹详情
 * @param ids id（多个用逗号分隔）
 **/
export const getResInfoByIds = (params: {
  ids: String
}) => {
  return API.GET("/resource/resource/getResInfoByIds", params)
}
/**
 * @name 目录列表(经过结构化之后)
 **/
export const getStructureCatalogListAPI = (params: { bookCode: string }) => {
  return API.POST("/resource/catalog/getStructureCatalogList", params);
};
/**
 * @name 年级列表
 **/
export const gradeListAPI = (params: { schoolId?: string; phase?: string; }) => {
  return API.GET("/resource/grade/list", params);
};
/**
 * @name 学科列表
 **/
export const subjectListAPI = (params: { gradeCode?: string }) => {
  return API.GET("/resource/subject/list", params);
};
/**
 * @name 版本列表
 **/
export const editionListAPI = (params: {
  userId?: string;
  gradeCode: string;
  subjectCode: string;
  bookType?: string;
}) => {
  return API.GET("/resource/edition/list", params);
};
/**
 * @name 教材列表
 **/
export const bookListAPI = (params: {
  gradeCode: string;
  subjectCode: string;
  editionCode: string;
  userId?: string;
  isOpenQues?: string;
  type?: number;
}) => {
  return API.GET("/resource/book/list", params);
};
/**
 * @name 获取用户的教材
 **/
export const userbookListAPI = (params: { userId: string }) => {
  return API.GET("/resource/userbook/list", params);
};
/**
 * @name 新增用户的教材
 **/
export const insertUserBookAPI = (params: {
  userId: string;
  gradeCode: string;
  gradeName: string;
  subjectCode: string;
  subjectName: string;
  editionCode: string;
  editionName: string;
  bookCode: string;
  bookName: string;
}) => {
  return API.GET("/resource/userbook/insert", params);
};
/**
 * @name 删除用户教材
 **/
export const deleteUserBookAPI = (params: {
  //用户教材关系id
  id: string;
}) => {
  return API.GET("/resource/userbook/delete", params);
};
/**
 * @name 校本教材列表
 **/
export const listBySchoolIdAPI = (params: {
  gradeCode?: string;
  subjectCode?: string;
  editionCode?: string;
  userId?: string;
  schoolId: string;
  keyWord?: string;
  page: number;
  limit: number;
}) => {
  return API.GET("/resource/book/listBySchoolId", params);
};
/**
 * @name 查询校本统一教材配置范围
 */
export const getSchoolBookConfigAPI = (params: object) => {
  return API.GET("/resource/schoolBook/getSchoolBookConfig", params);
};


var wrs_strings = new Array();
wrs_strings['cancel'] = '取消';
wrs_strings['accept'] = '确定';
wrs_strings['manual'] = '说明书';
wrs_strings['insert_math'] = '插入数学公式 - MathType';
wrs_strings['insert_chem'] = '插入化学公式 - ChemType';
wrs_strings['latex'] = 'LaTeX';
wrs_strings['latex_name_label'] = 'Latex Formula';
wrs_strings['minimize'] = '最小化';
wrs_strings['maximize'] = '最大化';
wrs_strings['fullscreen'] = '全屏';
wrs_strings['exit_fullscreen'] = '退出全屏';
wrs_strings['close'] = '关闭';
wrs_strings['mathtype'] = '公式编辑器';
wrs_strings['tittle_modalwindow'] = '公式编辑器窗口';
wrs_strings['close_modal_warning'] = '你确定要离开吗？您所做的更改将会丢失。';
wrs_strings['browser_no_compatible'] = '您的浏览器与AJAX技术不兼容。请使用Mozilla Firefox的最新版本。';
wrs_strings['error_convert_accessibility'] = '从MathML转换为可访问文本时出错。';
wrs_strings['exception_cross_site'] = '跨站点脚本只允许用于HTTP。';
wrs_strings['exception_high_surrogate'] = 'High surrogate not followed by low surrogate in fixedCharCodeAt()';
wrs_strings['exception_string_length'] = 'Invalid string. Length must be a multiple of 4';
wrs_strings['exception_key_nonobject'] = 'Object.keys called on non-object';
wrs_strings['exception_null_or_undefined'] = ' this is null or not defined';
wrs_strings['exception_not_function'] = ' is not a function';
wrs_strings['exception_invalid_date_format'] = 'Invalid date format : ';
wrs_strings['exception_casting'] = 'Cannot cast ';
wrs_strings['exception_casting_to'] = ' to ';
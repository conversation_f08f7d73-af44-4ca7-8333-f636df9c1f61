﻿<template>
  <!--地区,学校,年级,学科-->
  <el-row :gutter="20">
    <el-col :span="24">
      <el-input
        placeholder="请输入教师名称搜索"
        v-model="teacherAPIParams.wd"
        clearable
        suffix-icon="el-icon-search"
        @change="searchTeacher"
        @clear="searchTeacher"
        class="search-bar"
      >
      </el-input>
    </el-col>
    <template v-if="!isOriginSchool">
      <template v-if="!isCustomContent">
        <!--地区-->
        <el-col :span="12">
          <el-select
            v-if="
              [REGION_TYPE.CUSTOM, REGION_TYPE.DISTRICT].includes(
                platFormInfo.regionPlatformType
              )
            "
            :popper-append-to-body="false"
            v-model="curDistrict"
            placeholder="请选择区域"
            clearable
            @change="switchArea"
            value-key="areaId"
            style="margin-top: 5px"
          >
            <el-option
              v-for="sitem in areaList"
              :key="sitem.areaId"
              :label="sitem.areaName"
              :value="sitem"
            >
            </el-option>
          </el-select>
          <area-select
            v-else
            :ascription="[
              curDistrict.provinceId,
              curDistrict.cityId,
              curDistrict.districtId
            ]"
            @get-select-area="switchArea"
          >
          </area-select>
        </el-col>
        <!-- 学校 -->
        <el-col :span="12">
          <el-select
            style="margin-top: 5px"
            v-model="curSchool"
            v-loadselect="loadSchool"
            placeholder="选择学校或输入学校"
            @change="switchSchool"
            :popper-append-to-body="false"
            :remote-method="searchSchool"
            remote
            filterable
            default-first-option
            clearable
            value-key="schoolId"
            @clear="searchSchool('')"
          >
            <el-option
              v-for="sitem in schoolList"
              :key="sitem.schoolId"
              :label="sitem.schoolName"
              :value="sitem"
            >
            </el-option>
          </el-select>
        </el-col>
      </template>
      <el-col v-else>
        <slot name="customSchool"></slot>
      </el-col>
    </template>

    <el-col :span="12">
      <!--年级-->
      <el-cascader
        ref="gradeRef"
        style="margin-top: 5px"
        v-model="curGrade"
        placeholder="请选择学科"
        :props="{
          value: 'gradeId',
          label: 'gradeName'
        }"
        :options="gradeList"
        @change="switchGrade"
      ></el-cascader>
    </el-col>
    <el-col :span="12">
      <!--学科-->
      <el-select
        :popper-append-to-body="false"
        v-model="curSubject"
        placeholder="请选择学科"
        clearable
        @change="switchSubject"
        value-key="id"
        style="margin-top: 5px"
      >
        <el-option
          v-for="sitem in subjectLsit"
          :key="sitem.id"
          :label="sitem.subject_name"
          :value="sitem"
        >
        </el-option>
      </el-select>
    </el-col>
  </el-row>

  <!--用户列表-->
  <div class="school-user-list" v-loading="teacherOptions.loading">
    <template v-if="teacherList.length">
      <el-row class="school-user-list-header">
        <!--:indeterminate="isIndeterminate" 半选状态（暂时不加）-->
        <el-checkbox
          class="check-all"
          v-model="isCheckAll"
          @change="handleCheckAllChange"
        >
          全部教师
        </el-checkbox>
        <!--        <span>{{ teacherAPIParams.wd==''?teacherList.length: teacherOptions.total }} 人</span>-->
      </el-row>
      <el-tree
        ref="treeRef"
        v-loadmore="loadMoreTeacher"
        :data="teacherList"
        :props="{
          label: 'realName',
          disabled: (data) => {
            return disabledKeys.includes(data.userId);
          }
        }"
        node-key="id"
        class="user-tree"
        show-checkbox
        :check-on-click-node="true"
        @check="handleCheckItemChange"
      >
        <template #default="{ node, data }">
          <div
            class="user-item ellipsis"
            :title="data.realName || data.userName"
          >
            <el-avatar
              :src="data.avatar"
              style="vertical-align: middle"
              :size="30"
            >
              <img
                  :src="addFsUrl('aliba/avatar/default/default.png')"
              />
            </el-avatar>
            {{ data.realName || data.userName }}
          </div>
        </template>
      </el-tree>
    </template>
    <mk-no-data v-else></mk-no-data>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  computed,
  reactive,
  toRefs,
  ref,
  nextTick,
  onMounted,
  watch
} from 'vue';
import { querySchoolPlatformListAPI } from '@/library/src/service/API/base';
import {
  getAllGradeListAPI,
  getSubjectListByPhaseAPI,
  getRegionTeacherList,
  getTeacherListSupportSubjectAndGradeAPI
} from '@/library/src/service/API/base';
import { IGlobalState } from '@/library/src/store';
import { useStore } from 'vuex';
import AreaSelect from '@/library/ui/mk-share-res/modules/AreaSelect.vue';
import { REGION_TYPE, USER_TYPE } from '@/library/src/utils/gloableEnum';
import { groupByKey, unique,addFsUrl } from '@/library/src/utils/globalFunction';

export default defineComponent({
  name: 'school-user',

  components: {
    AreaSelect
  },

  emits: ['switch-teacher', 'on-clear'],

  props: {
    // 已选用户列表
    userList: {
      type: Array,
      default: () => []
    },
    // 是否本校
    isOriginSchool: {
      type: Boolean,
      default: false
    },
    // 禁用列表
    disabledKeys: {
      type: Array,
      default: () => []
    },
    // 是否自定义学校列表
    isCustomContent: {
      type: Boolean,
      default: false
    },
    // 当前学校
    curSchoolObj: {
      type: Object,
      default: {
        phase: '',
        schoolId: '',
        schoolName: '全部学校'
      }
    }
  },

  setup(props, ctx) {
    const treeRef = ref<any>(null);
    const gradeRef = ref<any>(null);
    const store = useStore<IGlobalState>();
    const state = reactive({
      // 区域信息
      platFormInfo: computed(() => store.state.app.platFormInfo),
      // 地区列表
      areaList: computed(() => {
        return store.state.app.platFormInfo.region.map((item: any) => {
          item.areaId = item.districtId
            ? `${item.provinceId}-${item.cityId}-${item.districtId}`
            : item.cityId
            ? `${item.provinceId}-${item.cityId}`
            : item.provinceId;
          item.areaName = item.districtName
            ? `${item.provinceName}>${item.cityName}>${item.districtName}`
            : item.cityName
            ? `${item.provinceName}>${item.cityName}`
            : item.provinceName;
          return item;
        });
      }),
      // 当前地区
      curDistrict: {
        areaId: '',
        areaName: '',
        cityId: '',
        cityName: '',
        districtId: '',
        districtName: '',
        provinceId: '',
        provinceName: ''
      },

      // 学校列表
      schoolList: [] as any[],
      // 当前学校
      curSchool: props.curSchoolObj,
      // 学校请求参数
      schoolAPIParams: {
        keyWord: '',
        page: 1,
        limit: 10
      },
      // 学校查询配置参数
      schoolOptions: {
        timer: null as any,
        loading: false,
        total: 0,
        pageCount: 1
      },

      // 年级列表
      gradeList: [] as any[],
      // 当前年级
      curGrade: [],
      // 当前年级数据
      curGradeData: {
        gradeId: '',
        gradeName: '全年级',
        phase: store.state.user.phase,
        year: ''
      },

      // 学科列表
      subjectLsit: [],
      // 当前学科
      curSubject: {
        center_code: '',
        id: '',
        phase: 0,
        subject_code: '',
        subject_name: ''
      },

      // 教师列表
      teacherList: [] as any[],
      // 当前教师列表
      curTeacherList: [] as any[],
      // 教师请求参数
      teacherAPIParams: {
        wd: '',
        page: 1,
        limit: 50
      },
      // 教师查询配置参数
      teacherOptions: {
        timer: null as any,
        loading: false,
        total: 0,
        pageCount: 0,
        finished: false
      },
      // 是否全选
      isCheckAll: false,
      // 半选状态
      isIndeterminate: true,
      //取消选中的数据
      deleteUserList: [] as any[]
    });
    /**
     * @name: 切换地区
     * @param item 地区对象
     */
    const switchArea = (item: any) => {
      state.curDistrict = item;
      initSchool();
      initGrade();
      initSubject();
      searchSchool();
    };
    /**
     * @name: 切换学校
     * @param item 学校对象
     */
    const switchSchool = (item: any) => {
      initSchool(item);
      initGrade();
      initSubject();
      getGradeList();
    };
    /**
     * @name: 切换年级
     * @param item 当前年级
     */
    const switchGrade = (item: any) => {
      initGrade(item);
      initSubject();

      let phase: any = state.curGrade[0];
      if (state.curSchool.schoolId) {
        const gradeId = item[0];
        phase =
          gradeId == ''
            ? state.curSchool.phase
            : state.gradeList.find((sitem: any) => {
                return sitem.gradeId == gradeId;
              })?.phase;
      }
      getSubjectList(phase);
    };
    /**
     * @name: 切换学科
     * @param item 当前学科
     */
    const switchSubject = (item: any) => {
      initSubject(item);

      state.isCheckAll = false;
      state.isIndeterminate = true;
      searchTeacher();
    };

    /**
     * @name: 获取学校列表
     */
    const getSchoolList = async () => {
      state.schoolOptions.loading = true;
      const res = await querySchoolPlatformListAPI({
        platformId: store.state.app.platFormInfo.id,
        provinceId: state.curDistrict.provinceId,
        cityId: state.curDistrict.cityId,
        districtId: state.curDistrict.districtId,
        schoolType: 1,
        regionalCategory: 2,
        platformState: 1,
        ...state.schoolAPIParams,
        regionId: store.state.app.platFormInfo.id
      });

      let schoolList: any[] = state.schoolList.length
        ? state.schoolList
        : [
            {
              schoolId: '',
              schoolName: '全部学校'
            }
          ];
      if (res.code === 1) {
        schoolList = [...schoolList, ...res.data.rows];
        state.schoolOptions.pageCount = res.data.page_count;
        state.schoolOptions.total = res.data.total_rows;
      }
      state.schoolList = schoolList;
      state.schoolOptions.loading = false;

      // 教师,获取教师学校
      let school;
      if (store.state.user.userType == USER_TYPE.TEACHER) {
        school = state.schoolList.find((item: any) => {
          return item.schoolId == store.state.user.schoolId;
        });
      }
      school = school || state.schoolList[0];
      // 非空条件: 非搜索状态
      // 第一页条件: 非下拉状态
      state.schoolAPIParams.page === 1 &&
        state.schoolList.length &&
        state.schoolAPIParams.keyWord === '' &&
        switchSchool(school);
    };
    /**
     * @name: 查询学校
     * @param keyWord 关键词
     */
    const searchSchool = async (keyWord: string = '') => {
      clearTimeout(state.schoolOptions.timer);
      state.schoolOptions.timer = setTimeout(() => {
        state.teacherList = [];
        state.teacherAPIParams.page = 1;
        state.schoolAPIParams.keyWord = keyWord;
        state.schoolAPIParams.page = 1;
        state.schoolList = [];
        getSchoolList();
      }, 300);
    };
    /**
     * @name: 下拉加载学校
     */
    const loadSchool = () => {
      state.schoolAPIParams.page++;
      if (state.schoolAPIParams.page > state.schoolOptions.pageCount) {
        return;
      }
      getSchoolList();
    };
    /**
     * @name: 获取年级列表
     */
    const getGradeList = async () => {
      const res = await getAllGradeListAPI({
        phases: state.curSchool.schoolId === '' ? state.curSchool.phase : ''
      });
      let gradeList = res.code === 1 ? res.data : [];
      if (state.curSchool.schoolId === '') {
        gradeList = groupByKey(gradeList, 'phase');
        gradeList = gradeList.map((item: any) => {
          let children = [
            {
              gradeId: '',
              gradeName: '全年级',
              phase: item.data,
              year: ''
            },
            ...item.list
          ];
          return {
            gradeId: item.data,
            gradeName:
              item.data == 1 ? '小学' : item.data == 2 ? '初中' : '高中',
            phase: item.data,
            children
          };
        });
        state.gradeList = gradeList;
        if (state.gradeList.length) {
          const grade = [
            state.gradeList[0].gradeId,
            state.gradeList[0].children[0].gradeId
          ];
          switchGrade(grade);
        }
      } else {
        gradeList = gradeList.filter((item: any) => {
          return state.curSchool.phase.includes(item.phase);
        });
        gradeList = [
          {
            gradeId: '',
            gradeName: '全年级',
            phase: '',
            year: ''
          },
          ...gradeList
        ];

        state.gradeList = gradeList;
        if (state.gradeList.length) {
          const grade = [state.gradeList[0].phase];
          switchGrade(grade);
        }
      }
    };
    /**
     * @name: 获取学科列表
     */
    const getSubjectList = async (phase: any) => {
      const res = await getSubjectListByPhaseAPI({
        phase
      });
      let subjectLsit = res.code === 1 ? res.data : [];
      if (state.curSchool.schoolId !== '') {
        subjectLsit.unshift({
          center_code: '',
          id: '',
          phase: 0,
          subject_code: '',
          subject_name: '全学科'
        });
      }
      state.subjectLsit = subjectLsit;
      switchSubject(state.subjectLsit[0]);
    };

    /**
     * @name: 初始化年级
     * @param grade 当前年级对象
     */
    const initGrade = (grade?: any) => {
      state.curGrade = grade || [];

      nextTick(() => {
        if (gradeRef.value) {
          const nodes = gradeRef.value.getCheckedNodes();
          if (nodes && nodes.length) {
            state.curGradeData = nodes[0].data;
          }
        }
      });
    };
    /**
     * @name: 初始化学校
     * @param school 当前学校对象
     */
    const initSchool = (school?: any) => {
      state.curSchool = school || {
        phase: '',
        schoolId: '',
        schoolName: '全部学校'
      };
    };
    /**
     * @name: 初始化学科
     * @param subject 当前学科对象
     */
    const initSubject = (subject?: any) => {
      state.curSubject = subject || {
        center_code: '',
        id: '',
        phase: 0,
        subject_code: '',
        subject_name: '全学科'
      };
    };

    /**
     * @name: 获取教师列表
     */
    const getTeacherList = async () => {
      state.teacherOptions.loading = true;
      let Fn;
      let params;
      let schoolId = state.curSchool.schoolId;

      if (schoolId) {
        Fn = getTeacherListSupportSubjectAndGradeAPI;
        params = {
          regionId: store.state.app.platFormInfo.id,
          subjectId: state.curSubject.id,
          schoolId,
          year: state.curGradeData.year,
          phase: state.curGradeData.phase,
          ...state.teacherAPIParams
        };
      } else {
        Fn = getRegionTeacherList;
        params = {
          regionId: store.state.app.platFormInfo.id,
          subjectId: state.curSubject.id,
          year: state.curGradeData.year,
          phase: state.curGradeData.phase,
          ...state.teacherAPIParams
        };
      }
      const res = await Fn(params);
      if (res.code === 1) {
        const teacherList = res.data.rows.map((item: any) => {
          item.userId = item.id;
          if (schoolId === '') {
            item.userName = item.realName;
            item.schoolName = item.schoolName || item.school_name;
          } else {
            item.userName = item.userName;
            item.schoolName = state.curSchool.schoolName;
            item.schoolId = schoolId;
          }
          return item;
        });
        state.teacherList =
          res.code === 1 ? [...state.teacherList, ...teacherList] : [];

        if (state.teacherAPIParams.wd === '') {
          state.teacherOptions.finished = res.data.rows.length === 0;
        } else {
          state.teacherOptions.finished =
            state.teacherAPIParams.page > state.teacherOptions.pageCount;
        }
      } else {
        state.teacherList = [];
      }

      state.teacherOptions.loading = false;
      state.teacherOptions.total = res.data.total_rows || 0;
      state.teacherOptions.pageCount = res.data.page_count || 0;
      nextTick(() => {
        setCheckedTree();
      });
    };
    /**
     * @name: 查询教师
     * @param keyWord 关键词
     */
    const searchTeacher = async (keyWord: string = '') => {
      clearTimeout(state.teacherOptions.timer);
      state.teacherOptions.timer = setTimeout(() => {
        state.teacherList = [];
        state.teacherAPIParams.wd = keyWord;
        state.teacherAPIParams.page = 1;
        getTeacherList();
      }, 300);
    };

    /**
     * @name: 下拉加载更多
     */
    const loadMoreTeacher = async () => {
      clearTimeout(state.teacherOptions.timer);

      if (state.teacherOptions.finished) {
        return;
      }

      state.teacherOptions.timer = setTimeout(() => {
        state.teacherAPIParams.page++;
        if (
          state.teacherAPIParams.wd !== '' &&
          state.teacherAPIParams.page > state.teacherOptions.pageCount
        ) {
          return;
        }
        getTeacherList();
      }, 300);
    };
    /**
     * @name: 全选
     */
    const handleCheckAllChange = (isCheckAll: any) => {
      let checkedNodes = [];
      if (isCheckAll) {
        checkedNodes = state.teacherList;
        if (props.disabledKeys.length) {
          //过滤已经禁选的列表
          checkedNodes = checkedNodes.filter((item: any) => {
            return !props.disabledKeys.includes(item.userId);
          });
        }
      } else {
        checkedNodes = [];
        state.curTeacherList = state.curTeacherList.filter((item: any) => {
          if (
            state.teacherList.some((sitem) => {
              return sitem.userId === item.userId;
            })
          ) {
            //获取当前需要删除的用户
            state.deleteUserList.push(item);
          }
          return !state.teacherList.some((sitem) => {
            return sitem.userId === item.userId;
          });
        });
      }

      handleCheckItemChange(null, { checkedNodes });
    };
    /**
     * @name: 设置树已选状态
     */
    const setCheckedTree = () => {
      if (!treeRef.value) {
        return;
      }
      const checkedKeys = [...state.curTeacherList].map((item: any) => {
        return item.userId;
      });
      treeRef.value.setCheckedKeys(checkedKeys);
      resetCheckAll();
    };

    /**
     * @name: 设置用户权限状态
     */
    const resetCheckAll = () => {
      if (!treeRef.value) {
        return;
      }
      const userCheckedNodes = treeRef.value.getCheckedNodes();
      if (userCheckedNodes) {
        let checkedCount = userCheckedNodes.length;
        state.isCheckAll =
          state.curTeacherList.length > 0 &&
          checkedCount === state.teacherList.length - props.disabledKeys.length;
        state.isIndeterminate =
          checkedCount > 0 && checkedCount < state.teacherList.length;
      }
    };

    /**
     * @name: 单个选择
     */
    const handleCheckItemChange = (value: any, data: any) => {
      let curTeacherList = [...state.curTeacherList, ...data.checkedNodes];
      //取消选中数据
      let deleteList: any = [];
      if (value) {
        const currentNode = treeRef.value.getNode(value);
        if (currentNode && !currentNode.checked) {
          deleteList.push(value);
          curTeacherList = curTeacherList.filter((item: any) => {
            return item.userId !== value.userId;
          });
        }
      } else {
        //取消全选时
        if (data.checkedNodes.length == 0) {
          deleteList = state.deleteUserList;
        }
      }
      state.curTeacherList = unique(curTeacherList, 'userId');
      let checkedCount = data.checkedNodes.length;
      state.isCheckAll =
        state.teacherList.length > 0 &&
        checkedCount === state.teacherList.length - props.disabledKeys.length;
      state.isIndeterminate =
        checkedCount > 0 && checkedCount < state.teacherList.length;
      ctx.emit('switch-teacher', state.curTeacherList, deleteList);
      setCheckedTree();
    };
    /**
     * @name: 清空已选
     */
    const clear = () => {
      state.curTeacherList = [];
      handleCheckItemChange(null, { checkedNodes: [] });
    };
    /**
     * @name: 删除已选
     * @param item 当前已选对象
     */
    const deleteUser = (item: any) => {
      if (!treeRef.value) {
        return;
      }
      state.curTeacherList = state.curTeacherList.filter((sitem: any) => {
        return sitem.userId !== item.userId;
      });
      treeRef.value.setChecked(item.userId, false);
      const checkedNodes = treeRef.value.getCheckedNodes();
      handleCheckItemChange(item, { checkedNodes });
    };

    /**
     * @name: 监听已选用户列表
     */
    watch(
      () => props.userList,
      () => {
        state.curTeacherList = props.userList;
      }
    );

    onMounted(() => {
      state.curTeacherList = props.userList;
      if (props.isOriginSchool) {
        switchSchool({
          phase: store.state.user.phase,
          schoolId: store.state.user.schoolId,
          schoolName: store.state.user.schoolName
        });
      } else {
        if (!props.isCustomContent) {
          // 没有自定义学校--请求区域
          switchArea(
            state.areaList.length ? state.areaList[0] : state.curDistrict
          );
        } else {
          // 自定义学校--直接请求当前学校用户
          switchSchool(state.curSchool);
        }
      }
    });

    return {
      ...toRefs(state),
      treeRef,
      gradeRef,
      addFsUrl,
      REGION_TYPE,
      searchSchool,
      loadSchool,
      searchTeacher,
      switchArea,
      switchSchool,
      loadMoreTeacher,
      switchGrade,
      switchSubject,
      handleCheckAllChange,
      handleCheckItemChange,
      clear,
      deleteUser
    };
  }
});
</script>

<style lang="scss" scoped>
.school-user-list {
  border: 1px solid #e6e8ed;
  border-radius: 4px;
  margin: 10px 0 0 0;
  .school-user-list-header {
    position: relative;
    padding: 0 10px;
    span {
      position: absolute;
      top: 10px;
      right: 10px;
    }
  }
  .user-tree {
    height: 255px;
    overflow: auto;
  }
}
</style>
<style lang="scss">
.school-user-list-header {
  ::v-deep(.el-checkbox__input.is-indeterminate) {
    background-color: transparent;
    border-color: transparent;
  }
}
.search-bar {
  margin-bottom: 5px;
  .el-input__inner {
    border-radius: 17px;
  }
}
.check-all {
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: transparent;
  }
}
.user-tree {
  .el-tree-node {
    margin: 10px 0;
  }
}
</style>

/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'fo', {
	alt: 'Alternativur tekstur',
	border: 'Bordi',
	btnUpload: 'Send til ambætaran',
	button2Img: 'Skal valdi myndaknøttur gerast til vanliga mynd?',
	hSpace: 'Høgri breddi',
	img2Button: 'Skal valda mynd gerast til myndaknøtt?',
	infoTab: 'Myndaupplýsingar',
	linkTab: 'Tilknýti',
	lockRatio: '<PERSON><PERSON><PERSON> lutfallið',
	menu: 'Myndaeginleikar',
	resetSize: 'Upprunastødd',
	title: 'Mynda<PERSON>inleikar',
	titleButton: 'Eginleikar fyri myndaknøtt',
	upload: 'Send',
	urlMissing: 'URL til mynd manglar.',
	vSpace: '<PERSON><PERSON><PERSON> breddi',
	validateBorder: 'Bordi má vera eitt heiltal.',
	validateHSpace: 'HSpace má vera eitt heiltal.',
	validateVSpace: 'VSpace má vera eitt heiltal.'
} );

# VCS支持功能实现

## 功能概述

实现了完整的版本控制系统（VCS）支持，包括Git和SVN，以及安全的凭据管理系统。用户现在可以：

1. **选择版本控制系统类型**：支持Git和SVN
2. **管理VCS凭据**：安全存储和管理访问凭据
3. **项目级别配置**：每个项目可以独立配置VCS类型和凭据
4. **公开仓库支持**：支持无需凭据的公开仓库

## 核心功能

### 1. VCS凭据管理

#### 模型特性
- **安全加密存储**：密码使用AES-256-CBC加密
- **多类型支持**：Git和SVN凭据分别管理
- **完整CRUD操作**：创建、读取、更新、删除
- **密码保护**：API响应中不包含密码信息

#### 凭据字段
```javascript
{
  id: "唯一标识符",
  name: "凭据名称",
  type: "git|svn",
  username: "用户名",
  password: "加密存储的密码",
  email: "邮箱（Git专用）",
  description: "描述信息",
  createdAt: "创建时间",
  updatedAt: "更新时间"
}
```

### 2. 项目VCS配置

#### 扩展字段
```javascript
{
  repositoryUrl: "仓库URL（替代gitUrl）",
  vcsType: "git|svn",
  vcsCredentialId: "关联的凭据ID（可选）",
  // 保留原有字段以兼容旧数据
  gitUrl: "兼容字段"
}
```

#### 配置选项
- **VCS类型选择**：Git或SVN
- **凭据关联**：可选择已创建的凭据或无凭据
- **动态加载**：根据VCS类型动态加载对应凭据列表

### 3. VCS服务统一接口

#### 支持操作
- **克隆仓库**：`cloneRepository(url, dir, type, credentialId, branch)`
- **更新仓库**：`updateRepository(dir, type, credentialId)`
- **切换分支**：`checkoutBranch(dir, branch, credentialId)` (Git专用)
- **检测类型**：`detectVcsType(dir)`
- **获取信息**：`getRepositoryInfo(dir, type)`
- **测试连接**：`testConnection(url, type, credentialId)`

#### Git支持
- 分支管理
- 远程仓库操作
- 认证URL构建
- 拉取和推送

#### SVN支持
- 检出和更新
- 用户名密码认证
- 非交互式操作
- 服务器证书信任

## 前端界面

### 1. VCS凭据管理页面

#### 功能特性
- **凭据列表展示**：卡片式布局，清晰显示凭据信息
- **类型标识**：Git和SVN使用不同颜色和图标
- **操作按钮**：测试连接、编辑、删除
- **模态框表单**：创建和编辑凭据
- **密码安全**：编辑时不显示现有密码

#### 页面访问
```
http://localhost:3000/vcs-credentials
```

### 2. 项目管理增强

#### 表单改进
- **VCS类型选择**：下拉菜单选择Git或SVN
- **仓库URL字段**：替代原Git URL字段
- **凭据选择**：动态加载对应类型的凭据
- **智能提示**：提供VCS凭据管理页面链接

#### 编辑支持
- **自动填充**：编辑时自动加载对应类型凭据
- **兼容性**：支持旧项目的gitUrl字段
- **验证增强**：必填字段验证

## API接口

### VCS凭据管理
```
GET    /api/vcs-credentials           # 获取所有凭据
GET    /api/vcs-credentials/type/:type # 按类型获取凭据
GET    /api/vcs-credentials/:id       # 获取单个凭据
POST   /api/vcs-credentials           # 创建凭据
PUT    /api/vcs-credentials/:id       # 更新凭据
DELETE /api/vcs-credentials/:id       # 删除凭据
POST   /api/vcs-credentials/:id/test  # 测试连接
```

### 项目管理（扩展）
```
# 项目创建/更新支持新字段
{
  "repositoryUrl": "仓库URL",
  "vcsType": "git|svn",
  "vcsCredentialId": "凭据ID（可选）"
}
```

## 安全机制

### 1. 密码加密
- **算法**：AES-256-CBC
- **密钥生成**：scrypt密钥派生
- **随机IV**：每次加密使用随机初始化向量
- **安全存储**：密码以加密形式存储在JSON文件中

### 2. API安全
- **密码过滤**：API响应中自动过滤密码字段
- **权限控制**：仅内部服务可获取完整凭据信息
- **输入验证**：严格的参数验证和类型检查

### 3. 前端安全
- **密码隐藏**：编辑表单中不显示现有密码
- **确认机制**：密码为空时提示用户确认
- **HTTPS建议**：生产环境建议使用HTTPS

## 部署集成

### 1. 自动识别
- **VCS类型检测**：自动识别项目使用的VCS类型
- **凭据应用**：根据项目配置自动使用对应凭据
- **降级处理**：凭据失效时的错误处理

### 2. 操作流程
```
1. 读取项目VCS配置
2. 获取对应凭据（如果配置了）
3. 根据VCS类型选择操作方法
4. 执行克隆/更新操作
5. 处理分支切换（Git专用）
6. 继续构建流程
```

## 使用指南

### 1. 创建VCS凭据
1. 访问"VCS凭据管理"页面
2. 点击"添加凭据"按钮
3. 选择VCS类型（Git/SVN）
4. 填写凭据信息
5. 保存凭据

### 2. 配置项目VCS
1. 在项目管理页面创建/编辑项目
2. 选择版本控制系统类型
3. 输入仓库URL
4. 选择对应的VCS凭据（可选）
5. 保存项目配置

### 3. 测试连接
1. 在VCS凭据管理页面
2. 点击凭据的"测试"按钮
3. 输入要测试的仓库URL
4. 查看连接测试结果

## 兼容性

### 1. 向后兼容
- **旧项目支持**：自动识别gitUrl字段并转换
- **默认配置**：未配置VCS类型时默认为Git
- **渐进升级**：可逐步迁移现有项目

### 2. 数据迁移
```javascript
// 自动处理旧字段
const repositoryUrl = project.repositoryUrl || project.gitUrl;
const vcsType = project.vcsType || 'git';
```

## 错误处理

### 1. 常见错误
- **凭据不存在**：提示用户检查凭据配置
- **认证失败**：提示检查用户名密码
- **仓库不存在**：提示检查仓库URL
- **网络错误**：提示检查网络连接

### 2. 降级策略
- **凭据失效**：尝试无凭据访问
- **VCS工具缺失**：提示安装对应工具
- **权限不足**：提示检查仓库权限

## 系统要求

### 1. Git支持
- **Git客户端**：系统需安装Git
- **网络访问**：能够访问Git仓库
- **认证支持**：支持HTTPS认证

### 2. SVN支持
- **SVN客户端**：系统需安装Subversion
- **网络访问**：能够访问SVN服务器
- **证书信任**：自动信任服务器证书

## 测试验证

### 1. 功能测试
```bash
node test-vcs-functionality.js
```

### 2. 测试覆盖
- ✅ VCS凭据CRUD操作
- ✅ 密码加密/解密
- ✅ 项目VCS配置
- ✅ 前端界面集成
- ✅ API接口测试
- ✅ 连接测试功能

## 未来扩展

### 1. 功能增强
- **SSH密钥支持**：支持SSH密钥认证
- **多分支管理**：更强大的分支管理功能
- **仓库浏览**：在线浏览仓库内容
- **提交历史**：查看提交历史和差异

### 2. 安全增强
- **密钥轮换**：定期更换加密密钥
- **访问日志**：记录凭据使用日志
- **权限管理**：基于角色的凭据访问控制

现在您的部署系统已经完全支持Git和SVN，并提供了完整的凭据管理功能！

/**
 * @name 导入样式
 */
import "./css/install.scss";
import "./assets/icon/iconfont.css";
// 使用symbol
import "./assets/icon/iconfont.js";

import MkDialog from "./ui/mk-dialog/";
import MkConfirmDialog from "./ui/mk-confirm-dialog";
import MkTooltip from "./ui/mk-tooltip/";
import MkPagination from "./ui/mk-pagination";
import MkExportTable from "./ui/mk-export-table";
import MkPreviewSlider from "./ui/mk-preview-slider";
import MkTabs from "./ui/mk-tabs";
import MkNoData from "./ui/mk-no-data";
import MkCardTitle from "./ui/mk-card-title";
import MkSearch from "./ui/mk-search";
import MkBreadCrumb from "./ui/mk-bread-crumb";
import MkCourseCard from "./ui/mk-course-card";
import MkSchoolCard from "./ui/mk-school-card";
import MkLiveCard from "./ui/mk-live-card";
import MkInfoCard from "./ui/mk-info-card";
import MkLogout from "./ui/mk-logout";
import MkUpload from "./ui/mk-upload";
import MkSelect from "./ui/mk-select";
import MkNetresourceDialog from "./ui/mk-netresource-dialog";
import MkBanner from "./ui/mk-banner";
import MkSortCaret from "./ui/mk-sort-caret";
import MKEditor from "./ui/mk-editor";
import MKImage from "./ui/mk-image";
import MKRollingNumber from "./ui/mk-rolling-number";
import MkCarousel from "./ui/mk-carousel";
import MkLogin from "./ui/mk-login";
import MkAreaSelect from "./ui/mk-area-select";

const components = [
  MkDialog,
  MkConfirmDialog,
  MkTooltip,
  MkPagination,
  MkExportTable,
  MkPreviewSlider,
  MkTabs,
  MkNoData,
  MkSearch,
  MkBreadCrumb,
  MkCardTitle,
  MkCourseCard,
  MkSchoolCard,
  MkLiveCard,
  MkInfoCard,
  MkLogout,
  MkUpload,
  MkSelect,
  MkNetresourceDialog,
  MkBanner,
  MkSortCaret,
  MKEditor,
  MKImage,
  MKRollingNumber,
  MkCarousel,
  MkLogin,
  MkAreaSelect
];
import { service } from "./src/service/index";

const install = function(Vue: any) {
  components.forEach((component) => {
    Vue.component(component.name, component);
  });
};

/**
 * library挂载vue实例函数
 * @param {*} vue vue实例
 * @param opts 可配置参数
 * @opts service 接口对象，包含GET,POST方法
 */
const libraryInstall = (
  app: any,
  opts: { service: any; [propName: string]: any }
) => {
  install(app);
  service(opts.service);
};

export default libraryInstall;

<template>
  <div
    class="mk-module-title"
    :data-type="type"
    :class="isJLWX() ? 'jlwx--module-title' : ''"
  >
    <div class="title-text">
      <ul v-if="type === 'dot'" class="mk-module-title-dot">
        <li></li>
        <li></li>
        <li></li>
        <li></li>
      </ul>
      <span class="mk-module-left-star flex-center" v-if="type === 'star'">
        <i class="icon iconfont iconxing"></i>
        <i class="icon iconfont iconxing"></i>
        <i class="icon iconfont iconxing"></i>
      </span>
      <!--自定义标题内容 -->
      <slot v-if="isCustomTitle" name="title"></slot>
      <span class="title" v-else> {{ title || "" }}</span>
      <span class="mk-module-right-star flex-center" v-if="type === 'star'">
        <i class="icon iconfont iconxing"></i>
        <i class="icon iconfont iconxing"></i>
        <i class="icon iconfont iconxing"></i>
      </span>
      <span class="mk-module-right-star flex-center" v-if="type === 'custom'">
      </span>
    </div>
    <div class="more" @click="gotoMore()" v-if="isShowMore">
      <slot name="rightText">
        <span class="text">{{ rightText }}</span>
      </slot>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, PropType } from "vue";
import { isJLWX } from "@/library/src/utils/valiteSite";

export default defineComponent({
  name: "mk-card-title",

  emits: ["goto-more"],

  props: {
    // 展示类型
    type: {
      type: String as PropType<
        "line" | "card" | "dot" | "star" | "custom" | "card2"
      >,
      default: "card"
    },
    // 标题
    title: {
      type: String,
      require: true
    },
    // 是否自定义标题内容
    isCustomTitle: {
      type: Boolean,
      default: false
    },
    // 右侧文本
    rightText: {
      type: String,
      default: "查看更多"
    },
    // 是否显示更多
    isShowMore: {
      type: Boolean,
      default: true
    }
  },

  setup(props, context) {
    /**
     * 查看更多
     */
    function gotoMore() {
      context.emit("goto-more");
    }

    return {
      isJLWX,
      gotoMore
    };
  }
});
</script>

<style lang="scss" scoped>
@import "../../css/_mixins-theme.scss";
.mk-module-title {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  .title-text {
    display: inline-flex;
    align-items: flex-end;
    justify-content: flex-start;
    font-size: 20px;
    color: #333;
    font-weight: bold;
    margin-right: 13px;
    .mk-module-title-dot {
      position: relative;
      margin-right: 10px;
      margin-left: 20px;
      li {
        position: absolute;
        left: 0;
        width: 12px;
        height: 12px;
        background: transparent;
        border-radius: 50%;
        position: absolute;
        box-shadow: -10px -13px 3px 1px $mainColor-red;
        list-style: none;
        &:nth-child(1) {
          opacity: 0.3;
          top: -10px;
          left: -10px;
        }
        &:nth-child(2) {
          opacity: 0.5;
          top: -5px;
          left: -5px;
        }
        &:nth-child(3) {
          display: none;
        }
      }
    }
  }

  .more {
    display: flex;
    align-items: center;
    cursor: pointer;
    .text {
      font-size: 14px;
      color: #666;
      margin-right: 7px;
      cursor: pointer;
      &:hover {
        @include theme_color();
      }
    }
  }

  &[data-type="card"] {
    position: relative;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    &:before {
      position: absolute;
      content: "";
      width: 45px;
      height: 6px;
      background: linear-gradient(to right, #2e8bff, transparent);
      float: left;
      bottom: 16px;
      left: 1px;
      border-bottom-right-radius: 50%;
      border-top-right-radius: 50%;
      z-index: 0;
    }
  }
  &[data-type="custom"] {
    position: relative;
    border-bottom: 1px solid rgba(244, 244, 244, 0.61);
    font-size: 20px;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #333333;
  }
  &[data-type="card2"] {
    position: relative;
    padding-bottom: 20px;
    &:before {
      position: absolute;
      content: "";
      width: 45px;
      height: 6px;
      background: linear-gradient(to right, #d41414, transparent);
      float: left;
      bottom: 16px;
      left: 1px;
      border-bottom-right-radius: 50%;
      border-top-right-radius: 50%;
      z-index: 0;
    }
  }
  &[data-type="line"] {
    position: relative;
    height: 20px;
    &:before {
      content: "";
      display: inline-block;
      width: 4px;
      height: 20px;
      border-radius: 0px 4px 4px 0px;
      position: absolute;
      left: 0;
      top: 0px;

      @include theme_background-color();
    }
    .title-text {
      margin: 0 13px 0 10px;
    }
  }
  &[data-type="star"] {
    padding-bottom: 20px;
    .title-text {
      margin: 0 auto;
      font-size: 24px;
      font-weight: 400;
      .title {
        margin: 0 5px;
      }
      i {
        margin: 3px;
        font-size: 12px;
      }
      .mk-module-left-star {
        i {
          &:nth-child(1) {
            opacity: 0.3;
          }
          &:nth-child(2) {
            opacity: 0.6;
          }
          &:nth-child(3) {
            opacity: 1;
          }
        }
      }
      .mk-module-right-star {
        i {
          &:nth-child(1) {
            opacity: 1;
          }
          &:nth-child(2) {
            opacity: 0.6;
          }
          &:nth-child(3) {
            opacity: 0.3;
          }
        }
      }
    }
  }
}
.jlwx--module-title {
  .title-text {
    .title {
      font-family: PingFangSC-Medium;
      font-size: 22px;
      color: #333333;
    }
  }
  .more {
    .text {
      font-family: PingFangSC-Medium;
      font-size: 16px;
      &:hover {
        @include theme_color();
      }
    }
  }
}

[data-theme="red"] {
  .mk-module-title-dot li {
    box-shadow: -10px -13px 3px 1px $mainColor-red !important;
  }
  [data-type="card"]:before {
    background: linear-gradient(
      to right,
      $mainColor-red,
      transparent
    ) !important;
  }

  [data-type="star"] {
    .title-text {
      color: $mainColor-red !important;
      i {
        color: $mainColor-red !important;
      }
    }
  }
}
[data-theme="cyan"] {
  .mk-module-title-dot li {
    box-shadow: -10px -13px 3px 1px $mainColor-cyan !important;
  }
  [data-type="card"]:before {
    background: linear-gradient(
      to right,
      $mainColor-cyan,
      transparent
    ) !important;
  }

  [data-type="star"] {
    .title-text {
      color: $mainColor-cyan !important;
      i {
        color: $mainColor-cyan !important;
      }
    }
  }
}
[data-theme="yellow"] {
  .mk-module-title-dot li {
    box-shadow: -10px -13px 3px 1px $mainColor-yellow !important;
  }
  [data-type="card"]:before {
    background: linear-gradient(
      to right,
      $mainColor-yellow,
      transparent
    ) !important;
  }

  [data-type="star"] {
    .title-text {
      color: $mainColor-yellow !important;
      i {
        color: $mainColor-yellow !important;
      }
    }
  }
}
[data-theme="blue"] {
  .mk-module-title-dot li {
    box-shadow: -10px -13px 3px 1px $mainColor-blue !important;
  }
  [data-type="card"]:before {
    background: linear-gradient(
      to right,
      $mainColor-blue,
      transparent
    ) !important;
  }

  [data-type="star"] {
    .title-text {
      color: $mainColor-blue !important;
      i {
        color: $mainColor-blue !important;
      }
    }
  }
}
[data-theme="green"] {
  .mk-module-title-dot li {
    box-shadow: -10px -13px 3px 1px $mainColor-green !important;
  }
  [data-type="card"]:before {
    background: linear-gradient(
      to right,
      $mainColor-green,
      transparent
    ) !important;
  }

  [data-type="star"] {
    .title-text {
      color: $mainColor-green !important;
      i {
        color: $mainColor-green !important;
      }
    }
  }
}
[data-theme="purple"] {
  .mk-module-title-dot li {
    box-shadow: -10px -13px 3px 1px $mainColor-purple !important;
  }
  [data-type="card"]:before {
    background: linear-gradient(
      to right,
      $mainColor-purple,
      transparent
    ) !important;
  }

  [data-type="star"] {
    .title-text {
      color: $mainColor-purple !important;
      i {
        color: $mainColor-purple !important;
      }
    }
  }
}
[data-theme="seablue"] {
  .mk-module-title-dot li {
    box-shadow: -10px -13px 3px 1px $mainColor-seablue !important;
  }
  [data-type="card"]:before {
    background: linear-gradient(
      to right,
      $mainColor-seablue,
      transparent
    ) !important;
  }

  [data-type="star"] {
    .title-text {
      color: $mainColor-seablue !important;
      i {
        color: $mainColor-seablue !important;
      }
    }
  }
}
[data-theme="weekblue"] {
  .mk-module-title-dot li {
    box-shadow: -10px -13px 3px 1px $mainColor-weekblue !important;
  }
  [data-type="card"]:before {
    background: linear-gradient(
      to right,
      $mainColor-weekblue,
      transparent
    ) !important;
  }

  [data-type="star"] {
    .title-text {
      color: $mainColor-weekblue !important;
      i {
        color: $mainColor-weekblue !important;
      }
    }
  }
}
[data-theme="darkreen"] {
  .mk-module-title-dot li {
    box-shadow: -10px -13px 3px 1px $mainColor-darkreen !important;
  }
  [data-type="card"]:before {
    background: linear-gradient(
      to right,
      $mainColor-darkreen,
      transparent
    ) !important;
  }

  [data-type="star"] {
    .title-text {
      color: $mainColor-darkreen !important;
      i {
        color: $mainColor-darkreen !important;
      }
    }
  }
}
</style>

﻿import { API } from "../index";

/****************************** public *****************************************/
/**
 * @name: 导出excel生成统计报表
 * @param type 导出模块类别(字典表base_dict中318类别,001:普通作业导出,002:考试导出)
 * @param primaryKey 主键id(作业id,考试id等)
 * @param title 显示名称
 * @param paramJson 业务参数json(存储那些个性化导出需要的参数)
 */
export const commonExportRecord = (params: {
  type: string;
  primaryKey: string;
  title: string;
  paramJson?: string;
}) => {
  return API.POST("/public/export/commonExportRecord", params);
};
/**
 * @name: 导出excel获取列表
 * @param type 导出模块类别(字典表base_dict中318类别,001:普通作业导出,002:考试导出)
 * @param primaryKey 主键id(作业id,考试id等)
 * @param keyWord 显示名称，作业/考试/关联考试名
 * @param page 页码
 * @param limit 请求条目
 */
export const getCommonExportRecord = (params: {
  type: string;
  primaryKey: string;
  keyWord?: string;
  page: number;
  limit: number;
  optUserId: string;
  optRealName: string;
}) => {
  return API.POST("/public/export/getCommonExportRecord", params);
};
/**
 * @name [公共缓存]-公共缓存集合
 **/
export const getCommonCatchListAPI = (params: {
  appType: string;
  uId: string;
}) => {
  return API.GET("/public/catch/getCommonCatchList", params);
};
/**
 * @name:[公共缓存]-插入缓存接口
 * @param appType 相关权限编码
 * @param uId 用户id
 * @param catchList 要存入的json数据
 */
export const insertCommonCatchAPI = (params: {
  appType: string;
  uId: string;
  catchList: string;
}) => {
  return API.GET("/public/catch/insertCommonCatch", params);
};
/**
 * @name: 获取STS上传授权秘钥
 * @param {params} 接口参数
 *    @path 权限目录
 *    @timestamp 时间戳
 *    @secure 加密规则
 */
export const getSTSTokenAPI = (params: {
  path: string;
  timestamp: string;
  secure: string;
}) => {
  return API.POST("/public/oss/getALiYunSTS", params);
};

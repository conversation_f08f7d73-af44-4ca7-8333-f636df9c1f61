<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .nav-link { display: block; padding: 10px; margin: 5px 0; background: #f0f0f0; text-decoration: none; color: #333; }
        .nav-link:hover { background: #e0e0e0; }
        .test-result { margin: 20px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>导航链接修复测试</h1>
    
    <div class="test-result info">
        <strong>测试说明：</strong><br>
        - 带有 data-page 属性的链接应该被JavaScript拦截（不跳转）<br>
        - 没有 data-page 属性的链接应该正常跳转
    </div>
    
    <h2>测试链接</h2>
    
    <!-- 内部页面链接（应该被拦截） -->
    <a href="#" class="nav-link" data-page="projects">
        📁 项目管理 (内部页面 - 应该被拦截)
    </a>
    
    <a href="#" class="nav-link" data-page="servers">
        🖥️ 服务器管理 (内部页面 - 应该被拦截)
    </a>
    
    <!-- 外部页面链接（应该正常跳转） -->
    <a href="/vcs-credentials" class="nav-link">
        🔑 VCS凭据管理 (外部页面 - 应该正常跳转)
    </a>
    
    <a href="/user-management" class="nav-link">
        👥 用户管理 (外部页面 - 应该正常跳转)
    </a>
    
    <a href="https://www.baidu.com" class="nav-link" target="_blank">
        🌐 外部网站 (应该正常跳转)
    </a>
    
    <div id="test-results" class="test-result success" style="display: none;">
        <strong>测试结果：</strong><br>
        <div id="test-log"></div>
    </div>
    
    <script>
        // 模拟修复后的导航处理逻辑
        const navLinks = document.querySelectorAll('.nav-link');
        const testLog = document.getElementById('test-log');
        const testResults = document.getElementById('test-results');
        
        function logTest(message) {
            testLog.innerHTML += message + '<br>';
            testResults.style.display = 'block';
        }
        
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const page = link.getAttribute('data-page');
                const href = link.getAttribute('href');
                
                if (!page) {
                    // 没有 data-page 属性，允许正常跳转
                    logTest(`✅ 允许跳转: ${link.textContent.trim()} -> ${href}`);
                    return; // 不阻止默认行为
                }
                
                // 有 data-page 属性，阻止默认行为
                e.preventDefault();
                logTest(`🚫 拦截内部页面: ${link.textContent.trim()} (data-page="${page}")`);
                
                // 这里可以添加显示内部页面的逻辑
                alert(`内部页面被拦截: ${page}`);
            });
        });
        
        // 页面加载完成后显示说明
        window.addEventListener('load', () => {
            logTest('📋 导航处理逻辑已加载，点击上方链接进行测试');
        });
    </script>
</body>
</html>

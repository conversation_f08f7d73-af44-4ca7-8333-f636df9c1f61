<template>
  <el-row
    class="rank-container"
    type="flex"
    align="baseline"
    justify="space-between"
    :gutter="18"
  >
    <el-col
      v-for="(item, index) in rankInfo"
      :key="index"
      :span="8"
      class="rank-list-item"
    >
      <ranking-list
        :color="item.color"
        :title="item.title"
        :icon="item.icon"
        :showType="item.showType"
        :rank-list="item.list"
      ></ranking-list>
    </el-col>
  </el-row>
</template>

<script lang="ts">
import { defineComponent, PropType } from "vue";

export interface IRank {
  /** 名称 */
  title: string;
  /** 图标 */
  icon: string;
  /** 颜色色值 */
  color: string;
  /** 是否展示类型 */
  showType: number;
}
export default defineComponent({
  name: "mk-rank-info-card",

  props: {
    dataList: {
      type: [] as PropType<IRank[]>,
      required: true,
    },
  },
});
</script>

<style lang="scss" scoped>
.rank-list-item {
  background-color: #fff;
  flex: 0 0 32.2%;
}
</style>
﻿<template>
  <canvas ref="waterMarkerCanvasRef" :width="width"  :height="height" style="border:1px solid #eee;border-radius: 6px;max-width: 100%"></canvas>
</template>

<script lang="ts">
import {defineComponent, onMounted, PropType, ref, watch} from 'vue'

/** 水印配置项 */
export interface IWaterMarkerOptions{
  /** 文字大小 */
  fontSize?: string;
  /** 文字颜色 */
  color?: string;
  /** 水印文案 */
  text: string;
}

/**
 * @Name: 水印图片
 * @Descripttion: 支持url图片地址和通过id找<img>元素设置图片水印
 *                url优先级  >  id<img>元素
 *                支持单行,多行文本居中显示
 * @Author: gaohan
 * @Date: 2022/8/29 16:55
 * @LastEditors: gaohan
 * @LastEditTime: 2022/8/29
 */
export default defineComponent({
  name: 'mk-water-marker',

  emits: ['on-water-marker'],

  props: {
    // img标签id
    id: {
      type:String,
      default: ''
    },
    // img图片地址
    url: {
      type: String,
      default: ''
    },
    // 配置项
    options: {
      type: Object as PropType<IWaterMarkerOptions>,
      default: ()=>{
        return {
          fontSize: '20px',
          color: '#fff',
          text: ''
        }
      }
    },
    // 宽度
    width: {
      type: Number,
      default: 192
    },
    // 宽度
    height: {
      type: Number,
      default: 108
    }
  },

  setup(props,ctx) {
    let image:any = null
    const waterMarkerCanvasRef = ref<any>(null)

    /**
     * @name: 获取图片地址
     */
    const getImgSrc = ()=>{
      let src:any = null
      if( props.url ){
        src = props.url
      }else{
        const img:any = document.getElementById(props.id)
        if( img ){
            src = img.src
        }
      }
      return src
    }
    /**
     * @name: 设置水印
     */
    const setWaterMarker = ()=>{
      const src = getImgSrc()
      if( !src ){
          return
      }
      // image异步问题,先销毁image对象,保证唯一性
      image = null
      image = new Image();

      image.src = /^http/gi.test(src) ? `${src}?v=${Math.random()}` : src
      image.crossOrigin = '*';
      image.onload =  () => {
        const canvas= waterMarkerCanvasRef.value
        const ctx = canvas.getContext('2d')
        ctx.clearRect(0, 0, canvas.width, canvas.height)
        ctx.drawImage(image, 0, 0, canvas.width, canvas.height)

        if( !props.options.text ){
          onWaterMarker()
          return
        }

        fillTextCenter(ctx,24,26,canvas.width, canvas.height)
        onWaterMarker()
      };
    }
    /**
     * @name: 获取字节长度
     * @param str 字符串
     * @return: 字节长度
     */
    const getByteLength = (str:string)=>{
      let len = str.length, truelen = 0;
      for(let x = 0; x < len; x++){
        if(str.charCodeAt(x) > 128){
          truelen += 2;
        }else{
          truelen += 1;
        }
      }
      return truelen;
    }
    /**
     * @name: 按字节长度截取字符串
     * @param str 字符串
     * @param leng 字节长度
     * @return: 截取位置
     */
    const cutString = (str:string,leng:number)=>{
      let len = str.length, tlen = len, nlen = 0;
      for(let x = 0; x < len; x++){
        if(str.charCodeAt(x) > 128){
          if(nlen + 2 < leng){
            nlen += 2;
          }else{
            tlen = x;
            break;
          }
        }else{
          if(nlen + 1 < leng){
            nlen += 1;
          }else{
            tlen = x;
            break;
          }
        }
      }
      return tlen;
    }
    /**
     * @name: 设置居中文字水印
     * @param ctx canvas上下问
     * @param lineHeight 行高
     * @param byteLength 字节长度
     * @param width canvas宽度
     * @param height canvas高度
     */
    const fillTextCenter = (ctx:any, lineHeight:number, byteLength:number, width:number, height:number) =>{
      let text = props.options.text
      let btl = getByteLength(text)
      let left = width/2
      let top = 0
      // 文字样式
      const fontFamily = ' 微软雅黑, Microsoft YaHei, Verdana, Arial, Helvetica, sans-serif'
      ctx.font = 'bold ' + (props.options.fontSize || '20px') + fontFamily
      ctx.fillStyle = props.options.color || '#fff'
      ctx.textBaseline = "middle";
      ctx.textAlign = 'center';
      // 单行文字
      if( btl <= 24 ){
        ctx.fillText(text, left, height/2);
        return
      }
      // 多行文字
      for(let i = 1; btl > 0; i++){
        let tl = cutString(text, byteLength);
        let yL = (i-1) * lineHeight + top/2+ (height-top-lineHeight)/2
        ctx.fillText(text.substr(0, tl).replace(/^\s+|\s+$/, ""), left,yL);
        text = text.substr(tl);
        btl = getByteLength(text)
      }
    }
    /**
     * @name: 传递事件
     */
    const onWaterMarker = ()=>{
      const url = waterMarkerCanvasRef.value?.toDataURL()
      ctx.emit('on-water-marker', url)
    }

    onMounted(()=>{
      setWaterMarker()
    })

    watch(()=>props.url, ()=>{
      setWaterMarker()
    })
    watch(()=>props.options, ()=>{
      setWaterMarker()
    })

    return {
      waterMarkerCanvasRef
    }
  }
})
</script>
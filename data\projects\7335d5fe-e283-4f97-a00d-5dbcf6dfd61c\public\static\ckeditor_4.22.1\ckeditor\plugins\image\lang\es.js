/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'es', {
	alt: 'Texto Alternativo',
	border: 'Borde',
	btnUpload: 'Enviar al Servidor',
	button2Img: '¿Desea convertir el botón de imagen en una simple imagen?',
	hSpace: 'Esp.Horiz',
	img2Button: '¿Desea convertir la imagen en un botón de imagen?',
	infoTab: 'Información de Imagen',
	linkTab: 'Vínculo',
	lockRatio: 'Proporcional',
	menu: 'Propiedades de Imagen',
	resetSize: 'Tamaño Original',
	title: 'Propiedades de Imagen',
	titleButton: 'Propiedades de Botón de Imagen',
	upload: 'Cargar',
	urlMissing: 'Debe indicar la URL de la imagen.',
	vSpace: 'Esp.Vert',
	validateBorder: 'El borde debe ser un número.',
	validateHSpace: 'El espaciado horizontal debe ser un número.',
	validateVSpace: 'El espaciado vertical debe ser un número.'
} );

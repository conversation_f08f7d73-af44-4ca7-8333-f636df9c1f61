<template>
  <div>
    <el-select
      v-model="selectValue"
      v-loadselect="loadMore"
      :placeholder="placeholder"
      :clearable="isClearable"
      :filearable="isFilterable"
      :multiple="isMultiple"
      :remote="isRemote"
      :popper-append-to-body="false"
      :remote-method="remoteMethod"
      @change="changeMethod"
    >
      <el-option
        v-for="item in list"
        :key="item[value]"
        :label="item[label]"
        :value="item[value]"
      >
        <template v-if="isShowLeft">
          <span style="float: left">{{ item[label] }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{
            item[value]
          }}</span>
        </template>
        <template v-else>
          <span>{{ item[label] }}</span>
        </template>
      </el-option>
    </el-select>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from "vue";
export default defineComponent({
  name: "mk-select",
  props: {
    label: String,
    value: String,
    modelValue: String,
    //占位符
    placeholder: {
      type: String,
      default: "",
    },
    //是否显示label，value数据
    isShowLeft: {
      type: Boolean,
      default: false,
    },
    //是否可清空
    isClearable: {
      type: Boolean,
      default: true,
    },
    //是否可搜索
    isFilterable: {
      type: Boolean,
      default: true,
    },
    //是否远程搜索
    isRemote: {
      type: Boolean,
      default: false,
    },
    //是否多选
    isMultiple: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: [],
    },
  },
  setup(props, context) {
    const state = reactive({
      selectValue: props.modelValue,
    });

    /**
     * 加载更多
     */
    function loadMore() {
      context.emit("load-more");
    }
    /**
     * 远程搜索
     */
    function remoteMethod() {
      context.emit("remote-method");
    }

    /**
     * 切换数据
     */
    function changeMethod() {
      context.emit("change-method", state.selectValue);
    }

    return {
      ...toRefs(state),
      loadMore,
      remoteMethod,
      changeMethod,
    };
  },
});
</script>

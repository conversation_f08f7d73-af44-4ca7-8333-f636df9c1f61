<template>
  <div class="look-box">
    <div class="look-title">查看：</div>
    <mk-tooltip :placement="'left'">
      <template #custom-tooltip-content>
        <div class="tooltip-content">
          1、限制资源分享到校本资源后被查看/下载/存网盘的权限<br />
          2、公开：全校所有老师都能看到。<br />
          3、指定教研组：可以指定教研组以及教研组（学科组）下的备课组（年级学科组）成员可见<br />
        </div>
      </template>
      <template #tooltip-content>
        <i class="icon iconfont iconicon-test16"></i>
      </template>
    </mk-tooltip>
    <div class="look-radio">
      <el-radio-group v-model="lookRadio" @change="changeRadio">
        <el-radio :label="LOOK_TYPE.open">公开</el-radio>
        <el-radio :label="LOOK_TYPE.group">教研组</el-radio>
      </el-radio-group>
    </div>
  </div>
  <!--教研组级联选择-->
  <div class="group-cascader" v-if="lookRadio == LOOK_TYPE.group">
    <span style="color: red">*</span>
    <span>教研组：</span>
    <el-cascader
      v-model="groupValue"
      clearable
      :props="groupProps"
      @change="groupChange"
      placeholder="请选择教研组"
      empty="暂无数据"
      style="width: 260px"
    >
    </el-cascader>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch, onMounted } from 'vue';
import { getTeachGroupListAPI } from '@/library/src/service/API/base';
import { ElMessage } from 'element-plus';
import { useStore } from 'vuex';
import { IGlobalState } from '@/library/src/store';
/**查看选择类型*/
export enum LOOK_TYPE {
  //公开
  open = 1,
  //教研组
  group = 2
}
export default defineComponent({
  name: 'group-select',
  props: {
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    ///教研组绑定值(级联框参数集合)
    groupValue: {
      type: Array,
      default: () => []
    },
    ////查看 绑定的类型值
    lookRadio: {
      type: Number,
      default: LOOK_TYPE.open
    }
  },
  setup(props, context) {
    const store = useStore<IGlobalState>();
    const state = reactive({
      //查看 绑定的类型值
      lookRadio: props.lookRadio,
      //教研组绑定值(级联框参数集合)
      groupValue: props.groupValue,
      //配置项
      groupProps: {
        lazy: true,
        // 严格的遵守父子节点不互相关联
        checkStrictly: true,
        // 加载动态数据的方法
        lazyLoad: lazyLoadGroup
      },
      //教研组列表
      groupList: [] as any[]
    });
    /**
     * @name 加载教研组数据
     */
    async function lazyLoadGroup(node: any, resolve: any) {
      // 禁用选项直接结束,避免接口多次请求
      if (node.data && node.data.disabled) {
        resolve([]);
        return;
      }
      const { level } = node;
      node.loading = level <= 1;
      if (node.loading) {
        const id: any = node.data && node.data.value ? node.data.value : '';
        const res = await getTeachGroupList(id);
        let groupList = res.data.rows;
        const nodes = await groupList.map((item: any) => ({
          value: item.id,
          label: item.title,
          leaf: level >= 1
        }));
        resolve(nodes);
      }
    }
    /**
     * @name 获取教研组列表
     */
    async function getTeachGroupList(id?: any) {
      const res = await getTeachGroupListAPI({
        schoolId: store.state.user.schoolId,
        type: id ? '2' : '1',
        parentId: id || '',
        page: 1,
        limit: 999
      });
      if (res.code == 1) {
        return res;
      }
      ElMessage.error('获取教研组数据失败,请重试!');
      return null;
    }
    /**
     * @name 节点选中
     * @param val 返回的节点id 或 id集合
     */
    function groupChange(val: any) {
      if (val === null || !val.length) {
        state.groupValue = [];
      } else {
        state.groupValue = val;
      }
      context.emit('select-group', state.groupValue);
    }
    /**
     * @name 改变查看选择单选框
     */
    function changeRadio(value: any) {
      state.lookRadio = value;
      //教研组数据为空时 点击教研组的时候警告提示 状态还是在“公开”
      if (!state.groupList.length) {
        state.lookRadio = LOOK_TYPE.open;
        ElMessage.warning('本校暂未创建教研组，请联系校管理员创建');
      }
      context.emit('change-look-radio', state.lookRadio);
    }
    watch(
      () => props.groupValue,
      (val: any) => {
        state.groupValue = val;
      },
      {
        deep: true
      }
    );
    watch(
      () => props.lookRadio,
      (val: any) => {
        state.lookRadio = val;
      },
      {
        deep: true
      }
    );
    onMounted(async () => {
      //第一次请求数据 判断是否存在教研组
      const res = await getTeachGroupList();
      state.groupList = res.data.rows;
      //教研组数据为空时 状态还是在“公开”
      if (!state.groupList.length) {
        state.lookRadio = LOOK_TYPE.open;
      }
    });

    return {
      ...toRefs(state),
      LOOK_TYPE,
      lazyLoadGroup,
      groupChange,
      changeRadio
    };
  }
});
</script>
<style lang="scss">
.look-box {
  font-size: 16px;
  display: flex;
  align-items: center;
  .iconicon-test16 {
    font-size: 20px;
    cursor: pointer;
  }
  .look-radio {
    margin-left: 10px;
  }
}
.tooltip-content {
  width: 400px;
  line-height: 30px;
  font-size: 14px;
}
.group-cascader {
  margin: 10px 0 0 68px;
}
</style>
 
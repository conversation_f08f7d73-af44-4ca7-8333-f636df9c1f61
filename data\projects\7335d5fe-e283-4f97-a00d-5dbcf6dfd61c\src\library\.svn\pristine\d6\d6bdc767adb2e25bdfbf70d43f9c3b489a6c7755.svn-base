﻿<template>
  <mk-dialog
    :isShowDialog="true"
    :title="'知识点'"
    :dialogWidth="'38%'"
    @click-sure="confirm"
    @click-cancel="cancel"
  >
    <template v-slot:customSlot>
      <el-row class="tea-subject-container">
        <el-col :span="12">
          <el-header class="know-header flex-start">
            <div class="knowledge-name">知识点</div>
            <subject-select
              @switch-subject="switchSubject"
              :curSubObj="curSubject"
            ></subject-select>
          </el-header>
          <knowledge-tree
            ref="knowledgeTreeRef"
            :maxHeight="'310px'"
            :phaseId="curSubject.phaseId"
            :subjectId="curSubject.subjectId"
            :checkedKeys="checkedKeys"
            @checked-change="switchKnowledge"
          ></knowledge-tree>
        </el-col>

        <el-col
          :span="12"
          style="border-left: 1px solid #e5e5e5; height: 400px"
        >
          <el-header class="flex-between know-header">
            <span
              >已选中
              <b class="select-count">{{ selfResult.length }}</b>
              个知识点</span
            >
            <i
              class="el-icon-delete"
              v-if="selfResult.length"
              @click="clearKnowLedge"
            ></i>
          </el-header>
          <ul v-if="selfResult.length" class="know-content">
            <li
              v-for="(item, index) in selfResult"
              :key="index"
              class="flex-between knowledge-item"
            >
              <span>· {{ item.name }}</span>
              <i class="el-icon-error" @click="deleteKnowLedge(item)"></i>
            </li>
          </ul>
          <mk-no-data
            v-else
            :noDataTipTex="'暂无选中知识点'"
            style="height: 270px"
          ></mk-no-data>
        </el-col>
      </el-row>
    </template>
  </mk-dialog>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, toRefs } from 'vue';
import { useStore } from 'vuex';
import KnowledgeTree from '@/library/ui/mk-share-res/KnowledgeTree/index.vue';
import SubjectSelect, {
  ISubjectModel
} from '@/library/ui/mk-share-res/SubjectSelect/index.vue';
import { IGlobalState } from '@/library/src/store';
const getCurSubject = (val: any) => {
  return JSON.stringify(val) == '{}'
    ? {
        phaseId: '1',
        subjectId: '24',
        id: '24',
        name: '小学语文'
      }
    : val;
};

export default defineComponent({
  name: 'knowledge-dialog',

  emits: ['on-cancle', 'on-confirm'],

  components: { KnowledgeTree, SubjectSelect },

  props: {
    // 默认已选择列表
    checkedKeys: {
      type: Array,
      default: () => []
    },
    //已选当前学科
    curSubObj: {
      type: Object,
      default: () => {
        return getCurSubject({});
      }
    }
  },

  setup(props, ctx) {
    const store = useStore<IGlobalState>();
    const knowledgeTreeRef = ref<any>(null);
    const state = reactive({
      // 当前学科
      curSubject: props.curSubObj as ISubjectModel,
      // 传给接口知识点列表
      knowledges: [] as any[],
      // 当前知识点路径集合
      selfResult: [] as any[],
      //已勾选得节点
      checkedKeyList: props.checkedKeys
    });

    /**
     * @name: 关闭
     */
    const cancel = () => {
      ctx.emit('on-cancle');
    };
    /**
     * @name: 确认
     */
    const confirm = () => {
      ctx.emit(
        'on-confirm',
        state.knowledges,
        state.selfResult,
        state.curSubject
      );
      cancel();
    };
    /**
     * @name: 切换学科
     * @param subjectInfo 当前学科
     */
    const switchSubject = (subjectInfo: ISubjectModel) => {
      state.curSubject = subjectInfo;
      state.knowledges = [];
      state.selfResult = [];
      state.checkedKeyList = [];
    };
    /**
     * @name: 切换已选知识点
     * @param knowledges 已选知识点列表
     */
    const switchKnowledge = (
      node: any,
      knowledges: any[],
      selfResult: any[]
    ) => {
      state.knowledges = knowledges;
      state.selfResult = selfResult;
    };
    /**
     * @name: 清空知识点
     */
    const clearKnowLedge = () => {
      knowledgeTreeRef.value && knowledgeTreeRef.value.clearKnowLedge();
    };
    /**
     * @name: 删除知识点
     * @param item 当前知识点对象
     */
    const deleteKnowLedge = (item: any) => {
      knowledgeTreeRef.value && knowledgeTreeRef.value.deleteKnowLedge(item);
    };

    return {
      knowledgeTreeRef,
      ...toRefs(state),
      confirm,
      cancel,
      switchSubject,
      switchKnowledge,
      clearKnowLedge,
      deleteKnowLedge
    };
  }
});
</script>

<style lang="scss" scoped>
.tea-subject-container {
  background-color: #fff;
  border: 1px solid #e5e5e5;
  .knowledge-name {
    display: inline-block;
    color: #fff;
    width: 90px;
    height: 38px;
    border-radius: 4px;
    line-height: 38px;
    text-align: center;
    margin-right: 5px;
    @include theme_background-color();
  }
  .know-header {
    margin: 10px 0;
    border-bottom: 1px solid #e5e5e5;
    .select-count {
      @include theme_color();
    }
  }
  .know-content {
    max-height: 310px;
    overflow-y: auto;
  }
  .knowledge-item {
    padding: 5px 10px;
    span {
      display: inline-block;
      max-width: 95%;
    }
    &:hover {
      background-color: #f5f7fa;
    }
    .iconguanbi {
      font-size: 12px;
      cursor: pointer;
      &:hover {
        color: red;
      }
    }
  }
}
</style>

/*
 * @Description: 
 * @Author: l<PERSON><PERSON>e <EMAIL>
 * @Date: 2024-09-20 15:03:11
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-12-21 09:54:49
 */
CKEDITOR.dialog.add("FourLine", function (editor) {
  var getHtml = function (count) {
    let container = document.createElement("div");
    container.className = "dynamic four-line-container";
    for (let i = 0; i < count; i++) {
      let rows = document.createElement("div");
      rows.className = `split-tag split-tag-noparent four-line-grid four-line-${i}`;
      rows.contentEditable = false;
      rows.style.height = 10 + 'mm';
      if ($(editor.element.$).parents('.onlycard').length) {
        rows.style.width = (Number(editor.element.$.clientWidth) - 20) + 'px';
      } else {
        rows.style.width = (Number(editor.element.$.clientWidth) - 8) + 'px';
      }
      container.appendChild(rows);
      for (let j = 0; j < 4; j++) {
        let line = document.createElement("div");
        line.className = `four-line`;
        rows.appendChild(line);
      }
    }
    return container.outerHTML + `<p>&ZeroWidthSpace;</p>`;;
  };
  return {
    title: "插入四线格", //对话框标题
    minWidth: 300, //对话框宽度
    minHeight: 200, //对话框高度
    contents: [
      {
        //对话框内容
        id: "FourLine",
        name: "FourLine",
        elements: [
          {
            type: "text",
            id: "count",
            label: "行数",
            default: 1,
            onShow: function (params) {
              // 添加输入验证
              var input = this.getInputElement();
              input.on("input", function () {
                var value = input.$.value.replace(/[^0-9]/g, "");
                if (value.length > 4) {
                  value = value.slice(0, 4);
                }
                if (value !== input.$.value) {
                  input.$.value = value;
                }
              });
            },
          },
        ],
      },
    ],
    onOk: function () {
      var count = this.getValueOf("FourLine", "count");
      var html = getHtml(Number(count));
      try {
        let $ele = editor.getSelectedRanges()[0].endContainer.$
        let cName = $ele.className;
        if (!cName || !cName.includes("subject-para-p")) {
          const paras = Array.from(editor.element.$.getElementsByClassName("subject-para-p"));
          if (paras[0].contains($ele)) {
            // 跳过首个分数段落
            paras[0].insertAdjacentHTML("afterend", html);
          } else {
            editor.insertHtml(html);
          }
        } else {
          editor.insertHtml(html);
        }
      } catch (e) {
        editor.insertHtml(html);
      }

      editor.fire('insertWritingCells')
    },
  };
});

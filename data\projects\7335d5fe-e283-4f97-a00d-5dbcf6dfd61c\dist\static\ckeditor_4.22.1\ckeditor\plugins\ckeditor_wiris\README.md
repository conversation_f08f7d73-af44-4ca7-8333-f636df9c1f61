MathType for CKEditor 4 [![Tweet](https://img.shields.io/twitter/url/http/shields.io.svg?style=social)](https://twitter.com/wirismath)
===

Type and handwrite mathematical notation with MathType.

Easily include quality math equations in your documents and digital content.

![MathType for CKEditor 4 screenshot](http://www.wiris.com/system/files/attachments/1202/CKEditor_editor_plugin.png)

# Table of Contents

- [Install instructions](#install-instructions)
- [Services](#services)
- [Documentation](#documentation)
- [Displaying on Target Page](#displaying-on-target-page)

## Install instructions

1. Install the npm module:

   ```
   npm install @wiris/mathtype-ckeditor4
   ```

2. Add the plugin as an external plugin:

   ```js
   CKEDITOR.plugins.addExternal('ckeditor_wiris', '../node_modules/@wiris/mathtype-ckeditor4/', 'plugin.js');
   ```

3. Update the CKEditor configuration by adding the new plugin and allowing MathML content:

   ```js
   CKEDITOR.editorConfig = function (config) {
       extraPlugins = 'ckeditor_wiris';
       // Allow MathML content.
       allowedContent = true;
   };
   ```

   Notice the example assumes this directory structure:

   ```
   └───index.html
   └───node_modules
       └───@wiris/mathtype-ckeditor4
   ```

## Services

This npm module uses remotely hosted services to render MathML data. However, we strongly recommend you install these services on the backend. This will allow you, among other things, to configure the service and to locally store the images generated by MathType.

The services are available for Java, PHP, .NET and Ruby on Rails. If you use any of these technologies, please download the plugin for your backend technology from [our website](http://www.wiris.com/en/plugins3/ckeditor/download).

In order to install the plugin along with the correspondent services, please follow the [CKEditor4 install instructions](http://docs.wiris.com/en/mathtype/mathtype_web/integrations/html/ckeditor).

## Displaying on Target Page

In order to display mathematical formulas on the target page, i.e. the page where content produced by the HTML editor will be visible, the target page needs to include the [MathType script](https://docs.wiris.com/en/mathtype/mathtype_web/integrations/mathml-mode#add_a_script_to_head). For example for the default setting this would be:
```html
<script src="https://wiris.net/demo/plugins/app/WIRISplugins.js?viewer=image"></script>
```

## Documentation

To find out more information about MathType, please go to the following documentation:

* [Install instructions](http://docs.wiris.com/en/mathtype/mathtype_web/integrations/html/ckeditor)
* [MathType documentation](http://docs.wiris.com/en/mathtype/mathtype_web/start)
* [Introductory tutorials](http://docs.wiris.com/en/mathtype/mathtype_web/intro_tutorials)
* [Service customization](http://docs.wiris.com/en/mathtype/mathtype_web/integrations/config-table)
* [Testing](http://docs.wiris.com/en/mathtype/mathtype_web/integrations/html/plugins-test)
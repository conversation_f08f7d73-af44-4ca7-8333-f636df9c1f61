const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');

class VcsCredential {
  constructor() {
    this.configDir = path.join(process.cwd(), 'data', 'configs');
    this.credentialsFile = path.join(this.configDir, 'vcs_credentials.json');
    fs.ensureDirSync(this.configDir);
    this.initCredentialsFile();
  }

  // 初始化凭据文件
  async initCredentialsFile() {
    if (!(await fs.pathExists(this.credentialsFile))) {
      await fs.writeJson(this.credentialsFile, { credentials: [] }, { spaces: 2 });
    }
  }

  // 简单的加密函数（使用更安全的加密方式）
  encrypt(text) {
    const algorithm = 'aes-256-cbc';
    const key = crypto.scryptSync('vcs-credential-key', 'salt', 32);
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return iv.toString('hex') + ':' + encrypted;
  }

  // 简单的解密函数
  decrypt(encryptedText) {
    try {
      const algorithm = 'aes-256-cbc';
      const key = crypto.scryptSync('vcs-credential-key', 'salt', 32);
      const textParts = encryptedText.split(':');
      const iv = Buffer.from(textParts.shift(), 'hex');
      const encryptedData = textParts.join(':');
      const decipher = crypto.createDecipheriv(algorithm, key, iv);
      let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (error) {
      return null; // 解密失败返回null
    }
  }

  // 获取所有凭据（不包含密码）
  async getAll() {
    try {
      const data = await fs.readJson(this.credentialsFile);
      return data.credentials.map(cred => {
        const { password, ...safeCred } = cred;
        return safeCred;
      });
    } catch (error) {
      throw new Error(`获取VCS凭据列表失败: ${error.message}`);
    }
  }

  // 根据ID获取凭据（不包含密码）
  async getById(id) {
    try {
      const data = await fs.readJson(this.credentialsFile);
      const credential = data.credentials.find(cred => cred.id === id);
      if (!credential) {
        return null;
      }
      const { password, ...safeCred } = credential;
      return safeCred;
    } catch (error) {
      throw new Error(`获取VCS凭据失败: ${error.message}`);
    }
  }

  // 根据ID获取完整凭据（包含解密后的密码，仅供内部使用）
  async getByIdWithPassword(id) {
    try {
      const data = await fs.readJson(this.credentialsFile);
      const credential = data.credentials.find(cred => cred.id === id);
      if (!credential) {
        return null;
      }
      
      // 解密密码
      if (credential.password) {
        credential.password = this.decrypt(credential.password);
      }
      
      return credential;
    } catch (error) {
      throw new Error(`获取VCS凭据失败: ${error.message}`);
    }
  }

  // 创建新凭据
  async create(credentialData) {
    try {
      const data = await fs.readJson(this.credentialsFile);
      
      const id = uuidv4();
      const credential = {
        id,
        name: credentialData.name,
        type: credentialData.type, // 'git' 或 'svn'
        username: credentialData.username,
        password: credentialData.password ? this.encrypt(credentialData.password) : '',
        email: credentialData.email || '', // Git可能需要邮箱
        description: credentialData.description || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      data.credentials.push(credential);
      await fs.writeJson(this.credentialsFile, data, { spaces: 2 });
      
      // 返回不含密码的凭据信息
      const { password, ...safeCredential } = credential;
      return safeCredential;
    } catch (error) {
      throw new Error(`创建VCS凭据失败: ${error.message}`);
    }
  }

  // 更新凭据
  async update(id, credentialData) {
    try {
      const data = await fs.readJson(this.credentialsFile);
      const credentialIndex = data.credentials.findIndex(cred => cred.id === id);
      
      if (credentialIndex === -1) {
        throw new Error('VCS凭据不存在');
      }
      
      // 如果密码为空或未定义，则不更新密码字段
      const updateData = { ...credentialData };
      if (updateData.password) {
        updateData.password = this.encrypt(updateData.password);
      } else {
        delete updateData.password;
      }
      
      const updatedCredential = {
        ...data.credentials[credentialIndex],
        ...updateData,
        updatedAt: new Date().toISOString()
      };
      
      data.credentials[credentialIndex] = updatedCredential;
      await fs.writeJson(this.credentialsFile, data, { spaces: 2 });
      
      // 返回不含密码的凭据信息
      const { password, ...safeCredential } = updatedCredential;
      return safeCredential;
    } catch (error) {
      throw new Error(`更新VCS凭据失败: ${error.message}`);
    }
  }

  // 删除凭据
  async delete(id) {
    try {
      const data = await fs.readJson(this.credentialsFile);
      const credentialIndex = data.credentials.findIndex(cred => cred.id === id);
      
      if (credentialIndex === -1) {
        return false;
      }
      
      data.credentials.splice(credentialIndex, 1);
      await fs.writeJson(this.credentialsFile, data, { spaces: 2 });
      
      return true;
    } catch (error) {
      throw new Error(`删除VCS凭据失败: ${error.message}`);
    }
  }

  // 根据类型获取凭据列表
  async getByType(type) {
    try {
      const data = await fs.readJson(this.credentialsFile);
      return data.credentials
        .filter(cred => cred.type === type)
        .map(cred => {
          const { password, ...safeCred } = cred;
          return safeCred;
        });
    } catch (error) {
      throw new Error(`获取${type}凭据列表失败: ${error.message}`);
    }
  }
}

module.exports = new VcsCredential();

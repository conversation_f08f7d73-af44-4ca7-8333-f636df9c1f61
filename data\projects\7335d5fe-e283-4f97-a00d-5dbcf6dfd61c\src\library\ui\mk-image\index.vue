<template>
  <el-image
    ref="mkImageRef"
    :src="url"
    :fit="fit"
    :lazy="lazy"
    :style="{
      width: '100%',
      height: '100%',
      display: 'block'
    }"
  >
    <template #error>
      <img :src="errImg" class="err-img" />
    </template>
  </el-image>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, ref, onMounted, watch } from "vue";
import { getOssResizeImg, addFsUrl } from "../../src/utils/globalFunction";
import { ElImage } from "element-plus";

export default defineComponent({
  name: "mk-image",

  components: {
    ElImage,
  },

  props: {
    // 图片路径
    src: {
      type: String,
      default: "",
    },
    // 图片填充方式
    fit: {
      type: String,
      default: "unset",
    },
    // 是否懒加载
    lazy: {
      type: Boolean,
      default: true,
    },
    // 加载失败默认图
    errImg: {
      type: String,
      default: addFsUrl("aliba/region/default/err.png"),
    },
    // 是否忽略CDN
    ignoreCDN: {
      type: Boolean,
      default: false,
    }
  },

  setup(props) {
    const mkImageRef = ref<any>(null);
    const state = reactive({
      // 当前图片地址
      url: "",
    });
    /**
     * @name: 获取图片地址
     */
    const getImgFullPath = (): string => {
      let url = props.src
      if (/^https?:/i.test(url)) {
        // 标记阿里云的进行压缩,minio的不支持,不增加压缩
        if( /fs/gi.test(url) ){
          try {
            const width = mkImageRef.value.$el.clientWidth;
            const height = mkImageRef.value.$el.clientHeight;
            if (width && height) {
              url = getOssResizeImg(url, width, height);

              if( props.ignoreCDN ){
                // 增加版本号,使用最新图片
                if( !url.match(/\?/gi) ){
                  url += `?v=${Date.now()}`
                }else{
                  url += `&v=${Date.now()}`
                }
              }
            }
          } catch (e) {}
        }else{
          if( props.ignoreCDN ){
            // 增加版本号,使用最新图片
            if( !url.match(/\?/gi) ){
              url += `?v=${Date.now()}`
            }
          }
        }
      }
      return url;
    };

    onMounted(() => {
      state.url = getImgFullPath();
    });

    watch(
      () => props.src,
      () => {
        state.url = getImgFullPath();
      }
    );
    return {
      ...toRefs(state),
      mkImageRef,
    };
  },
});
</script>

<style lang="scss" scoped>
.err-img {
  display: block;
  width: 100%;
  height: 100%;
}
</style>

import {createStore} from 'vuex';
import app from './modules/app';
import user from './modules/user';
import {IAppState} from '../typings/app';
import {IUserState} from '../typings/user';
import {PLATFORM_TYPE} from "@/library/src/utils/gloableEnum";

export interface IGlobalState {
    app: IAppState,
    user: IUserState
}

const store = createStore<IGlobalState>({
    mutations: {},
    actions: {},
    modules: {
        app,
        user
    },
    getters: {
        // 入口
        entry(state) {
            return state.app.source || 'region'
        },
        // 是否校级平台
        isSchoolPlatform(state) {
             return state.app.platFormInfo.platformType === PLATFORM_TYPE.SCHOOL
        }
    }
});
export default store;

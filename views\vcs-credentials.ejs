<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>VCS凭据管理 - 前端部署工具</title>
  <link href="/bootstrap/bootstrap.min.css" rel="stylesheet">
  <link href="/bootstrap/bootstrap-icons.css" rel="stylesheet">
  <style>
    .sidebar {
      min-height: 100vh;
      background-color: #f8f9fa;
    }
    .nav-link.active {
      background-color: #0d6efd;
      color: white !important;
    }
    .content-area {
      padding: 20px;
    }
    .credential-card {
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 15px;
      background-color: #fff;
    }
    .credential-type-badge {
      font-size: 0.75rem;
      padding: 0.25rem 0.5rem;
    }
  </style>
</head>
<body>
  <div class="container-fluid">
    <div class="row">
      <!-- 侧边栏 -->
      <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
        <div class="position-sticky pt-3">
          <h5 class="sidebar-heading d-flex justify-content-between align-items-center px-3 mt-4 mb-1 text-muted">
            <span>管理面板</span>
          </h5>
          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="/">
                <i class="bi bi-house"></i> 仪表盘
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/?page=projects">
                <i class="bi bi-folder"></i> 项目管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/?page=servers">
                <i class="bi bi-server"></i> 服务器管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="/vcs-credentials">
                <i class="bi bi-key"></i> VCS凭据管理
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/queue">
                <i class="bi bi-list-task"></i> 构建队列
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/?page=nodeVersions">
                <i class="bi bi-gear"></i> Node.js版本
              </a>
            </li>
          </ul>
        </div>
      </nav>

      <!-- 主内容区域 -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
          <h1 class="h2">VCS凭据管理</h1>
          <div class="btn-toolbar mb-2 mb-md-0">
            <button class="btn btn-sm btn-outline-primary" id="add-credential-button">
              <i class="bi bi-plus-lg"></i> 添加凭据
            </button>
          </div>
        </div>

        <!-- 凭据列表 -->
        <div class="row">
          <div class="col-12">
            <div id="credentials-container">
              <!-- 凭据卡片将在这里动态加载 -->
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>

  <!-- 添加/编辑凭据模态框 -->
  <div class="modal fade" id="addCredentialModal" tabindex="-1" aria-labelledby="addCredentialModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="addCredentialModalLabel">添加VCS凭据</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="add-credential-form">
            <div class="mb-3">
              <label for="credential-name" class="form-label">凭据名称</label>
              <input type="text" class="form-control" id="credential-name" required>
              <div class="form-text">为这个凭据起一个易于识别的名称</div>
            </div>
            <div class="mb-3">
              <label for="credential-type" class="form-label">版本控制系统类型</label>
              <select class="form-select" id="credential-type" required>
                <option value="">请选择类型</option>
                <option value="git">Git</option>
                <option value="svn">SVN</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="credential-username" class="form-label">用户名</label>
              <input type="text" class="form-control" id="credential-username" required>
            </div>
            <div class="mb-3">
              <label for="credential-password" class="form-label">密码</label>
              <input type="password" class="form-control" id="credential-password" required>
              <div class="form-text">密码将被加密存储</div>
            </div>
            <div class="mb-3" id="email-field" style="display: none;">
              <label for="credential-email" class="form-label">邮箱</label>
              <input type="email" class="form-control" id="credential-email">
              <div class="form-text">Git提交时使用的邮箱地址（可选）</div>
            </div>
            <div class="mb-3">
              <label for="credential-description" class="form-label">描述</label>
              <textarea class="form-control" id="credential-description" rows="3"></textarea>
              <div class="form-text">可选的描述信息</div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
          <button type="button" class="btn btn-primary" id="save-credential-btn">保存凭据</button>
        </div>
      </div>
    </div>
  </div>

  <!-- 测试连接模态框 -->
  <div class="modal fade" id="testConnectionModal" tabindex="-1" aria-labelledby="testConnectionModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="testConnectionModalLabel">测试VCS连接</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="test-connection-form">
            <div class="mb-3">
              <label for="test-repository-url" class="form-label">仓库URL</label>
              <input type="url" class="form-control" id="test-repository-url" required>
              <div class="form-text">输入要测试连接的仓库URL</div>
            </div>
            <div id="test-result" class="mt-3" style="display: none;"></div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
          <button type="button" class="btn btn-primary" id="test-connection-btn">测试连接</button>
        </div>
      </div>
    </div>
  </div>

  <script src="/bootstrap/bootstrap.bundle.min.js"></script>
  <script src="/js/vcs-credentials.js"></script>
</body>
</html>

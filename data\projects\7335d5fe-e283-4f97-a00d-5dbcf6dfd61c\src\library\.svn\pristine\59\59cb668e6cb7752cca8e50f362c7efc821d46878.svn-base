﻿import { Module } from "vuex";
import { IGlobalState } from "..";
import Types from "../action-types";
import {
  getUserInfoAPI,
  getUserRoleIdList,
  platformLoginAPI,
  shangYuLoginAPI,
  getSameAccountAPI,
  loginByTokenAPI,
} from "../../service/API/base";
import { IUserState } from "../../typings/user";
import { ADMIN_TYPE, USER_TYPE } from "../../utils/gloableEnum";
import { isSY } from "@/library/src/utils/valiteSite";
declare let ssoClient: any;

const state: IUserState = {
  userInfoList: [],
  userId: "",
  userName: "",
  mobile: "",
  email: "",
  avatar: "",
  realName: "",
  sex: "",
  userType: USER_TYPE.TEACHER,
  adminType: ADMIN_TYPE.NOADMIN,
  schoolId: "",
  schoolName: "",
  subjectId: "",
  subjectName: "",
  phase: "",
  token: "",
  huohuaId: "",
  huohuaToken: "",
  roleList: [],
  userPhase: "",
  Authorization: "",
};

const user: Module<IUserState, IGlobalState> = {
  namespaced: true,
  state,
  mutations: {
    [Types.SET_USER_INFO_LIST](state, payload: any) {
      state.userInfoList = payload;
    },
    [Types.SET_USER_INFO_QIANKUN](state, payload: any) {
      state.userId = payload.userId;
      state.userName = payload.userName;
      state.mobile = payload.mobile;
      state.email = payload.email;
      state.avatar = payload.avatar;
      state.realName = payload.realName;
      state.sex = payload.sex;
      state.userType = payload.userType;
      state.adminType = payload.adminType;
      state.schoolId = payload.schoolId;
      state.schoolName = payload.schoolName;
      state.subjectId = payload.subjectId;
      state.subjectName = payload.subjectName;
      state.phase = payload.phase;
      state.token = payload.token;
      state.huohuaId = payload.huohuaId;
      state.huohuaToken = payload.huohuaToken || "";
      state.userPhase = payload.userPhase || "";
    },
    [Types.SET_USER_INFO](state, payload: any) {
      state.userId = payload.id;
      state.userName = payload.user_name;
      state.mobile = payload.mobile;
      state.email = payload.email;
      state.avatar = payload.avatar;
      state.realName = payload.realname;
      state.sex = payload.sex;
      state.userType = payload.user_type;
      state.adminType = payload.admin_type;
      state.schoolId =
        state.userType === USER_TYPE.REGION ? "" : payload.schoolid;
      state.schoolName = payload.school_name;
      state.subjectId = payload.subjectid;
      state.subjectName = payload.sub_name_list;
      state.phase = payload.phase;
      state.token = payload.token;
      state.huohuaId = payload.huohua_id;
      state.huohuaToken = payload.huohuaToken || "";
      state.userPhase = payload.userPhase || "";
      ssoClient && ssoClient.setCookie(state.token);
    },
    [Types.SET_USER_ROLE](state, payload: any) {
      state.roleList = payload.roleList;
    },
    [Types.SET_AUTOHR](state, payload: any) {
      state.Authorization = payload;
    },
  },
  actions: {
    async [Types.SET_AUTOHR]({ commit }, token) {
      return new Promise((resolve, reject) => {
        loginByTokenAPI({ tokenId: token, type: 3 }).then((res: any) => {
          if (res && res.code > 0) {
            commit(Types.SET_AUTOHR, res.result.token);
            resolve(res.result);
          } else {
            reject();
          }
        });
      });
    },
    async [Types.GET_USER_INFO]({ commit, dispatch }, param) {
      return new Promise((resolve, reject) => {
        getUserInfoAPI(param)
          .then(async (response: any) => {
            if (response && response.code > 0) {
              if (response.code === 1) {
                commit(Types.SET_USER_INFO, response.data);
                await dispatch(Types.GET_USER_ROLE, param);
                // dispatch(Types.SET_AUTOHR, response.data.token)
                resolve(response.data);
              }
            } else {
              reject(response.msg);
            }
          })
          .catch(function(error: any) {
            reject(error);
          });
      });
    },
    async [Types.GET_USER_ROLE]({ commit }, param) {
      const params = { userId: param.userId };
      getUserRoleIdList(params)
        .then((res: any) => {
          if (res && res.code === 1) {
            if (res.data.length) {
              commit(Types.SET_USER_ROLE, { roleList: res.data });
            }
          } else {
            throw new Error("get user role fail");
          }
        })
        .catch(function() {
          console.log("获取用户身份接口错误");
        });
    },
    async [Types.PLATFORM_LOGIN]({ commit, dispatch }, param) {
      return new Promise((resolve, reject) => {
        if (isSY()) {
          shangYuLoginAPI({
            username: param.account,
            password: param.passWord,
          }).then(async (res: any) => {
            if (res && res.code == 1) {
              if (res.data.length > 0) {
                await dispatch(Types.GET_USER_INFO, {
                  userId: res.data[0].userId,
                });
              }
              res.data[0].id = res.data[0].userId;
              resolve(res);
            } else {
              reject(res.msg);
            }
          });
        } else {
          platformLoginAPI(param)
            .then(function(response: any) {
              if (response && response.code > 0) {
                if (response.code === 1) {
                  const data = response.data;
                  if (data.length > 0) {
                    // commit(Types.SET_USER_INFO, data[0]);
                    // commit(Types.SET_USER_INFO_LIST, data);
                  }
                  resolve(response);
                }
              } else {
                reject(response.msg);
              }
            })
            .catch(function(error: any) {
              reject(error);
            });
        }
      });
    },
    async [Types.SET_PLATFORM_LOGIN_USER_INFO](
      { commit, dispatch, state },
      param
    ) {
      return new Promise((resolve, reject) => {
        if (state.userId) {
          getSameAccountAPI(param)
            .then(async function(response: any) {
              if (response && response.code > 0) {
                if (response.code === 1) {
                  const data = response.data;
                  if (data.length > 0) {
                    commit(Types.SET_USER_INFO_LIST, data);
                  }
                  await dispatch(Types.GET_USER_INFO, param);
                  resolve(response);
                }
              } else {
                reject(response.msg);
              }
            })
            .catch(function(error: any) {
              reject(error);
            });
        }
      });
    },
  },
};
export default user;

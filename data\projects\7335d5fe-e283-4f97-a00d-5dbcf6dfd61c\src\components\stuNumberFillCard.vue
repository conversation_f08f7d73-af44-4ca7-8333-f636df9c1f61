<!--
 * @Description: 学号填涂卡
 * @Author: qmzhang
 * @Date: 2024-11-19 11:35:09
 * @LastEditTime: 2025-02-12 15:39:26
 * @FilePath: \make-card\src\components\stuNumberFillCard.vue
-->
<template>
    <table class="stu-no-table" :class="`stu-no-table--${optionBlock}`">
        <tbody>
            <slot name="table-header"></slot>

            <tr class="stu-no-first-tr" v-if="optionBlock === 'bracket'">
                <td :colspan="Paper.stuNoLength" class="stu-num">学号</td>
            </tr>

            <tr class="stu-no-second-tr">
                <th class="empty-th" v-for="index of Paper.stuNoLength" :key="index">
                    {{ optionBlock === 'bracket' ? "&nbsp;" : "" }}
                </th>
            </tr>

            <tr class="stu-no-three-tr" id="stu-no-three-tr" v-if="optionBlock === 'bracket'">
                <td :class="index === 1 ? 'stu-tb-left' : 'stu-num-td'" v-for="index of Paper.stuNoLength" :key="index">
                    <div name="stuNum" class="stu-num-full-sec" :style="{ width: Paper.stuNoLength > 12 ? '6mm' : '7mm' }"
                        v-for="(item, subIndex) of 10" :key="item">
                        [<span class="stu-num-txt">{{ subIndex }}</span>]
                    </div>
                </td>
            </tr>

            <template v-else>
                <tr class="stu-no-three-tr" v-for="(item, subIndex) of 10" :key="item">
                    <td class="stu-num-td" v-for="index of Paper.stuNoLength" :key="index">
                        <div name="stuNum" class="stu-num-full-sec">
                            <span class="stu-num-txt">{{ subIndex }}</span>
                        </div>
                    </td>
                </tr>
            </template>
        </tbody>
    </table>

    <template v-if="Paper.isNumberHasX">
        <div style="position: absolute;right: 0;" name="stuNum" class="stu-num-full-sec" id="stu-num-x"
            :style="{ width: Paper.stuNoLength > 12 ? '6mm' : '7mm', 'text-align': 'center' }">
            <template v-if="optionBlock === 'bracket'">
                [<span class="stu-num-txt">x</span>]
            </template>
            <template v-else>
                <span class="stu-num-txt">x</span>
            </template>
        </div>
    </template>
</template>

<script lang="ts">
import Paper from "../../src/views/paper";
import {
    defineComponent,
    reactive,
    toRefs,
} from 'vue';

export default defineComponent({
    props: {
        // 选项的显示方式 bracket|rect
        optionBlock: {
            type: String,
            default: "bracket",
        },
    },
    setup(props, ctx) {
        const state = reactive({
            Paper: Paper,
        });

        return {
            ...toRefs(state),
        }
    }
})

</script>

<style lang="scss" scoped>
.stu-no-table {
    border-collapse: collapse;
    padding: 0;
    margin: 0 auto;
    text-align: center;
    color: #000;

    &--rect {
        border-collapse: separate;

        ::v-deep {

            th{
                border: 0.4mm solid #000 !important;
                height: 6mm;
            }
            td{
                border: none !important;
            }
        }

        .stu-no-second-tr {
            height: 4.7mm;
        }
        .stu-num-td{
            width: 6mm;
        }
        .stu-num-txt {
            font-family: Arial, 'Microsoft YaHei';
        }
    }
}

.stu-no-first-tr {
    height: 4mm;
}

.stu-num {
    width: 30mm;
    font-size: 3mm !important;
    border: 0.1mm solid #000;
    border-collapse: collapse;
    text-align: center;
}

.stu-no-second-tr {
    height: 5mm;
}

.stu-no-three-tr {
    margin: 0;
    padding: 0;
}

.stu-tb-left {
    border: 0.1mm solid #000;
    border-collapse: collapse;
    padding-top: 0;
    border-bottom-color: #000;
    width: 6.6mm;
    color: #000;
    border-left-color: #000;
}

.stu-num-td {
    border: 0.1mm solid #000;
    border-collapse: collapse;
    padding-top: 0;
    border-bottom-color: #000;
    width: 6.6mm;
    color: #000;

    &:last-child {
        border-right-color: #000;
    }
}

.stu-num-full-sec {
    position: relative;
    // display: inline-block;
    line-height: 4.1mm;
    vertical-align: bottom;
    border: 0 solid #000;
    width: 6.6mm;
    height: 4.1mm;
    padding: 0;
    margin: 0;
    font-size: 2.5mm !important;
    font-family: 宋体, STSong, SimSun;
}

.stu-num-txt {
    margin: 0;
    padding: 0 0.6mm;
}

.empty-th {
    border: 0.1mm solid #000;
    border-collapse: collapse;
    font-size: 2.5mm !important;
}
</style>
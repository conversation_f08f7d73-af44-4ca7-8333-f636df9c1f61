<!--@Descripttion: 区域学科设置--头部学科筛选  -->
<template>
  <div :class="['mk-region-select', { select: isSelect }]">
    <template v-if="isSelect">
      <!--多选-->
      <template v-if="multiple">
        <el-select
          v-if="isShowPhase"
          class="select-div multiple"
          v-model="phaseSelectList"
          value-key="code"
          multiple
          @change="changePhase"
          :disabled="disabled"
          :popper-append-to-body="false"
          :multiple-limit="limit"
          placeholder="请选择学段"
        >
          <el-option
            v-for="item in phaseList"
            :key="item.code"
            :label="item.name"
            :value="item"
            :title="item.name"
          ></el-option>
        </el-select>
        <el-select
          v-if="isShowGrade"
          :class="['select-div multiple', { mr: isShowPhase }]"
          v-model="gradeSelectList"
          multiple
          value-key="code"
          @change="changeGrade"
          :disabled="disabled"
          :popper-append-to-body="false"
          :multiple-limit="limit"
          placeholder="请选择年级"
        >
          <el-option
            v-for="item in gradeList"
            :key="item.code"
            :label="item.name"
            :value="item"
            :title="item.name"
          ></el-option>
        </el-select>
        <el-select
          v-if="isShowSubject"
          v-model="subejctSelectList"
          multiple
          value-key="code"
          :class="['select-div multiple', { mr: isShowPhase || isShowGrade }]"
          @change="changeSubject"
          :disabled="disabled"
          :popper-append-to-body="false"
          :multiple-limit="limit"
          placeholder="请选择学科"
        >
          <el-option
            v-for="item in subjectList"
            :key="item.code + item.id"
            :label="item.name"
            :value="item"
            :title="item.name"

          ></el-option>
        </el-select>
      </template>
      <!--单选-->
      <template v-else>
        <el-select
          v-if="isShowPhase"
          class="select-div"
          v-model="phaseInfo"
          value-key="code"
          @change="changePhase"
          :disabled="disabled"
          :popper-append-to-body="false"
          placeholder="请选择学段"
        >
          <el-option
            v-for="item in phaseList"
            :key="item.code"
            :label="item.name"
            :value="item"
            :title="item.name"
          ></el-option>
        </el-select>
        <el-select
          v-if="isShowGrade"
          :class="['select-div', { mr: isShowPhase }]"
          v-model="gradeInfo"
          value-key="code"
          @change="changeGrade"
          :disabled="disabled"
          :popper-append-to-body="false"
          placeholder="请选择年级"
        >
          <el-option
            v-for="item in gradeList"
            :key="item.code"
            :label="item.name"
            :value="item"
            :title="item.name"
          ></el-option>
        </el-select>
        <el-select
          v-if="isShowSubject"
          v-model="subjectInfo"
          value-key="code"
          :class="['select-div', { mr: isShowPhase || isShowGrade }]"
          @change="changeSubject"
          :disabled="disabled"
          :popper-append-to-body="false"
          placeholder="请选择学科"
        >
          <el-option
            v-for="item in subjectList"
            :key="item.code + item.id"
            :label="item.name"
            :value="item"
            :title="item.name"
          ></el-option>
        </el-select>
      </template>
    </template>
    <template v-else>
      <!--学段-->
      <label-list
        v-if="isShowPhase && phaseList.length"
        :labelTitle="'学段:'"
        :labelList="phaseList"
        :value="phaseInfo.code"
        @change-type="changePhase"
      ></label-list>
      <!--年级-->
      <label-list
        v-if="isShowGrade && phaseList.length"
        :labelTitle="'年级:'"
        :labelList="gradeList"
        :value="gradeInfo.code"
        @change-type="changeGrade"
      ></label-list>
      <!--学科-->
      <label-list
        v-if="isShowSubject && subjectList.length"
        :labelTitle="'学科:'"
        :labelList="subjectList"
        :value="subjectInfo.code"
        @change-type="changeSubject"
      ></label-list>
    </template>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, watch } from 'vue';
import { ElMessage, ElSelect, ElOption } from 'element-plus';
import { listRegionSubjectAPI } from '@/library/src/service/API/tearesearch';
import {
  getAllGradeListAPI,
  getSchoolByIdAPI
} from '@/library/src/service/API/base';
import { getPhaseName } from '@/library/src/utils/globalFunction';
import LabelList from '@/library/ui/mk-region-subject/LabelList.vue';
import { useStore } from 'vuex';
import { IGlobalState } from '@/library/src/store';

export default defineComponent({
  name: 'mk-subject',

  components: { LabelList, ElSelect, ElOption },

  emits: ['on-change'],

  props: {
    // 是否选中下拉显示
    isSelect: {
      type: Boolean,
      default: false
    },
    // 是否展示学段
    isShowPhase: {
      type: Boolean,
      default: true
    },
    // 是否展示年级
    isShowGrade: {
      type: Boolean,
      default: false
    },
    // 是否展示学科
    isShowSubject: {
      type: Boolean,
      default: true
    },
    // 是否展示全部学段
    isShowAllPhase: {
      type: Boolean,
      default: true
    },
    // 是否展示全部年级
    isShowAllGrade: {
      type: Boolean,
      default: true
    },
    // 是否展示全部学科
    isShowAllSubject: {
      type: Boolean,
      default: true
    },
    // 是否设置默认值
    isDefaultValue: {
      type: Boolean,
      default: true
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是支持多选, 多选情况下,不支持全部
    multiple: {
      type: Boolean,
      default: false
    },
    // 当前学段, 多选情况下以逗号分割
    phase: {
      type: String,
      default: ''
    },
    // 当前年级, 多选情况下以逗号分割
    gradeCode: {
      type: String,
      default: ''
    },
    // 当前学科, 多选情况下以逗号分割
    subjectCode: {
      type: String,
      default: ''
    },
    // 多选条目限制
    limit: {
      type:Number,
      default: 10
    }
  },

  setup(props, ctx) {
    /** 全部学科/学段名称类型 */
    enum ALL_NAME_TYPE {
      phase = 'phase',
      grade = 'grade',
      subject = 'subject'
    }

    const store = useStore<IGlobalState>();
    /**
     * @name: 获取全部学段/年级/学科名称
     * @param type 类型
     */
    const getAllName = (type: keyof typeof ALL_NAME_TYPE) => {
      let name: string =
        type === ALL_NAME_TYPE.phase
          ? '全部学段'
          : type === ALL_NAME_TYPE.grade
          ? '全部年级'
          : '全部学科';
      return props.isSelect ? name : '全部';
    };

    const state = reactive({
      // 学段集合
      phaseList: [] as any[],
      // 当前学段
      phaseInfo: {
        name: getAllName(ALL_NAME_TYPE.phase),
        code: props.phase
      },
      // 当前已选学段集合
      phaseSelectList: [] as any[],

      // 区域学科集合
      regionSubjectList: [] as any[],
      // 当前学科集合
      subjectList: [] as any[],
      // 当前学科
      subjectInfo: {
        name: getAllName(ALL_NAME_TYPE.subject),
        code: '',
        id: '',
        phase: '',
        subjectType: ''
      },
      // 当前已选学科集合
      subejctSelectList: [] as any[],

      // 区域年级集合
      regionGradeList: [] as any[],
      // 当前年级集合
      gradeList: [] as any[],
      // 当前年级
      gradeInfo: {
        gradeId: '',
        gradeName: getAllName(ALL_NAME_TYPE.grade),
        name: getAllName(ALL_NAME_TYPE.grade),
        code: '',
        phase: props.phase,
        year: new Date().getFullYear()
      } as any,
      // 当前已选年级集合
      gradeSelectList: [] as any[],
      // 当前学校信息
      schoolInfo: {
        phase: ''
      },
      // 计时器
      timer: null as any
    });
    /**
     * @name: 设置当前学段
     * @param phase 当前学段或学段集合
     */
    const setPhase = () => {
      let phase
      if (props.multiple) {
        phase = props.isShowPhase ? state.phaseList.filter((item: any) => props.phase.split(',').includes(item.code)): [];
      } else {
        phase = state.phaseList.find((item: any) => item.code == props.phase + '');
        phase = phase || state.phaseList[0];
      }
      changePhase(phase);
    };
    /**
     * @name:获取区域学科
     */
    const listRegionSubject = async () => {
      const res = await listRegionSubjectAPI({ phase: state.schoolInfo.phase });
      if (res.code == 1) {
        let phaseList: any[] = [];
        let subjectList = res.data.map((item: any) => {
          const phaseIndex = phaseList.findIndex((sitem: any) => {
            return sitem.code === item.phase + '';
          });
          if (phaseIndex === -1) {
            phaseList.push({
              name: getPhaseName(item.phase),
              code: item.phase + ''
            });
          }
          return {
            name: item.subjectName,
            code: item.subjectId,
            id: item.id,
            phase: item.phase + '',
            phaseName: getPhaseName(item.phase),
            subjectType: item.subjectType
          };
        });
        if (props.isShowAllSubject && !props.multiple) {
          subjectList.unshift({
            name: getAllName(ALL_NAME_TYPE.subject),
            code: '',
            id: '',
            phase: props.phase,
            phaseName: '',
            subjectType: ''
          });
        }
        if (props.isShowAllPhase && !props.multiple) {
          phaseList.unshift({
            name: getAllName(ALL_NAME_TYPE.phase),
            code: props.phase
          });
        }
        state.regionSubjectList = subjectList;
        state.subjectList = subjectList;
        state.phaseList = phaseList;
        if (phaseList.length) {
          if (props.isDefaultValue) {
            setPhase();
          }
        }
      } else {
        ElMessage.warning(res.msg);
      }
    };
    /**
     * @name: 获取学校信息
     */
    const getSchoolInfo = async () => {
      const res = await getSchoolByIdAPI({
        schoolId: store.state.app.platFormInfo.schoolId
      });
      if (res.code == 1) {
        state.schoolInfo = res.data;
      } else {
        ElMessage.warning(res.msg);
      }
    };
    /**
     * @name: 获取年级
     */
    const getGradeList = async () => {
      const res = await getAllGradeListAPI({ phases: state.schoolInfo.phase });
      if (res.code === 1) {
        let regionGradeList = res.data.map((item: any) => {
          item.code = item.gradeId;
          item.name = item.gradeName;
          return item;
        });
        if (props.isShowAllGrade && !props.multiple) {
          regionGradeList.unshift({
            gradeId: '',
            gradeName: getAllName(ALL_NAME_TYPE.grade),
            code: '',
            name: getAllName(ALL_NAME_TYPE.grade),
            phase: props.phase,
            year: new Date().getFullYear()
          });
        }
        state.regionGradeList = regionGradeList;
      } else {
        ElMessage.warning(res.msg);
      }
    };
    /**
     * @name:切换学段
     * @param item 当前学段item
     */
    const changePhase = (item: any) => {
      let gradeList, grade;

      if (props.multiple) {
        const phases = getCurPhaseList();
        gradeList = props.isShowPhase?state.regionGradeList.filter((sitem: any) => phases.includes(sitem.phase + '')) : state.regionGradeList;
        state.phaseSelectList = item;
        state.gradeList = gradeList;
        if (props.isDefaultValue) {
          if (props.gradeCode) {
            grade = gradeList.filter((sitem: any) => props.gradeCode.split(',').includes(sitem.code));
          }
        }
      } else {
        if (props.isShowAllSubject) {
          state.regionSubjectList[0].phase = item.code;
        }
        if (props.isShowAllGrade) {
          state.regionGradeList[0].phase = item.code;
        }
        if (item.code === '') {
          gradeList = state.regionGradeList;
        } else {
          gradeList = state.regionGradeList.filter((sitem: any) => {
            return item.code == sitem.phase || !sitem.phase;
          });
        }
        state.phaseInfo = item;
        state.gradeList = gradeList;

        if (props.isDefaultValue) {
          grade = state.gradeList.find((item: any) => item.code == props.gradeCode);
          grade = grade || state.gradeList[0];
        }
      }
      changeGrade(grade);
    };
    /**
     * @name:切换年级
     * @param item 当前年级item
     */
    const changeGrade = (item?: any) => {
      let subjectList, subject;
      if (props.multiple) {
        let phases = [...state.gradeSelectList].map((sitem: any) => sitem.phase + '');
        phases = phases.length?phases:getCurPhaseList()
        subjectList = phases.length
            ? state.regionSubjectList.filter((sitem: any) => phases.includes(sitem.phase + ''))
            : state.regionSubjectList;
        state.gradeSelectList = item || [];
        state.subjectList = subjectList;
        if (props.isDefaultValue) {
          if( props.subjectCode ){
            subject = subjectList.filter((sitem: any) => props.subjectCode.split(',').includes(sitem.code));
            if( props.isShowGrade && state.gradeSelectList.length===0 ){
              subject = []
            }
          }
        }
      } else {
        const phase = item ? item.phase : state.phaseInfo.code;
        if (phase === '') {
          subjectList = state.regionSubjectList;
        } else {
          subjectList = state.regionSubjectList.filter((sitem: any) => {
            return phase == sitem.phase || !sitem.phase;
          });
        }
        state.subjectList = subjectList;
        state.gradeInfo = item || {
          gradeId: '',
          gradeName: getAllName(ALL_NAME_TYPE.grade),
          phase,
          year: new Date().getFullYear()
        };
        if (props.isDefaultValue) {
          subject = state.subjectList.find((sitem: any) => sitem.code == props.subjectCode);
          subject = subject || state.subjectList[0];
        }
      }


      changeSubject(subject);
    };
    /**
     * @name:切换学科
     * @param item 当前学科item
     */
    const changeSubject = (item?: any) => {
      if (props.multiple) {
        state.subejctSelectList = item || []
        state.phaseSelectList = props.isShowPhase?state.phaseSelectList:[...state.gradeSelectList].map((sitem:any)=>{
          return {
            name: getPhaseName(sitem.phase),
            code: sitem.phase
          }
        })
      } else {
        state.subjectInfo = item || {
          name: getAllName(ALL_NAME_TYPE.subject),
          code: '',
          id: '',
          phase: state.gradeInfo.phase,
          subjectType: ''
        };
        state.phaseInfo.code = state.subjectInfo.phase;
        state.gradeInfo.phase = state.subjectInfo.phase
      }
      onChange();
    };
    /**
     * @name: 选择事件
     */
    const onChange = () => {
      let data;
      if (props.multiple) {
        data = {
          phase: state.phaseSelectList,
          gradeInfo: state.gradeSelectList,
          subjectInfo: state.subejctSelectList
        };
      } else {
        data = {
          phase: state.phaseInfo.code,
          gradeInfo: state.gradeInfo,
          subjectInfo: state.subjectInfo
        };
      }
      ctx.emit('on-change', data);
    };
    /**
     * @name: 获取当前已选学段
     * @return: 当前已选学段code集合
     */
    const getCurPhaseList = ()=>{
      let phases = []
      if( props.multiple ){
        // 已选学段的code
        phases = [...state.phaseSelectList].map((sitem: any) => sitem.code + '')
        // 已选年级的学段code
        const gradePhases = [...state.gradeSelectList].map((sitem: any) => sitem.phase + '')
        phases = [...phases,...gradePhases]
      }else{
        phases = [state.phaseInfo.code, state.gradeInfo.code]
      }
      phases = [...new Set(phases)]
      phases = phases.filter((item:any)=>item!=='')
      return phases
    }
    /**
     * @name: 回调监听
     */
    const doWatch = (type: ALL_NAME_TYPE,newValue:string)=>{
      if( state.regionSubjectList.length===0 ){
          return
      }
      clearTimeout(state.timer)
      state.timer = setTimeout(() => {
        let oldValue
        switch (type) {
          case ALL_NAME_TYPE.phase:
            oldValue = props.multiple ? [...state.phaseSelectList].map((item:any)=>item.code).toString() : state.phaseInfo.code
            break
          case ALL_NAME_TYPE.grade:
            oldValue = props.multiple ? [...state.gradeSelectList].map((item:any)=>item.code).toString() : state.gradeInfo.code
            break
          case ALL_NAME_TYPE.subject:
            oldValue = props.multiple ? [...state.subejctSelectList].map((item:any)=>item.code).toString() : state.subjectInfo.code
            break
        }
        if (newValue !== oldValue) {
          setPhase()
        }
        clearTimeout(state.timer)
        state.timer = null
      }, 30);
    }

    onMounted(async () => {
      if (store.getters.isSchoolPlatform) {
        await getSchoolInfo();
      }
      await getGradeList();
      await listRegionSubject();
    });

    watch(() => props.phase, (newValue) => {
        doWatch(ALL_NAME_TYPE.phase,newValue)
    });
    watch(() => props.gradeCode, (newValue) => {
        doWatch(ALL_NAME_TYPE.grade,newValue)
    });
    watch(() => props.subjectCode, (newValue) => {
        doWatch(ALL_NAME_TYPE.subject,newValue)
    });

    return {
      ...toRefs(state),
      changePhase,
      changeGrade,
      changeSubject
    };
  }
});
</script>

<style lang="scss" scoped>
.mk-region-select {
  .select-div {
    width: 130px;
    font-size: 14px !important;

    &.mr {
      margin-left: 20px;
    }
    ::v-deep(.el-select .el-input.is-focus .el-input__inner) {
      @include theme_border-color;
    }
    ::v-deep(.el-input.is-focus .el-input__inner),
    ::v-deep(.el-input__inner:focus) {
      @include theme_border-color();
    }
  }
  &.select {
    display: inline-flex;
    align-items: flex-start;
  }
}
</style>
<style lang="scss">
.mk-region-select {
  .el-select-dropdown__item.selected {
    @include theme_color(true);
    max-width: calc(100% - 2px);
  }
  .el-select__tags > span {
    display: inline-block;
    max-height: 120px;
    overflow-y: auto;
    overflow-x: hidden;
  }
}
</style>

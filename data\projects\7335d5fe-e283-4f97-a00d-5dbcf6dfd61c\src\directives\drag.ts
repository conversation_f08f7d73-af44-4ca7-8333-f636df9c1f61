import type { Directive } from "vue";

interface DragElement extends HTMLElement {
  dragData?: {
    dragging: boolean;
    startX: number;
    startY: number;
    offsetX: number;
    offsetY: number;
    initialTransform: string;
    currentX: number;
    currentY: number;
    bounds?: {
      left: number;
      right: number;
      top: number;
      bottom: number;
    };
  };
  dragIcon?: HTMLElement;
}

export const drag: Directive = {
  mounted(el: DragElement) {
    // 初始化拖拽数据
    el.dragData = {
      dragging: false,
      startX: 0,
      startY: 0,
      offsetX: 0,
      offsetY: 0,
      currentX: 0,
      currentY: 0,
      initialTransform:
        getComputedStyle(el).transform === "none"
          ? ""
          : getComputedStyle(el).transform,
    };

    // 创建拖动图标
    const dragIcon = document.createElement("div");
    dragIcon.title = "按住鼠标可拖动";
    dragIcon.innerHTML = `
      <svg t="1735610950457" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4344" width="32" height="32"><path d="M1013.7 539.59c0.09-0.14 0.18-0.29 0.28-0.43 0.35-0.54 0.69-1.08 1.02-1.63 0.11-0.18 0.21-0.37 0.32-0.55 0.3-0.52 0.6-1.05 0.88-1.58 0.1-0.19 0.2-0.39 0.3-0.58 0.28-0.54 0.54-1.07 0.8-1.62 0.09-0.18 0.17-0.37 0.25-0.55 0.26-0.56 0.51-1.13 0.74-1.71 0.07-0.16 0.13-0.32 0.19-0.49 0.24-0.6 0.47-1.2 0.68-1.81 0.05-0.14 0.09-0.28 0.14-0.42 0.22-0.63 0.42-1.27 0.62-1.91 0.04-0.12 0.07-0.24 0.1-0.36 0.19-0.66 0.37-1.31 0.53-1.98 0.03-0.12 0.05-0.24 0.08-0.36 0.16-0.66 0.31-1.33 0.44-2 0.03-0.14 0.05-0.28 0.08-0.43 0.12-0.64 0.23-1.29 0.33-1.94 0.03-0.2 0.05-0.4 0.08-0.6 0.08-0.59 0.16-1.19 0.21-1.79 0.03-0.32 0.05-0.65 0.07-0.97a44.52 44.52 0 0 0 0.16-3.75v-0.14c0-0.83-0.02-1.67-0.06-2.5-0.01-0.26-0.04-0.51-0.06-0.77-0.04-0.56-0.07-1.12-0.13-1.69-0.04-0.36-0.08-0.71-0.13-1.06-0.06-0.46-0.11-0.92-0.17-1.37-0.06-0.38-0.13-0.77-0.19-1.15-0.07-0.42-0.14-0.85-0.23-1.27-0.08-0.38-0.16-0.75-0.25-1.12-0.1-0.43-0.19-0.86-0.3-1.29-0.09-0.34-0.18-0.68-0.27-1.01-0.13-0.46-0.25-0.92-0.39-1.38l-0.27-0.84c-0.17-0.51-0.34-1.03-0.52-1.54l-0.24-0.63c-0.22-0.57-0.44-1.15-0.68-1.72-0.05-0.13-0.11-0.26-0.17-0.39a46.1 46.1 0 0 0-0.86-1.9c-0.02-0.05-0.05-0.1-0.08-0.15a49.774 49.774 0 0 0-9.66-13.59L883.51 352.78c-19.53-19.53-51.18-19.52-70.71 0-19.53 19.53-19.53 51.18 0 70.71l38.5 38.5H562v-289.3l38.5 38.5c9.76 9.76 22.56 14.64 35.36 14.64 12.79 0 25.59-4.88 35.36-14.64 19.53-19.53 19.53-51.18 0-70.71L547.37 16.64a49.895 49.895 0 0 0-9-7.12c-0.17-0.11-0.34-0.22-0.52-0.33-0.38-0.23-0.78-0.45-1.16-0.67-0.34-0.19-0.67-0.39-1.01-0.57-0.26-0.14-0.53-0.27-0.79-0.4-0.48-0.25-0.95-0.49-1.44-0.72-0.15-0.07-0.31-0.14-0.46-0.21-0.6-0.28-1.2-0.55-1.81-0.8-0.06-0.02-0.12-0.05-0.18-0.07-2.93-1.2-6-2.14-9.18-2.78-0.02 0-0.03-0.01-0.05-0.01-0.78-0.15-1.56-0.29-2.35-0.41l-0.44-0.06c-0.66-0.09-1.32-0.18-1.98-0.25-0.41-0.04-0.82-0.06-1.22-0.09-0.41-0.03-0.83-0.07-1.24-0.09-0.84-0.04-1.68-0.06-2.52-0.06H512c-0.31 0-0.62 0.02-0.92 0.02-0.52 0.01-1.04 0.01-1.55 0.04-13.01 0.63-24.72 6.25-33.25 14.98L352.82 140.49c-19.53 19.53-19.53 51.18 0 70.71 19.53 19.53 51.18 19.53 70.71 0L462 172.73V462H172.73l38.47-38.47c19.53-19.53 19.53-51.18 0-70.71-19.53-19.53-51.18-19.53-70.71 0L17.04 476.27c-8.72 8.54-14.34 20.24-14.98 33.25-0.03 0.52-0.03 1.03-0.04 1.55 0 0.31-0.02 0.62-0.02 0.93v0.02c0 0.84 0.02 1.68 0.06 2.51 0.02 0.42 0.06 0.83 0.09 1.24 0.03 0.41 0.05 0.82 0.09 1.22 0.07 0.67 0.15 1.33 0.25 1.98 0.02 0.15 0.03 0.3 0.06 0.44 0.12 0.79 0.26 1.58 0.41 2.36 0 0.01 0 0.02 0.01 0.03 0.63 3.2 1.57 6.27 2.78 9.2 0.02 0.06 0.05 0.12 0.07 0.18 0.25 0.61 0.53 1.21 0.8 1.81 0.07 0.15 0.14 0.31 0.21 0.46 0.23 0.49 0.48 0.96 0.72 1.44 0.14 0.26 0.26 0.53 0.4 0.79 0.18 0.34 0.38 0.67 0.57 1.01 0.22 0.39 0.44 0.78 0.67 1.16 0.11 0.18 0.22 0.35 0.33 0.52 2.01 3.23 4.38 6.26 7.12 9l123.84 123.84c9.76 9.76 22.56 14.64 35.36 14.64s25.59-4.88 35.36-14.64c19.53-19.53 19.53-51.18 0-70.71L172.7 562H462v289.31l-38.5-38.5c-19.53-19.53-51.19-19.52-70.71 0-19.53 19.53-19.53 51.18 0 70.71l123.84 123.84c4.07 4.07 8.67 7.28 13.57 9.65l0.18 0.09c0.62 0.3 1.24 0.58 1.87 0.85l0.42 0.18c0.56 0.23 1.12 0.45 1.69 0.66 0.22 0.08 0.44 0.17 0.66 0.25 0.5 0.18 1 0.35 1.51 0.51 0.29 0.09 0.58 0.19 0.87 0.28 0.45 0.14 0.9 0.26 1.36 0.39 0.35 0.1 0.69 0.19 1.04 0.28 0.42 0.1 0.84 0.2 1.25 0.29 0.38 0.09 0.77 0.18 1.16 0.25 0.41 0.08 0.82 0.15 1.23 0.22 0.4 0.07 0.79 0.14 1.19 0.2 0.44 0.07 0.88 0.11 1.32 0.17 0.37 0.04 0.74 0.1 1.11 0.13 0.54 0.05 1.08 0.09 1.62 0.12 0.28 0.02 0.55 0.05 0.83 0.06 0.83 0.04 1.66 0.06 2.48 0.06h0.02c0.1 0 0.2-0.01 0.31-0.01 0.71 0 1.43-0.02 2.14-0.05 0.5-0.02 1-0.07 1.5-0.11 0.3-0.02 0.61-0.04 0.91-0.07 0.62-0.06 1.22-0.14 1.83-0.22 0.18-0.02 0.37-0.04 0.55-0.07 0.67-0.1 1.33-0.21 1.98-0.33 0.13-0.02 0.26-0.04 0.38-0.07 0.68-0.13 1.36-0.28 2.03-0.44 0.11-0.03 0.22-0.05 0.33-0.08 0.68-0.17 1.34-0.35 2.01-0.54l0.33-0.09c0.65-0.2 1.3-0.41 1.94-0.63 0.13-0.04 0.26-0.08 0.38-0.13 0.62-0.22 1.24-0.45 1.85-0.7l0.45-0.18c0.59-0.24 1.16-0.49 1.74-0.75 0.17-0.08 0.35-0.15 0.52-0.23 0.56-0.26 1.11-0.54 1.65-0.82 0.18-0.09 0.37-0.18 0.55-0.28 0.55-0.29 1.08-0.59 1.62-0.9 0.17-0.1 0.34-0.19 0.52-0.3 0.56-0.33 1.12-0.68 1.66-1.04 0.13-0.09 0.27-0.17 0.4-0.26 0.62-0.41 1.24-0.84 1.84-1.28 0.05-0.04 0.1-0.07 0.15-0.11 2.2-1.61 4.25-3.4 6.16-5.35l123.45-123.45c19.53-19.53 19.53-51.18 0-70.71-19.53-19.52-51.18-19.53-70.71 0L562 851.27V562h289.27l-38.47 38.47c-19.53 19.53-19.53 51.18 0 70.71 9.76 9.76 22.56 14.64 35.36 14.64s25.59-4.88 35.36-14.64l123.45-123.45c1.94-1.9 3.73-3.96 5.34-6.15 0.04-0.06 0.08-0.12 0.13-0.18 0.43-0.6 0.85-1.2 1.26-1.81z" fill="#616161" p-id="4345"></path></svg>
    `;
    dragIcon.style.cssText = `
      position: absolute;
      left: -24px;
      top: 40%;
      transform: translateY(-50%);
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: move;
      opacity: 0;
      transition: all 0.2s;
      z-index: 1;
    `;

    // 添加hover效果
    dragIcon.addEventListener("mouseenter", () => {
      dragIcon.querySelector("path")?.setAttribute("fill", "#409EFF");
    });

    dragIcon.addEventListener("mouseleave", () => {
      if (!el.dragData!.dragging) {
        dragIcon.querySelector("path")?.setAttribute("fill", "#666");
      }
    });

    el.appendChild(dragIcon);
    el.dragIcon = dragIcon;

    // 鼠标进入显示图标
    const showIcon = () => {
      if (dragIcon) {
        dragIcon.style.opacity = "1";
        dragIcon.style.transform = "translateY(-50%) scale(1)";
      }
    };

    // 鼠标离开隐藏图标
    const hideIcon = () => {
      if (dragIcon && !el.dragData!.dragging) {
        dragIcon.style.opacity = "0";
        dragIcon.style.transform = "translateY(-50%) scale(0.8)";
      }
    };

    el?.addEventListener("mouseenter", showIcon);
    el.parentElement?.addEventListener("mouseenter", showIcon);
    el.addEventListener("mouseleave", hideIcon);

    // 计算拖动范围
    const calculateBounds = () => {
      const parent = el.parentElement as HTMLElement;
      if (!parent) return;

      const parentRect = parent.getBoundingClientRect();
      const elRect = el.getBoundingClientRect();

      const parentStyle = getComputedStyle(parent);
      const paddingLeft = parseFloat(parentStyle.paddingLeft);
      const paddingTop = parseFloat(parentStyle.paddingTop);
      const paddingRight = parseFloat(parentStyle.paddingRight);
      const paddingBottom = parseFloat(parentStyle.paddingBottom);

      el.dragData!.bounds = {
        left: -elRect.left + parentRect.left + paddingLeft,
        top: -elRect.top + parentRect.top + paddingTop,
        right: parentRect.right - elRect.right - paddingRight,
        bottom: parentRect.bottom - elRect.bottom - paddingBottom,
      };
    };

    const handleMousedown = (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();

      calculateBounds();

      const dragData = el.dragData!;
      dragData.dragging = true;
      showIcon();

      dragData.startX = e.clientX - dragData.currentX;
      dragData.startY = e.clientY - dragData.currentY;

      document.addEventListener("mousemove", handleMousemove);
      document.addEventListener("mouseup", handleMouseup);
    };

    const handleMousemove = (e: MouseEvent) => {
      if (!el.dragData!.dragging) return;

      const dragData = el.dragData!;
      const bounds = dragData.bounds!;

      let newOffsetX = e.clientX - dragData.startX;
      let newOffsetY = e.clientY - dragData.startY;

      newOffsetX = Math.max(bounds.left, Math.min(bounds.right, newOffsetX));
      newOffsetY = Math.max(bounds.top, Math.min(bounds.bottom, newOffsetY));

      dragData.currentX = newOffsetX;
      dragData.currentY = newOffsetY;

      const translateStr = `translate(${newOffsetX}px, ${newOffsetY}px)`;
      el.style.transform = dragData.initialTransform
        ? `${dragData.initialTransform} ${translateStr}`
        : translateStr;
    };

    const handleMouseup = () => {
      el.dragData!.dragging = false;
      document.removeEventListener("mousemove", handleMousemove);
      document.removeEventListener("mouseup", handleMouseup);
      hideIcon();
    };

    const handleResize = () => {
      if (el.dragData!.dragging) return;
      calculateBounds();
    };
    window.addEventListener("resize", handleResize);

    // 允许通过整个元素进行拖动
    el.addEventListener("mousedown", handleMousedown);
    dragIcon.addEventListener("mousedown", handleMousedown);
    calculateBounds();
  },

  unmounted(el: DragElement) {
    window.removeEventListener("resize", () => {});
    if (el.dragIcon) {
      el.removeChild(el.dragIcon);
    }
    delete el.dragData;
  },
};

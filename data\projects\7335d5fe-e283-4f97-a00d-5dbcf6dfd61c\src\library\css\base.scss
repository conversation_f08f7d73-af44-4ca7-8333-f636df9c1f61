﻿@charset "utf-8";
html {
  color: #454545;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
}

body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
pre,
code,
form,
fieldset,
legend,
input,
textarea,
p,
blockquote,
th,
td,
hr,
button,
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  margin: 0;
  padding: 0;
}

article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section {
  display: block;
}

audio,
canvas,
video {
  display: inline-block;
  *display: inline;
  *zoom: 1;
}

body,
html,
#app {
  width: 100%;
  height: 100%;
}

body,
button,
input,
select,
textarea {
  font: 12px/1.5 tahoma, 微软雅黑, \5b8b\4f53;
}

input,
select,
textarea {
  font-size: 100%;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

th {
  text-align: inherit;
}

fieldset,
img {
  border: 0;
}

iframe {
  display: block;
}

abbr,
acronym {
  border: 0;
  font-variant: normal;
}

del {
  text-decoration: line-through;
}

// address,
// caption,
// cite,
// code,
// dfn,
// em,
// th,
// var {
//   font-style: normal;
//   font-weight: 500;
// }

ol,
ul {
  list-style: none;
}

caption,
th {
  text-align: left;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 100%;
  font-weight: 500;
}

q:before,
q:after {
  content: "";
}

sub,
sup {
  font-webkit-scrollbar-tracksize: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

a:hover {
  text-decoration: underline;
}

ins,
a {
  text-decoration: none;
}

.clearfix:after {
  visibility: hidden;
  display: block;
  font-size: 0;
  content: " ";
  clear: both;
  height: 0;
}
.fn-left,
.fn-right {
  display: inline;
}

.fn-left {
  float: left;
}

.fn-right {
  float: right;
}

.none {
  display: none;
}

input,
div,
textarea {
  outline: 0;
  outline: 0;
}

a {
  /*color           : #323232;*/
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

::selection {
  background: #01cc7d;
  color: #fff;
}

::-moz-selection {
  background: #ff9632;
  color: #000;
}

::-webkit-selection {
  background: #ff9632;
  color: #000;
}

.ms-controller {
  visibility: hidden;
}

[v-cloak] {
  display: none;
}

/*友盟统计*/

.bottom-count-tj {
  position: fixed;
  bottom: 10px;
  right: 20px;
}

.head {
  background: white;
  padding: 10px;
  margin: 10px 10px;
  height: 28px;
}

.head-tab {
  height: 25px;
  margin-bottom: 10px;
}

.toolbar {
  padding: 2px 20px 10px 20px;
}

* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

button {
  outline: 0;
  border: 0;
}

button:focus {
  outline: 0;
}

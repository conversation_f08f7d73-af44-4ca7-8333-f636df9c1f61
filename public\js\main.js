/**
 * 前端部署工具主JavaScript文件
 */
document.addEventListener('DOMContentLoaded', () => {
  // 检查用户是否已登录
  const user = JSON.parse(localStorage.getItem('user') || 'null');
  const token = localStorage.getItem('token');
  
  if (!user || !token) {
    // 未登录，重定向到登录页面
    window.location.href = '/login';
    return;
  }
  
  // 显示用户信息
  document.getElementById('sidebar-user-info').classList.remove('d-none');
  document.getElementById('sidebar-username').textContent = user.displayName || user.username;
  
  // 退出登录
  document.getElementById('logout-btn').addEventListener('click', () => {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    window.location.href = '/login';
  });
  
  // 初始化Socket.io连接
  const socket = io();
  
  // 发送用户连接信息
  socket.emit('user_connected', {
    userId: user.id,
    username: user.username
  });
  
  // 全局变量
  let currentBuildId = null;
  let projects = [];
  let servers = [];
  let nodeVersions = []; // 存储Node.js版本信息
  
  // 页面导航
  const navLinks = document.querySelectorAll('.nav-link');
  const pageContents = document.querySelectorAll('.page-content');
  const pageTitle = document.getElementById('page-title');
  const pageActions = document.getElementById('page-actions');
  
  // 模态框
  const addProjectModal = new bootstrap.Modal(document.getElementById('addProjectModal'));
  const addServerModal = new bootstrap.Modal(document.getElementById('addServerModal'));
  const deployModal = new bootstrap.Modal(document.getElementById('deployModal'));
  
  // 导航处理
  navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const page = link.getAttribute('data-page');
      
      if (!page) return; // 跳过外部链接
      
      // 更新导航链接激活状态
      navLinks.forEach(l => l.classList.remove('active'));
      link.classList.add('active');
      
      // 显示相应页面
      pageContents.forEach(content => content.classList.remove('active'));
      document.getElementById(`page-${page}`).classList.add('active');
      
      // 更新页面标题
      pageTitle.textContent = link.textContent.trim();
      
      // 更新页面操作按钮
      updatePageActions(page);
      
      // 加载页面数据
      loadPageData(page);
    });
  });
  
  /**
   * 更新页面操作按钮
   * @param {string} page - 页面名称
   */
  function updatePageActions(page) {
    pageActions.innerHTML = '';
    
    switch (page) {
      case 'projects':
        pageActions.innerHTML = `
          <button class="btn btn-sm btn-outline-primary" id="add-project-button">
            <i class="bi bi-plus-lg"></i> 添加项目
          </button>
        `;
        document.getElementById('add-project-button').addEventListener('click', () => {
          // 重置表单和标题
          document.getElementById('add-project-form').reset();
          document.getElementById('addProjectModalLabel').textContent = '添加项目';
          document.getElementById('save-project-btn').removeAttribute('data-project-id');
          loadServersForProjectForm();
          addProjectModal.show();
        });
        break;
        
      case 'servers':
        pageActions.innerHTML = `
          <button class="btn btn-sm btn-outline-primary" id="add-server-button">
            <i class="bi bi-plus-lg"></i> 添加服务器
          </button>
        `;
        document.getElementById('add-server-button').addEventListener('click', () => {
          resetServerForm();
          addServerModal.show();
        });
        break;
        
      case 'nodeVersions':
        pageActions.innerHTML = `
          <button class="btn btn-sm btn-outline-secondary" id="refresh-versions-button">
            <i class="bi bi-arrow-clockwise"></i> 刷新
          </button>
        `;
        document.getElementById('refresh-versions-button').addEventListener('click', () => {
          loadNodeVersionsData();
        });
        break;
        
      case 'queue':
        pageActions.innerHTML = `
          <button class="btn btn-sm btn-outline-secondary" id="refresh-queue-button">
            <i class="bi bi-arrow-clockwise"></i> 刷新
          </button>
        `;
        document.getElementById('refresh-queue-button').addEventListener('click', () => {
          loadQueueData();
          loadHistoryData();
        });
        break;
    }
  }
  
  /**
   * 加载页面数据
   * @param {string} page - 页面名称
   */
  function loadPageData(page) {
    switch (page) {
      case 'dashboard':
        loadProjects();
        loadRecentDeployments();
        loadActiveBuilds();
        break;
        
      case 'projects':
        loadProjects();
        break;
        
      case 'servers':
        loadServers();
        break;
        
      case 'nodeVersions':
        loadNodeVersionsData();
        break;
        
      case 'queue':
        loadQueueData();
        loadHistoryData();
        break;
    }
  }
  
  /**
   * 加载Node.js版本数据
   */
  function loadNodeVersionsData() {
    // 加载已安装的版本
    loadInstalledNodeVersions();
    
    // 监听直接安装按钮
    const installVersionDirectBtn = document.getElementById('install-version-direct-btn');
    if (installVersionDirectBtn) {
      installVersionDirectBtn.addEventListener('click', function() {
        const versionInput = document.getElementById('node-version-input');
        const version = versionInput.value.trim();
        
        if (!version) {
          alert('请输入有效的Node.js版本号');
          return;
        }
        
        // 显示安装进度
        const installResult = document.getElementById('install-result');
        const installStatus = document.getElementById('install-direct-status');
        
        installResult.classList.remove('d-none');
        installStatus.textContent = '正在安装...';
        installVersionDirectBtn.disabled = true;
        
        // 发送安装请求
        fetch('/api/node-versions/install', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ version })
        })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              installStatus.textContent = `Node.js v${version} 安装成功！`;
              
              // 清空输入框
              versionInput.value = '';
              
              // 刷新已安装版本列表
              setTimeout(() => {
                loadInstalledNodeVersions();
                
                // 重置按钮和进度条
                setTimeout(() => {
                  installResult.classList.add('d-none');
                  installVersionDirectBtn.disabled = false;
                }, 1500);
              }, 500);
            } else {
              installStatus.textContent = `安装失败: ${data.error}`;
              installVersionDirectBtn.disabled = false;
            }
          })
          .catch(error => {
            console.error('安装Node.js版本失败:', error);
            installStatus.textContent = '安装失败，请查看控制台日志';
            installVersionDirectBtn.disabled = false;
          });
      });
    }
  }
  
  /**
   * 加载已安装的Node.js版本
   */
  function loadInstalledNodeVersions() {
    const container = document.getElementById('installed-versions-container');
    if (!container) return;
    
    container.innerHTML = '<p class="text-center"><i class="bi bi-hourglass-split"></i> 正在加载...</p>';
    
    fetch('/api/node-versions/installed')
      .then(response => response.json())
      .then(data => {
        if (data.success && data.versions && data.versions.length > 0) {
          nodeVersions = data.versions;
          
          // 显示版本列表
          let html = '<div class="list-group">';
          nodeVersions.forEach(version => {
            html += `
              <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                <div>
                  <i class="bi bi-check-circle-fill text-success me-2"></i>
                  Node.js v${version}
                </div>
                <button class="btn btn-sm btn-outline-primary switch-version" data-version="${version}">
                  切换到此版本
                </button>
              </div>
            `;
          });
          html += '</div>';
          
          container.innerHTML = html;
          
          // 添加切换版本事件监听器
          document.querySelectorAll('.switch-version').forEach(button => {
            button.addEventListener('click', function() {
              const version = this.getAttribute('data-version');
              
              // 切换版本
              fetch('/api/node-versions/switch', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({ version })
              })
                .then(response => response.json())
                .then(data => {
                  if (data.success) {
                    alert(`已成功切换到Node.js v${version}`);
                  } else {
                    alert(`切换失败: ${data.error}`);
                  }
                })
                .catch(error => {
                  console.error('切换Node.js版本失败:', error);
                  alert('切换Node.js版本失败');
                });
            });
          });
        } else {
          container.innerHTML = `
            <div class="alert alert-warning">
              <p><strong>未检测到已安装的Node.js版本</strong></p>
              <p>可能的原因:</p>
              <ul>
                <li>系统中未安装NVS (Node Version Switcher)</li>
                <li>NVS未正确配置或无法访问</li>
                <li>NVS没有安装任何Node.js版本</li>
              </ul>
              <p>您仍然可以在右侧面板输入版本号来安装Node.js</p>
            </div>
          `;
        }
      })
      .catch(error => {
        console.error('获取已安装的Node.js版本失败:', error);
        container.innerHTML = `
          <div class="alert alert-danger">
            <p><strong>获取版本信息失败</strong></p>
            <p>错误信息: ${error.message}</p>
            <p>您仍然可以在右侧面板输入版本号来安装Node.js</p>
          </div>
        `;
      });
  }
  
  /**
   * 加载项目列表
   */
  function loadProjects() {
    fetch('/api/projects')
      .then(response => response.json())
      .then(data => {
        projects = data;
        // 更新快速部署表单
        const projectSelect = document.getElementById('project-select');
        projectSelect.innerHTML = '<option value="" selected disabled>选择项目</option>';
        
        projects.forEach(project => {
          const option = document.createElement('option');
          option.value = project.id;
          option.textContent = project.name;
          projectSelect.appendChild(option);
        });
        
        // 更新项目表格
        const projectsTableBody = document.getElementById('projects-table-body');
        if (projectsTableBody) {
          projectsTableBody.innerHTML = '';
          
          if (projects.length === 0) {
            projectsTableBody.innerHTML = `
              <tr>
                <td colspan="5" class="text-center">暂无项目</td>
              </tr>
            `;
            return;
          }
          
          projects.forEach(project => {
            projectsTableBody.innerHTML += `
              <tr>
                <td>${project.name}</td>
                <td>${project.gitUrl}</td>
                <td>${project.buildCommand}</td>
                <td>${project.servers?.length}</td>
                <td>
                  <button class="btn btn-sm btn-primary deploy-project" data-id="${project.id}">
                    <i class="bi bi-cloud-upload"></i> 部署
                  </button>
                  <button class="btn btn-sm btn-outline-secondary edit-project" data-id="${project.id}">
                    <i class="bi bi-pencil"></i> 编辑
                  </button>
                  <button class="btn btn-sm btn-outline-danger delete-project" data-id="${project.id}">
                    <i class="bi bi-trash"></i> 删除
                  </button>
                </td>
              </tr>
            `;
          });
          
          // 添加事件监听器
          document.querySelectorAll('.deploy-project').forEach(button => {
            button.addEventListener('click', (e) => {
              const projectId = e.target.closest('button').getAttribute('data-id');
              prepareDeployment(projectId);
            });
          });
          
          document.querySelectorAll('.edit-project').forEach(button => {
            button.addEventListener('click', (e) => {
              const projectId = e.target.closest('button').getAttribute('data-id');
              editProject(projectId);
            });
          });
          
          document.querySelectorAll('.delete-project').forEach(button => {
            button.addEventListener('click', (e) => {
              const projectId = e.target.closest('button').getAttribute('data-id');
              deleteProject(projectId);
            });
          });
        }
      })
      .catch(error => {
        console.error('加载项目失败:', error);
        alert('加载项目失败');
      });
  }
  
  /**
   * 加载服务器列表
   */
  function loadServers() {
    fetch('/api/servers')
      .then(response => response.json())
      .then(data => {
        servers = data;
        
        const serversTableBody = document.getElementById('servers-table-body');
        if (serversTableBody) {
          serversTableBody.innerHTML = '';
          
          if (servers.length === 0) {
            serversTableBody.innerHTML = `
              <tr>
                <td colspan="5" class="text-center">暂无服务器</td>
              </tr>
            `;
            return;
          }
          
          servers.forEach(server => {
            serversTableBody.innerHTML += `
              <tr>
                <td>${server.name}</td>
                <td>${server.host}</td>
                <td>${server.username}</td>
                <td>
                  <button class="btn btn-sm btn-outline-secondary edit-server" data-id="${server.id}">
                    <i class="bi bi-pencil"></i> 编辑
                  </button>
                  <button class="btn btn-sm btn-outline-danger delete-server" data-id="${server.id}">
                    <i class="bi bi-trash"></i> 删除
                  </button>
                </td>
              </tr>
            `;
          });
          
          // 添加事件监听器
          document.querySelectorAll('.edit-server').forEach(button => {
            button.addEventListener('click', (e) => {
              const serverId = e.target.closest('button').getAttribute('data-id');
              editServer(serverId);
            });
          });
          
          document.querySelectorAll('.delete-server').forEach(button => {
            button.addEventListener('click', (e) => {
              const serverId = e.target.closest('button').getAttribute('data-id');
              deleteServer(serverId);
            });
          });
        }
      })
      .catch(error => {
        console.error('加载服务器失败:', error);
        alert('加载服务器失败');
      });
  }
  
  /**
   * 加载队列数据
   */
  function loadQueueData() {
    fetch('/api/queue')
      .then(response => response.json())
      .then(data => {
        const queueTableBody = document.getElementById('queue-table-body');
        if (!queueTableBody) return;
        
        const activeBuildCount = document.getElementById('active-builds-count');
        if (activeBuildCount) {
          activeBuildCount.textContent = data.filter(build => build.status === 'running').length;
        }
        
        if (data.length === 0) {
          queueTableBody.innerHTML = `
            <tr>
              <td colspan="7" class="text-center">当前没有构建任务</td>
            </tr>
          `;
          return;
        }
        
        queueTableBody.innerHTML = '';
        data.forEach(build => {
          const shortId = build.id.substring(0, 8);
          const statusBadge = getStatusBadge(build.status);
          const date = new Date(build.queuedAt).toLocaleString();
          
          queueTableBody.innerHTML += `
            <tr>
              <td>${shortId}</td>
              <td>${build.projectName}</td>
              <td>${build.branch}</td>
              <td>${statusBadge}</td>
              <td>${build.username}</td>
              <td>${date}</td>
              <td>
                <button class="btn btn-sm btn-outline-info view-logs" data-id="${build.id}">
                  <i class="bi bi-file-text"></i> 日志
                </button>
                ${build.status === 'queued' ? `
                  <button class="btn btn-sm btn-outline-danger cancel-build" data-id="${build.id}">
                    <i class="bi bi-x-circle"></i> 取消
                  </button>
                ` : ''}
              </td>
            </tr>
          `;
        });
        
        // 添加日志查看事件监听器
        document.querySelectorAll('.view-logs').forEach(button => {
          button.addEventListener('click', function() {
            const buildId = this.getAttribute('data-id');
            viewBuildLog(buildId);
          });
        });
        
        // 添加取消构建事件监听器
        document.querySelectorAll('.cancel-build').forEach(button => {
          button.addEventListener('click', function() {
            const buildId = this.getAttribute('data-id');
            cancelBuild(buildId);
          });
        });
      })
      .catch(error => {
        console.error('加载队列数据失败:', error);
        const queueTableBody = document.getElementById('queue-table-body');
        if (queueTableBody) {
          queueTableBody.innerHTML = `
            <tr>
              <td colspan="7" class="text-center text-danger">加载失败</td>
            </tr>
          `;
        }
      });
  }
  
  // 加载Node.js版本信息
  loadNodeVersions();
  
  /**
   * 加载Node.js版本信息
   */
  function loadNodeVersions() {
    fetch('/api/node-versions/installed')
      .then(response => response.json())
      .then(data => {
        if (data.success && data.versions) {
          nodeVersions = data.versions;
          
          // 更新项目表单中的Node.js版本选择器
          const nodeVersionSelect = document.getElementById('node-version');
          if (nodeVersionSelect) {
            // 保留默认选项
            nodeVersionSelect.innerHTML = '<option value="" selected>使用系统默认版本</option>';
            
            // 添加可用版本
            nodeVersions.forEach(version => {
              const option = document.createElement('option');
              option.value = version;
              option.textContent = `v${version}`;
              nodeVersionSelect.appendChild(option);
            });
          }
        }
      })
      .catch(error => {
        console.error('获取Node.js版本失败:', error);
      });
  }
  
  /**
   * 加载历史数据
   */
  function loadHistoryData() {
    fetch('/api/queue/history')
      .then(response => response.json())
      .then(data => {
        const historyTableBody = document.getElementById('history-table-body');
        if (!historyTableBody) return;
        
        if (data.length === 0) {
          historyTableBody.innerHTML = `
            <tr>
              <td colspan="7" class="text-center">暂无构建历史</td>
            </tr>
          `;
          return;
        }
        
        historyTableBody.innerHTML = '';
        data.forEach(build => {
          const shortId = build.id.substring(0, 8);
          const statusBadge = getStatusBadge(build.status);
          const date = new Date(build.completedAt || build.queuedAt).toLocaleString();
          
          historyTableBody.innerHTML += `
            <tr>
              <td>${shortId}</td>
              <td>${build.projectName}</td>
              <td>${build.branch}</td>
              <td>${statusBadge}</td>
              <td>${build.username}</td>
              <td>${date}</td>
              <td>
                <button class="btn btn-sm btn-outline-info view-logs" data-id="${build.id}">
                  <i class="bi bi-file-text"></i> 日志
                </button>
                <button class="btn btn-sm btn-outline-secondary rebuild" data-id="${build.id}">
                  <i class="bi bi-arrow-repeat"></i> 重新构建
                </button>
              </td>
            </tr>
          `;
        });
        
        // 添加日志查看事件监听器
        document.querySelectorAll('.view-logs').forEach(button => {
          button.addEventListener('click', function() {
            const buildId = this.getAttribute('data-id');
            viewBuildLog(buildId);
          });
        });
        
        // 添加重新构建事件监听器
        document.querySelectorAll('.rebuild').forEach(button => {
          button.addEventListener('click', function() {
            const buildId = this.getAttribute('data-id');
            rebuildBuild(buildId);
          });
        });
      })
      .catch(error => {
        console.error('加载历史数据失败:', error);
        const historyTableBody = document.getElementById('history-table-body');
        if (historyTableBody) {
          historyTableBody.innerHTML = `
            <tr>
              <td colspan="7" class="text-center text-danger">加载失败</td>
            </tr>
          `;
        }
      });
  }
  
  /**
   * 查看构建日志
   * @param {string} buildId - 构建ID
   */
  function viewBuildLog(buildId) {
    fetch(`/api/queue/${buildId}`)
      .then(response => response.json())
      .then(build => {
        // 设置日志模态框内容
        document.getElementById('log-project-name').textContent = build.projectName;
        document.getElementById('log-branch-name').textContent = build.branch;
        document.getElementById('log-status').textContent = getStatusText(build.status);
        
        // 获取日志内容
        fetch(`/api/queue/${buildId}/logs`)
          .then(response => response.json())
          .then(logs => {
            const logContent = document.getElementById('build-log-content');
            logContent.innerHTML = '';
            
            if (logs.length === 0) {
              logContent.textContent = '暂无日志';
              return;
            }
            
            logs.forEach(log => {
              const time = new Date(log.time).toLocaleTimeString();
              logContent.innerHTML += `[${time}] ${log.message}\n`;
            });
            
            // 显示日志模态框
            const buildLogModal = new bootstrap.Modal(document.getElementById('buildLogModal'));
            buildLogModal.show();
          })
          .catch(error => {
            console.error('获取构建日志失败:', error);
            alert('获取构建日志失败');
          });
      })
      .catch(error => {
        console.error('获取构建详情失败:', error);
        alert('获取构建详情失败');
      });
  }
  
  /**
   * 取消构建
   * @param {string} buildId - 构建ID
   */
  function cancelBuild(buildId) {
    if (confirm('确定要取消此构建任务吗？')) {
      fetch(`/api/queue/${buildId}/cancel`, {
        method: 'POST'
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('构建任务已取消');
            loadQueueData();
          } else {
            alert(`取消失败: ${data.error}`);
          }
        })
        .catch(error => {
          console.error('取消构建失败:', error);
          alert('取消构建失败');
        });
    }
  }
  
  /**
   * 重新构建
   * @param {string} buildId - 构建ID
   */
  function rebuildBuild(buildId) {
    if (confirm('确定要重新执行此构建任务吗？')) {
      fetch(`/api/queue/${buildId}/rebuild`, {
        method: 'POST'
      })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            alert('已添加到构建队列');
            loadQueueData();
            loadHistoryData();
          } else {
            alert(`重新构建失败: ${data.error}`);
          }
        })
        .catch(error => {
          console.error('重新构建失败:', error);
          alert('重新构建失败');
        });
    }
  }
  
  /**
   * 获取状态文字
   * @param {string} status - 状态代码
   * @returns {string} 状态文字
   */
  function getStatusText(status) {
    switch (status) {
      case 'queued':
        return '等待中';
      case 'running':
        return '构建中';
      case 'success':
        return '成功';
      case 'failed':
        return '失败';
      case 'cancelled':
        return '已取消';
      default:
        return '未知';
    }
  }
  
  /**
   * 加载最近部署
   */
  function loadRecentDeployments() {
    // 获取用户的最近部署
    fetch(`/api/queue/user/${user.id}?limit=5`)
      .then(response => response.json())
      .then(data => {
        const recentDeployments = document.getElementById('recent-deployments');
        
        if (data.length === 0) {
          recentDeployments.innerHTML = `<p class="text-muted">暂无最近部署</p>`;
          return;
        }
        
        let html = '<div class="list-group">';
        data.forEach(build => {
          const date = new Date(build.queuedAt).toLocaleString();
          const statusBadge = getStatusBadge(build.status);
          
          html += `
            <a href="/queue" class="list-group-item list-group-item-action">
              <div class="d-flex w-100 justify-content-between">
                <h6 class="mb-1">${build.projectName} (${build.branch})</h6>
                ${statusBadge}
              </div>
              <small class="text-muted">${date}</small>
            </a>
          `;
        });
        html += '</div>';
        
        recentDeployments.innerHTML = html;
      })
      .catch(error => {
        console.error('加载最近部署失败:', error);
      });
  }
  
  /**
   * 加载活跃构建
   */
  function loadActiveBuilds() {
    fetch('/api/queue')
      .then(response => response.json())
      .then(data => {
        const activeBuildsTable = document.getElementById('active-builds-table');
        
        if (data.length === 0) {
          activeBuildsTable.innerHTML = `
            <tr>
              <td colspan="6" class="text-center">当前没有活跃构建</td>
            </tr>
          `;
          return;
        }
        
        activeBuildsTable.innerHTML = '';
        data.forEach(build => {
          const shortId = build.id.substring(0, 8);
          const statusBadge = getStatusBadge(build.status);
          const date = new Date(build.queuedAt).toLocaleString();
          
          activeBuildsTable.innerHTML += `
            <tr>
              <td>${shortId}</td>
              <td>${build.projectName}</td>
              <td>${build.branch}</td>
              <td>${statusBadge}</td>
              <td>${build.username}</td>
              <td>${date}</td>
            </tr>
          `;
        });
      })
      .catch(error => {
        console.error('加载活跃构建失败:', error);
      });
  }
  
  /**
   * 获取状态徽章
   */
  function getStatusBadge(status) {
    switch (status) {
      case 'queued':
        return '<span class="badge bg-secondary">等待中</span>';
      case 'running':
        return '<span class="badge bg-primary">构建中</span>';
      case 'success':
        return '<span class="badge bg-success">成功</span>';
      case 'failed':
        return '<span class="badge bg-danger">失败</span>';
      case 'cancelled':
        return '<span class="badge bg-warning text-dark">已取消</span>';
      default:
        return '<span class="badge bg-secondary">未知</span>';
    }
  }
  
  /**
   * 为项目表单加载服务器
   * @param {Array} selectedServers - 已选择的服务器配置数组
   */
  function loadServersForProjectForm(selectedServers = []) {
    fetch('/api/servers')
      .then(response => response.json())
      .then(data => {
        const serverCheckboxes = document.getElementById('server-checkboxes');
        serverCheckboxes.innerHTML = '';
        
        if (data.length === 0) {
          serverCheckboxes.innerHTML = `<p class="text-muted">暂无可用服务器，请先添加服务器</p>`;
          return;
        }
        
        data.forEach(server => {
          // 查找该服务器是否已被选中，并获取对应的部署路径
          const serverConfig = selectedServers.find(s => {
            // 检查是对象（新格式）还是字符串ID（旧格式）
            if (typeof s === 'object') {
              return s.serverId === server.id;
            } else {
              return s === server.id;
            }
          });
          
          const isChecked = !!serverConfig;
          const deployPath = (serverConfig && typeof serverConfig === 'object') ? 
            serverConfig.deployPath : '/var/www/html';
          
          const serverId = server.id;
          serverCheckboxes.innerHTML += `
            <div class="server-item mb-3 border-bottom pb-2">
              <div class="form-check">
                <input class="form-check-input server-checkbox" type="checkbox" 
                  value="${serverId}" id="server-${serverId}" ${isChecked ? 'checked' : ''}>
                <label class="form-check-label" for="server-${serverId}">
                  ${server.name} (${server.host})
                </label>
              </div>
              <div class="server-deploy-path ${isChecked ? '' : 'd-none'} mt-2">
                <label class="form-label">部署路径</label>
                <input type="text" class="form-control deploy-path-input" 
                  data-server-id="${serverId}" value="${deployPath}">
              </div>
            </div>
          `;
        });
        
        // 为服务器复选框添加事件监听器
        document.querySelectorAll('.server-checkbox').forEach(checkbox => {
          checkbox.addEventListener('change', function() {
            const deployPathDiv = this.closest('.server-item').querySelector('.server-deploy-path');
            if (this.checked) {
              deployPathDiv.classList.remove('d-none');
            } else {
              deployPathDiv.classList.add('d-none');
            }
          });
        });
      })
      .catch(error => {
        console.error('加载服务器失败:', error);
      });
  }
  
  /**
   * 准备部署
   * @param {string} projectId - 项目ID
   */
  function prepareDeployment(projectId) {
    const project = projects.find(p => p.id === projectId);
    if (!project) return;
    
    // 获取项目分支
    fetch(`/api/projects/${projectId}/branches`)
      .then(response => response.json())
      .then(branches => {
        // 获取项目服务器
        fetch(`/api/servers/project/${projectId}`)
          .then(response => response.json())
          .then(projectServers => {
            // 创建分支选择器
            const branchSelect = document.createElement('select');
            branchSelect.className = 'form-select mb-3';
            branchSelect.id = 'deploy-branch-select';
            
            branches.forEach(branch => {
              const option = document.createElement('option');
              option.value = branch;
              option.textContent = branch;
              branchSelect.appendChild(option);
            });
            
            // 创建服务器选择器
            let serverOptions = '';
            if (projectServers.length === 0) {
              serverOptions = `<p class="text-muted">该项目没有配置服务器</p>`;
            } else {
              serverOptions = '<div class="mb-3"><label class="form-label">选择部署服务器</label>';
              projectServers.forEach(server => {
                serverOptions += `
                  <div class="form-check">
                    <input class="form-check-input deploy-server-checkbox" type="checkbox" value="${server.id}" id="deploy-server-${server.id}" checked>
                    <label class="form-check-label" for="deploy-server-${server.id}">
                      ${server.name} (${server.host})
                    </label>
                  </div>
                `;
              });
              serverOptions += '</div>';
            }
            
            // 显示确认对话框
            Swal.fire({
              title: '部署项目',
              html: `
                <p>您将部署项目: <strong>${project.name}</strong></p>
                <div class="mb-3">
                  <label class="form-label">选择分支</label>
                  ${branchSelect.outerHTML}
                </div>
                ${serverOptions}
              `,
              showCancelButton: true,
              confirmButtonText: '开始部署',
              cancelButtonText: '取消',
              preConfirm: () => {
                const branch = document.getElementById('deploy-branch-select').value;
                const selectedServers = [];
                document.querySelectorAll('.deploy-server-checkbox:checked').forEach(checkbox => {
                  selectedServers.push(checkbox.value);
                });
                
                return { branch, servers: selectedServers };
              }
            }).then((result) => {
              if (result.isConfirmed) {
                startDeployment(projectId, result.value.branch, result.value.servers);
              }
            });
          })
          .catch(error => {
            console.error('获取项目服务器失败:', error);
            alert('获取项目服务器失败');
          });
      })
      .catch(error => {
        console.error('获取分支失败:', error);
        alert('获取分支失败');
      });
  }
  
  /**
   * 开始部署
   * @param {string} projectId - 项目ID
   * @param {string} branch - 分支名称
   * @param {Array} servers - 服务器配置数组
   */
  function startDeployment(projectId, branch, servers) {
    const project = projects.find(p => p.id === projectId);
    if (!project) return;
    
    // 添加到构建队列
    fetch('/api/queue', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        projectId,
        branch,
        userId: user.id,
        username: user.displayName || user.username,
        servers
      })
    })
      .then(response => response.json())
      .then(data => {
        if (data.id) {
          currentBuildId = data.id;
          
          // 显示部署模态框
          document.getElementById('deploy-project-name').textContent = project.name;
          document.getElementById('deploy-branch-name').textContent = branch;
          document.getElementById('deploy-status').textContent = '排队中';
          document.getElementById('deploy-log-content').innerHTML = `[${new Date().toLocaleTimeString()}] 构建任务已添加到队列\n`;
          
          deployModal.show();
          
          // 监听构建日志
          socket.on(`build-${currentBuildId}`, (log) => {
            const logContent = document.getElementById('deploy-log-content');
            const time = new Date(log.time).toLocaleTimeString();

            // 根据日志级别添加不同的样式
            let logClass = '';
            if (log.level === 'error') {
              logClass = 'text-danger';
            } else if (log.level === 'warn') {
              logClass = 'text-warning';
            }

            const logLine = logClass ?
              `<span class="${logClass}">[${time}] ${log.message}</span>\n` :
              `[${time}] ${log.message}\n`;

            logContent.innerHTML += logLine;

            // 自动滚动到底部
            logContent.scrollTop = logContent.scrollHeight;

            // 更新状态
            if (log.message.includes('开始构建') || log.message.includes('开始部署')) {
              document.getElementById('deploy-status').textContent = '构建中';
            } else if (log.message.includes('构建成功完成') || log.message.includes('部署成功完成')) {
              document.getElementById('deploy-status').textContent = '成功';
            } else if (log.message.includes('构建失败') || log.message.includes('部署失败') || log.level === 'error') {
              document.getElementById('deploy-status').textContent = '失败';
            }
          });

          // 监听构建错误
          socket.on(`build-error-${currentBuildId}`, (errorData) => {
            const logContent = document.getElementById('deploy-log-content');
            const time = new Date(errorData.time).toLocaleTimeString();

            // 显示错误信息
            logContent.innerHTML += `<span class="text-danger fw-bold">[${time}] ❌ ${errorData.message}</span>\n`;

            // 如果有详细错误信息，也显示出来
            if (errorData.error && errorData.error.stack) {
              const errorLine = errorData.error.stack.split('\n')[0];
              logContent.innerHTML += `<span class="text-muted small">错误详情: ${errorLine}</span>\n`;
            }

            logContent.scrollTop = logContent.scrollHeight;

            // 更新状态为失败
            document.getElementById('deploy-status').textContent = '失败';

            // 显示错误提示
            const deployStatus = document.getElementById('deploy-status');
            if (deployStatus) {
              deployStatus.className = 'badge bg-danger';
            }
          });
          
          // 刷新仪表盘数据
          loadRecentDeployments();
          loadActiveBuilds();
        } else {
          alert('添加部署任务失败');
        }
      })
      .catch(error => {
        console.error('开始部署失败:', error);
        alert('开始部署失败');
      });
  }
  
  /**
   * 项目选择变更处理
   */
  document.getElementById('project-select').addEventListener('change', function() {
    const projectId = this.value;
    const branchSelect = document.getElementById('branch-select');
    const deployBtn = document.getElementById('deploy-btn');
    const serverOptions = document.getElementById('server-deploy-options');
    
    if (projectId) {
      // 加载分支
      branchSelect.disabled = true;
      branchSelect.innerHTML = '<option value="" selected disabled>加载中...</option>';
      
      fetch(`/api/projects/${projectId}/branches`)
        .then(response => response.json())
        .then(branches => {
          branchSelect.innerHTML = '<option value="" selected disabled>选择分支</option>';
          
          branches.forEach(branch => {
            const option = document.createElement('option');
            option.value = branch;
            option.textContent = branch;
            branchSelect.appendChild(option);
          });
          
          branchSelect.disabled = false;
        })
        .catch(error => {
          console.error('获取分支失败:', error);
          branchSelect.innerHTML = '<option value="" selected disabled>加载分支失败</option>';
        });
      
      // 加载服务器
      serverOptions.innerHTML = '<p class="text-muted">加载中...</p>';
      
      fetch(`/api/servers/project/${projectId}`)
        .then(response => response.json())
        .then(projectServers => {
          if (projectServers.length === 0) {
            serverOptions.innerHTML = `<p class="text-muted">该项目没有配置服务器</p>`;
          } else {
            let html = '';
            projectServers.forEach(server => {
              html += `
                <div class="server-item mb-3">
                  <div class="form-check">
                    <input class="form-check-input quick-deploy-server" type="checkbox" value="${server.id}" 
                      id="quick-server-${server.id}" checked data-deploy-path="${server.deployPath}">
                    <label class="form-check-label" for="quick-server-${server.id}">
                      ${server.name} (${server.host}) - ${server.deployPath}
                    </label>
                  </div>
                </div>
              `;
            });
            serverOptions.innerHTML = html;
          }
        })
        .catch(error => {
          console.error('获取项目服务器失败:', error);
          serverOptions.innerHTML = '<p class="text-danger">加载服务器失败</p>';
        });
    } else {
      branchSelect.innerHTML = '<option value="" selected disabled>选择分支</option>';
      branchSelect.disabled = true;
      deployBtn.disabled = true;
      serverOptions.innerHTML = '<p class="text-muted">请先选择项目</p>';
    }
  });
  
  /**
   * 分支选择变更处理
   */
  document.getElementById('branch-select').addEventListener('change', function() {
    const deployBtn = document.getElementById('deploy-btn');
    deployBtn.disabled = !this.value;
  });
  
  /**
   * 快速部署表单提交
   */
  document.getElementById('quick-deploy-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const projectId = document.getElementById('project-select').value;
    const branch = document.getElementById('branch-select').value;
    
    if (!projectId || !branch) {
      alert('请选择项目和分支');
      return;
    }
    
    // 获取选中的服务器和对应的部署路径
    const selectedServers = [];
    document.querySelectorAll('.quick-deploy-server:checked').forEach(checkbox => {
      const serverId = checkbox.value;
      const deployPath = checkbox.getAttribute('data-deploy-path') || '/var/www/html';
      selectedServers.push({ serverId, deployPath });
    });
    
    // 开始部署
    startDeployment(projectId, branch, selectedServers);
  });
  
  /**
   * 添加项目表单提交
   */
  document.getElementById('save-project-btn').addEventListener('click', function() {
    const projectId = this.getAttribute('data-project-id');
    const name = document.getElementById('project-name').value;
    const gitUrl = document.getElementById('git-url').value;
    const buildCommand = document.getElementById('build-command').value;
    const outputDir = document.getElementById('output-dir').value;
    const description = document.getElementById('project-description').value;
    const nodeVersion = document.getElementById('node-version').value;
    
    // 获取选中的服务器和对应的部署路径
    const servers = [];
    document.querySelectorAll('#server-checkboxes .server-checkbox:checked').forEach(checkbox => {
      const serverId = checkbox.value;
      const deployPathInput = checkbox.closest('.server-item').querySelector('.deploy-path-input');
      const deployPath = deployPathInput ? deployPathInput.value || '/var/www/html' : '/var/www/html';
      servers.push({ serverId, deployPath });
    });
    
    if (!name || !gitUrl) {
      alert('项目名称和Git URL为必填项');
      return;
    }
    
    const projectData = {
      name,
      gitUrl,
      buildCommand,
      outputDir,
      servers, // 确保这个字段被包含
      description,
      nodeVersion
    };
    
    console.log('保存项目数据:', projectData); // 添加日志，检查servers字段
    
    // 判断是添加还是编辑
    const isEdit = !!projectId;
    const url = isEdit ? `/api/projects/${projectId}` : '/api/projects';
    const method = isEdit ? 'PUT' : 'POST';
    
    // 发送请求
    fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectData)
    })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        addProjectModal.hide();
        loadProjects();
        
        // 重置表单和按钮状态
        document.getElementById('add-project-form').reset();
        document.getElementById('addProjectModalLabel').textContent = '添加项目';
        this.removeAttribute('data-project-id');
      })
      .catch(error => {
        console.error(`${isEdit ? '更新' : '创建'}项目失败:`, error);
        alert(`${isEdit ? '更新' : '创建'}项目失败: ${error.message}`);
      });
  });
  
  /**
   * 重置服务器表单
   */
  function resetServerForm() {
    // 重置表单
    document.getElementById('add-server-form').reset();

    // 重置模态框标题
    document.getElementById('addServerModalLabel').textContent = '添加服务器';

    // 重置按钮文本
    document.getElementById('save-server-btn').textContent = '保存服务器';

    // 移除服务器ID属性
    document.getElementById('save-server-btn').removeAttribute('data-server-id');

    // 设置默认端口
    document.getElementById('server-port').value = '22';
  }

  /**
   * 添加/编辑服务器表单提交
   */
  document.getElementById('save-server-btn').addEventListener('click', function() {
    const name = document.getElementById('server-name').value;
    const host = document.getElementById('server-host').value;
    const port = document.getElementById('server-port').value;
    const username = document.getElementById('server-username').value;
    const password = document.getElementById('server-password').value;
    const description = document.getElementById('server-description').value;
    const serverId = this.getAttribute('data-server-id');

    // 检查必填项
    if (!name || !host || !username) {
      alert('服务器名称、主机和用户名为必填项');
      return;
    }

    // 如果是编辑模式且密码为空，提示用户
    if (serverId && !password) {
      if (!confirm('密码为空，是否保持原密码不变？')) {
        return;
      }
    } else if (!serverId && !password) {
      alert('创建服务器时密码为必填项');
      return;
    }

    const isEdit = !!serverId;
    const url = isEdit ? `/api/servers/${serverId}` : '/api/servers';
    const method = isEdit ? 'PUT' : 'POST';

    // 构建请求数据
    const requestData = {
      name,
      host,
      port,
      username,
      description
    };

    // 只有在密码不为空时才包含密码字段
    if (password) {
      requestData.password = password;
    }

    // 发送请求
    fetch(url, {
      method: method,
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    })
      .then(response => response.json())
      .then(data => {
        addServerModal.hide();
        loadServers();

        // 重置表单和状态
        resetServerForm();

        // 显示成功消息
        alert(isEdit ? '服务器更新成功' : '服务器创建成功');
      })
      .catch(error => {
        console.error(`${isEdit ? '更新' : '创建'}服务器失败:`, error);
        alert(`${isEdit ? '更新' : '创建'}服务器失败`);
      });
  });
  
  /**
   * 编辑项目
   * @param {string} projectId - 项目ID
   */
  function editProject(projectId) {
    // 获取项目详情
    fetch(`/api/projects/${projectId}`)
      .then(response => response.json())
      .then(project => {
        // 设置模态框标题
        document.getElementById('addProjectModalLabel').textContent = '编辑项目';
        
        // 填充表单数据
        document.getElementById('project-name').value = project.name;
        document.getElementById('git-url').value = project.gitUrl;
        document.getElementById('build-command').value = project.buildCommand;
        document.getElementById('output-dir').value = project.outputDir;
        document.getElementById('project-description').value = project.description || '';
        
        // 设置Node.js版本
        const nodeVersionSelect = document.getElementById('node-version');
        if (project.nodeVersion && nodeVersionSelect) {
          // 查找对应版本的选项
          for (let i = 0; i < nodeVersionSelect.options.length; i++) {
            if (nodeVersionSelect.options[i].value === project.nodeVersion) {
              nodeVersionSelect.selectedIndex = i;
              break;
            }
          }
          
          // 如果没找到匹配的版本，添加一个新选项
          if (nodeVersionSelect.value !== project.nodeVersion) {
            const option = document.createElement('option');
            option.value = project.nodeVersion;
            option.textContent = `v${project.nodeVersion}`;
            nodeVersionSelect.appendChild(option);
            option.selected = true;
          }
        } else {
          // 没有指定版本，选择默认选项
          nodeVersionSelect.selectedIndex = 0;
        }
        
        // 加载服务器选项
        loadServersForProjectForm(project.servers);
        
        // 将项目ID存储在保存按钮上
        document.getElementById('save-project-btn').setAttribute('data-project-id', projectId);
        
        // 显示模态框
        addProjectModal.show();
      })
      .catch(error => {
        console.error('获取项目详情失败:', error);
        alert('获取项目详情失败');
      });
  }
  
  /**
   * 删除项目
   * @param {string} projectId - 项目ID
   */
  function deleteProject(projectId) {
    if (confirm('确定要删除此项目吗？')) {
      fetch(`/api/projects/${projectId}`, {
        method: 'DELETE'
      })
        .then(response => response.json())
        .then(data => {
          loadProjects();
        })
        .catch(error => {
          console.error('删除项目失败:', error);
          alert('删除项目失败');
        });
    }
  }
  
  /**
   * 编辑服务器
   * @param {string} serverId - 服务器ID
   */
  function editServer(serverId) {
    // 获取服务器详情
    fetch(`/api/servers/${serverId}`)
      .then(response => response.json())
      .then(server => {
        // 设置模态框标题
        document.getElementById('addServerModalLabel').textContent = '编辑服务器';

        // 填充表单数据
        document.getElementById('server-name').value = server.name;
        document.getElementById('server-host').value = server.host;
        document.getElementById('server-port').value = server.port || 22;
        document.getElementById('server-username').value = server.username;
        document.getElementById('server-password').value = ''; // 出于安全考虑，不显示密码
        document.getElementById('server-description').value = server.description || '';

        // 将服务器ID存储在保存按钮上
        document.getElementById('save-server-btn').setAttribute('data-server-id', serverId);

        // 更改按钮文本
        document.getElementById('save-server-btn').textContent = '更新服务器';

        // 显示模态框
        addServerModal.show();
      })
      .catch(error => {
        console.error('获取服务器详情失败:', error);
        alert('获取服务器详情失败');
      });
  }
  
  /**
   * 删除服务器
   * @param {string} serverId - 服务器ID
   */
  function deleteServer(serverId) {
    if (confirm('确定要删除此服务器吗？')) {
      fetch(`/api/servers/${serverId}`, {
        method: 'DELETE'
      })
        .then(response => response.json())
        .then(data => {
          loadServers();
        })
        .catch(error => {
          console.error('删除服务器失败:', error);
          alert('删除服务器失败');
        });
    }
  }
  
  // 监听构建事件
  socket.on('build-queued', (build) => {
    loadActiveBuilds();
  });

  socket.on('build-started', (build) => {
    loadActiveBuilds();
  });

  socket.on('build-log', (log) => {
    loadActiveBuilds();

    // 如果是错误日志，显示通知
    if (log.level === 'error') {
      showErrorNotification(`构建错误: ${log.message}`, log.projectName);
    }
  });

  // 监听全局构建错误
  socket.on('build-error', (errorData) => {
    loadActiveBuilds();
    showErrorNotification(`部署失败: ${errorData.message}`, errorData.projectName);
  });

  // 显示错误通知的函数
  function showErrorNotification(message, projectName = '') {
    // 创建错误通知元素
    const notification = document.createElement('div');
    notification.className = 'alert alert-danger alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
    notification.innerHTML = `
      <strong>构建错误</strong> ${projectName ? `(${projectName})` : ''}<br>
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // 5秒后自动移除
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 5000);
  }
  
  // 初始加载仪表盘
  loadPageData('dashboard');
  
  // 添加 SweetAlert2 库
  const script = document.createElement('script');
  script.src = 'https://cdn.jsdelivr.net/npm/sweetalert2@11';
  document.head.appendChild(script);
}); 
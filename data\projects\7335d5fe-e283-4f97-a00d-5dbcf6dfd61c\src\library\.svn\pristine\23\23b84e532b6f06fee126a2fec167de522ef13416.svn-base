﻿<template>
  <span :class="['mk-sort-caret', { disabled }]">
    <i
      :class="[
        'sort-btn ascending',
        { active: sortType === SORT_TYPE.ascending },
        size
      ]"
      @click="onSort(SORT_TYPE.ascending)"
    ></i>
    <i
      :class="[
        'sort-btn descending',
        { active: sortType === SORT_TYPE.descending },
        size
      ]"
      @click="onSort(SORT_TYPE.descending)"
    ></i>
  </span>
</template>
<script lang="ts">
import { defineComponent, toRefs, reactive, PropType, watch } from "vue";

/** 尺寸类型 */
type TSize = "small" | "medium" | "large";
/** 排序类型 */
export enum SORT_TYPE {
  // 未排序
  no = "no",
  // 升序
  ascending = "ascending",
  // 降序
  descending = "descending"
}
/**
 * @Name: 排序插入符
 * @Descripttion: 上下箭头升降序排序组件
 * @Author: gaohan
 * @Date: 2021/9/7 10:53
 * @LastEditors: gaohan
 * @LastEditTime: 2021/9/7
 * @Events: on-sort 点击箭头时触发的事件 参数为(no:取消排序,ascending:升序,descending:降序)
 */
export default defineComponent({
  name: "mk-sort-caret",

  emits: ["on-sort"],

  props: {
    // 当前值
    value: {
      type: String as PropType<keyof typeof SORT_TYPE>,
      default: SORT_TYPE.no
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 尺寸大小
    size: {
      type: String as PropType<TSize>,
      default: "medium"
    }
  },

  setup(props, ctx) {
    const state = reactive({
      // 当前排序状态
      sortType: props.value
    });

    /**
     * @name: 排序切换
     * @param sortType 参数说明
     */
    const onSort = (sortType: keyof typeof SORT_TYPE = SORT_TYPE.no) => {
      switch (state.sortType) {
        case SORT_TYPE.no:
          state.sortType = sortType;
          break;
        case SORT_TYPE.ascending:
          state.sortType =
            sortType === SORT_TYPE.ascending ? SORT_TYPE.no : sortType;
          break;
        case SORT_TYPE.descending:
          state.sortType =
            sortType === SORT_TYPE.descending ? SORT_TYPE.no : sortType;
          break;
      }
      ctx.emit("on-sort", state.sortType);
    };

    watch(
      () => props.value,
      (val: keyof typeof SORT_TYPE) => {
        state.sortType = val;
      }
    );

    return {
      ...toRefs(state),
      SORT_TYPE,
      onSort
    };
  }
});
</script>

<style lang="scss" scoped>
.mk-sort-caret {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  height: 34px;
  width: 24px;
  margin-left: -5px;
  vertical-align: middle;
  cursor: pointer;
  overflow: initial;
  position: relative;
  &.disabled {
    pointer-events: none;
  }

  .sort-btn {
    width: 0;
    height: 0;
    position: absolute;
    &.small {
      border: 5px solid transparent;
      left: 7px;
    }
    &.medium {
      border: 8px solid transparent;
      left: unset;
    }
    &.large {
      border: 12px solid transparent;
      left: 2px;
    }

    &.ascending {
      border-bottom-color: #c0c4cc;
      &.active {
        @include theme_border-bottom-color();
      }
      &.small {
        top: 5px;
      }
      &.medium {
        top: 0;
      }
      &.large {
        top: -10px;
      }
    }
    &.descending {
      border-top-color: #c0c4cc;
      &.active {
        @include theme_border-top-color();
      }
      &.small {
        bottom: 7px;
      }
      &.medium {
        bottom: 0;
      }
      &.large {
        bottom: -7px;
      }
    }
  }
}
</style>

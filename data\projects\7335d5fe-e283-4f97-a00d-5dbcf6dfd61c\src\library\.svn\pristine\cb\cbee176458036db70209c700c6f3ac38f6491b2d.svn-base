﻿<template>
  <div v-for="(item, index) in formatLabelList" :key="index" class="label-item">
    <!--学段-->
    <el-form-item
      v-if="item.type === LABEL_TYPE.PHASE"
      label="学段:"
      :required="item.isRequired == 1"
    >
      <el-select
        :popper-append-to-body="false"
        v-model="curPhase"
        placeholder="请选择学段"
        clearable
        @change="switchPhase"
        value-key="optionCode"
        style="margin-top: 5px"
      >
        <el-option
          v-for="sitem in phaseList"
          :key="sitem.optionCode"
          :label="sitem.optionName"
          :value="{
            labelId: item.labelId,
            type: item.type,
            ...sitem,
            labelTitle: item.labelTitle,
          }"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <!--年级-->
    <el-form-item
      v-if="item.type === LABEL_TYPE.GRADE"
      label="年级:"
      :required="item.isRequired == 1"
    >
      <el-select
        :popper-append-to-body="false"
        v-model="curGrade"
        placeholder="请选择年级"
        clearable
        @change="switchGrade"
        value-key="gradeId"
        style="margin-top: 5px"
      >
        <el-option
          v-for="sitem in gradeList"
          :key="sitem.gradeId"
          :label="sitem.gradeName"
          :value="{
            labelId: item.labelId,
            type: item.type,
            ...sitem,
            labelTitle: item.labelTitle,
          }"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <!--学科-->
    <el-form-item
      v-if="item.type === LABEL_TYPE.SUBJECT"
      label="学科:"
      :required="item.isRequired == 1"
    >
      <el-select
        :popper-append-to-body="false"
        v-model="curSubject"
        placeholder="请选择学科"
        clearable
        @change="switchSubject"
        value-key="id"
        style="margin-top: 5px"
      >
        <el-option
          v-for="sitem in subjectLsit"
          :key="sitem.id"
          :label="sitem.subject_name"
          :value="{
            labelId: item.labelId,
            type: item.type,
            ...sitem,
            labelTitle: item.labelTitle,
          }"
        >
        </el-option>
      </el-select>
    </el-form-item>
    <!--作者来源-->
    <el-form-item
      v-if="item.type === LABEL_TYPE.SOURCE"
      label="作者来源:"
      :required="item.isRequired == 1"
    >
      <el-form-item label="地区" label-width="40px">
        <!-- 自定义-区 -->
        <el-select
          v-if="
            [REGION_TYPE.CUSTOM, REGION_TYPE.DISTRICT].includes(
              platFormInfo.regionPlatformType
            )
          "
          :popper-append-to-body="false"
          v-model="curDistrict"
          placeholder="请选择区域"
          clearable
          @change="
            (value) => {
              switchArea({
                labelId: item.labelId,
                type: item.type,
                ...value,
              });
            }
          "
          value-key="areaId"
          style="margin-top: 5px"
        >
          <el-option
            v-for="sitem in areaList"
            :key="sitem.areaId"
            :label="sitem.areaName"
            :value="sitem"
          >
          </el-option>
        </el-select>
        <!-- 行政--省市校 -->
        <area-select
          v-else
          :ascription="[
            curDistrict.provinceId,
            curDistrict.cityId,
            curDistrict.districtId,
          ]"
          @get-select-area="selectArea"
        >
        </area-select>
      </el-form-item>
      <el-form-item label="学校" label-width="40px">
        <el-select
          style="margin-top: 5px"
          v-model="curSchool"
          placeholder="选择学校或输入学校"
          :popper-append-to-body="false"
          :remote-method="searchSchool"
          :loading="schoolOptions.loading"
          remote
          filterable
          allow-create
          default-first-option
          clearable
          value-key="schoolId"
          @change="
            (value) => {
              switchSchool({
                ...value,
                labelId: item.labelId,
                type: item.type,
              });
            }
          "
          @clear="searchSchool('')"
        >
          <el-option
            v-for="sitem in schoolList"
            :key="sitem.schoolId"
            :label="sitem.schoolName"
            :value="sitem"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="教师" label-width="40px">
        <el-select
          style="margin-top: 5px"
          v-model="curTeacherList"
          placeholder="选择教师或输入教师"
          :popper-append-to-body="false"
          :remote-method="searchTeacher"
          v-loadselect="loadTeacherMore"
          :loading="teacherOptions.loading"
          remote
          filterable
          clearable
          allow-create
          default-first-option
          multiple
          value-key="id"
          @change="
            (value) => {
              switchTeacher(value, item.type, item.labelId);
            }
          "
          @clear="searchTeacher('')"
        >
          <el-option
            v-for="sitem in teacherList"
            :key="sitem.id"
            :label="sitem.realname"
            :value="sitem"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form-item>
    <!--简介-->
    <el-form-item
      v-if="item.type === LABEL_TYPE.INTRO"
      label="简介:"
      :required="item.isRequired == 1"
    >
      <el-input
        type="textarea"
        v-model="intro"
        :autosize="{ minRows: 2, maxRows: 4 }"
        :placeholder="item.isRequired == 1 ? '至少输入3字以上' : '请输入简介'"
        show-word-limit
        maxlength="300"
        @change="
          (value) => {
            switchIntro(value, item.type, item.labelId);
          }
        "
      ></el-input>
    </el-form-item>
    <!--自定义标签-->
    <el-form-item
      v-if="item.type === LABEL_TYPE.CUSTOMLABEL"
      :label="item.labelTitle + ':'"
      :required="item.isRequired == 1"
    >
      <el-select
        :popper-append-to-body="false"
        :placeholder="'请选择' + item.labelTitle"
        clearable
        v-model="item.curValue"
        style="margin-top: 5px"
        value-key="optionCode"
        @change="
          (value) => {
            item.curValue = value;
            switchCustomLabel({
              labelId: item.labelId,
              labelTitle: item.labelTitle,
              type: item.type,
              ...value,
            });
          }
        "
      >
        <el-option
          v-for="sitem in item.optionValue"
          :key="sitem.optionCode"
          :label="sitem.optionName"
          :value="sitem"
        >
        </el-option>
      </el-select>
    </el-form-item>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  computed,
  reactive,
  toRefs,
  onMounted,
} from "vue";
import { useStore } from "vuex";
import { IGlobalState } from "@/library/src/store";
import { querySchoolPlatformListAPI } from "@/library/src/service/API/base";
import {
  getAllGradeListAPI,
  getSubjectListByPhaseAPI,
  userListAPI,
} from "@/library/src/service/API/base";
import { ElMessage } from "element-plus";
import AreaSelect from "@/library/ui/mk-share-res/modules/AreaSelect.vue";
import { REGION_TYPE } from "@/library/src/utils/gloableEnum";
/** 标签类型 */
export enum LABEL_TYPE {
  /** 学段 */
  PHASE = 1,
  /** 年级 */
  GRADE = 2,
  /** 学科 */
  SUBJECT = 3,
  /** 类型 */
  TYPE = 4,
  /** 教材目录 */
  TEXTBOOK = 5,
  /** 学科知识点 */
  KNOWLEDGE = 6,
  /** 自定义标签 */
  CUSTOMLABEL = 7,
  /** 作者来源 */
  SOURCE = 8,
  /** 版本 */
  EDITION = 9,
  /**自定义目录树 */
  CUSTOMTREE = 10,
  /** 格式 */
  FORMAT = 11,
  /** 简介 */
  INTRO = 12,
}

export default defineComponent({
  name: "platform-label-item",
  emits: ["switch-label", "switch-customlabel"],
  components: {
    AreaSelect,
  },
  props: {
    // 标签列表
    labelList: {
      type: Array,
      default: () => {},
    },
    // 区域id
    platFromId: {
      type: String,
      default: "",
    },
  },
  setup(props, ctx) {
    const store = useStore<IGlobalState>();

    const state = reactive({
      // 格式化后的标签列表
      formatLabelList: computed(() => {
        return props.labelList.map((item: any) => {
          item.curValue = {
            labelId: "",
            optionCode: "",
            optionName: "",
          };
          try {
            item.optionValue =
              typeof item.optionValue === "string"
                ? JSON.parse(item.optionValue)
                : item.optionValue;
          } catch (e) {
            item.optionValue = [];
          }
          return item;
        });
      }),
      // 学段列表
      phaseList: [
        {
          optionName: "小学",
          optionCode: "1",
        },
        {
          optionName: "初中",
          optionCode: "2",
        },
        {
          optionName: "高中",
          optionCode: "3",
        },
      ],
      // 当前学段
      curPhase: {
        optionName: "",
        optionCode: "",
      },
      // 年级列表
      gradeList: [],
      // 当前年级
      curGrade: {
        gradeId: "",
        gradeName: "",
        phase: "",
      },
      // 学科列表
      subjectLsit: [],
      // 当前学科
      curSubject: {
        center_code: "",
        id: "",
        phase: 0,
        subject_code: "",
        subject_name: "",
      },
      // 地区列表
      areaList: computed(() => {
        return store.state.app.platFormInfo.region.map((item: any) => {
          item.areaId = item.districtId
            ? `${item.provinceId}-${item.cityId}-${item.districtId}`
            : item.cityId
            ? `${item.provinceId}-${item.cityId}`
            : item.provinceId;
          item.areaName = item.districtName
            ? `${item.provinceName}>${item.cityName}>${item.districtName}`
            : item.cityName
            ? `${item.provinceName}>${item.cityName}`
            : item.provinceName;
          return item;
        });
      }),
      // 当前地区
      curDistrict: {
        areaId: "",
        areaName: "",
        cityId: "",
        cityName: "",
        districtId: "",
        districtName: "",
        provinceId: "",
        provinceName: "",
      },
      // 学校列表
      schoolList: [] as any[],
      // 当前学校
      curSchool: {} as any,
      // 学校请求参数
      schoolAPIParams: {
        keyWord: "",
        page: 1,
        limit: 50,
      },
      // 学校查询配置参数
      schoolOptions: {
        timer: null as any,
        loading: false,
      },
      // 教师列表
      teacherList: [] as any[],
      // 当前教师列表
      curTeacherList: [] as any[],
      // 教师请求参数
      teacherAPIParams: {
        keyWord: "",
        page: 1,
        limit: 50,
        pageCount: 0,
      },
      // 教师查询配置参数
      teacherOptions: {
        timer: null as any,
        loading: false,
      },
      // 简介
      intro: "",
      // 已选自定义标签列表
      customLabelList: [] as any[],
      // 登录用户区域信息
      platFormInfo: store.state.app.platFormInfo,
    });
    /**
     * @name 校验表单
     */
    const validateForm = () => {
      let labelList: any = [];
      //过滤出必选项
      labelList = state.formatLabelList.filter((val: any) => {
        return val.isRequired == 1;
      });
      for (let i = 0; i < labelList.length; i++) {
        let item = labelList[i];
        //学段
        if (
          item.type == LABEL_TYPE.PHASE &&
          (!state.curPhase || state.curPhase.optionCode == "")
        ) {
          ElMessage({ message: "请选择学段！", type: "warning" });
          return false;
        }
        //年级
        else if (
          item.type == LABEL_TYPE.GRADE &&
          (!state.curGrade || state.curGrade.gradeId == "")
        ) {
          ElMessage({ message: "请选择年级！", type: "warning" });
          return false;
        }
        //学科
        else if (
          item.type == LABEL_TYPE.SUBJECT &&
          (!state.curSubject || state.curSubject.id == "")
        ) {
          ElMessage({ message: "请选择学科！", type: "warning" });
          return false;
        }
        //作者来源
        else if (
          item.type == LABEL_TYPE.SOURCE &&
          state.curTeacherList.length == 0
        ) {
          ElMessage({ message: "请选择作者来源！", type: "warning" });
          return false;
        }
        //简介
        else if (item.type == LABEL_TYPE.INTRO) {
          let introText = state.intro.trim();
          if (introText == "") {
            ElMessage({ message: "请输入简介！", type: "warning" });
            return false;
          } else if (introText.length < 3) {
            ElMessage({ message: "简介至少三个字哦~", type: "warning" });
            return false;
          }
        }
        //自定义标签
        else if (
          item.type == LABEL_TYPE.CUSTOMLABEL &&
          (!item.curValue || item.curValue.optionCode == "")
        ) {
          ElMessage({
            message: `请选择${item.labelTitle}！`,
            type: "warning",
          });
          return false;
        }
      }
      return true;
    };
    /**
     * @name: 自定义--切换地区
     * @param: area 当前地区
     */
    const selectArea = (area: any) => {
      state.curDistrict = area;
      getSchoolList();
    };

    /**
     * @name: 切换地区
     * @param item 地区对象
     */
    const switchArea = (item: any) => {
      state.curDistrict = item;
      state.curSchool = {
        schoolId: "",
        schoolName: "",
      };
      state.curTeacherList = [];
      state.schoolList = [];
      state.teacherList = [];
      state.schoolAPIParams.page = 1;
      const label = {
        type: item.type,
        labelId: item.labelId,
        districtId: item.areaId,
        districtName: item.areaName,
        schoolId: "",
        schoolName: "",
        optionCode: "",
        optionName: "",
      };
      ctx.emit("switch-label", label);
      getSchoolList();
    };
    /**
     * @name: 切换学校
     * @param item 学校对象
     */
    const switchSchool = (item: any) => {
      state.curSchool = item;
      state.curTeacherList = [];
      state.teacherList = [];
      state.teacherAPIParams.page = 1;
      const label = {
        type: item.type,
        labelId: item.labelId,
        districtId: state.curDistrict.areaId,
        districtName: state.curDistrict.areaName,
        schoolId: item.schoolId,
        schoolName: item.schoolName,
        optionCode: "",
        optionName: "",
      };
      ctx.emit("switch-label", label);
      getTeacherList();
    };
    /**
     * @name: 切换教师
     * @param item 教师对象
     */
    const switchTeacher = (
      curTeacherList: any,
      type: number,
      labelId: string
    ) => {
      state.curTeacherList = curTeacherList;
      let optionCode: any[] = [];
      let optionName: any[] = [];
      curTeacherList.forEach((item: any) => {
        if (typeof item === "string") {
          optionCode.push(item);
          optionName.push(item);
        } else {
          optionCode.push(item.id);
          optionName.push(item.realname);
        }
      });
      const label = {
        type,
        labelId,
        districtId: state.curDistrict.areaId,
        districtName: state.curDistrict.areaName,
        schoolId: state.curSchool.schoolId,
        schoolName: state.curSchool.schoolName,
        optionCode: optionCode.toString(),
        optionName: optionName.toString(),
      };
      ctx.emit("switch-label", label);
    };
    /**
     * @name: 获取教师列表
     */
    const getTeacherList = async () => {
      const res = await userListAPI({
        userType: 1,
        schoolId: state.curSchool.schoolId,
        ...state.teacherAPIParams,
      });
      state.teacherAPIParams.pageCount = res.data.page_count;
      state.teacherList =
        res.code === 1 ? state.teacherList.concat(res.data.rows) : [];
    };
    /**
     * @name: 查询教师
     * @param keyWord 关键词
     */
    const searchTeacher = async (keyWord: string = "") => {
      clearTimeout(state.teacherOptions.timer);
      state.teacherOptions.timer = setTimeout(() => {
        state.teacherList = [];
        state.teacherAPIParams.keyWord = keyWord;
        state.teacherAPIParams.page = 1;
        state.teacherOptions.loading = true;
        getTeacherList().then(() => {
          state.teacherOptions.loading = false;
        });
      }, 300);
    };
    /**
     *  @name 滚动加载获取更多教师
     */
    const loadTeacherMore = () => {
      if (state.teacherAPIParams.page < state.teacherAPIParams.pageCount) {
        state.teacherAPIParams.page++;
        getTeacherList();
      }
    };
    /**
     * @name: 切换简介
     * @param curCatalog 当前简介
     */
    const switchIntro = (intro: any, type: number, labelId: string) => {
      state.intro = intro;
      const label = {
        type,
        labelId,
        optionCode: "",
        optionName: intro.trim(),
      };
      ctx.emit("switch-label", label);
    };
    /**
     * @name: 切换学段
     * @param item 当前学段
     */
    const switchPhase = (item: any) => {
      state.curPhase = item;
      ctx.emit("switch-label", item);
    };
    /**
     * @name: 切换年级
     * @param item 当前年级
     */
    const switchGrade = (item: any) => {
      state.curGrade = item;
      const label = {
        type: item.type,
        labelId: item.labelId,
        labelTitle: item.labelTitle,
        optionCode: item.gradeId,
        optionName: item.gradeName,
      };
      ctx.emit("switch-label", label);
    };
    /**
     * @name: 切换学科
     * @param item 当前学科
     */
    const switchSubject = (item: any) => {
      state.curSubject = item;
      const label = {
        type: item.type,
        labelId: item.labelId,
        labelTitle: item.labelTitle,
        optionCode: item.id,
        optionName: item.subject_name,
      };
      ctx.emit("switch-label", label);
    };
    /**
     * @name: 切换自定义标签
     * @param item 当前自定义标签
     * @param item 当前自定义标签
     */
    const switchCustomLabel = (item: any) => {
      const index = state.customLabelList.findIndex((sitem: any) => {
        return sitem.labelId === item.labelId;
      });
      const label = {
        type: item.type,
        labelId: item.labelId,
        labelTitle: item.labelTitle,
        optionCode: item.optionCode,
        optionName: item.optionName,
      };
      if (index === -1) {
        state.customLabelList.push(label);
      } else {
        state.customLabelList.splice(index, 1, label);
      }
      ctx.emit("switch-customlabel", state.customLabelList);
    };
    /**
     * @name: 获取学校列表
     */
    const getSchoolList = async () => {
      state.schoolOptions.loading = true;
      const res = await querySchoolPlatformListAPI({
        platformId: store.state.app.platFormInfo.id,
        provinceId: state.curDistrict.provinceId,
        cityId: state.curDistrict.cityId,
        districtId: state.curDistrict.districtId,
        schoolType: 1,
        regionalCategory: 2,
        platformState: 1,
        ...state.schoolAPIParams,
        regionId: props.platFromId,
      });
      state.schoolList = res.code === 1 ? res.data.rows : [];
      state.schoolOptions.loading = false;
    };
    /**
     * @name: 查询学校
     * @param keyWord 关键词
     */
    const searchSchool = async (keyWord: "") => {
      clearTimeout(state.schoolOptions.timer);
      state.schoolOptions.timer = setTimeout(() => {
        state.schoolList = [];
        state.schoolAPIParams.keyWord = keyWord;
        state.schoolAPIParams.page = 1;
        getSchoolList();
      }, 300);
    };

    /**
     * @name: 获取年级列表
     */
    const getGradeList = async () => {
      const res = await getAllGradeListAPI({
        phases: state.curPhase.optionCode as string,
      });
      state.gradeList = res.code === 1 ? res.data : [];
    };
    /**
     * @name: 获取学科列表
     */
    const getSubjectList = async () => {
      const res = await getSubjectListByPhaseAPI({
        phase: "1,2,3",
      });
      state.subjectLsit = res.code === 1 ? res.data : [];
    };

    onMounted(() => {
      getGradeList();
      getSubjectList();
    });

    return {
      ...toRefs(state),
      LABEL_TYPE,
      searchSchool,
      searchTeacher,
      switchArea,
      switchSchool,
      switchTeacher,
      switchIntro,
      switchPhase,
      switchGrade,
      switchSubject,
      switchCustomLabel,
      REGION_TYPE,
      selectArea,
      validateForm,
      loadTeacherMore,
    };
  },
});
</script>

<style lang="scss" scoped>
.label-item {
  .el-form-item {
    margin-bottom: 10px !important;
  }
}
</style>

const express = require('express');
const router = express.Router();
const User = require('../models/User');
const { v4: uuidv4 } = require('uuid');

// 获取所有用户 (仅管理员)
router.get('/', async (req, res) => {
  try {
    const users = await User.getAll();
    res.json(users);
  } catch (error) {
    req.logger.error(`获取用户列表失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 根据ID获取用户
router.get('/:id', async (req, res) => {
  try {
    const user = await User.getById(req.params.id);
    if (!user) {
      return res.status(404).json({ error: '用户不存在' });
    }
    res.json(user);
  } catch (error) {
    req.logger.error(`获取用户详情失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 创建新用户 (注册)
router.post('/register', async (req, res) => {
  try {
    const { username, password, email, displayName } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码为必填项' });
    }
    
    // 检查用户名是否已存在
    const existingUser = await User.getByUsername(username);
    if (existingUser) {
      return res.status(400).json({ error: '用户名已存在' });
    }
    
    const user = await User.create({
      username,
      password,
      email,
      displayName,
      role: 'user' // 默认为普通用户
    });
    
    res.status(201).json({
      success: true,
      message: '注册成功，请等待管理员审核',
      user
    });
  } catch (error) {
    req.logger.error(`用户注册失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码为必填项' });
    }
    
    const result = await User.validateUser(username, password);
    if (!result) {
      return res.status(401).json({ error: '用户名或密码错误' });
    }

    // 检查是否是错误对象（账户状态问题）
    if (result.error) {
      let message = '登录失败';
      switch (result.status) {
        case 'pending':
          message = '账户待审核，请等待管理员审核后再登录';
          break;
        case 'disabled':
          message = '账户已被禁用，请联系管理员';
          break;
        default:
          message = '账户状态异常，请联系管理员';
      }
      return res.status(403).json({ error: message, status: result.status });
    }

    const user = result;
    
    // 生成会话令牌 (简单实现，实际应使用 JWT 或其他会话管理)
    const token = uuidv4();
    
    res.json({ 
      success: true,
      message: '登录成功',
      user,
      token
    });
  } catch (error) {
    req.logger.error(`用户登录失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 更新用户 (仅自己或管理员)
router.put('/:id', async (req, res) => {
  try {
    const { displayName, email, password, role } = req.body;
    
    // 在实际应用中，这里应该验证用户权限
    
    const user = await User.update(req.params.id, {
      displayName,
      email,
      password,
      role
    });
    
    res.json(user);
  } catch (error) {
    req.logger.error(`更新用户失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 删除用户 (仅管理员)
router.delete('/:id', async (req, res) => {
  try {
    // 在实际应用中，这里应该验证用户权限

    const result = await User.delete(req.params.id);
    if (!result) {
      return res.status(404).json({ error: '用户不存在' });
    }
    res.json({ success: true });
  } catch (error) {
    req.logger.error(`删除用户失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取待审核用户 (仅管理员)
router.get('/admin/pending', async (req, res) => {
  try {
    const pendingUsers = await User.getPendingUsers();
    res.json(pendingUsers);
  } catch (error) {
    req.logger.error(`获取待审核用户失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 获取用户统计信息 (仅管理员)
router.get('/admin/stats', async (req, res) => {
  try {
    const stats = await User.getUserStats();
    res.json(stats);
  } catch (error) {
    req.logger.error(`获取用户统计失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 审核用户 (仅管理员)
router.post('/:id/approve', async (req, res) => {
  try {
    const { approved, adminId } = req.body;

    if (typeof approved !== 'boolean') {
      return res.status(400).json({ error: '审核结果为必填项' });
    }

    if (!adminId) {
      return res.status(400).json({ error: '管理员ID为必填项' });
    }

    const user = await User.approveUser(req.params.id, adminId, approved);

    res.json({
      success: true,
      message: approved ? '用户审核通过' : '用户审核拒绝',
      user
    });
  } catch (error) {
    req.logger.error(`审核用户失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 更新用户状态 (仅管理员)
router.put('/:id/status', async (req, res) => {
  try {
    const { status, adminId } = req.body;

    if (!status) {
      return res.status(400).json({ error: '用户状态为必填项' });
    }

    const user = await User.updateStatus(req.params.id, status, adminId);

    res.json({
      success: true,
      message: '用户状态更新成功',
      user
    });
  } catch (error) {
    req.logger.error(`更新用户状态失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// 创建管理员用户 (仅超级管理员)
router.post('/admin/create', async (req, res) => {
  try {
    const { username, password, email, displayName } = req.body;

    if (!username || !password) {
      return res.status(400).json({ error: '用户名和密码为必填项' });
    }

    // 检查用户名是否已存在
    const existingUser = await User.getByUsername(username);
    if (existingUser) {
      return res.status(400).json({ error: '用户名已存在' });
    }

    const user = await User.create({
      username,
      password,
      email,
      displayName,
      role: 'admin',
      status: 'active' // 管理员直接激活
    });

    res.status(201).json({
      success: true,
      message: '管理员创建成功',
      user
    });
  } catch (error) {
    req.logger.error(`创建管理员失败: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;
<template>
  <el-row class="course-box" :gutter="cardGutters">
    <el-col
      class="course-box-item"
      v-for="(item, index) in list"
      :key="item.uuid"
      :span="span"
      @click="lookCourseInfo(item)"
    >
      <!--最外层增加阴影动效-->
      <div class="course-item transtop play-icon">
        <!--课程封面-->
        <div class="course-box-item-img-box playicon">
          <mk-image
            :src="item.backgroundSrc"
            style="border-radius: 10px 10px 0px 0px"
          ></mk-image>

          <div
            class="course-box-item-img-box-tag"
            v-if="item.tagText != '' && item.tagIcon"
          >
            <img :src="item.tagIcon" class="tag-icon" v-if="item.tagIcon" />
            <span>{{ item.tagText }}</span>
          </div>
        </div>
        <!-- 课程信息 -->
        <div class="course-box-item-info">
          <!--课程名称-->
          <div class="course-box-item-info-title">{{ item.title || "" }}</div>
          <!--日期-->
          <div
            class="course-box-item-date"
            v-if="item.infoType && item.infoType == 'date'"
          >
            <div class="course-box-item-info-date">•{{ item.date || "" }}</div>
            <div class="course-box-item-info-sub-data">
              {{ item.subInfo || "" }}
            </div>
          </div>
          <!--教师以及学校名称-->
          <template v-else>
            <div class="course-box-item-info-name">
              <div class="teacher-name ellipsis">
                {{ item.teacherName || "" }}
              </div>
              <!--如果为校级平台不展示学校名称-->
              <template v-if="item.schoolName && platformType == 'region'">
                <div class="split-line"></div>
                <div class="school-name ellipsis">{{ item.schoolName }}</div>
              </template>
            </div>
            <div class="course-box-item-info-sub-data" v-if="item.subInfo">
              {{ item.subInfo }}
            </div>
          </template>
        </div>

        <slot name="append" :data="item"></slot>
      </div>
    </el-col>
  </el-row>
</template>
<script lang="ts">
import { computed, defineComponent, PropType, reactive, toRefs } from "vue";
import MkImage from "@/library/ui/mk-image/index.vue";
import { generateUUID } from "@/library/src/utils/globalFunction";
export interface ICourse {
  /** 背景图 */
  backgroundSrc: string;
  /** 标签图标 **/
  tagIcon?: string;
  /** 标签文字 **/
  tagText?: string;
  /** 标题 */
  title: string;
  /** 日期 */
  date?: string;
  /** 教师名称 */
  teacherName: string;
  /** 学校名称 */
  schoolName?: string;
  /** 学校id */
  schoolId?: string;
  /** 其他信息 */
  subInfo?: string;
  /** 信息展示类型 */
  infoType?: string;
  /** id */
  id?: string;
  /** 教师id */
  teaId?: string;
}

export default defineComponent({
  name: "mk-course-card",
  components: {
    MkImage
  },
  emits: ["look-course-info"],
  props: {
    // 间隔
    cardGutters: {
      type: Number,
      default: 28
    },
    // 数据列表
    dataList: {
      type: [] as PropType<ICourse[]>,
      required: true
    },
    // 区域类型
    platformType: {
      type: String,
      default: "region"
    },
    // 栅格
    span: {
      type: Number,
      default: 6
    }
  },

  setup(props, context) {
    const state = reactive({
      list: computed(() => {
        return props.dataList.map((item: any) => {
          item.uuid = generateUUID();
          return item;
        });
      })
    });

    /**
     * 查看课程信息
     * @param item
     */
    function lookCourseInfo(item: ICourse) {
      context.emit("look-course-info", item);
    }

    return {
      ...toRefs(state),
      lookCourseInfo
    };
  }
});
</script>

<style lang="scss" scoped>
@import "../../css/animation";
.course-box {
  .course-box-item {
    margin-bottom: 15px;
    cursor: pointer;

    &.el-col-5 {
      max-width: 20%;
      flex: 0 0 20%;
    }
    .course-item:hover .course-box-item-img-box-tag {
      right: 0px !important;
    }
    .course-box-item-img-box {
      width: 100%;
      height: 146px;
      border-radius: 10px;
      img {
        width: 100%;
        height: 100%;
        border-radius: 10px 10px 0px 0px;
      }
      .course-box-item-img-box-tag {
        width: 80px;
        height: 24px;
        position: absolute;
        right: 18px;
        top: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        line-height: 24px;
        background: rgba(0, 0, 0, 0.4);
        border-radius: 12px 0 0 12px;
        font-size: 12px;
        .tag-icon {
          width: 12px;
          height: 12px;
          margin-right: 6px;
        }
      }
    }
    .course-box-item-info {
      width: 100%;
      padding: 15px;
      background: #fff;
      border-radius: 0 0 10px 10px;
      .course-box-item-info-title {
        font-size: 16px;
        color: #333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .course-box-item-date {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
      }

      .course-box-item-info-date {
        margin-top: 15px;
        background: #f5f5f5;
        border-radius: 10px;
        padding: 5px 8px;
        display: inline-block;
      }
      .course-box-item-info-name {
        margin-top: 15px;
        display: flex;
        align-items: center;
        .teacher-name {
          font-size: 14px;
          color: #bbb;
          display: inline-block;
        }
        .split-line {
          margin: 0 12px;
          height: 12px;
          width: 1px;
          background-color: #bbb;
        }
        .school-name {
          font-size: 12px;
          color: #bbb;
          display: inline-block;
        }
      }
      .course-box-item-info-sub-data {
        margin-top: 15px;
        font-size: 12px;
        color: #999;
        display: inline-block;
      }
    }
  }
}
</style>

var rrweb=function(ee){"use strict";var F;(function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"})(F||(F={}));function rr(t){return t.nodeType===t.ELEMENT_NODE}function je(t){var e=t?.host;return Boolean(e?.shadowRoot===t)}function Ve(t){return Object.prototype.toString.call(t)==="[object ShadowRoot]"}function jn(t){return t.includes(" background-clip: text;")&&!t.includes(" -webkit-background-clip: text;")&&(t=t.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;")),t}function ft(t){try{var e=t.rules||t.cssRules;return e?jn(Array.from(e).map(nr).join("")):null}catch{return null}}function nr(t){var e=t.cssText;if(Vn(t))try{e=ft(t.styleSheet)||e}catch{}return e}function Vn(t){return"styleSheet"in t}var gt=function(){function t(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return t.prototype.getId=function(e){var r;if(!e)return-1;var n=(r=this.getMeta(e))===null||r===void 0?void 0:r.id;return n??-1},t.prototype.getNode=function(e){return this.idNodeMap.get(e)||null},t.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},t.prototype.getMeta=function(e){return this.nodeMetaMap.get(e)||null},t.prototype.removeNodeFromMap=function(e){var r=this,n=this.getId(e);this.idNodeMap.delete(n),e.childNodes&&e.childNodes.forEach(function(o){return r.removeNodeFromMap(o)})},t.prototype.has=function(e){return this.idNodeMap.has(e)},t.prototype.hasNode=function(e){return this.nodeMetaMap.has(e)},t.prototype.add=function(e,r){var n=r.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,r)},t.prototype.replace=function(e,r){var n=this.getNode(e);if(n){var o=this.nodeMetaMap.get(n);o&&this.nodeMetaMap.set(r,o)}this.idNodeMap.set(e,r)},t.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},t}();function or(){return new gt}function yt(t){var e=t.maskInputOptions,r=t.tagName,n=t.type,o=t.value,s=t.maskInputFn,i=o||"";return(e[r.toLowerCase()]||e[n])&&(s?i=s(i):i="*".repeat(i.length)),i}var ir="__rrweb_original__";function Bn(t){var e=t.getContext("2d");if(!e)return!0;for(var r=50,n=0;n<t.width;n+=r)for(var o=0;o<t.height;o+=r){var s=e.getImageData,i=ir in s?s[ir]:s,l=new Uint32Array(i.call(e,n,o,Math.min(r,t.width-n),Math.min(r,t.height-o)).data.buffer);if(l.some(function(a){return a!==0}))return!1}return!0}var Gn=1,zn=new RegExp("[^a-z0-9-_:]"),Be=-2;function sr(){return Gn++}function Hn(t){if(t instanceof HTMLFormElement)return"form";var e=t.tagName.toLowerCase().trim();return zn.test(e)?"div":e}function Yn(t){return t.cssRules?Array.from(t.cssRules).map(function(e){return e.cssText||""}).join(""):""}function Xn(t){var e="";return t.indexOf("//")>-1?e=t.split("/").slice(0,3).join("/"):e=t.split("/")[0],e=e.split("?")[0],e}var De,ar,Zn=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,Kn=/^(?!www\.|(?:http|ftp)s?:\/\/|[A-Za-z]:\\|\/\/|#).*/,Jn=/^(data:)([^,]*),(.*)/i;function et(t,e){return(t||"").replace(Zn,function(r,n,o,s,i,l){var a=o||i||l,c=n||s||"";if(!a)return r;if(!Kn.test(a)||Jn.test(a))return"url(".concat(c).concat(a).concat(c,")");if(a[0]==="/")return"url(".concat(c).concat(Xn(e)+a).concat(c,")");var u=e.split("/"),d=a.split("/");u.pop();for(var h=0,p=d;h<p.length;h++){var m=p[h];m!=="."&&(m===".."?u.pop():u.push(m))}return"url(".concat(c).concat(u.join("/")).concat(c,")")})}var Qn=/^[^ \t\n\r\u000c]+/,qn=/^[, \t\n\r\u000c]+/;function eo(t,e){if(e.trim()==="")return e;var r=0;function n(c){var u,d=c.exec(e.substring(r));return d?(u=d[0],r+=u.length,u):""}for(var o=[];n(qn),!(r>=e.length);){var s=n(Qn);if(s.slice(-1)===",")s=tt(t,s.substring(0,s.length-1)),o.push(s);else{var i="";s=tt(t,s);for(var l=!1;;){var a=e.charAt(r);if(a===""){o.push((s+i).trim());break}else if(l)a===")"&&(l=!1);else if(a===","){r+=1,o.push((s+i).trim());break}else a==="("&&(l=!0);i+=a,r+=1}}}return o.join(", ")}function tt(t,e){if(!e||e.trim()==="")return e;var r=t.createElement("a");return r.href=e,r.href}function to(t){return Boolean(t.tagName==="svg"||t.ownerSVGElement)}function vt(){var t=document.createElement("a");return t.href="",t.href}function lr(t,e,r,n){return r==="src"||r==="href"&&n&&!(e==="use"&&n[0]==="#")||r==="xlink:href"&&n&&n[0]!=="#"||r==="background"&&n&&(e==="table"||e==="td"||e==="th")?tt(t,n):r==="srcset"&&n?eo(t,n):r==="style"&&n?et(n,vt()):e==="object"&&r==="data"&&n?tt(t,n):n}function ro(t,e,r){if(typeof e=="string"){if(t.classList.contains(e))return!0}else for(var n=t.classList.length;n--;){var o=t.classList[n];if(e.test(o))return!0}return r?t.matches(r):!1}function rt(t,e,r){if(!t)return!1;if(t.nodeType!==t.ELEMENT_NODE)return r?rt(t.parentNode,e,r):!1;for(var n=t.classList.length;n--;){var o=t.classList[n];if(e.test(o))return!0}return r?rt(t.parentNode,e,r):!1}function cr(t,e,r){var n=t.nodeType===t.ELEMENT_NODE?t:t.parentElement;if(n===null)return!1;if(typeof e=="string"){if(n.classList.contains(e)||n.closest(".".concat(e)))return!0}else if(rt(n,e,!0))return!0;return!!(r&&(n.matches(r)||n.closest(r)))}function no(t,e,r){var n=t.contentWindow;if(n){var o=!1,s;try{s=n.document.readyState}catch{return}if(s!=="complete"){var i=setTimeout(function(){o||(e(),o=!0)},r);t.addEventListener("load",function(){clearTimeout(i),o=!0,e()});return}var l="about:blank";if(n.location.href!==l||t.src===l||t.src==="")return setTimeout(e,0),t.addEventListener("load",e);t.addEventListener("load",e)}}function oo(t,e,r){var n=!1,o;try{o=t.sheet}catch{return}if(!o){var s=setTimeout(function(){n||(e(),n=!0)},r);t.addEventListener("load",function(){clearTimeout(s),n=!0,e()})}}function io(t,e){var r=e.doc,n=e.mirror,o=e.blockClass,s=e.blockSelector,i=e.maskTextClass,l=e.maskTextSelector,a=e.inlineStylesheet,c=e.maskInputOptions,u=c===void 0?{}:c,d=e.maskTextFn,h=e.maskInputFn,p=e.dataURLOptions,m=p===void 0?{}:p,y=e.inlineImages,v=e.recordCanvas,f=e.keepIframeSrcFn,g=e.newlyAddedElement,b=g===void 0?!1:g,N=so(r,n);switch(t.nodeType){case t.DOCUMENT_NODE:return t.compatMode!=="CSS1Compat"?{type:F.Document,childNodes:[],compatMode:t.compatMode}:{type:F.Document,childNodes:[]};case t.DOCUMENT_TYPE_NODE:return{type:F.DocumentType,name:t.name,publicId:t.publicId,systemId:t.systemId,rootId:N};case t.ELEMENT_NODE:return lo(t,{doc:r,blockClass:o,blockSelector:s,inlineStylesheet:a,maskInputOptions:u,maskInputFn:h,dataURLOptions:m,inlineImages:y,recordCanvas:v,keepIframeSrcFn:f,newlyAddedElement:b,rootId:N});case t.TEXT_NODE:return ao(t,{maskTextClass:i,maskTextSelector:l,maskTextFn:d,rootId:N});case t.CDATA_SECTION_NODE:return{type:F.CDATA,textContent:"",rootId:N};case t.COMMENT_NODE:return{type:F.Comment,textContent:t.textContent||"",rootId:N};default:return!1}}function so(t,e){if(e.hasNode(t)){var r=e.getId(t);return r===1?void 0:r}}function ao(t,e){var r,n=e.maskTextClass,o=e.maskTextSelector,s=e.maskTextFn,i=e.rootId,l=t.parentNode&&t.parentNode.tagName,a=t.textContent,c=l==="STYLE"?!0:void 0,u=l==="SCRIPT"?!0:void 0;if(c&&a){try{t.nextSibling||t.previousSibling||!((r=t.parentNode.sheet)===null||r===void 0)&&r.cssRules&&(a=Yn(t.parentNode.sheet))}catch(d){console.warn("Cannot get CSS styles from text's parentNode. Error: ".concat(d),t)}a=et(a,vt())}return u&&(a="SCRIPT_PLACEHOLDER"),!c&&!u&&a&&cr(t,n,o)&&(a=s?s(a):a.replace(/[\S]/g,"*")),{type:F.Text,textContent:a||"",isStyle:c,rootId:i}}function lo(t,e){for(var r=e.doc,n=e.blockClass,o=e.blockSelector,s=e.inlineStylesheet,i=e.maskInputOptions,l=i===void 0?{}:i,a=e.maskInputFn,c=e.dataURLOptions,u=c===void 0?{}:c,d=e.inlineImages,h=e.recordCanvas,p=e.keepIframeSrcFn,m=e.newlyAddedElement,y=m===void 0?!1:m,v=e.rootId,f=ro(t,n,o),g=Hn(t),b={},N=t.attributes.length,R=0;R<N;R++){var k=t.attributes[R];b[k.name]=lr(r,g,k.name,k.value)}if(g==="link"&&s){var x=Array.from(r.styleSheets).find(function(S){return S.href===t.href}),E=null;x&&(E=ft(x)),E&&(delete b.rel,delete b.href,b._cssText=et(E,x.href))}if(g==="style"&&t.sheet&&!(t.innerText||t.textContent||"").trim().length){var E=ft(t.sheet);E&&(b._cssText=et(E,vt()))}if(g==="input"||g==="textarea"||g==="select"){var H=t.value,W=t.checked;b.type!=="radio"&&b.type!=="checkbox"&&b.type!=="submit"&&b.type!=="button"&&H?b.value=yt({type:b.type,tagName:g,value:H,maskInputOptions:l,maskInputFn:a}):W&&(b.checked=W)}if(g==="option"&&(t.selected&&!l.select?b.selected=!0:delete b.selected),g==="canvas"&&h){if(t.__context==="2d")Bn(t)||(b.rr_dataURL=t.toDataURL(u.type,u.quality));else if(!("__context"in t)){var Z=t.toDataURL(u.type,u.quality),P=document.createElement("canvas");P.width=t.width,P.height=t.height;var B=P.toDataURL(u.type,u.quality);Z!==B&&(b.rr_dataURL=Z)}}if(g==="img"&&d){De||(De=r.createElement("canvas"),ar=De.getContext("2d"));var L=t,U=L.crossOrigin;L.crossOrigin="anonymous";var A=function(){try{De.width=L.naturalWidth,De.height=L.naturalHeight,ar.drawImage(L,0,0),b.rr_dataURL=De.toDataURL(u.type,u.quality)}catch(S){console.warn("Cannot inline img src=".concat(L.currentSrc,"! Error: ").concat(S))}U?b.crossOrigin=U:L.removeAttribute("crossorigin")};L.complete&&L.naturalWidth!==0?A():L.onload=A}if((g==="audio"||g==="video")&&(b.rr_mediaState=t.paused?"paused":"played",b.rr_mediaCurrentTime=t.currentTime),y||(t.scrollLeft&&(b.rr_scrollLeft=t.scrollLeft),t.scrollTop&&(b.rr_scrollTop=t.scrollTop)),f){var D=t.getBoundingClientRect(),z=D.width,w=D.height;b={class:b.class,rr_width:"".concat(z,"px"),rr_height:"".concat(w,"px")}}return g==="iframe"&&!p(b.src)&&(t.contentDocument||(b.rr_src=b.src),delete b.src),{type:F.Element,tagName:g,attributes:b,childNodes:[],isSVG:to(t)||void 0,needBlock:f,rootId:v}}function Y(t){return t===void 0?"":t.toLowerCase()}function co(t,e){return!!(e.comment&&t.type===F.Comment||t.type===F.Element&&(e.script&&(t.tagName==="script"||t.tagName==="link"&&t.attributes.rel==="preload"&&t.attributes.as==="script"||t.tagName==="link"&&t.attributes.rel==="prefetch"&&typeof t.attributes.href=="string"&&t.attributes.href.endsWith(".js"))||e.headFavicon&&(t.tagName==="link"&&t.attributes.rel==="shortcut icon"||t.tagName==="meta"&&(Y(t.attributes.name).match(/^msapplication-tile(image|color)$/)||Y(t.attributes.name)==="application-name"||Y(t.attributes.rel)==="icon"||Y(t.attributes.rel)==="apple-touch-icon"||Y(t.attributes.rel)==="shortcut icon"))||t.tagName==="meta"&&(e.headMetaDescKeywords&&Y(t.attributes.name).match(/^description|keywords$/)||e.headMetaSocial&&(Y(t.attributes.property).match(/^(og|twitter|fb):/)||Y(t.attributes.name).match(/^(og|twitter):/)||Y(t.attributes.name)==="pinterest")||e.headMetaRobots&&(Y(t.attributes.name)==="robots"||Y(t.attributes.name)==="googlebot"||Y(t.attributes.name)==="bingbot")||e.headMetaHttpEquiv&&t.attributes["http-equiv"]!==void 0||e.headMetaAuthorship&&(Y(t.attributes.name)==="author"||Y(t.attributes.name)==="generator"||Y(t.attributes.name)==="framework"||Y(t.attributes.name)==="publisher"||Y(t.attributes.name)==="progid"||Y(t.attributes.property).match(/^article:/)||Y(t.attributes.property).match(/^product:/))||e.headMetaVerification&&(Y(t.attributes.name)==="google-site-verification"||Y(t.attributes.name)==="yandex-verification"||Y(t.attributes.name)==="csrf-token"||Y(t.attributes.name)==="p:domain_verify"||Y(t.attributes.name)==="verify-v1"||Y(t.attributes.name)==="verification"||Y(t.attributes.name)==="shopify-checkout-api-token"))))}function Oe(t,e){var r=e.doc,n=e.mirror,o=e.blockClass,s=e.blockSelector,i=e.maskTextClass,l=e.maskTextSelector,a=e.skipChild,c=a===void 0?!1:a,u=e.inlineStylesheet,d=u===void 0?!0:u,h=e.maskInputOptions,p=h===void 0?{}:h,m=e.maskTextFn,y=e.maskInputFn,v=e.slimDOMOptions,f=e.dataURLOptions,g=f===void 0?{}:f,b=e.inlineImages,N=b===void 0?!1:b,R=e.recordCanvas,k=R===void 0?!1:R,x=e.onSerialize,E=e.onIframeLoad,H=e.iframeLoadTimeout,W=H===void 0?5e3:H,Z=e.onStylesheetLoad,P=e.stylesheetLoadTimeout,B=P===void 0?5e3:P,L=e.keepIframeSrcFn,U=L===void 0?function(){return!1}:L,A=e.newlyAddedElement,D=A===void 0?!1:A,z=e.preserveWhiteSpace,w=z===void 0?!0:z,S=io(t,{doc:r,mirror:n,blockClass:o,blockSelector:s,maskTextClass:i,maskTextSelector:l,inlineStylesheet:d,maskInputOptions:p,maskTextFn:m,maskInputFn:y,dataURLOptions:g,inlineImages:N,recordCanvas:k,keepIframeSrcFn:U,newlyAddedElement:D});if(!S)return console.warn(t,"not serialized"),null;var M;n.hasNode(t)?M=n.getId(t):co(S,v)||!w&&S.type===F.Text&&!S.isStyle&&!S.textContent.replace(/^\s+|\s+$/gm,"").length?M=Be:M=sr();var I=Object.assign(S,{id:M});if(n.add(t,I),M===Be)return null;x&&x(t);var $=!c;if(I.type===F.Element){$=$&&!I.needBlock,delete I.needBlock;var G=t.shadowRoot;G&&Ve(G)&&(I.isShadowHost=!0)}if((I.type===F.Document||I.type===F.Element)&&$){v.headWhitespace&&I.type===F.Element&&I.tagName==="head"&&(w=!1);for(var K={doc:r,mirror:n,blockClass:o,blockSelector:s,maskTextClass:i,maskTextSelector:l,skipChild:c,inlineStylesheet:d,maskInputOptions:p,maskTextFn:m,maskInputFn:y,slimDOMOptions:v,dataURLOptions:g,inlineImages:N,recordCanvas:k,preserveWhiteSpace:w,onSerialize:x,onIframeLoad:E,iframeLoadTimeout:W,onStylesheetLoad:Z,stylesheetLoadTimeout:B,keepIframeSrcFn:U},O=0,te=Array.from(t.childNodes);O<te.length;O++){var ne=te[O],J=Oe(ne,K);J&&I.childNodes.push(J)}if(rr(t)&&t.shadowRoot)for(var de=0,j=Array.from(t.shadowRoot.childNodes);de<j.length;de++){var ne=j[de],J=Oe(ne,K);J&&(Ve(t.shadowRoot)&&(J.isShadow=!0),I.childNodes.push(J))}}return t.parentNode&&je(t.parentNode)&&Ve(t.parentNode)&&(I.isShadow=!0),I.type===F.Element&&I.tagName==="iframe"&&no(t,function(){var ce=t.contentDocument;if(ce&&E){var qe=Oe(ce,{doc:ce,mirror:n,blockClass:o,blockSelector:s,maskTextClass:i,maskTextSelector:l,skipChild:!1,inlineStylesheet:d,maskInputOptions:p,maskTextFn:m,maskInputFn:y,slimDOMOptions:v,dataURLOptions:g,inlineImages:N,recordCanvas:k,preserveWhiteSpace:w,onSerialize:x,onIframeLoad:E,iframeLoadTimeout:W,onStylesheetLoad:Z,stylesheetLoadTimeout:B,keepIframeSrcFn:U});qe&&E(t,qe)}},W),I.type===F.Element&&I.tagName==="link"&&I.attributes.rel==="stylesheet"&&oo(t,function(){if(Z){var ce=Oe(t,{doc:r,mirror:n,blockClass:o,blockSelector:s,maskTextClass:i,maskTextSelector:l,skipChild:!1,inlineStylesheet:d,maskInputOptions:p,maskTextFn:m,maskInputFn:y,slimDOMOptions:v,dataURLOptions:g,inlineImages:N,recordCanvas:k,preserveWhiteSpace:w,onSerialize:x,onIframeLoad:E,iframeLoadTimeout:W,onStylesheetLoad:Z,stylesheetLoadTimeout:B,keepIframeSrcFn:U});ce&&Z(t,ce)}},B),I}function uo(t,e){var r=e||{},n=r.mirror,o=n===void 0?new gt:n,s=r.blockClass,i=s===void 0?"rr-block":s,l=r.blockSelector,a=l===void 0?null:l,c=r.maskTextClass,u=c===void 0?"rr-mask":c,d=r.maskTextSelector,h=d===void 0?null:d,p=r.inlineStylesheet,m=p===void 0?!0:p,y=r.inlineImages,v=y===void 0?!1:y,f=r.recordCanvas,g=f===void 0?!1:f,b=r.maskAllInputs,N=b===void 0?!1:b,R=r.maskTextFn,k=r.maskInputFn,x=r.slimDOM,E=x===void 0?!1:x,H=r.dataURLOptions,W=r.preserveWhiteSpace,Z=r.onSerialize,P=r.onIframeLoad,B=r.iframeLoadTimeout,L=r.onStylesheetLoad,U=r.stylesheetLoadTimeout,A=r.keepIframeSrcFn,D=A===void 0?function(){return!1}:A,z=N===!0?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:N===!1?{password:!0}:N,w=E===!0||E==="all"?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:E==="all",headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:E===!1?{}:E;return Oe(t,{doc:t,mirror:o,blockClass:i,blockSelector:a,maskTextClass:u,maskTextSelector:h,skipChild:!1,inlineStylesheet:m,maskInputOptions:z,maskTextFn:R,maskInputFn:k,slimDOMOptions:w,dataURLOptions:H,inlineImages:v,recordCanvas:g,preserveWhiteSpace:W,onSerialize:Z,onIframeLoad:P,iframeLoadTimeout:B,onStylesheetLoad:L,stylesheetLoadTimeout:U,keepIframeSrcFn:D,newlyAddedElement:!1})}var ur=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g;function ho(t,e){e===void 0&&(e={});var r=1,n=1;function o(w){var S=w.match(/\n/g);S&&(r+=S.length);var M=w.lastIndexOf(`
`);n=M===-1?n+w.length:w.length-M}function s(){var w={line:r,column:n};return function(S){return S.position=new i(w),m(),S}}var i=function(){function w(S){this.start=S,this.end={line:r,column:n},this.source=e.source}return w}();i.prototype.content=t;var l=[];function a(w){var S=new Error("".concat(e.source||"",":").concat(r,":").concat(n,": ").concat(w));if(S.reason=w,S.filename=e.source,S.line=r,S.column=n,S.source=t,e.silent)l.push(S);else throw S}function c(){var w=h();return{type:"stylesheet",stylesheet:{source:e.source,rules:w,parsingErrors:l}}}function u(){return p(/^{\s*/)}function d(){return p(/^}/)}function h(){var w,S=[];for(m(),y(S);t.length&&t.charAt(0)!=="}"&&(w=D()||z());)w!==!1&&(S.push(w),y(S));return S}function p(w){var S=w.exec(t);if(S){var M=S[0];return o(M),t=t.slice(M.length),S}}function m(){p(/^\s*/)}function y(w){w===void 0&&(w=[]);for(var S;S=v();)S!==!1&&w.push(S),S=v();return w}function v(){var w=s();if(!(t.charAt(0)!=="/"||t.charAt(1)!=="*")){for(var S=2;t.charAt(S)!==""&&(t.charAt(S)!=="*"||t.charAt(S+1)!=="/");)++S;if(S+=2,t.charAt(S-1)==="")return a("End of comment missing");var M=t.slice(2,S-2);return n+=2,o(M),t=t.slice(S),n+=2,w({type:"comment",comment:M})}}function f(){var w=p(/^([^{]+)/);if(w)return ve(w[0]).replace(/\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*\/+/g,"").replace(/"(?:\\"|[^"])*"|'(?:\\'|[^'])*'/g,function(S){return S.replace(/,/g,"\u200C")}).split(/\s*(?![^(]*\)),\s*/).map(function(S){return S.replace(/\u200C/g,",")})}function g(){var w=s(),S=p(/^(\*?[-#\/\*\\\w]+(\[[0-9a-z_-]+\])?)\s*/);if(S){var M=ve(S[0]);if(!p(/^:\s*/))return a("property missing ':'");var I=p(/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^\)]*?\)|[^};])+)/),$=w({type:"declaration",property:M.replace(ur,""),value:I?ve(I[0]).replace(ur,""):""});return p(/^[;\s]*/),$}}function b(){var w=[];if(!u())return a("missing '{'");y(w);for(var S;S=g();)S!==!1&&(w.push(S),y(w)),S=g();return d()?w:a("missing '}'")}function N(){for(var w,S=[],M=s();w=p(/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/);)S.push(w[1]),p(/^,\s*/);if(S.length)return M({type:"keyframe",values:S,declarations:b()})}function R(){var w=s(),S=p(/^@([-\w]+)?keyframes\s*/);if(S){var M=S[1];if(S=p(/^([-\w]+)\s*/),!S)return a("@keyframes missing name");var I=S[1];if(!u())return a("@keyframes missing '{'");for(var $,G=y();$=N();)G.push($),G=G.concat(y());return d()?w({type:"keyframes",name:I,vendor:M,keyframes:G}):a("@keyframes missing '}'")}}function k(){var w=s(),S=p(/^@supports *([^{]+)/);if(S){var M=ve(S[1]);if(!u())return a("@supports missing '{'");var I=y().concat(h());return d()?w({type:"supports",supports:M,rules:I}):a("@supports missing '}'")}}function x(){var w=s(),S=p(/^@host\s*/);if(S){if(!u())return a("@host missing '{'");var M=y().concat(h());return d()?w({type:"host",rules:M}):a("@host missing '}'")}}function E(){var w=s(),S=p(/^@media *([^{]+)/);if(S){var M=ve(S[1]);if(!u())return a("@media missing '{'");var I=y().concat(h());return d()?w({type:"media",media:M,rules:I}):a("@media missing '}'")}}function H(){var w=s(),S=p(/^@custom-media\s+(--[^\s]+)\s*([^{;]+);/);if(S)return w({type:"custom-media",name:ve(S[1]),media:ve(S[2])})}function W(){var w=s(),S=p(/^@page */);if(S){var M=f()||[];if(!u())return a("@page missing '{'");for(var I=y(),$;$=g();)I.push($),I=I.concat(y());return d()?w({type:"page",selectors:M,declarations:I}):a("@page missing '}'")}}function Z(){var w=s(),S=p(/^@([-\w]+)?document *([^{]+)/);if(S){var M=ve(S[1]),I=ve(S[2]);if(!u())return a("@document missing '{'");var $=y().concat(h());return d()?w({type:"document",document:I,vendor:M,rules:$}):a("@document missing '}'")}}function P(){var w=s(),S=p(/^@font-face\s*/);if(S){if(!u())return a("@font-face missing '{'");for(var M=y(),I;I=g();)M.push(I),M=M.concat(y());return d()?w({type:"font-face",declarations:M}):a("@font-face missing '}'")}}var B=A("import"),L=A("charset"),U=A("namespace");function A(w){var S=new RegExp("^@"+w+"\\s*([^;]+);");return function(){var M=s(),I=p(S);if(I){var $={type:w};return $[w]=I[1].trim(),M($)}}}function D(){if(t[0]==="@")return R()||E()||H()||k()||B()||L()||U()||Z()||W()||x()||P()}function z(){var w=s(),S=f();return S?(y(),w({type:"rule",selectors:S,declarations:b()})):a("selector missing")}return bt(c())}function ve(t){return t?t.replace(/^\s+|\s+$/g,""):""}function bt(t,e){for(var r=t&&typeof t.type=="string",n=r?t:e,o=0,s=Object.keys(t);o<s.length;o++){var i=s[o],l=t[i];Array.isArray(l)?l.forEach(function(a){bt(a,n)}):l&&typeof l=="object"&&bt(l,n)}return r&&Object.defineProperty(t,"parent",{configurable:!0,writable:!0,enumerable:!1,value:e||null}),t}var dr={script:"noscript",altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"};function po(t){var e=dr[t.tagName]?dr[t.tagName]:t.tagName;return e==="link"&&t.attributes._cssText&&(e="style"),e}function mo(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}var hr=/([^\\]):hover/,fo=new RegExp(hr.source,"g");function pr(t,e){var r=e?.stylesWithHoverClass.get(t);if(r)return r;var n=ho(t,{silent:!0});if(!n.stylesheet)return t;var o=[];if(n.stylesheet.rules.forEach(function(l){"selectors"in l&&(l.selectors||[]).forEach(function(a){hr.test(a)&&o.push(a)})}),o.length===0)return t;var s=new RegExp(o.filter(function(l,a){return o.indexOf(l)===a}).sort(function(l,a){return a.length-l.length}).map(function(l){return mo(l)}).join("|"),"g"),i=t.replace(s,function(l){var a=l.replace(fo,"$1.\\:hover");return"".concat(l,", ").concat(a)});return e?.stylesWithHoverClass.set(t,i),i}function mr(){var t=new Map;return{stylesWithHoverClass:t}}function go(t,e){var r=e.doc,n=e.hackCss,o=e.cache;switch(t.type){case F.Document:return r.implementation.createDocument(null,"",null);case F.DocumentType:return r.implementation.createDocumentType(t.name||"html",t.publicId,t.systemId);case F.Element:{var s=po(t),i;t.isSVG?i=r.createElementNS("http://www.w3.org/2000/svg",s):i=r.createElement(s);var l={};for(var a in t.attributes)if(Object.prototype.hasOwnProperty.call(t.attributes,a)){var c=t.attributes[a];if(!(s==="option"&&a==="selected"&&c===!1)){if(c===!0&&(c=""),a.startsWith("rr_")){l[a]=c;continue}var u=s==="textarea"&&a==="value",d=s==="style"&&a==="_cssText";if(d&&n&&typeof c=="string"&&(c=pr(c,o)),(u||d)&&typeof c=="string"){for(var h=r.createTextNode(c),p=0,m=Array.from(i.childNodes);p<m.length;p++){var y=m[p];y.nodeType===i.TEXT_NODE&&i.removeChild(y)}i.appendChild(h);continue}try{if(t.isSVG&&a==="xlink:href")i.setAttributeNS("http://www.w3.org/1999/xlink",a,c.toString());else if(a==="onload"||a==="onclick"||a.substring(0,7)==="onmouse")i.setAttribute("_"+a,c.toString());else if(s==="meta"&&t.attributes["http-equiv"]==="Content-Security-Policy"&&a==="content"){i.setAttribute("csp-content",c.toString());continue}else s==="link"&&t.attributes.rel==="preload"&&t.attributes.as==="script"||s==="link"&&t.attributes.rel==="prefetch"&&typeof t.attributes.href=="string"&&t.attributes.href.endsWith(".js")||(s==="img"&&t.attributes.srcset&&t.attributes.rr_dataURL?i.setAttribute("rrweb-original-srcset",t.attributes.srcset):i.setAttribute(a,c.toString()))}catch{}}}var v=function(g){var b=l[g];if(s==="canvas"&&g==="rr_dataURL"){var N=document.createElement("img");N.onload=function(){var k=i.getContext("2d");k&&k.drawImage(N,0,0,N.width,N.height)},N.src=b.toString(),i.RRNodeType&&(i.rr_dataURL=b.toString())}else if(s==="img"&&g==="rr_dataURL"){var R=i;R.currentSrc.startsWith("data:")||(R.setAttribute("rrweb-original-src",t.attributes.src),R.src=b.toString())}if(g==="rr_width")i.style.width=b.toString();else if(g==="rr_height")i.style.height=b.toString();else if(g==="rr_mediaCurrentTime"&&typeof b=="number")i.currentTime=b;else if(g==="rr_mediaState")switch(b){case"played":i.play().catch(function(k){return console.warn("media playback error",k)});break;case"paused":i.pause();break}};for(var f in l)v(f);if(t.isShadowHost)if(!i.shadowRoot)i.attachShadow({mode:"open"});else for(;i.shadowRoot.firstChild;)i.shadowRoot.removeChild(i.shadowRoot.firstChild);return i}case F.Text:return r.createTextNode(t.isStyle&&n?pr(t.textContent,o):t.textContent);case F.CDATA:return r.createCDATASection(t.textContent);case F.Comment:return r.createComment(t.textContent);default:return null}}function Ge(t,e){var r=e.doc,n=e.mirror,o=e.skipChild,s=o===void 0?!1:o,i=e.hackCss,l=i===void 0?!0:i,a=e.afterAppend,c=e.cache,u=go(t,{doc:r,hackCss:l,cache:c});if(!u)return null;if(t.rootId&&n.getNode(t.rootId)!==r&&n.replace(t.rootId,r),t.type===F.Document&&(r.close(),r.open(),t.compatMode==="BackCompat"&&t.childNodes&&t.childNodes[0].type!==F.DocumentType&&(t.childNodes[0].type===F.Element&&"xmlns"in t.childNodes[0].attributes&&t.childNodes[0].attributes.xmlns==="http://www.w3.org/1999/xhtml"?r.write('<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">'):r.write('<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">')),u=r),n.add(u,t),(t.type===F.Document||t.type===F.Element)&&!s)for(var d=0,h=t.childNodes;d<h.length;d++){var p=h[d],m=Ge(p,{doc:r,mirror:n,skipChild:!1,hackCss:l,afterAppend:a,cache:c});if(!m){console.warn("Failed to rebuild",p);continue}p.isShadow&&rr(u)&&u.shadowRoot?u.shadowRoot.appendChild(m):u.appendChild(m),a&&a(m,p.id)}return u}function yo(t,e){function r(i){e(i)}for(var n=0,o=t.getIds();n<o.length;n++){var s=o[n];t.has(s)&&r(t.getNode(s))}}function vo(t,e){var r=e.getMeta(t);if(r?.type===F.Element){var n=t;for(var o in r.attributes)if(Object.prototype.hasOwnProperty.call(r.attributes,o)&&o.startsWith("rr_")){var s=r.attributes[o];o==="rr_scrollLeft"&&(n.scrollLeft=s),o==="rr_scrollTop"&&(n.scrollTop=s)}}}function bo(t,e){var r=e.doc,n=e.onVisit,o=e.hackCss,s=o===void 0?!0:o,i=e.afterAppend,l=e.cache,a=e.mirror,c=a===void 0?new gt:a,u=Ge(t,{doc:r,mirror:c,skipChild:!1,hackCss:s,afterAppend:i,cache:l});return yo(c,function(d){n&&n(d),vo(d,c)}),u}function ae(t,e,r=document){const n={capture:!0,passive:!0};return r.addEventListener(t,e,n),()=>r.removeEventListener(t,e,n)}const xe=`Please stop import mirror directly. Instead of that,\r
now you can use replayer.getMirror() to access the mirror instance of a replayer,\r
or you can use record.mirror to access the mirror instance during recording.`;ee.mirror={map:{},getId(){return console.error(xe),-1},getNode(){return console.error(xe),null},removeNodeFromMap(){console.error(xe)},has(){return console.error(xe),!1},reset(){console.error(xe)}},typeof window<"u"&&window.Proxy&&window.Reflect&&(ee.mirror=new Proxy(ee.mirror,{get(t,e,r){return e==="map"&&console.error(xe),Reflect.get(t,e,r)}}));function Le(t,e,r={}){let n=null,o=0;return function(...s){const i=Date.now();!o&&r.leading===!1&&(o=i);const l=e-(i-o),a=this;l<=0||l>e?(n&&(clearTimeout(n),n=null),o=i,t.apply(a,s)):!n&&r.trailing!==!1&&(n=setTimeout(()=>{o=r.leading===!1?0:Date.now(),n=null,t.apply(a,s)},l))}}function ze(t,e,r,n,o=window){const s=o.Object.getOwnPropertyDescriptor(t,e);return o.Object.defineProperty(t,e,n?r:{set(i){setTimeout(()=>{r.set.call(this,i)},0),s&&s.set&&s.set.call(this,i)}}),()=>ze(t,e,s||{},!0)}function Ne(t,e,r){try{if(!(e in t))return()=>{};const n=t[e],o=r(n);return typeof o=="function"&&(o.prototype=o.prototype||{},Object.defineProperties(o,{__rrweb_original__:{enumerable:!1,value:n}})),t[e]=o,()=>{t[e]=n}}catch{return()=>{}}}function St(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function wt(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function le(t,e,r,n){if(!t)return!1;const o=t.nodeType===t.ELEMENT_NODE?t:t.parentElement;if(!o)return!1;if(typeof e=="string"){if(o.classList.contains(e)||n&&o.closest("."+e)!==null)return!0}else if(rt(o,e,n))return!0;return!!(r&&(t.matches(r)||n&&o.closest(r)!==null))}function fr(t,e){return e.getId(t)!==-1}function nt(t,e){return e.getId(t)===Be}function Nt(t,e){if(je(t))return!1;const r=e.getId(t);return e.has(r)?t.parentNode&&t.parentNode.nodeType===t.DOCUMENT_NODE?!1:t.parentNode?Nt(t.parentNode,e):!0:!0}function Et(t){return Boolean(t.changedTouches)}function Ct(t=window){"NodeList"in t&&!t.NodeList.prototype.forEach&&(t.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in t&&!t.DOMTokenList.prototype.forEach&&(t.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=(...e)=>{let r=e[0];if(!(0 in e))throw new TypeError("1 argument is required");do if(this===r)return!0;while(r=r&&r.parentNode);return!1})}function gr(t){const e={},r=(o,s)=>{const i={value:o,parent:s,children:[]};return e[o.node.id]=i,i},n=[];for(const o of t){const{nextId:s,parentId:i}=o;if(s&&s in e){const l=e[s];if(l.parent){const a=l.parent.children.indexOf(l);l.parent.children.splice(a,0,r(o,l.parent))}else{const a=n.indexOf(l);n.splice(a,0,r(o,null))}continue}if(i in e){const l=e[i];l.children.push(r(o,l));continue}n.push(r(o,null))}return n}function Tt(t,e){e(t.value);for(let r=t.children.length-1;r>=0;r--)Tt(t.children[r],e)}function Ae(t,e){return Boolean(t.nodeName==="IFRAME"&&e.getMeta(t))}function Mt(t,e){return Boolean(t.nodeName==="LINK"&&t.nodeType===t.ELEMENT_NODE&&t.getAttribute&&t.getAttribute("rel")==="stylesheet"&&e.getMeta(t))}function It(t,e){var r,n;const o=(n=(r=t.ownerDocument)==null?void 0:r.defaultView)==null?void 0:n.frameElement;if(!o||o===e)return{x:0,y:0,relativeScale:1,absoluteScale:1};const s=o.getBoundingClientRect(),i=It(o,e),l=s.height/o.clientHeight;return{x:s.x*i.relativeScale+i.x,y:s.y*i.relativeScale+i.y,relativeScale:l,absoluteScale:i.absoluteScale*l}}function Ie(t){return Boolean(t?.shadowRoot)}function Fe(t,e){const r=t[e[0]];return e.length===1?r:Fe(r.cssRules[e[1]].cssRules,e.slice(2))}function kt(t){const e=[...t],r=e.pop();return{positions:e,index:r}}function yr(t){const e=new Set,r=[];for(let n=t.length;n--;){const o=t[n];e.has(o.id)||(r.push(o),e.add(o.id))}return r}class Rt{constructor(){this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}getId(e){var r;return(r=this.styleIDMap.get(e))!=null?r:-1}has(e){return this.styleIDMap.has(e)}add(e,r){if(this.has(e))return this.getId(e);let n;return r===void 0?n=this.id++:n=r,this.styleIDMap.set(e,n),this.idStyleMap.set(n,e),n}getStyle(e){return this.idStyleMap.get(e)||null}reset(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}generateId(){return this.id++}}var So=Object.freeze({__proto__:null,on:ae,get _mirror(){return ee.mirror},throttle:Le,hookSetter:ze,patch:Ne,getWindowHeight:St,getWindowWidth:wt,isBlocked:le,isSerialized:fr,isIgnored:nt,isAncestorRemoved:Nt,isTouchEvent:Et,polyfill:Ct,queueToResolveTrees:gr,iterateResolveTree:Tt,isSerializedIframe:Ae,isSerializedStylesheet:Mt,getBaseDimension:It,hasShadowRoot:Ie,getNestedRule:Fe,getPositionsAndIndex:kt,uniqueTextMutations:yr,StyleSheetMirror:Rt}),T=(t=>(t[t.DomContentLoaded=0]="DomContentLoaded",t[t.Load=1]="Load",t[t.FullSnapshot=2]="FullSnapshot",t[t.IncrementalSnapshot=3]="IncrementalSnapshot",t[t.Meta=4]="Meta",t[t.Custom=5]="Custom",t[t.Plugin=6]="Plugin",t))(T||{}),C=(t=>(t[t.Mutation=0]="Mutation",t[t.MouseMove=1]="MouseMove",t[t.MouseInteraction=2]="MouseInteraction",t[t.Scroll=3]="Scroll",t[t.ViewportResize=4]="ViewportResize",t[t.Input=5]="Input",t[t.TouchMove=6]="TouchMove",t[t.MediaInteraction=7]="MediaInteraction",t[t.StyleSheetRule=8]="StyleSheetRule",t[t.CanvasMutation=9]="CanvasMutation",t[t.Font=10]="Font",t[t.Log=11]="Log",t[t.Drag=12]="Drag",t[t.StyleDeclaration=13]="StyleDeclaration",t[t.Selection=14]="Selection",t[t.AdoptedStyleSheet=15]="AdoptedStyleSheet",t))(C||{}),ie=(t=>(t[t.MouseUp=0]="MouseUp",t[t.MouseDown=1]="MouseDown",t[t.Click=2]="Click",t[t.ContextMenu=3]="ContextMenu",t[t.DblClick=4]="DblClick",t[t.Focus=5]="Focus",t[t.Blur=6]="Blur",t[t.TouchStart=7]="TouchStart",t[t.TouchMove_Departed=8]="TouchMove_Departed",t[t.TouchEnd=9]="TouchEnd",t[t.TouchCancel=10]="TouchCancel",t))(ie||{}),be=(t=>(t[t["2D"]=0]="2D",t[t.WebGL=1]="WebGL",t[t.WebGL2=2]="WebGL2",t))(be||{}),Se=(t=>(t[t.Play=0]="Play",t[t.Pause=1]="Pause",t[t.Seeked=2]="Seeked",t[t.VolumeChange=3]="VolumeChange",t[t.RateChange=4]="RateChange",t))(Se||{}),V=(t=>(t.Start="start",t.Pause="pause",t.Resume="resume",t.Resize="resize",t.Finish="finish",t.FullsnapshotRebuilded="fullsnapshot-rebuilded",t.LoadStylesheetStart="load-stylesheet-start",t.LoadStylesheetEnd="load-stylesheet-end",t.SkipStart="skip-start",t.SkipEnd="skip-end",t.MouseInteraction="mouse-interaction",t.EventCast="event-cast",t.CustomEvent="custom-event",t.Flush="flush",t.StateChange="state-change",t.PlayBack="play-back",t.Destroy="destroy",t))(V||{});function vr(t){return"__ln"in t}class wo{constructor(){this.length=0,this.head=null}get(e){if(e>=this.length)throw new Error("Position outside of list range");let r=this.head;for(let n=0;n<e;n++)r=r?.next||null;return r}addNode(e){const r={value:e,previous:null,next:null};if(e.__ln=r,e.previousSibling&&vr(e.previousSibling)){const n=e.previousSibling.__ln.next;r.next=n,r.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=r,n&&(n.previous=r)}else if(e.nextSibling&&vr(e.nextSibling)&&e.nextSibling.__ln.previous){const n=e.nextSibling.__ln.previous;r.previous=n,r.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=r,n&&(n.next=r)}else this.head&&(this.head.previous=r),r.next=this.head,this.head=r;this.length++}removeNode(e){const r=e.__ln;!this.head||(r.previous?(r.previous.next=r.next,r.next&&(r.next.previous=r.previous)):(this.head=r.next,this.head&&(this.head.previous=null)),e.__ln&&delete e.__ln,this.length--)}}const br=(t,e)=>`${t}@${e}`;class No{constructor(){this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=e=>{e.forEach(this.processMutation),this.emit()},this.emit=()=>{if(this.frozen||this.locked)return;const e=[],r=new wo,n=l=>{let a=l,c=Be;for(;c===Be;)a=a&&a.nextSibling,c=a&&this.mirror.getId(a);return c},o=l=>{var a,c,u,d;let h=null;((c=(a=l.getRootNode)==null?void 0:a.call(l))==null?void 0:c.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&l.getRootNode().host&&(h=l.getRootNode().host);let p=h;for(;((d=(u=p?.getRootNode)==null?void 0:u.call(p))==null?void 0:d.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&p.getRootNode().host;)p=p.getRootNode().host;const m=!this.doc.contains(l)&&(!p||!this.doc.contains(p));if(!l.parentNode||m)return;const y=je(l.parentNode)?this.mirror.getId(h):this.mirror.getId(l.parentNode),v=n(l);if(y===-1||v===-1)return r.addNode(l);const f=Oe(l,{doc:this.doc,mirror:this.mirror,blockClass:this.blockClass,blockSelector:this.blockSelector,maskTextClass:this.maskTextClass,maskTextSelector:this.maskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:this.inlineStylesheet,maskInputOptions:this.maskInputOptions,maskTextFn:this.maskTextFn,maskInputFn:this.maskInputFn,slimDOMOptions:this.slimDOMOptions,dataURLOptions:this.dataURLOptions,recordCanvas:this.recordCanvas,inlineImages:this.inlineImages,onSerialize:g=>{Ae(g,this.mirror)&&this.iframeManager.addIframe(g),Mt(g,this.mirror)&&this.stylesheetManager.trackLinkElement(g),Ie(l)&&this.shadowDomManager.addShadowRoot(l.shadowRoot,this.doc)},onIframeLoad:(g,b)=>{this.iframeManager.attachIframe(g,b),this.shadowDomManager.observeAttachShadow(g)},onStylesheetLoad:(g,b)=>{this.stylesheetManager.attachLinkElement(g,b)}});f&&e.push({parentId:y,nextId:v,node:f})};for(;this.mapRemoves.length;)this.mirror.removeNodeFromMap(this.mapRemoves.shift());for(const l of Array.from(this.movedSet.values()))Sr(this.removes,l,this.mirror)&&!this.movedSet.has(l.parentNode)||o(l);for(const l of Array.from(this.addedSet.values()))!Nr(this.droppedSet,l)&&!Sr(this.removes,l,this.mirror)||Nr(this.movedSet,l)?o(l):this.droppedSet.add(l);let s=null;for(;r.length;){let l=null;if(s){const a=this.mirror.getId(s.value.parentNode),c=n(s.value);a!==-1&&c!==-1&&(l=s)}if(!l)for(let a=r.length-1;a>=0;a--){const c=r.get(a);if(c){const u=this.mirror.getId(c.value.parentNode);if(n(c.value)===-1)continue;if(u!==-1){l=c;break}else{const d=c.value;if(d.parentNode&&d.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){const h=d.parentNode.host;if(this.mirror.getId(h)!==-1){l=c;break}}}}}if(!l){for(;r.head;)r.removeNode(r.head.value);break}s=l.previous,r.removeNode(l.value),o(l.value)}const i={texts:this.texts.map(l=>({id:this.mirror.getId(l.node),value:l.value})).filter(l=>this.mirror.has(l.id)),attributes:this.attributes.map(l=>({id:this.mirror.getId(l.node),attributes:l.attributes})).filter(l=>this.mirror.has(l.id)),removes:this.removes,adds:e};!i.texts.length&&!i.attributes.length&&!i.removes.length&&!i.adds.length||(this.texts=[],this.attributes=[],this.removes=[],this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.movedMap={},this.mutationCb(i))},this.processMutation=e=>{if(!nt(e.target,this.mirror))switch(e.type){case"characterData":{const r=e.target.textContent;!le(e.target,this.blockClass,this.blockSelector,!1)&&r!==e.oldValue&&this.texts.push({value:cr(e.target,this.maskTextClass,this.maskTextSelector)&&r?this.maskTextFn?this.maskTextFn(r):r.replace(/[\S]/g,"*"):r,node:e.target});break}case"attributes":{const r=e.target;let n=e.target.getAttribute(e.attributeName);if(e.attributeName==="value"&&(n=yt({maskInputOptions:this.maskInputOptions,tagName:e.target.tagName,type:e.target.getAttribute("type"),value:n,maskInputFn:this.maskInputFn})),le(e.target,this.blockClass,this.blockSelector,!1)||n===e.oldValue)return;let o=this.attributes.find(s=>s.node===e.target);if(r.tagName==="IFRAME"&&e.attributeName==="src"&&!this.keepIframeSrcFn(n))if(!r.contentDocument)e.attributeName="rr_src";else return;if(o||(o={node:e.target,attributes:{}},this.attributes.push(o)),e.attributeName==="style"){const s=this.doc.createElement("span");e.oldValue&&s.setAttribute("style",e.oldValue),(o.attributes.style===void 0||o.attributes.style===null)&&(o.attributes.style={});const i=o.attributes.style;for(const l of Array.from(r.style)){const a=r.style.getPropertyValue(l),c=r.style.getPropertyPriority(l);(a!==s.style.getPropertyValue(l)||c!==s.style.getPropertyPriority(l))&&(c===""?i[l]=a:i[l]=[a,c])}for(const l of Array.from(s.style))r.style.getPropertyValue(l)===""&&(i[l]=!1)}else o.attributes[e.attributeName]=lr(this.doc,r.tagName,e.attributeName,n);break}case"childList":{if(le(e.target,this.blockClass,this.blockSelector,!0))return;e.addedNodes.forEach(r=>this.genAdds(r,e.target)),e.removedNodes.forEach(r=>{const n=this.mirror.getId(r),o=je(e.target)?this.mirror.getId(e.target.host):this.mirror.getId(e.target);le(e.target,this.blockClass,this.blockSelector,!1)||nt(r,this.mirror)||!fr(r,this.mirror)||(this.addedSet.has(r)?(Dt(this.addedSet,r),this.droppedSet.add(r)):this.addedSet.has(e.target)&&n===-1||Nt(e.target,this.mirror)||(this.movedSet.has(r)&&this.movedMap[br(n,o)]?Dt(this.movedSet,r):this.removes.push({parentId:o,id:n,isShadow:je(e.target)&&Ve(e.target)?!0:void 0})),this.mapRemoves.push(r))});break}}},this.genAdds=(e,r)=>{if(this.mirror.hasNode(e)){if(nt(e,this.mirror))return;this.movedSet.add(e);let n=null;r&&this.mirror.hasNode(r)&&(n=this.mirror.getId(r)),n&&n!==-1&&(this.movedMap[br(this.mirror.getId(e),n)]=!0)}else this.addedSet.add(e),this.droppedSet.delete(e);le(e,this.blockClass,this.blockSelector,!1)||e.childNodes.forEach(n=>this.genAdds(n))}}init(e){["mutationCb","blockClass","blockSelector","maskTextClass","maskTextSelector","inlineStylesheet","maskInputOptions","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager"].forEach(r=>{this[r]=e[r]})}freeze(){this.frozen=!0,this.canvasManager.freeze()}unfreeze(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}isFrozen(){return this.frozen}lock(){this.locked=!0,this.canvasManager.lock()}unlock(){this.locked=!1,this.canvasManager.unlock(),this.emit()}reset(){this.shadowDomManager.reset(),this.canvasManager.reset()}}function Dt(t,e){t.delete(e),e.childNodes.forEach(r=>Dt(t,r))}function Sr(t,e,r){return t.length===0?!1:wr(t,e,r)}function wr(t,e,r){const{parentNode:n}=e;if(!n)return!1;const o=r.getId(n);return t.some(s=>s.id===o)?!0:wr(t,n,r)}function Nr(t,e){return t.size===0?!1:Er(t,e)}function Er(t,e){const{parentNode:r}=e;return r?t.has(r)?!0:Er(t,r):!1}var Eo=Object.defineProperty,Co=Object.defineProperties,To=Object.getOwnPropertyDescriptors,Cr=Object.getOwnPropertySymbols,Mo=Object.prototype.hasOwnProperty,Io=Object.prototype.propertyIsEnumerable,Tr=(t,e,r)=>e in t?Eo(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Mr=(t,e)=>{for(var r in e||(e={}))Mo.call(e,r)&&Tr(t,r,e[r]);if(Cr)for(var r of Cr(e))Io.call(e,r)&&Tr(t,r,e[r]);return t},ko=(t,e)=>Co(t,To(e));const ke=[],Ir=typeof CSSGroupingRule<"u",kr=typeof CSSMediaRule<"u",Rr=typeof CSSSupportsRule<"u",Dr=typeof CSSConditionRule<"u";function He(t){try{if("composedPath"in t){const e=t.composedPath();if(e.length)return e[0]}else if("path"in t&&t.path.length)return t.path[0];return t.target}catch{return t.target}}function Or(t,e){var r,n;const o=new No;ke.push(o),o.init(t);let s=window.MutationObserver||window.__rrMutationObserver;const i=(n=(r=window?.Zone)==null?void 0:r.__symbol__)==null?void 0:n.call(r,"MutationObserver");i&&window[i]&&(s=window[i]);const l=new s(o.processMutations.bind(o));return l.observe(e,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),l}function Ro({mousemoveCb:t,sampling:e,doc:r,mirror:n}){if(e.mousemove===!1)return()=>{};const o=typeof e.mousemove=="number"?e.mousemove:50,s=typeof e.mousemoveCallback=="number"?e.mousemoveCallback:500;let i=[],l;const a=Le(d=>{const h=Date.now()-l;t(i.map(p=>(p.timeOffset-=h,p)),d),i=[],l=null},s),c=Le(d=>{const h=He(d),{clientX:p,clientY:m}=Et(d)?d.changedTouches[0]:d;l||(l=Date.now()),i.push({x:p,y:m,id:n.getId(h),timeOffset:Date.now()-l}),a(typeof DragEvent<"u"&&d instanceof DragEvent?C.Drag:d instanceof MouseEvent?C.MouseMove:C.TouchMove)},o,{trailing:!1}),u=[ae("mousemove",c,r),ae("touchmove",c,r),ae("drag",c,r)];return()=>{u.forEach(d=>d())}}function Do({mouseInteractionCb:t,doc:e,mirror:r,blockClass:n,blockSelector:o,sampling:s}){if(s.mouseInteraction===!1)return()=>{};const i=s.mouseInteraction===!0||s.mouseInteraction===void 0?{}:s.mouseInteraction,l=[],a=c=>u=>{const d=He(u);if(le(d,n,o,!0))return;const h=Et(u)?u.changedTouches[0]:u;if(!h)return;const p=r.getId(d),{clientX:m,clientY:y}=h;t({type:ie[c],id:p,x:m,y})};return Object.keys(ie).filter(c=>Number.isNaN(Number(c))&&!c.endsWith("_Departed")&&i[c]!==!1).forEach(c=>{const u=c.toLowerCase(),d=a(c);l.push(ae(u,d,e))}),()=>{l.forEach(c=>c())}}function xr({scrollCb:t,doc:e,mirror:r,blockClass:n,blockSelector:o,sampling:s}){const i=Le(l=>{const a=He(l);if(!a||le(a,n,o,!0))return;const c=r.getId(a);if(a===e){const u=e.scrollingElement||e.documentElement;t({id:c,x:u.scrollLeft,y:u.scrollTop})}else t({id:c,x:a.scrollLeft,y:a.scrollTop})},s.scroll||100);return ae("scroll",i,e)}function Oo({viewportResizeCb:t}){let e=-1,r=-1;const n=Le(()=>{const o=St(),s=wt();(e!==o||r!==s)&&(t({width:Number(s),height:Number(o)}),e=o,r=s)},200);return ae("resize",n,window)}function Lr(t,e){const r=Mr({},t);return e||delete r.userTriggered,r}const xo=["INPUT","TEXTAREA","SELECT"],Ar=new WeakMap;function Lo({inputCb:t,doc:e,mirror:r,blockClass:n,blockSelector:o,ignoreClass:s,maskInputOptions:i,maskInputFn:l,sampling:a,userTriggeredOnInput:c}){function u(v){let f=He(v);const g=v.isTrusted;if(f&&f.tagName==="OPTION"&&(f=f.parentElement),!f||!f.tagName||xo.indexOf(f.tagName)<0||le(f,n,o,!0))return;const b=f.type;if(f.classList.contains(s))return;let N=f.value,R=!1;b==="radio"||b==="checkbox"?R=f.checked:(i[f.tagName.toLowerCase()]||i[b])&&(N=yt({maskInputOptions:i,tagName:f.tagName,type:b,value:N,maskInputFn:l})),d(f,Lr({text:N,isChecked:R,userTriggered:g},c));const k=f.name;b==="radio"&&k&&R&&e.querySelectorAll(`input[type="radio"][name="${k}"]`).forEach(x=>{x!==f&&d(x,Lr({text:x.value,isChecked:!R,userTriggered:!1},c))})}function d(v,f){const g=Ar.get(v);if(!g||g.text!==f.text||g.isChecked!==f.isChecked){Ar.set(v,f);const b=r.getId(v);t(ko(Mr({},f),{id:b}))}}const h=(a.input==="last"?["change"]:["input","change"]).map(v=>ae(v,u,e)),p=e.defaultView;if(!p)return()=>{h.forEach(v=>v())};const m=p.Object.getOwnPropertyDescriptor(p.HTMLInputElement.prototype,"value"),y=[[p.HTMLInputElement.prototype,"value"],[p.HTMLInputElement.prototype,"checked"],[p.HTMLSelectElement.prototype,"value"],[p.HTMLTextAreaElement.prototype,"value"],[p.HTMLSelectElement.prototype,"selectedIndex"],[p.HTMLOptionElement.prototype,"selected"]];return m&&m.set&&h.push(...y.map(v=>ze(v[0],v[1],{set(){u({target:this})}},!1,p))),()=>{h.forEach(v=>v())}}function ot(t){const e=[];function r(n,o){if(Ir&&n.parentRule instanceof CSSGroupingRule||kr&&n.parentRule instanceof CSSMediaRule||Rr&&n.parentRule instanceof CSSSupportsRule||Dr&&n.parentRule instanceof CSSConditionRule){const s=Array.from(n.parentRule.cssRules).indexOf(n);o.unshift(s)}else if(n.parentStyleSheet){const s=Array.from(n.parentStyleSheet.cssRules).indexOf(n);o.unshift(s)}return o}return r(t,e)}function Ee(t,e,r){let n,o;return t?(t.ownerNode?n=e.getId(t.ownerNode):o=r.getId(t),{styleId:o,id:n}):{}}function Ao({styleSheetRuleCb:t,mirror:e,stylesheetManager:r},{win:n}){const o=n.CSSStyleSheet.prototype.insertRule;n.CSSStyleSheet.prototype.insertRule=function(u,d){const{id:h,styleId:p}=Ee(this,e,r.styleMirror);return(h&&h!==-1||p&&p!==-1)&&t({id:h,styleId:p,adds:[{rule:u,index:d}]}),o.apply(this,[u,d])};const s=n.CSSStyleSheet.prototype.deleteRule;n.CSSStyleSheet.prototype.deleteRule=function(u){const{id:d,styleId:h}=Ee(this,e,r.styleMirror);return(d&&d!==-1||h&&h!==-1)&&t({id:d,styleId:h,removes:[{index:u}]}),s.apply(this,[u])};let i;n.CSSStyleSheet.prototype.replace&&(i=n.CSSStyleSheet.prototype.replace,n.CSSStyleSheet.prototype.replace=function(u){const{id:d,styleId:h}=Ee(this,e,r.styleMirror);return(d&&d!==-1||h&&h!==-1)&&t({id:d,styleId:h,replace:u}),i.apply(this,[u])});let l;n.CSSStyleSheet.prototype.replaceSync&&(l=n.CSSStyleSheet.prototype.replaceSync,n.CSSStyleSheet.prototype.replaceSync=function(u){const{id:d,styleId:h}=Ee(this,e,r.styleMirror);return(d&&d!==-1||h&&h!==-1)&&t({id:d,styleId:h,replaceSync:u}),l.apply(this,[u])});const a={};Ir?a.CSSGroupingRule=n.CSSGroupingRule:(kr&&(a.CSSMediaRule=n.CSSMediaRule),Dr&&(a.CSSConditionRule=n.CSSConditionRule),Rr&&(a.CSSSupportsRule=n.CSSSupportsRule));const c={};return Object.entries(a).forEach(([u,d])=>{c[u]={insertRule:d.prototype.insertRule,deleteRule:d.prototype.deleteRule},d.prototype.insertRule=function(h,p){const{id:m,styleId:y}=Ee(this.parentStyleSheet,e,r.styleMirror);return(m&&m!==-1||y&&y!==-1)&&t({id:m,styleId:y,adds:[{rule:h,index:[...ot(this),p||0]}]}),c[u].insertRule.apply(this,[h,p])},d.prototype.deleteRule=function(h){const{id:p,styleId:m}=Ee(this.parentStyleSheet,e,r.styleMirror);return(p&&p!==-1||m&&m!==-1)&&t({id:p,styleId:m,removes:[{index:[...ot(this),h]}]}),c[u].deleteRule.apply(this,[h])}}),()=>{n.CSSStyleSheet.prototype.insertRule=o,n.CSSStyleSheet.prototype.deleteRule=s,i&&(n.CSSStyleSheet.prototype.replace=i),l&&(n.CSSStyleSheet.prototype.replaceSync=l),Object.entries(a).forEach(([u,d])=>{d.prototype.insertRule=c[u].insertRule,d.prototype.deleteRule=c[u].deleteRule})}}function Fr({mirror:t,stylesheetManager:e},r){var n,o,s;let i=null;r.nodeName==="#document"?i=t.getId(r):i=t.getId(r.host);const l=r.nodeName==="#document"?(n=r.defaultView)==null?void 0:n.Document:(s=(o=r.ownerDocument)==null?void 0:o.defaultView)==null?void 0:s.ShadowRoot,a=Object.getOwnPropertyDescriptor(l?.prototype,"adoptedStyleSheets");return i===null||i===-1||!l||!a?()=>{}:(Object.defineProperty(r,"adoptedStyleSheets",{configurable:a.configurable,enumerable:a.enumerable,get(){var c;return(c=a.get)==null?void 0:c.call(this)},set(c){var u;const d=(u=a.set)==null?void 0:u.call(this,c);if(i!==null&&i!==-1)try{e.adoptStyleSheets(c,i)}catch{}return d}}),()=>{Object.defineProperty(r,"adoptedStyleSheets",{configurable:a.configurable,enumerable:a.enumerable,get:a.get,set:a.set})})}function Fo({styleDeclarationCb:t,mirror:e,ignoreCSSAttributes:r,stylesheetManager:n},{win:o}){const s=o.CSSStyleDeclaration.prototype.setProperty;o.CSSStyleDeclaration.prototype.setProperty=function(l,a,c){var u;if(r.has(l))return s.apply(this,[l,a,c]);const{id:d,styleId:h}=Ee((u=this.parentRule)==null?void 0:u.parentStyleSheet,e,n.styleMirror);return(d&&d!==-1||h&&h!==-1)&&t({id:d,styleId:h,set:{property:l,value:a,priority:c},index:ot(this.parentRule)}),s.apply(this,[l,a,c])};const i=o.CSSStyleDeclaration.prototype.removeProperty;return o.CSSStyleDeclaration.prototype.removeProperty=function(l){var a;if(r.has(l))return i.apply(this,[l]);const{id:c,styleId:u}=Ee((a=this.parentRule)==null?void 0:a.parentStyleSheet,e,n.styleMirror);return(c&&c!==-1||u&&u!==-1)&&t({id:c,styleId:u,remove:{property:l},index:ot(this.parentRule)}),i.apply(this,[l])},()=>{o.CSSStyleDeclaration.prototype.setProperty=s,o.CSSStyleDeclaration.prototype.removeProperty=i}}function _o({mediaInteractionCb:t,blockClass:e,blockSelector:r,mirror:n,sampling:o}){const s=l=>Le(a=>{const c=He(a);if(!c||le(c,e,r,!0))return;const{currentTime:u,volume:d,muted:h,playbackRate:p}=c;t({type:l,id:n.getId(c),currentTime:u,volume:d,muted:h,playbackRate:p})},o.media||500),i=[ae("play",s(Se.Play)),ae("pause",s(Se.Pause)),ae("seeked",s(Se.Seeked)),ae("volumechange",s(Se.VolumeChange)),ae("ratechange",s(Se.RateChange))];return()=>{i.forEach(l=>l())}}function Po({fontCb:t,doc:e}){const r=e.defaultView;if(!r)return()=>{};const n=[],o=new WeakMap,s=r.FontFace;r.FontFace=function(l,a,c){const u=new s(l,a,c);return o.set(u,{family:l,buffer:typeof a!="string",descriptors:c,fontSource:typeof a=="string"?a:JSON.stringify(Array.from(new Uint8Array(a)))}),u};const i=Ne(e.fonts,"add",function(l){return function(a){return setTimeout(()=>{const c=o.get(a);c&&(t(c),o.delete(a))},0),l.apply(this,[a])}});return n.push(()=>{r.FontFace=s}),n.push(i),()=>{n.forEach(l=>l())}}function $o(t){const{doc:e,mirror:r,blockClass:n,blockSelector:o,selectionCb:s}=t;let i=!0;const l=()=>{const a=e.getSelection();if(!a||i&&a?.isCollapsed)return;i=a.isCollapsed||!1;const c=[],u=a.rangeCount||0;for(let d=0;d<u;d++){const h=a.getRangeAt(d),{startContainer:p,startOffset:m,endContainer:y,endOffset:v}=h;le(p,n,o,!0)||le(y,n,o,!0)||c.push({start:r.getId(p),startOffset:m,end:r.getId(y),endOffset:v})}s({ranges:c})};return l(),ae("selectionchange",l)}function Wo(t,e){const{mutationCb:r,mousemoveCb:n,mouseInteractionCb:o,scrollCb:s,viewportResizeCb:i,inputCb:l,mediaInteractionCb:a,styleSheetRuleCb:c,styleDeclarationCb:u,canvasMutationCb:d,fontCb:h,selectionCb:p}=t;t.mutationCb=(...m)=>{e.mutation&&e.mutation(...m),r(...m)},t.mousemoveCb=(...m)=>{e.mousemove&&e.mousemove(...m),n(...m)},t.mouseInteractionCb=(...m)=>{e.mouseInteraction&&e.mouseInteraction(...m),o(...m)},t.scrollCb=(...m)=>{e.scroll&&e.scroll(...m),s(...m)},t.viewportResizeCb=(...m)=>{e.viewportResize&&e.viewportResize(...m),i(...m)},t.inputCb=(...m)=>{e.input&&e.input(...m),l(...m)},t.mediaInteractionCb=(...m)=>{e.mediaInteaction&&e.mediaInteaction(...m),a(...m)},t.styleSheetRuleCb=(...m)=>{e.styleSheetRule&&e.styleSheetRule(...m),c(...m)},t.styleDeclarationCb=(...m)=>{e.styleDeclaration&&e.styleDeclaration(...m),u(...m)},t.canvasMutationCb=(...m)=>{e.canvasMutation&&e.canvasMutation(...m),d(...m)},t.fontCb=(...m)=>{e.font&&e.font(...m),h(...m)},t.selectionCb=(...m)=>{e.selection&&e.selection(...m),p(...m)}}function Uo(t,e={}){const r=t.doc.defaultView;if(!r)return()=>{};Wo(t,e);const n=Or(t,t.doc),o=Ro(t),s=Do(t),i=xr(t),l=Oo(t),a=Lo(t),c=_o(t),u=Ao(t,{win:r}),d=Fr(t,t.doc),h=Fo(t,{win:r}),p=t.collectFonts?Po(t):()=>{},m=$o(t),y=[];for(const v of t.plugins)y.push(v.observer(v.callback,r,v.options));return()=>{ke.forEach(v=>v.reset()),n.disconnect(),o(),s(),i(),l(),a(),c(),u(),d(),h(),p(),m(),y.forEach(v=>v())}}class _r{constructor(e){this.generateIdFn=e,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}getId(e,r,n,o){const s=n||this.getIdToRemoteIdMap(e),i=o||this.getRemoteIdToIdMap(e);let l=s.get(r);return l||(l=this.generateIdFn(),s.set(r,l),i.set(l,r)),l}getIds(e,r){const n=this.getIdToRemoteIdMap(e),o=this.getRemoteIdToIdMap(e);return r.map(s=>this.getId(e,s,n,o))}getRemoteId(e,r,n){const o=n||this.getRemoteIdToIdMap(e);return typeof r!="number"?r:o.get(r)||-1}getRemoteIds(e,r){const n=this.getRemoteIdToIdMap(e);return r.map(o=>this.getRemoteId(e,o,n))}reset(e){if(!e){this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap;return}this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)}getIdToRemoteIdMap(e){let r=this.iframeIdToRemoteIdMap.get(e);return r||(r=new Map,this.iframeIdToRemoteIdMap.set(e,r)),r}getRemoteIdToIdMap(e){let r=this.iframeRemoteIdToIdMap.get(e);return r||(r=new Map,this.iframeRemoteIdToIdMap.set(e,r)),r}}class jo{constructor(e){this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new _r(sr),this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new _r(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}addIframe(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}addLoadListener(e){this.loadListener=e}attachIframe(e,r){var n;this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:r}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),(n=this.loadListener)==null||n.call(this,e),e.contentDocument&&e.contentDocument.adoptedStyleSheets&&e.contentDocument.adoptedStyleSheets.length>0&&this.stylesheetManager.adoptStyleSheets(e.contentDocument.adoptedStyleSheets,this.mirror.getId(e.contentDocument))}handleMessage(e){if(e.data.type==="rrweb"){if(!e.source)return;const r=this.crossOriginIframeMap.get(e.source);if(!r)return;const n=this.transformCrossOriginEvent(r,e.data.event);n&&this.wrappedEmit(n,e.data.isCheckout)}}transformCrossOriginEvent(e,r){var n;switch(r.type){case T.FullSnapshot:return this.crossOriginIframeMirror.reset(e),this.crossOriginIframeStyleMirror.reset(e),this.replaceIdOnNode(r.data.node,e),{timestamp:r.timestamp,type:T.IncrementalSnapshot,data:{source:C.Mutation,adds:[{parentId:this.mirror.getId(e),nextId:null,node:r.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}};case T.Meta:case T.Load:case T.DomContentLoaded:return!1;case T.Plugin:return r;case T.Custom:return this.replaceIds(r.data.payload,e,["id","parentId","previousId","nextId"]),r;case T.IncrementalSnapshot:switch(r.data.source){case C.Mutation:return r.data.adds.forEach(o=>{this.replaceIds(o,e,["parentId","nextId","previousId"]),this.replaceIdOnNode(o.node,e)}),r.data.removes.forEach(o=>{this.replaceIds(o,e,["parentId","id"])}),r.data.attributes.forEach(o=>{this.replaceIds(o,e,["id"])}),r.data.texts.forEach(o=>{this.replaceIds(o,e,["id"])}),r;case C.Drag:case C.TouchMove:case C.MouseMove:return r.data.positions.forEach(o=>{this.replaceIds(o,e,["id"])}),r;case C.ViewportResize:return!1;case C.MediaInteraction:case C.MouseInteraction:case C.Scroll:case C.CanvasMutation:case C.Input:return this.replaceIds(r.data,e,["id"]),r;case C.StyleSheetRule:case C.StyleDeclaration:return this.replaceIds(r.data,e,["id"]),this.replaceStyleIds(r.data,e,["styleId"]),r;case C.Font:return r;case C.Selection:return r.data.ranges.forEach(o=>{this.replaceIds(o,e,["start","end"])}),r;case C.AdoptedStyleSheet:return this.replaceIds(r.data,e,["id"]),this.replaceStyleIds(r.data,e,["styleIds"]),(n=r.data.styles)==null||n.forEach(o=>{this.replaceStyleIds(o,e,["styleId"])}),r}}}replace(e,r,n,o){for(const s of o)!Array.isArray(r[s])&&typeof r[s]!="number"||(Array.isArray(r[s])?r[s]=e.getIds(n,r[s]):r[s]=e.getId(n,r[s]));return r}replaceIds(e,r,n){return this.replace(this.crossOriginIframeMirror,e,r,n)}replaceStyleIds(e,r,n){return this.replace(this.crossOriginIframeStyleMirror,e,r,n)}replaceIdOnNode(e,r){this.replaceIds(e,r,["id"]),"childNodes"in e&&e.childNodes.forEach(n=>{this.replaceIdOnNode(n,r)})}}var Vo=Object.defineProperty,Bo=Object.defineProperties,Go=Object.getOwnPropertyDescriptors,Pr=Object.getOwnPropertySymbols,zo=Object.prototype.hasOwnProperty,Ho=Object.prototype.propertyIsEnumerable,$r=(t,e,r)=>e in t?Vo(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,Wr=(t,e)=>{for(var r in e||(e={}))zo.call(e,r)&&$r(t,r,e[r]);if(Pr)for(var r of Pr(e))Ho.call(e,r)&&$r(t,r,e[r]);return t},Ur=(t,e)=>Bo(t,Go(e));class Yo{constructor(e){this.shadowDoms=new WeakSet,this.restorePatches=[],this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror;const r=this;this.restorePatches.push(Ne(Element.prototype,"attachShadow",function(n){return function(o){const s=n.call(this,o);return this.shadowRoot&&r.addShadowRoot(this.shadowRoot,this.ownerDocument),s}}))}addShadowRoot(e,r){!Ve(e)||this.shadowDoms.has(e)||(this.shadowDoms.add(e),Or(Ur(Wr({},this.bypassOptions),{doc:r,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),e),xr(Ur(Wr({},this.bypassOptions),{scrollCb:this.scrollCb,doc:e,mirror:this.mirror})),setTimeout(()=>{e.adoptedStyleSheets&&e.adoptedStyleSheets.length>0&&this.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,this.mirror.getId(e.host)),Fr({mirror:this.mirror,stylesheetManager:this.bypassOptions.stylesheetManager},e)},0))}observeAttachShadow(e){if(e.contentWindow){const r=this;this.restorePatches.push(Ne(e.contentWindow.HTMLElement.prototype,"attachShadow",function(n){return function(o){const s=n.call(this,o);return this.shadowRoot&&r.addShadowRoot(this.shadowRoot,e.contentDocument),s}}))}}reset(){this.restorePatches.forEach(e=>e()),this.shadowDoms=new WeakSet}}for(var _e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Ye=typeof Uint8Array>"u"?[]:new Uint8Array(256),it=0;it<_e.length;it++)Ye[_e.charCodeAt(it)]=it;var Xo=function(t){var e=new Uint8Array(t),r,n=e.length,o="";for(r=0;r<n;r+=3)o+=_e[e[r]>>2],o+=_e[(e[r]&3)<<4|e[r+1]>>4],o+=_e[(e[r+1]&15)<<2|e[r+2]>>6],o+=_e[e[r+2]&63];return n%3===2?o=o.substring(0,o.length-1)+"=":n%3===1&&(o=o.substring(0,o.length-2)+"=="),o},Zo=function(t){var e=t.length*.75,r=t.length,n,o=0,s,i,l,a;t[t.length-1]==="="&&(e--,t[t.length-2]==="="&&e--);var c=new ArrayBuffer(e),u=new Uint8Array(c);for(n=0;n<r;n+=4)s=Ye[t.charCodeAt(n)],i=Ye[t.charCodeAt(n+1)],l=Ye[t.charCodeAt(n+2)],a=Ye[t.charCodeAt(n+3)],u[o++]=s<<2|i>>4,u[o++]=(i&15)<<4|l>>2,u[o++]=(l&3)<<6|a&63;return c};const jr=new Map;function Ko(t,e){let r=jr.get(t);return r||(r=new Map,jr.set(t,r)),r.has(e)||r.set(e,[]),r.get(e)}const Vr=(t,e,r)=>{if(!t||!(Gr(t,e)||typeof t=="object"))return;const n=t.constructor.name,o=Ko(r,n);let s=o.indexOf(t);return s===-1&&(s=o.length,o.push(t)),s};function st(t,e,r){if(t instanceof Array)return t.map(n=>st(n,e,r));if(t===null)return t;if(t instanceof Float32Array||t instanceof Float64Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Uint8Array||t instanceof Uint16Array||t instanceof Int16Array||t instanceof Int8Array||t instanceof Uint8ClampedArray)return{rr_type:t.constructor.name,args:[Object.values(t)]};if(t instanceof ArrayBuffer){const n=t.constructor.name,o=Xo(t);return{rr_type:n,base64:o}}else{if(t instanceof DataView)return{rr_type:t.constructor.name,args:[st(t.buffer,e,r),t.byteOffset,t.byteLength]};if(t instanceof HTMLImageElement){const n=t.constructor.name,{src:o}=t;return{rr_type:n,src:o}}else if(t instanceof HTMLCanvasElement){const n="HTMLImageElement",o=t.toDataURL();return{rr_type:n,src:o}}else{if(t instanceof ImageData)return{rr_type:t.constructor.name,args:[st(t.data,e,r),t.width,t.height]};if(Gr(t,e)||typeof t=="object"){const n=t.constructor.name,o=Vr(t,e,r);return{rr_type:n,index:o}}}}return t}const Br=(t,e,r)=>[...t].map(n=>st(n,e,r)),Gr=(t,e)=>{const r=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter(n=>typeof e[n]=="function");return Boolean(r.find(n=>t instanceof e[n]))};function Jo(t,e,r,n){const o=[],s=Object.getOwnPropertyNames(e.CanvasRenderingContext2D.prototype);for(const i of s)try{if(typeof e.CanvasRenderingContext2D.prototype[i]!="function")continue;const l=Ne(e.CanvasRenderingContext2D.prototype,i,function(a){return function(...c){return le(this.canvas,r,n,!0)||setTimeout(()=>{const u=Br([...c],e,this);t(this.canvas,{type:be["2D"],property:i,args:u})},0),a.apply(this,c)}});o.push(l)}catch{const a=ze(e.CanvasRenderingContext2D.prototype,i,{set(c){t(this.canvas,{type:be["2D"],property:i,args:[c],setter:!0})}});o.push(a)}return()=>{o.forEach(i=>i())}}function zr(t,e,r){const n=[];try{const o=Ne(t.HTMLCanvasElement.prototype,"getContext",function(s){return function(i,...l){return le(this,e,r,!0)||"__context"in this||(this.__context=i),s.apply(this,[i,...l])}});n.push(o)}catch{console.error("failed to patch HTMLCanvasElement.prototype.getContext")}return()=>{n.forEach(o=>o())}}function Hr(t,e,r,n,o,s,i){const l=[],a=Object.getOwnPropertyNames(t);for(const c of a)if(!["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(c))try{if(typeof t[c]!="function")continue;const u=Ne(t,c,function(d){return function(...h){const p=d.apply(this,h);if(Vr(p,i,this),!le(this.canvas,n,o,!0)){const m=Br([...h],i,this),y={type:e,property:c,args:m};r(this.canvas,y)}return p}});l.push(u)}catch{const d=ze(t,c,{set(h){r(this.canvas,{type:e,property:c,args:[h],setter:!0})}});l.push(d)}return l}function Qo(t,e,r,n,o){const s=[];return s.push(...Hr(e.WebGLRenderingContext.prototype,be.WebGL,t,r,n,o,e)),typeof e.WebGL2RenderingContext<"u"&&s.push(...Hr(e.WebGL2RenderingContext.prototype,be.WebGL2,t,r,n,o,e)),()=>{s.forEach(i=>i())}}function qo(t,e){var r=atob(t);if(e){for(var n=new Uint8Array(r.length),o=0,s=r.length;o<s;++o)n[o]=r.charCodeAt(o);return String.fromCharCode.apply(null,new Uint16Array(n.buffer))}return r}function ei(t,e,r){var n=e===void 0?null:e,o=r===void 0?!1:r,s=qo(t,o),i=s.indexOf(`
`,10)+1,l=s.substring(i)+(n?"//# sourceMappingURL="+n:""),a=new Blob([l],{type:"application/javascript"});return URL.createObjectURL(a)}function ti(t,e,r){var n;return function(s){return n=n||ei(t,e,r),new Worker(n,s)}}var ri=ti("Lyogcm9sbHVwLXBsdWdpbi13ZWItd29ya2VyLWxvYWRlciAqLwooZnVuY3Rpb24oKXsidXNlIHN0cmljdCI7Zm9yKHZhciByPSJBQkNERUZHSElKS0xNTk9QUVJTVFVWV1hZWmFiY2RlZmdoaWprbG1ub3BxcnN0dXZ3eHl6MDEyMzQ1Njc4OSsvIixwPXR5cGVvZiBVaW50OEFycmF5PiJ1Ij9bXTpuZXcgVWludDhBcnJheSgyNTYpLGY9MDtmPHIubGVuZ3RoO2YrKylwW3IuY2hhckNvZGVBdChmKV09Zjt2YXIgdT1mdW5jdGlvbihzKXt2YXIgZT1uZXcgVWludDhBcnJheShzKSxuLGE9ZS5sZW5ndGgsdD0iIjtmb3Iobj0wO248YTtuKz0zKXQrPXJbZVtuXT4+Ml0sdCs9clsoZVtuXSYzKTw8NHxlW24rMV0+PjRdLHQrPXJbKGVbbisxXSYxNSk8PDJ8ZVtuKzJdPj42XSx0Kz1yW2VbbisyXSY2M107cmV0dXJuIGElMz09PTI/dD10LnN1YnN0cmluZygwLHQubGVuZ3RoLTEpKyI9IjphJTM9PT0xJiYodD10LnN1YnN0cmluZygwLHQubGVuZ3RoLTIpKyI9PSIpLHR9O2NvbnN0IGM9bmV3IE1hcCxsPW5ldyBNYXA7YXN5bmMgZnVuY3Rpb24gdihzLGUsbil7Y29uc3QgYT1gJHtzfS0ke2V9YDtpZigiT2Zmc2NyZWVuQ2FudmFzImluIGdsb2JhbFRoaXMpe2lmKGwuaGFzKGEpKXJldHVybiBsLmdldChhKTtjb25zdCB0PW5ldyBPZmZzY3JlZW5DYW52YXMocyxlKTt0LmdldENvbnRleHQoIjJkIik7Y29uc3QgZz1hd2FpdChhd2FpdCB0LmNvbnZlcnRUb0Jsb2IobikpLmFycmF5QnVmZmVyKCksZD11KGcpO3JldHVybiBsLnNldChhLGQpLGR9ZWxzZSByZXR1cm4iIn1jb25zdCBpPXNlbGY7aS5vbm1lc3NhZ2U9YXN5bmMgZnVuY3Rpb24ocyl7aWYoIk9mZnNjcmVlbkNhbnZhcyJpbiBnbG9iYWxUaGlzKXtjb25zdHtpZDplLGJpdG1hcDpuLHdpZHRoOmEsaGVpZ2h0OnQsZGF0YVVSTE9wdGlvbnM6Z309cy5kYXRhLGQ9dihhLHQsZyksaD1uZXcgT2Zmc2NyZWVuQ2FudmFzKGEsdCk7aC5nZXRDb250ZXh0KCIyZCIpLmRyYXdJbWFnZShuLDAsMCksbi5jbG9zZSgpO2NvbnN0IHc9YXdhaXQgaC5jb252ZXJ0VG9CbG9iKGcpLHk9dy50eXBlLGI9YXdhaXQgdy5hcnJheUJ1ZmZlcigpLG89dShiKTtpZighYy5oYXMoZSkmJmF3YWl0IGQ9PT1vKXJldHVybiBjLnNldChlLG8pLGkucG9zdE1lc3NhZ2Uoe2lkOmV9KTtpZihjLmdldChlKT09PW8pcmV0dXJuIGkucG9zdE1lc3NhZ2Uoe2lkOmV9KTtpLnBvc3RNZXNzYWdlKHtpZDplLHR5cGU6eSxiYXNlNjQ6byx3aWR0aDphLGhlaWdodDp0fSksYy5zZXQoZSxvKX1lbHNlIHJldHVybiBpLnBvc3RNZXNzYWdlKHtpZDpzLmRhdGEuaWR9KX19KSgpOwoK",null,!1),Yr=Object.getOwnPropertySymbols,ni=Object.prototype.hasOwnProperty,oi=Object.prototype.propertyIsEnumerable,ii=(t,e)=>{var r={};for(var n in t)ni.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(t!=null&&Yr)for(var n of Yr(t))e.indexOf(n)<0&&oi.call(t,n)&&(r[n]=t[n]);return r},si=(t,e,r)=>new Promise((n,o)=>{var s=a=>{try{l(r.next(a))}catch(c){o(c)}},i=a=>{try{l(r.throw(a))}catch(c){o(c)}},l=a=>a.done?n(a.value):Promise.resolve(a.value).then(s,i);l((r=r.apply(t,e)).next())});class ai{constructor(e){this.pendingCanvasMutations=new Map,this.rafStamps={latestId:0,invokeId:null},this.frozen=!1,this.locked=!1,this.processMutation=(a,c)=>{(this.rafStamps.invokeId&&this.rafStamps.latestId!==this.rafStamps.invokeId||!this.rafStamps.invokeId)&&(this.rafStamps.invokeId=this.rafStamps.latestId),this.pendingCanvasMutations.has(a)||this.pendingCanvasMutations.set(a,[]),this.pendingCanvasMutations.get(a).push(c)};const{sampling:r="all",win:n,blockClass:o,blockSelector:s,recordCanvas:i,dataURLOptions:l}=e;this.mutationCb=e.mutationCb,this.mirror=e.mirror,i&&r==="all"&&this.initCanvasMutationObserver(n,o,s),i&&typeof r=="number"&&this.initCanvasFPSObserver(r,n,o,s,{dataURLOptions:l})}reset(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()}freeze(){this.frozen=!0}unfreeze(){this.frozen=!1}lock(){this.locked=!0}unlock(){this.locked=!1}initCanvasFPSObserver(e,r,n,o,s){const i=zr(r,n,o),l=new Map,a=new ri;a.onmessage=m=>{const{id:y}=m.data;if(l.set(y,!1),!("base64"in m.data))return;const{base64:v,type:f,width:g,height:b}=m.data;this.mutationCb({id:y,type:be["2D"],commands:[{property:"clearRect",args:[0,0,g,b]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:v}],type:f}]},0,0]}]})};const c=1e3/e;let u=0,d;const h=()=>{const m=[];return r.document.querySelectorAll("canvas").forEach(y=>{le(y,n,o,!0)||m.push(y)}),m},p=m=>{if(u&&m-u<c){d=requestAnimationFrame(p);return}u=m,h().forEach(y=>si(this,null,function*(){var v;const f=this.mirror.getId(y);if(l.get(f))return;if(l.set(f,!0),["webgl","webgl2"].includes(y.__context)){const b=y.getContext(y.__context);((v=b?.getContextAttributes())==null?void 0:v.preserveDrawingBuffer)===!1&&b?.clear(b.COLOR_BUFFER_BIT)}const g=yield createImageBitmap(y);a.postMessage({id:f,bitmap:g,width:y.width,height:y.height,dataURLOptions:s.dataURLOptions},[g])})),d=requestAnimationFrame(p)};d=requestAnimationFrame(p),this.resetObservers=()=>{i(),cancelAnimationFrame(d)}}initCanvasMutationObserver(e,r,n){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();const o=zr(e,r,n),s=Jo(this.processMutation.bind(this),e,r,n),i=Qo(this.processMutation.bind(this),e,r,n,this.mirror);this.resetObservers=()=>{o(),s(),i()}}startPendingCanvasMutationFlusher(){requestAnimationFrame(()=>this.flushPendingCanvasMutations())}startRAFTimestamping(){const e=r=>{this.rafStamps.latestId=r,requestAnimationFrame(e)};requestAnimationFrame(e)}flushPendingCanvasMutations(){this.pendingCanvasMutations.forEach((e,r)=>{const n=this.mirror.getId(r);this.flushPendingCanvasMutationFor(r,n)}),requestAnimationFrame(()=>this.flushPendingCanvasMutations())}flushPendingCanvasMutationFor(e,r){if(this.frozen||this.locked)return;const n=this.pendingCanvasMutations.get(e);if(!n||r===-1)return;const o=n.map(i=>ii(i,["type"])),{type:s}=n[0];this.mutationCb({id:r,type:s,commands:o}),this.pendingCanvasMutations.delete(e)}}class li{constructor(e){this.trackedLinkElements=new WeakSet,this.styleMirror=new Rt,this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}attachLinkElement(e,r){"_cssText"in r.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:r.id,attributes:r.attributes}]}),this.trackLinkElement(e)}trackLinkElement(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}adoptStyleSheets(e,r){if(e.length===0)return;const n={id:r,styleIds:[]},o=[];for(const s of e){let i;if(this.styleMirror.has(s))i=this.styleMirror.getId(s);else{i=this.styleMirror.add(s);const l=Array.from(s.rules||CSSRule);o.push({styleId:i,rules:l.map((a,c)=>({rule:nr(a),index:c}))})}n.styleIds.push(i)}o.length>0&&(n.styles=o),this.adoptedStyleSheetCb(n)}reset(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}trackStylesheetInLinkElement(e){}}var ci=Object.defineProperty,ui=Object.defineProperties,di=Object.getOwnPropertyDescriptors,Xr=Object.getOwnPropertySymbols,hi=Object.prototype.hasOwnProperty,pi=Object.prototype.propertyIsEnumerable,Zr=(t,e,r)=>e in t?ci(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,he=(t,e)=>{for(var r in e||(e={}))hi.call(e,r)&&Zr(t,r,e[r]);if(Xr)for(var r of Xr(e))pi.call(e,r)&&Zr(t,r,e[r]);return t},mi=(t,e)=>ui(t,di(e));function re(t){return mi(he({},t),{timestamp:Date.now()})}let q,at,Ot,lt=!1;const pe=or();function Re(t={}){const{emit:e,checkoutEveryNms:r,checkoutEveryNth:n,blockClass:o="rr-block",blockSelector:s=null,ignoreClass:i="rr-ignore",maskTextClass:l="rr-mask",maskTextSelector:a=null,inlineStylesheet:c=!0,maskAllInputs:u,maskInputOptions:d,slimDOMOptions:h,maskInputFn:p,maskTextFn:m,hooks:y,packFn:v,sampling:f={},dataURLOptions:g={},mousemoveWait:b,recordCanvas:N=!1,recordCrossOriginIframes:R=!1,userTriggeredOnInput:k=!1,collectFonts:x=!1,inlineImages:E=!1,plugins:H,keepIframeSrcFn:W=()=>!1,ignoreCSSAttributes:Z=new Set([])}=t,P=R?window.parent===window:!0;let B=!1;if(!P)try{window.parent.document,B=!1}catch{B=!0}if(P&&!e)throw new Error("emit function is required");b!==void 0&&f.mousemove===void 0&&(f.mousemove=b),pe.reset();const L=u===!0?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:d!==void 0?d:{password:!0},U=h===!0||h==="all"?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:h==="all",headMetaDescKeywords:h==="all"}:h||{};Ct();let A,D=0;const z=O=>{for(const te of H||[])te.eventProcessor&&(O=te.eventProcessor(O));return v&&(O=v(O)),O};q=(O,te)=>{var ne;if(((ne=ke[0])==null?void 0:ne.isFrozen())&&O.type!==T.FullSnapshot&&!(O.type===T.IncrementalSnapshot&&O.data.source===C.Mutation)&&ke.forEach(J=>J.unfreeze()),P)e?.(z(O),te);else if(B){const J={type:"rrweb",event:z(O),isCheckout:te};window.parent.postMessage(J,"*")}if(O.type===T.FullSnapshot)A=O,D=0;else if(O.type===T.IncrementalSnapshot){if(O.data.source===C.Mutation&&O.data.isAttachIframe)return;D++;const J=n&&D>=n,de=r&&O.timestamp-A.timestamp>r;(J||de)&&at(!0)}};const w=O=>{q(re({type:T.IncrementalSnapshot,data:he({source:C.Mutation},O)}))},S=O=>q(re({type:T.IncrementalSnapshot,data:he({source:C.Scroll},O)})),M=O=>q(re({type:T.IncrementalSnapshot,data:he({source:C.CanvasMutation},O)})),I=O=>q(re({type:T.IncrementalSnapshot,data:he({source:C.AdoptedStyleSheet},O)})),$=new li({mutationCb:w,adoptedStyleSheetCb:I}),G=new jo({mirror:pe,mutationCb:w,stylesheetManager:$,recordCrossOriginIframes:R,wrappedEmit:q});for(const O of H||[])O.getMirror&&O.getMirror({nodeMirror:pe,crossOriginIframeMirror:G.crossOriginIframeMirror,crossOriginIframeStyleMirror:G.crossOriginIframeStyleMirror});Ot=new ai({recordCanvas:N,mutationCb:M,win:window,blockClass:o,blockSelector:s,mirror:pe,sampling:f.canvas,dataURLOptions:g});const K=new Yo({mutationCb:w,scrollCb:S,bypassOptions:{blockClass:o,blockSelector:s,maskTextClass:l,maskTextSelector:a,inlineStylesheet:c,maskInputOptions:L,dataURLOptions:g,maskTextFn:m,maskInputFn:p,recordCanvas:N,inlineImages:E,sampling:f,slimDOMOptions:U,iframeManager:G,stylesheetManager:$,canvasManager:Ot,keepIframeSrcFn:W},mirror:pe});at=(O=!1)=>{var te,ne,J,de,j,ce;q(re({type:T.Meta,data:{href:window.location.href,width:wt(),height:St()}}),O),$.reset(),ke.forEach(oe=>oe.lock());const qe=uo(document,{mirror:pe,blockClass:o,blockSelector:s,maskTextClass:l,maskTextSelector:a,inlineStylesheet:c,maskAllInputs:L,maskTextFn:m,slimDOM:U,dataURLOptions:g,recordCanvas:N,inlineImages:E,onSerialize:oe=>{Ae(oe,pe)&&G.addIframe(oe),Mt(oe,pe)&&$.trackLinkElement(oe),Ie(oe)&&K.addShadowRoot(oe.shadowRoot,document)},onIframeLoad:(oe,tr)=>{G.attachIframe(oe,tr),K.observeAttachShadow(oe)},onStylesheetLoad:(oe,tr)=>{$.attachLinkElement(oe,tr)},keepIframeSrcFn:W});if(!qe)return console.warn("Failed to snapshot the document");q(re({type:T.FullSnapshot,data:{node:qe,initialOffset:{left:window.pageXOffset!==void 0?window.pageXOffset:document?.documentElement.scrollLeft||((ne=(te=document?.body)==null?void 0:te.parentElement)==null?void 0:ne.scrollLeft)||((J=document?.body)==null?void 0:J.scrollLeft)||0,top:window.pageYOffset!==void 0?window.pageYOffset:document?.documentElement.scrollTop||((j=(de=document?.body)==null?void 0:de.parentElement)==null?void 0:j.scrollTop)||((ce=document?.body)==null?void 0:ce.scrollTop)||0}}})),ke.forEach(oe=>oe.unlock()),document.adoptedStyleSheets&&document.adoptedStyleSheets.length>0&&$.adoptStyleSheets(document.adoptedStyleSheets,pe.getId(document))};try{const O=[];O.push(ae("DOMContentLoaded",()=>{q(re({type:T.DomContentLoaded,data:{}}))}));const te=J=>{var de;return Uo({mutationCb:w,mousemoveCb:(j,ce)=>q(re({type:T.IncrementalSnapshot,data:{source:ce,positions:j}})),mouseInteractionCb:j=>q(re({type:T.IncrementalSnapshot,data:he({source:C.MouseInteraction},j)})),scrollCb:S,viewportResizeCb:j=>q(re({type:T.IncrementalSnapshot,data:he({source:C.ViewportResize},j)})),inputCb:j=>q(re({type:T.IncrementalSnapshot,data:he({source:C.Input},j)})),mediaInteractionCb:j=>q(re({type:T.IncrementalSnapshot,data:he({source:C.MediaInteraction},j)})),styleSheetRuleCb:j=>q(re({type:T.IncrementalSnapshot,data:he({source:C.StyleSheetRule},j)})),styleDeclarationCb:j=>q(re({type:T.IncrementalSnapshot,data:he({source:C.StyleDeclaration},j)})),canvasMutationCb:M,fontCb:j=>q(re({type:T.IncrementalSnapshot,data:he({source:C.Font},j)})),selectionCb:j=>{q(re({type:T.IncrementalSnapshot,data:he({source:C.Selection},j)}))},blockClass:o,ignoreClass:i,maskTextClass:l,maskTextSelector:a,maskInputOptions:L,inlineStylesheet:c,sampling:f,recordCanvas:N,inlineImages:E,userTriggeredOnInput:k,collectFonts:x,doc:J,maskInputFn:p,maskTextFn:m,keepIframeSrcFn:W,blockSelector:s,slimDOMOptions:U,dataURLOptions:g,mirror:pe,iframeManager:G,stylesheetManager:$,shadowDomManager:K,canvasManager:Ot,ignoreCSSAttributes:Z,plugins:((de=H?.filter(j=>j.observer))==null?void 0:de.map(j=>({observer:j.observer,options:j.options,callback:ce=>q(re({type:T.Plugin,data:{plugin:j.name,payload:ce}}))})))||[]},y)};G.addLoadListener(J=>{O.push(te(J.contentDocument))});const ne=()=>{at(),O.push(te(document)),lt=!0};return document.readyState==="interactive"||document.readyState==="complete"?ne():O.push(ae("load",()=>{q(re({type:T.Load,data:{}})),ne()},window)),()=>{O.forEach(J=>J()),lt=!1}}catch(O){console.warn(O)}}Re.addCustomEvent=(t,e)=>{if(!lt)throw new Error("please add custom event after start recording");q(re({type:T.Custom,data:{tag:t,payload:e}}))},Re.freezePage=()=>{ke.forEach(t=>t.freeze())},Re.takeFullSnapshot=t=>{if(!lt)throw new Error("please take full snapshot after start recording");at(t)},Re.mirror=pe;var _;(function(t){t[t.Document=0]="Document",t[t.DocumentType=1]="DocumentType",t[t.Element=2]="Element",t[t.Text=3]="Text",t[t.CDATA=4]="CDATA",t[t.Comment=5]="Comment"})(_||(_={}));var fi=function(){function t(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return t.prototype.getId=function(e){var r;if(!e)return-1;var n=(r=this.getMeta(e))===null||r===void 0?void 0:r.id;return n??-1},t.prototype.getNode=function(e){return this.idNodeMap.get(e)||null},t.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},t.prototype.getMeta=function(e){return this.nodeMetaMap.get(e)||null},t.prototype.removeNodeFromMap=function(e){var r=this,n=this.getId(e);this.idNodeMap.delete(n),e.childNodes&&e.childNodes.forEach(function(o){return r.removeNodeFromMap(o)})},t.prototype.has=function(e){return this.idNodeMap.has(e)},t.prototype.hasNode=function(e){return this.nodeMetaMap.has(e)},t.prototype.add=function(e,r){var n=r.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,r)},t.prototype.replace=function(e,r){var n=this.getNode(e);if(n){var o=this.nodeMetaMap.get(n);o&&this.nodeMetaMap.set(r,o)}this.idNodeMap.set(e,r)},t.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},t}();function gi(){return new fi}function yi(t){const e={},r=/;(?![^(]*\))/g,n=/:(.+)/,o=/\/\*.*?\*\//g;return t.replace(o,"").split(r).forEach(function(s){if(s){const i=s.split(n);i.length>1&&(e[xt(i[0].trim())]=i[1].trim())}}),e}function Kr(t){const e=[];for(const r in t){const n=t[r];if(typeof n!="string")continue;const o=wi(r);e.push(`${o}: ${n};`)}return e.join(" ")}const vi=/-([a-z])/g,bi=/^--[a-zA-Z0-9-]+$/,xt=t=>bi.test(t)?t:t.replace(vi,(e,r)=>r?r.toUpperCase():""),Si=/\B([A-Z])/g,wi=t=>t.replace(Si,"-$1").toLowerCase();class me{constructor(...e){this.childNodes=[],this.parentElement=null,this.parentNode=null,this.ELEMENT_NODE=Q.ELEMENT_NODE,this.TEXT_NODE=Q.TEXT_NODE}get firstChild(){return this.childNodes[0]||null}get lastChild(){return this.childNodes[this.childNodes.length-1]||null}get nextSibling(){const e=this.parentNode;if(!e)return null;const r=e.childNodes,n=r.indexOf(this);return r[n+1]||null}contains(e){if(e===this)return!0;for(const r of this.childNodes)if(r.contains(e))return!0;return!1}appendChild(e){throw new Error("RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.")}insertBefore(e,r){throw new Error("RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.")}removeChild(e){throw new Error("RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.")}toString(){return"RRNode"}}function Ni(t){return class Un extends t{constructor(){super(...arguments),this.nodeType=Q.DOCUMENT_NODE,this.nodeName="#document",this.compatMode="CSS1Compat",this.RRNodeType=_.Document,this.textContent=null}get documentElement(){return this.childNodes.find(r=>r.RRNodeType===_.Element&&r.tagName==="HTML")||null}get body(){var r;return((r=this.documentElement)===null||r===void 0?void 0:r.childNodes.find(n=>n.RRNodeType===_.Element&&n.tagName==="BODY"))||null}get head(){var r;return((r=this.documentElement)===null||r===void 0?void 0:r.childNodes.find(n=>n.RRNodeType===_.Element&&n.tagName==="HEAD"))||null}get implementation(){return this}get firstElementChild(){return this.documentElement}appendChild(r){const n=r.RRNodeType;if((n===_.Element||n===_.DocumentType)&&this.childNodes.some(o=>o.RRNodeType===n))throw new Error(`RRDomException: Failed to execute 'appendChild' on 'RRNode': Only one ${n===_.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);return r.parentElement=null,r.parentNode=this,this.childNodes.push(r),r}insertBefore(r,n){const o=r.RRNodeType;if((o===_.Element||o===_.DocumentType)&&this.childNodes.some(i=>i.RRNodeType===o))throw new Error(`RRDomException: Failed to execute 'insertBefore' on 'RRNode': Only one ${o===_.Element?"RRElement":"RRDoctype"} on RRDocument allowed.`);if(n===null)return this.appendChild(r);const s=this.childNodes.indexOf(n);if(s==-1)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(s,0,r),r.parentElement=null,r.parentNode=this,r}removeChild(r){const n=this.childNodes.indexOf(r);if(n===-1)throw new Error("Failed to execute 'removeChild' on 'RRDocument': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(n,1),r.parentElement=null,r.parentNode=null,r}open(){this.childNodes=[]}close(){}write(r){let n;if(r==='<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "">'?n="-//W3C//DTD XHTML 1.0 Transitional//EN":r==='<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.0 Transitional//EN" "">'&&(n="-//W3C//DTD HTML 4.0 Transitional//EN"),n){const o=this.createDocumentType("html",n,"");this.open(),this.appendChild(o)}}createDocument(r,n,o){return new Un}createDocumentType(r,n,o){const s=new(Jr(me))(r,n,o);return s.ownerDocument=this,s}createElement(r){const n=new(Qr(me))(r);return n.ownerDocument=this,n}createElementNS(r,n){return this.createElement(n)}createTextNode(r){const n=new(qr(me))(r);return n.ownerDocument=this,n}createComment(r){const n=new(en(me))(r);return n.ownerDocument=this,n}createCDATASection(r){const n=new(tn(me))(r);return n.ownerDocument=this,n}toString(){return"RRDocument"}}}function Jr(t){return class extends t{constructor(e,r,n){super(),this.nodeType=Q.DOCUMENT_TYPE_NODE,this.RRNodeType=_.DocumentType,this.textContent=null,this.name=e,this.publicId=r,this.systemId=n,this.nodeName=e}toString(){return"RRDocumentType"}}}function Qr(t){return class extends t{constructor(e){super(),this.nodeType=Q.ELEMENT_NODE,this.RRNodeType=_.Element,this.attributes={},this.shadowRoot=null,this.tagName=e.toUpperCase(),this.nodeName=e.toUpperCase()}get textContent(){let e="";return this.childNodes.forEach(r=>e+=r.textContent),e}set textContent(e){this.childNodes=[this.ownerDocument.createTextNode(e)]}get classList(){return new Ci(this.attributes.class,e=>{this.attributes.class=e})}get id(){return this.attributes.id||""}get className(){return this.attributes.class||""}get style(){const e=this.attributes.style?yi(this.attributes.style):{},r=/\B([A-Z])/g;return e.setProperty=(n,o,s)=>{if(r.test(n))return;const i=xt(n);o?e[i]=o:delete e[i],s==="important"&&(e[i]+=" !important"),this.attributes.style=Kr(e)},e.removeProperty=n=>{if(r.test(n))return"";const o=xt(n),s=e[o]||"";return delete e[o],this.attributes.style=Kr(e),s},e}getAttribute(e){return this.attributes[e]||null}setAttribute(e,r){this.attributes[e]=r}setAttributeNS(e,r,n){this.setAttribute(r,n)}removeAttribute(e){delete this.attributes[e]}appendChild(e){return this.childNodes.push(e),e.parentNode=this,e.parentElement=this,e}insertBefore(e,r){if(r===null)return this.appendChild(e);const n=this.childNodes.indexOf(r);if(n==-1)throw new Error("Failed to execute 'insertBefore' on 'RRNode': The RRNode before which the new node is to be inserted is not a child of this RRNode.");return this.childNodes.splice(n,0,e),e.parentElement=this,e.parentNode=this,e}removeChild(e){const r=this.childNodes.indexOf(e);if(r===-1)throw new Error("Failed to execute 'removeChild' on 'RRElement': The RRNode to be removed is not a child of this RRNode.");return this.childNodes.splice(r,1),e.parentElement=null,e.parentNode=null,e}attachShadow(e){const r=this.ownerDocument.createElement("SHADOWROOT");return this.shadowRoot=r,r}dispatchEvent(e){return!0}toString(){let e="";for(const r in this.attributes)e+=`${r}="${this.attributes[r]}" `;return`${this.tagName} ${e}`}}}function Ei(t){return class extends t{attachShadow(e){throw new Error("RRDomException: Failed to execute 'attachShadow' on 'RRElement': This RRElement does not support attachShadow")}play(){this.paused=!1}pause(){this.paused=!0}}}function qr(t){return class extends t{constructor(e){super(),this.nodeType=Q.TEXT_NODE,this.nodeName="#text",this.RRNodeType=_.Text,this.data=e}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRText text=${JSON.stringify(this.data)}`}}}function en(t){return class extends t{constructor(e){super(),this.nodeType=Q.COMMENT_NODE,this.nodeName="#comment",this.RRNodeType=_.Comment,this.data=e}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRComment text=${JSON.stringify(this.data)}`}}}function tn(t){return class extends t{constructor(e){super(),this.nodeName="#cdata-section",this.nodeType=Q.CDATA_SECTION_NODE,this.RRNodeType=_.CDATA,this.data=e}get textContent(){return this.data}set textContent(e){this.data=e}toString(){return`RRCDATASection data=${JSON.stringify(this.data)}`}}}class Ci{constructor(e,r){if(this.classes=[],this.add=(...n)=>{for(const o of n){const s=String(o);this.classes.indexOf(s)>=0||this.classes.push(s)}this.onChange&&this.onChange(this.classes.join(" "))},this.remove=(...n)=>{this.classes=this.classes.filter(o=>n.indexOf(o)===-1),this.onChange&&this.onChange(this.classes.join(" "))},e){const n=e.trim().split(/\s+/);this.classes.push(...n)}this.onChange=r}}var Q;(function(t){t[t.PLACEHOLDER=0]="PLACEHOLDER",t[t.ELEMENT_NODE=1]="ELEMENT_NODE",t[t.ATTRIBUTE_NODE=2]="ATTRIBUTE_NODE",t[t.TEXT_NODE=3]="TEXT_NODE",t[t.CDATA_SECTION_NODE=4]="CDATA_SECTION_NODE",t[t.ENTITY_REFERENCE_NODE=5]="ENTITY_REFERENCE_NODE",t[t.ENTITY_NODE=6]="ENTITY_NODE",t[t.PROCESSING_INSTRUCTION_NODE=7]="PROCESSING_INSTRUCTION_NODE",t[t.COMMENT_NODE=8]="COMMENT_NODE",t[t.DOCUMENT_NODE=9]="DOCUMENT_NODE",t[t.DOCUMENT_TYPE_NODE=10]="DOCUMENT_TYPE_NODE",t[t.DOCUMENT_FRAGMENT_NODE=11]="DOCUMENT_FRAGMENT_NODE"})(Q||(Q={}));const Lt={svg:"http://www.w3.org/2000/svg","xlink:href":"http://www.w3.org/1999/xlink",xmlns:"http://www.w3.org/2000/xmlns/"},Ti={altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",fedropshadow:"feDropShadow",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient"};function ge(t,e,r,n){const o=t.childNodes,s=e.childNodes;n=n||e.mirror||e.ownerDocument.mirror,(o.length>0||s.length>0)&&rn(Array.from(o),s,t,r,n);let i=null,l=null;switch(e.RRNodeType){case _.Document:{l=e.scrollData;break}case _.Element:{const a=t,c=e;switch(Mi(a,c,n),l=c.scrollData,i=c.inputData,c.tagName){case"AUDIO":case"VIDEO":{const u=t,d=c;d.paused!==void 0&&(d.paused?u.pause():u.play()),d.muted!==void 0&&(u.muted=d.muted),d.volume!==void 0&&(u.volume=d.volume),d.currentTime!==void 0&&(u.currentTime=d.currentTime),d.playbackRate!==void 0&&(u.playbackRate=d.playbackRate);break}case"CANVAS":{const u=e;if(u.rr_dataURL!==null){const d=document.createElement("img");d.onload=()=>{const h=a.getContext("2d");h&&h.drawImage(d,0,0,d.width,d.height)},d.src=u.rr_dataURL}u.canvasMutations.forEach(d=>r.applyCanvas(d.event,d.mutation,t))}break;case"STYLE":{const u=a.sheet;u&&e.rules.forEach(d=>r.applyStyleSheetMutation(d,u))}break}if(c.shadowRoot){a.shadowRoot||a.attachShadow({mode:"open"});const u=a.shadowRoot.childNodes,d=c.shadowRoot.childNodes;(u.length>0||d.length>0)&&rn(Array.from(u),d,a.shadowRoot,r,n)}break}case _.Text:case _.Comment:case _.CDATA:t.textContent!==e.data&&(t.textContent=e.data);break}if(l&&r.applyScroll(l,!0),i&&r.applyInput(i),e.nodeName==="IFRAME"){const a=t.contentDocument,c=e;if(a){const u=n.getMeta(c.contentDocument);u&&r.mirror.add(a,Object.assign({},u)),ge(a,c.contentDocument,r,n)}}}function Mi(t,e,r){const n=t.attributes,o=e.attributes;for(const s in o){const i=o[s],l=r.getMeta(e);if(l&&"isSVG"in l&&l.isSVG&&Lt[s])t.setAttributeNS(Lt[s],s,i);else if(e.tagName==="CANVAS"&&s==="rr_dataURL"){const a=document.createElement("img");a.src=i,a.onload=()=>{const c=t.getContext("2d");c&&c.drawImage(a,0,0,a.width,a.height)}}else t.setAttribute(s,i)}for(const{name:s}of Array.from(n))s in o||t.removeAttribute(s);e.scrollLeft&&(t.scrollLeft=e.scrollLeft),e.scrollTop&&(t.scrollTop=e.scrollTop)}function rn(t,e,r,n,o){var s;let i=0,l=t.length-1,a=0,c=e.length-1,u=t[i],d=t[l],h=e[a],p=e[c],m,y;for(;i<=l&&a<=c;){const v=n.mirror.getId(u),f=n.mirror.getId(d),g=o.getId(h),b=o.getId(p);if(u===void 0)u=t[++i];else if(d===void 0)d=t[--l];else if(v!==-1&&v===g)ge(u,h,n,o),u=t[++i],h=e[++a];else if(f!==-1&&f===b)ge(d,p,n,o),d=t[--l],p=e[--c];else if(v!==-1&&v===b)r.insertBefore(u,d.nextSibling),ge(u,p,n,o),u=t[++i],p=e[--c];else if(f!==-1&&f===g)r.insertBefore(d,u),ge(d,h,n,o),d=t[--l],h=e[++a];else{if(!m){m={};for(let N=i;N<=l;N++){const R=t[N];R&&n.mirror.hasNode(R)&&(m[n.mirror.getId(R)]=N)}}if(y=m[o.getId(h)],y){const N=t[y];r.insertBefore(N,u),ge(N,h,n,o),t[y]=void 0}else{const N=At(h,n.mirror,o);r.nodeName==="#document"&&((s=n.mirror.getMeta(N))===null||s===void 0?void 0:s.type)===_.Element&&r.documentElement&&(r.removeChild(r.documentElement),t[i]=void 0,u=void 0),r.insertBefore(N,u||null),ge(N,h,n,o)}h=e[++a]}}if(i>l){const v=e[c+1];let f=null;for(v&&r.childNodes.forEach(g=>{n.mirror.getId(g)===o.getId(v)&&(f=g)});a<=c;++a){const g=At(e[a],n.mirror,o);r.insertBefore(g,f),ge(g,e[a],n,o)}}else if(a>c)for(;i<=l;i++){const v=t[i];v&&(r.removeChild(v),n.mirror.removeNodeFromMap(v))}}function At(t,e,r){const n=r.getId(t),o=r.getMeta(t);let s=null;if(n>-1&&(s=e.getNode(n)),s!==null)return s;switch(t.RRNodeType){case _.Document:s=new Document;break;case _.DocumentType:s=document.implementation.createDocumentType(t.name,t.publicId,t.systemId);break;case _.Element:{let i=t.tagName.toLowerCase();i=Ti[i]||i,o&&"isSVG"in o&&o?.isSVG?s=document.createElementNS(Lt.svg,i):s=document.createElement(t.tagName);break}case _.Text:s=document.createTextNode(t.data);break;case _.Comment:s=document.createComment(t.data);break;case _.CDATA:s=document.createCDATASection(t.data);break}return o&&e.add(s,Object.assign({},o)),s}class Pe extends Ni(me){constructor(e){super(),this.UNSERIALIZED_STARTING_ID=-2,this._unserializedId=this.UNSERIALIZED_STARTING_ID,this.mirror=Pi(),this.scrollData=null,e&&(this.mirror=e)}get unserializedId(){return this._unserializedId--}createDocument(e,r,n){return new Pe}createDocumentType(e,r,n){const o=new Ii(e,r,n);return o.ownerDocument=this,o}createElement(e){const r=e.toUpperCase();let n;switch(r){case"AUDIO":case"VIDEO":n=new ki(r);break;case"IFRAME":n=new Oi(r,this.mirror);break;case"CANVAS":n=new Ri(r);break;case"STYLE":n=new Di(r);break;default:n=new Xe(r);break}return n.ownerDocument=this,n}createComment(e){const r=new Li(e);return r.ownerDocument=this,r}createCDATASection(e){const r=new Ai(e);return r.ownerDocument=this,r}createTextNode(e){const r=new xi(e);return r.ownerDocument=this,r}destroyTree(){this.childNodes=[],this.mirror.reset()}open(){super.open(),this._unserializedId=this.UNSERIALIZED_STARTING_ID}}const Ii=Jr(me);class Xe extends Qr(me){constructor(){super(...arguments),this.inputData=null,this.scrollData=null}}class ki extends Ei(Xe){}class Ri extends Xe{constructor(){super(...arguments),this.rr_dataURL=null,this.canvasMutations=[]}getContext(){return null}}class Di extends Xe{constructor(){super(...arguments),this.rules=[]}}class Oi extends Xe{constructor(e,r){super(e),this.contentDocument=new Pe,this.contentDocument.mirror=r}}const xi=qr(me),Li=en(me),Ai=tn(me);function Fi(t){return t instanceof HTMLFormElement?"FORM":t.tagName.toUpperCase()}function nn(t,e,r,n){let o;switch(t.nodeType){case Q.DOCUMENT_NODE:n&&n.nodeName==="IFRAME"?o=n.contentDocument:(o=e,o.compatMode=t.compatMode);break;case Q.DOCUMENT_TYPE_NODE:{const i=t;o=e.createDocumentType(i.name,i.publicId,i.systemId);break}case Q.ELEMENT_NODE:{const i=t,l=Fi(i);o=e.createElement(l);const a=o;for(const{name:c,value:u}of Array.from(i.attributes))a.attributes[c]=u;i.scrollLeft&&(a.scrollLeft=i.scrollLeft),i.scrollTop&&(a.scrollTop=i.scrollTop);break}case Q.TEXT_NODE:o=e.createTextNode(t.textContent||"");break;case Q.CDATA_SECTION_NODE:o=e.createCDATASection(t.data);break;case Q.COMMENT_NODE:o=e.createComment(t.textContent||"");break;case Q.DOCUMENT_FRAGMENT_NODE:o=n.attachShadow({mode:"open"});break;default:return null}let s=r.getMeta(t);return e instanceof Pe&&(s||(s=on(o,e.unserializedId),r.add(t,s)),e.mirror.add(o,Object.assign({},s))),o}function _i(t,e=gi(),r=new Pe){function n(o,s){const i=nn(o,r,e,s);if(i!==null)if(s?.nodeName!=="IFRAME"&&o.nodeType!==Q.DOCUMENT_FRAGMENT_NODE&&(s?.appendChild(i),i.parentNode=s,i.parentElement=s),o.nodeName==="IFRAME"){const l=o.contentDocument;l&&n(l,i)}else(o.nodeType===Q.DOCUMENT_NODE||o.nodeType===Q.ELEMENT_NODE||o.nodeType===Q.DOCUMENT_FRAGMENT_NODE)&&(o.nodeType===Q.ELEMENT_NODE&&o.shadowRoot&&n(o.shadowRoot,i),o.childNodes.forEach(l=>n(l,i)))}return n(t,null),r}function Pi(){return new $i}class $i{constructor(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}getId(e){var r;if(!e)return-1;const n=(r=this.getMeta(e))===null||r===void 0?void 0:r.id;return n??-1}getNode(e){return this.idNodeMap.get(e)||null}getIds(){return Array.from(this.idNodeMap.keys())}getMeta(e){return this.nodeMetaMap.get(e)||null}removeNodeFromMap(e){const r=this.getId(e);this.idNodeMap.delete(r),e.childNodes&&e.childNodes.forEach(n=>this.removeNodeFromMap(n))}has(e){return this.idNodeMap.has(e)}hasNode(e){return this.nodeMetaMap.has(e)}add(e,r){const n=r.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,r)}replace(e,r){const n=this.getNode(e);if(n){const o=this.nodeMetaMap.get(n);o&&this.nodeMetaMap.set(r,o)}this.idNodeMap.set(e,r)}reset(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}}function on(t,e){switch(t.RRNodeType){case _.Document:return{id:e,type:t.RRNodeType,childNodes:[]};case _.DocumentType:{const r=t;return{id:e,type:t.RRNodeType,name:r.name,publicId:r.publicId,systemId:r.systemId}}case _.Element:return{id:e,type:t.RRNodeType,tagName:t.tagName.toLowerCase(),attributes:{},childNodes:[]};case _.Text:return{id:e,type:t.RRNodeType,textContent:t.textContent||""};case _.Comment:return{id:e,type:t.RRNodeType,textContent:t.textContent||""};case _.CDATA:return{id:e,type:t.RRNodeType,textContent:""}}}function sn(t){return{all:t=t||new Map,on:function(e,r){var n=t.get(e);n?n.push(r):t.set(e,[r])},off:function(e,r){var n=t.get(e);n&&(r?n.splice(n.indexOf(r)>>>0,1):t.set(e,[]))},emit:function(e,r){var n=t.get(e);n&&n.slice().map(function(o){o(r)}),(n=t.get("*"))&&n.slice().map(function(o){o(e,r)})}}}var Wi=Object.freeze({__proto__:null,default:sn});function Ui(t=window,e=document){if("scrollBehavior"in e.documentElement.style&&t.__forceSmoothScrollPolyfill__!==!0)return;const r=t.HTMLElement||t.Element,n=468,o={scroll:t.scroll||t.scrollTo,scrollBy:t.scrollBy,elementScroll:r.prototype.scroll||a,scrollIntoView:r.prototype.scrollIntoView},s=t.performance&&t.performance.now?t.performance.now.bind(t.performance):Date.now;function i(f){const g=["MSIE ","Trident/","Edge/"];return new RegExp(g.join("|")).test(f)}const l=i(t.navigator.userAgent)?1:0;function a(f,g){this.scrollLeft=f,this.scrollTop=g}function c(f){return .5*(1-Math.cos(Math.PI*f))}function u(f){if(f===null||typeof f!="object"||f.behavior===void 0||f.behavior==="auto"||f.behavior==="instant")return!0;if(typeof f=="object"&&f.behavior==="smooth")return!1;throw new TypeError("behavior member of ScrollOptions "+f.behavior+" is not a valid value for enumeration ScrollBehavior.")}function d(f,g){if(g==="Y")return f.clientHeight+l<f.scrollHeight;if(g==="X")return f.clientWidth+l<f.scrollWidth}function h(f,g){const b=t.getComputedStyle(f,null)["overflow"+g];return b==="auto"||b==="scroll"}function p(f){const g=d(f,"Y")&&h(f,"Y"),b=d(f,"X")&&h(f,"X");return g||b}function m(f){for(;f!==e.body&&p(f)===!1;)f=f.parentNode||f.host;return f}function y(f){const g=s();let b,N,R,k=(g-f.startTime)/n;k=k>1?1:k,b=c(k),N=f.startX+(f.x-f.startX)*b,R=f.startY+(f.y-f.startY)*b,f.method.call(f.scrollable,N,R),(N!==f.x||R!==f.y)&&t.requestAnimationFrame(y.bind(t,f))}function v(f,g,b){let N,R,k,x;const E=s();f===e.body?(N=t,R=t.scrollX||t.pageXOffset,k=t.scrollY||t.pageYOffset,x=o.scroll):(N=f,R=f.scrollLeft,k=f.scrollTop,x=a),y({scrollable:N,method:x,startTime:E,startX:R,startY:k,x:g,y:b})}t.scroll=t.scrollTo=function(){if(arguments[0]!==void 0){if(u(arguments[0])===!0){o.scroll.call(t,arguments[0].left!==void 0?arguments[0].left:typeof arguments[0]!="object"?arguments[0]:t.scrollX||t.pageXOffset,arguments[0].top!==void 0?arguments[0].top:arguments[1]!==void 0?arguments[1]:t.scrollY||t.pageYOffset);return}v.call(t,e.body,arguments[0].left!==void 0?~~arguments[0].left:t.scrollX||t.pageXOffset,arguments[0].top!==void 0?~~arguments[0].top:t.scrollY||t.pageYOffset)}},t.scrollBy=function(){if(arguments[0]!==void 0){if(u(arguments[0])){o.scrollBy.call(t,arguments[0].left!==void 0?arguments[0].left:typeof arguments[0]!="object"?arguments[0]:0,arguments[0].top!==void 0?arguments[0].top:arguments[1]!==void 0?arguments[1]:0);return}v.call(t,e.body,~~arguments[0].left+(t.scrollX||t.pageXOffset),~~arguments[0].top+(t.scrollY||t.pageYOffset))}},r.prototype.scroll=r.prototype.scrollTo=function(){if(arguments[0]===void 0)return;if(u(arguments[0])===!0){if(typeof arguments[0]=="number"&&arguments[1]===void 0)throw new SyntaxError("Value could not be converted");o.elementScroll.call(this,arguments[0].left!==void 0?~~arguments[0].left:typeof arguments[0]!="object"?~~arguments[0]:this.scrollLeft,arguments[0].top!==void 0?~~arguments[0].top:arguments[1]!==void 0?~~arguments[1]:this.scrollTop);return}const f=arguments[0].left,g=arguments[0].top;v.call(this,this,typeof f>"u"?this.scrollLeft:~~f,typeof g>"u"?this.scrollTop:~~g)},r.prototype.scrollBy=function(){if(arguments[0]!==void 0){if(u(arguments[0])===!0){o.elementScroll.call(this,arguments[0].left!==void 0?~~arguments[0].left+this.scrollLeft:~~arguments[0]+this.scrollLeft,arguments[0].top!==void 0?~~arguments[0].top+this.scrollTop:~~arguments[1]+this.scrollTop);return}this.scroll({left:~~arguments[0].left+this.scrollLeft,top:~~arguments[0].top+this.scrollTop,behavior:arguments[0].behavior})}},r.prototype.scrollIntoView=function(){if(u(arguments[0])===!0){o.scrollIntoView.call(this,arguments[0]===void 0?!0:arguments[0]);return}const f=m(this),g=f.getBoundingClientRect(),b=this.getBoundingClientRect();f!==e.body?(v.call(this,f,f.scrollLeft+b.left-g.left,f.scrollTop+b.top-g.top),t.getComputedStyle(f).position!=="fixed"&&t.scrollBy({left:g.left,top:g.top,behavior:"smooth"})):t.scrollBy({left:b.left,top:b.top,behavior:"smooth"})}}class ji{constructor(e=[],r){this.timeOffset=0,this.raf=null,this.actions=e,this.speed=r.speed,this.liveMode=r.liveMode}addAction(e){if(!this.actions.length||this.actions[this.actions.length-1].delay<=e.delay){this.actions.push(e);return}const r=this.findActionIndex(e);this.actions.splice(r,0,e)}start(){this.timeOffset=0;let e=performance.now();const r=()=>{const n=performance.now();for(this.timeOffset+=(n-e)*this.speed,e=n;this.actions.length;){const o=this.actions[0];if(this.timeOffset>=o.delay)this.actions.shift(),o.doAction();else break}(this.actions.length>0||this.liveMode)&&(this.raf=requestAnimationFrame(r))};this.raf=requestAnimationFrame(r)}clear(){this.raf&&(cancelAnimationFrame(this.raf),this.raf=null),this.actions.length=0}setSpeed(e){this.speed=e}toggleLiveMode(e){this.liveMode=e}isActive(){return this.raf!==null}findActionIndex(e){let r=0,n=this.actions.length-1;for(;r<=n;){const o=Math.floor((r+n)/2);if(this.actions[o].delay<e.delay)r=o+1;else if(this.actions[o].delay>e.delay)n=o-1;else return o+1}return r}}function an(t,e){if(t.type===T.IncrementalSnapshot&&t.data.source===C.MouseMove&&t.data.positions&&t.data.positions.length){const r=t.data.positions[0].timeOffset,n=t.timestamp+r;return t.delay=n-e,n-e}return t.delay=t.timestamp-e,t.delay}/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */function ln(t,e){var r=typeof Symbol=="function"&&t[Symbol.iterator];if(!r)return t;var n,o,s=r.call(t),i=[];try{for(;(e===void 0||e-- >0)&&!(n=s.next()).done;)i.push(n.value)}catch(l){o={error:l}}finally{try{n&&!n.done&&(r=s.return)&&r.call(s)}finally{if(o)throw o.error}}return i}var $e;(function(t){t[t.NotStarted=0]="NotStarted",t[t.Running=1]="Running",t[t.Stopped=2]="Stopped"})($e||($e={}));var cn={type:"xstate.init"};function Ft(t){return t===void 0?[]:[].concat(t)}function We(t){return{type:"xstate.assign",assignment:t}}function un(t,e){return typeof(t=typeof t=="string"&&e&&e[t]?e[t]:t)=="string"?{type:t}:typeof t=="function"?{type:t.name,exec:t}:t}function ct(t){return function(e){return t===e}}function dn(t){return typeof t=="string"?{type:t}:t}function hn(t,e){return{value:t,context:e,actions:[],changed:!1,matches:ct(t)}}function pn(t,e,r){var n=e,o=!1;return[t.filter(function(s){if(s.type==="xstate.assign"){o=!0;var i=Object.assign({},n);return typeof s.assignment=="function"?i=s.assignment(n,r):Object.keys(s.assignment).forEach(function(l){i[l]=typeof s.assignment[l]=="function"?s.assignment[l](n,r):s.assignment[l]}),n=i,!1}return!0}),n,o]}function mn(t,e){e===void 0&&(e={});var r=ln(pn(Ft(t.states[t.initial].entry).map(function(i){return un(i,e.actions)}),t.context,cn),2),n=r[0],o=r[1],s={config:t,_options:e,initialState:{value:t.initial,actions:n,context:o,matches:ct(t.initial)},transition:function(i,l){var a,c,u=typeof i=="string"?{value:i,context:t.context}:i,d=u.value,h=u.context,p=dn(l),m=t.states[d];if(m.on){var y=Ft(m.on[p.type]);try{for(var v=function(D){var z=typeof Symbol=="function"&&Symbol.iterator,w=z&&D[z],S=0;if(w)return w.call(D);if(D&&typeof D.length=="number")return{next:function(){return D&&S>=D.length&&(D=void 0),{value:D&&D[S++],done:!D}}};throw new TypeError(z?"Object is not iterable.":"Symbol.iterator is not defined.")}(y),f=v.next();!f.done;f=v.next()){var g=f.value;if(g===void 0)return hn(d,h);var b=typeof g=="string"?{target:g}:g,N=b.target,R=b.actions,k=R===void 0?[]:R,x=b.cond,E=x===void 0?function(){return!0}:x,H=N===void 0,W=N??d,Z=t.states[W];if(E(h,p)){var P=ln(pn((H?Ft(k):[].concat(m.exit,k,Z.entry).filter(function(D){return D})).map(function(D){return un(D,s._options.actions)}),h,p),3),B=P[0],L=P[1],U=P[2],A=N??d;return{value:A,context:L,actions:B,changed:N!==d||B.length>0||U,matches:ct(A)}}}}catch(D){a={error:D}}finally{try{f&&!f.done&&(c=v.return)&&c.call(v)}finally{if(a)throw a.error}}}return hn(d,h)}};return s}var fn=function(t,e){return t.actions.forEach(function(r){var n=r.exec;return n&&n(t.context,e)})};function gn(t){var e=t.initialState,r=$e.NotStarted,n=new Set,o={_machine:t,send:function(s){r===$e.Running&&(e=t.transition(e,s),fn(e,dn(s)),n.forEach(function(i){return i(e)}))},subscribe:function(s){return n.add(s),s(e),{unsubscribe:function(){return n.delete(s)}}},start:function(s){if(s){var i=typeof s=="object"?s:{context:t.config.context,value:s};e={value:i.value,actions:[],context:i.context,matches:ct(i.value)}}return r=$e.Running,fn(e,cn),o},stop:function(){return r=$e.Stopped,n.clear(),o},get state(){return e},get status(){return r}};return o}var Vi=Object.defineProperty,Bi=Object.defineProperties,Gi=Object.getOwnPropertyDescriptors,yn=Object.getOwnPropertySymbols,zi=Object.prototype.hasOwnProperty,Hi=Object.prototype.propertyIsEnumerable,vn=(t,e,r)=>e in t?Vi(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,_t=(t,e)=>{for(var r in e||(e={}))zi.call(e,r)&&vn(t,r,e[r]);if(yn)for(var r of yn(e))Hi.call(e,r)&&vn(t,r,e[r]);return t},Pt=(t,e)=>Bi(t,Gi(e));function Yi(t,e){for(let r=t.length-1;r>=0;r--){const n=t[r];if(n.type===T.Meta&&n.timestamp<=e)return t.slice(r)}return t}function Xi(t,{getCastFn:e,applyEventsSynchronously:r,emitter:n}){const o=mn({id:"player",context:t,initial:"paused",states:{playing:{on:{PAUSE:{target:"paused",actions:["pause"]},CAST_EVENT:{target:"playing",actions:"castEvent"},END:{target:"paused",actions:["resetLastPlayedEvent","pause"]},ADD_EVENT:{target:"playing",actions:["addEvent"]}}},paused:{on:{PLAY:{target:"playing",actions:["recordTimeOffset","play"]},CAST_EVENT:{target:"paused",actions:"castEvent"},TO_LIVE:{target:"live",actions:["startLive"]},ADD_EVENT:{target:"paused",actions:["addEvent"]}}},live:{on:{ADD_EVENT:{target:"live",actions:["addEvent"]},CAST_EVENT:{target:"live",actions:["castEvent"]}}}}},{actions:{castEvent:We({lastPlayedEvent:(s,i)=>i.type==="CAST_EVENT"?i.payload.event:s.lastPlayedEvent}),recordTimeOffset:We((s,i)=>{let l=s.timeOffset;return"payload"in i&&"timeOffset"in i.payload&&(l=i.payload.timeOffset),Pt(_t({},s),{timeOffset:l,baselineTime:s.events[0].timestamp+l})}),play(s){var i;const{timer:l,events:a,baselineTime:c,lastPlayedEvent:u}=s;l.clear();for(const m of a)an(m,c);const d=Yi(a,c);let h=u?.timestamp;u?.type===T.IncrementalSnapshot&&u.data.source===C.MouseMove&&(h=u.timestamp+((i=u.data.positions[0])==null?void 0:i.timeOffset)),c<(h||0)&&n.emit(V.PlayBack);const p=new Array;for(const m of d)if(!(h&&h<c&&(m.timestamp<=h||m===u)))if(m.timestamp<c)p.push(m);else{const y=e(m,!1);l.addAction({doAction:()=>{y()},delay:m.delay})}r(p),n.emit(V.Flush),l.start()},pause(s){s.timer.clear()},resetLastPlayedEvent:We(s=>Pt(_t({},s),{lastPlayedEvent:null})),startLive:We({baselineTime:(s,i)=>(s.timer.toggleLiveMode(!0),s.timer.start(),i.type==="TO_LIVE"&&i.payload.baselineTime?i.payload.baselineTime:Date.now())}),addEvent:We((s,i)=>{const{baselineTime:l,timer:a,events:c}=s;if(i.type==="ADD_EVENT"){const{event:u}=i.payload;an(u,l);let d=c.length-1;if(!c[d]||c[d].timestamp<=u.timestamp)c.push(u);else{let m=-1,y=0;for(;y<=d;){const v=Math.floor((y+d)/2);c[v].timestamp<=u.timestamp?y=v+1:d=v-1}m===-1&&(m=y),c.splice(m,0,u)}const h=u.timestamp<l,p=e(u,h);h?p():a.isActive()&&a.addAction({doAction:()=>{p()},delay:u.delay})}return Pt(_t({},s),{events:c})})}});return gn(o)}function Zi(t){const e=mn({id:"speed",context:t,initial:"normal",states:{normal:{on:{FAST_FORWARD:{target:"skipping",actions:["recordSpeed","setSpeed"]},SET_SPEED:{target:"normal",actions:["setSpeed"]}}},skipping:{on:{BACK_TO_NORMAL:{target:"normal",actions:["restoreSpeed"]},SET_SPEED:{target:"normal",actions:["setSpeed"]}}}}},{actions:{setSpeed:(r,n)=>{"payload"in n&&r.timer.setSpeed(n.payload.speed)},recordSpeed:We({normalSpeed:r=>r.timer.speed}),restoreSpeed:r=>{r.timer.setSpeed(r.normalSpeed)}}});return gn(e)}const Ki=t=>[`.${t} { background: currentColor }`,"noscript { display: none !important; }"];var Ji=(t,e,r)=>new Promise((n,o)=>{var s=a=>{try{l(r.next(a))}catch(c){o(c)}},i=a=>{try{l(r.throw(a))}catch(c){o(c)}},l=a=>a.done?n(a.value):Promise.resolve(a.value).then(s,i);l((r=r.apply(t,e)).next())});const bn=new Map;function Sn(t,e){let r=bn.get(t);return r||(r=new Map,bn.set(t,r)),r.has(e)||r.set(e,[]),r.get(e)}function Ce(t,e,r){return n=>Ji(this,null,function*(){if(n&&typeof n=="object"&&"rr_type"in n)if(r&&(r.isUnchanged=!1),n.rr_type==="ImageBitmap"&&"args"in n){const o=yield Ce(t,e,r)(n.args);return yield createImageBitmap.apply(null,o)}else if("index"in n){if(r||e===null)return n;const{rr_type:o,index:s}=n;return Sn(e,o)[s]}else if("args"in n){const{rr_type:o,args:s}=n,i=window[o];return new i(...yield Promise.all(s.map(Ce(t,e,r))))}else{if("base64"in n)return Zo(n.base64);if("src"in n){const o=t.get(n.src);if(o)return o;{const s=new Image;return s.src=n.src,t.set(n.src,s),s}}else if("data"in n&&n.rr_type==="Blob"){const o=yield Promise.all(n.data.map(Ce(t,e,r)));return new Blob(o,{type:n.type})}}else if(Array.isArray(n))return yield Promise.all(n.map(Ce(t,e,r)));return n})}var Qi=(t,e,r)=>new Promise((n,o)=>{var s=a=>{try{l(r.next(a))}catch(c){o(c)}},i=a=>{try{l(r.throw(a))}catch(c){o(c)}},l=a=>a.done?n(a.value):Promise.resolve(a.value).then(s,i);l((r=r.apply(t,e)).next())});function qi(t,e){try{return e===be.WebGL?t.getContext("webgl")||t.getContext("experimental-webgl"):t.getContext("webgl2")}catch{return null}}const es=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject"];function ts(t,e){if(!(e!=null&&e.constructor))return;const{name:r}=e.constructor;if(!es.includes(r))return;const n=Sn(t,r);n.includes(e)||n.push(e)}function rs(t){return Qi(this,arguments,function*({mutation:e,target:r,type:n,imageMap:o,errorHandler:s}){try{const i=qi(r,n);if(!i)return;if(e.setter){i[e.property]=e.args[0];return}const l=i[e.property],a=yield Promise.all(e.args.map(Ce(o,i))),c=l.apply(i,a);ts(i,c)}catch(i){s(e,i)}})}var ns=(t,e,r)=>new Promise((n,o)=>{var s=a=>{try{l(r.next(a))}catch(c){o(c)}},i=a=>{try{l(r.throw(a))}catch(c){o(c)}},l=a=>a.done?n(a.value):Promise.resolve(a.value).then(s,i);l((r=r.apply(t,e)).next())});function os(t){return ns(this,arguments,function*({event:e,mutation:r,target:n,imageMap:o,errorHandler:s}){try{const i=n.getContext("2d");if(r.setter){i[r.property]=r.args[0];return}const l=i[r.property];if(r.property==="drawImage"&&typeof r.args[0]=="string")o.get(e),l.apply(i,r.args);else{const a=yield Promise.all(r.args.map(Ce(o,i)));l.apply(i,a)}}catch(i){s(r,i)}})}var is=(t,e,r)=>new Promise((n,o)=>{var s=a=>{try{l(r.next(a))}catch(c){o(c)}},i=a=>{try{l(r.throw(a))}catch(c){o(c)}},l=a=>a.done?n(a.value):Promise.resolve(a.value).then(s,i);l((r=r.apply(t,e)).next())});function wn(t){return is(this,arguments,function*({event:e,mutation:r,target:n,imageMap:o,canvasEventMap:s,errorHandler:i}){try{const l=s.get(e)||r,a="commands"in l?l.commands:[l];if([be.WebGL,be.WebGL2].includes(r.type)){for(let c=0;c<a.length;c++){const u=a[c];yield rs({mutation:u,type:r.type,target:n,imageMap:o,errorHandler:i})}return}for(let c=0;c<a.length;c++){const u=a[c];yield os({event:e,mutation:u,target:n,imageMap:o,errorHandler:i})}}catch(l){i(r,l)}})}var ss=Object.defineProperty,as=Object.defineProperties,ls=Object.getOwnPropertyDescriptors,Nn=Object.getOwnPropertySymbols,cs=Object.prototype.hasOwnProperty,us=Object.prototype.propertyIsEnumerable,En=(t,e,r)=>e in t?ss(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,ut=(t,e)=>{for(var r in e||(e={}))cs.call(e,r)&&En(t,r,e[r]);if(Nn)for(var r of Nn(e))us.call(e,r)&&En(t,r,e[r]);return t},$t=(t,e)=>as(t,ls(e)),Wt=(t,e,r)=>new Promise((n,o)=>{var s=a=>{try{l(r.next(a))}catch(c){o(c)}},i=a=>{try{l(r.throw(a))}catch(c){o(c)}},l=a=>a.done?n(a.value):Promise.resolve(a.value).then(s,i);l((r=r.apply(t,e)).next())});const ds=10*1e3,hs=5*1e3,ps=sn||Wi,Ut="[replayer]",jt={duration:500,lineCap:"round",lineWidth:3,strokeStyle:"red"};function Cn(t){return t.type==T.IncrementalSnapshot&&(t.data.source==C.TouchMove||t.data.source==C.MouseInteraction&&t.data.type==ie.TouchStart)}class ms{constructor(e,r){if(this.usingVirtualDom=!1,this.virtualDom=new Pe,this.mouseTail=null,this.tailPositions=[],this.emitter=ps(),this.legacy_missingNodeRetryMap={},this.cache=mr(),this.imageMap=new Map,this.canvasEventMap=new Map,this.mirror=or(),this.styleMirror=new Rt,this.firstFullSnapshot=null,this.newDocumentQueue=[],this.mousePos=null,this.touchActive=null,this.lastSelectionData=null,this.constructedStyleMutations=[],this.adoptedStyleSheets=[],this.handleResize=l=>{this.iframe.style.display="inherit";for(const a of[this.mouseTail,this.iframe])!a||(a.setAttribute("width",String(l.width)),a.setAttribute("height",String(l.height)))},this.applyEventsSynchronously=l=>{for(const a of l){switch(a.type){case T.DomContentLoaded:case T.Load:case T.Custom:continue;case T.FullSnapshot:case T.Meta:case T.Plugin:case T.IncrementalSnapshot:break}this.getCastFn(a,!0)()}this.touchActive===!0?this.mouse.classList.add("touch-active"):this.touchActive===!1&&this.mouse.classList.remove("touch-active"),this.touchActive=null},this.getCastFn=(l,a=!1)=>{let c;switch(l.type){case T.DomContentLoaded:case T.Load:break;case T.Custom:c=()=>{this.emitter.emit(V.CustomEvent,l)};break;case T.Meta:c=()=>this.emitter.emit(V.Resize,{width:l.data.width,height:l.data.height});break;case T.FullSnapshot:c=()=>{var u;if(this.firstFullSnapshot){if(this.firstFullSnapshot===l){this.firstFullSnapshot=!0;return}}else this.firstFullSnapshot=!0;this.rebuildFullSnapshot(l,a),(u=this.iframe.contentWindow)==null||u.scrollTo(l.data.initialOffset),this.styleMirror.reset()};break;case T.IncrementalSnapshot:c=()=>{if(this.applyIncremental(l,a),!a&&(l===this.nextUserInteractionEvent&&(this.nextUserInteractionEvent=null,this.backToNormal()),this.config.skipInactive&&!this.nextUserInteractionEvent)){for(const u of this.service.state.context.events)if(!(u.timestamp<=l.timestamp)&&this.isUserInteraction(u)){u.delay-l.delay>ds*this.speedService.state.context.timer.speed&&(this.nextUserInteractionEvent=u);break}if(this.nextUserInteractionEvent){const u=this.nextUserInteractionEvent.delay-l.delay,d={speed:Math.min(Math.round(u/hs),this.config.maxSpeed)};this.speedService.send({type:"FAST_FORWARD",payload:d}),this.emitter.emit(V.SkipStart,d)}}};break}return()=>{c&&c();for(const d of this.config.plugins||[])d.handler&&d.handler(l,a,{replayer:this});this.service.send({type:"CAST_EVENT",payload:{event:l}});const u=this.service.state.context.events.length-1;if(l===this.service.state.context.events[u]){const d=()=>{u<this.service.state.context.events.length-1||(this.backToNormal(),this.service.send("END"),this.emitter.emit(V.Finish))};l.type===T.IncrementalSnapshot&&l.data.source===C.MouseMove&&l.data.positions.length?setTimeout(()=>{d()},Math.max(0,-l.data.positions[0].timeOffset+50)):d()}this.emitter.emit(V.EventCast,l)}},!(r!=null&&r.liveMode)&&e.length<2)throw new Error("Replayer need at least 2 events.");const n={speed:1,maxSpeed:360,root:document.body,loadTimeout:0,skipInactive:!1,showWarning:!0,showDebug:!1,blockClass:"rr-block",liveMode:!1,insertStyleRules:[],triggerFocus:!0,UNSAFE_replayCanvas:!1,pauseAnimation:!0,mouseTail:jt,useVirtualDom:!0};this.config=Object.assign({},n,r),this.handleResize=this.handleResize.bind(this),this.getCastFn=this.getCastFn.bind(this),this.applyEventsSynchronously=this.applyEventsSynchronously.bind(this),this.emitter.on(V.Resize,this.handleResize),this.setupDom();for(const l of this.config.plugins||[])l.getMirror&&l.getMirror({nodeMirror:this.mirror});this.emitter.on(V.Flush,()=>{if(this.usingVirtualDom){const l={mirror:this.mirror,applyCanvas:(a,c,u)=>{wn({event:a,mutation:c,target:u,imageMap:this.imageMap,canvasEventMap:this.canvasEventMap,errorHandler:this.warnCanvasMutationFailed.bind(this)})},applyInput:this.applyInput.bind(this),applyScroll:this.applyScroll.bind(this),applyStyleSheetMutation:(a,c)=>{a.source===C.StyleSheetRule?this.applyStyleSheetRule(a,c):a.source===C.StyleDeclaration&&this.applyStyleDeclaration(a,c)}};if(this.iframe.contentDocument&&ge(this.iframe.contentDocument,this.virtualDom,l,this.virtualDom.mirror),this.virtualDom.destroyTree(),this.usingVirtualDom=!1,Object.keys(this.legacy_missingNodeRetryMap).length)for(const a in this.legacy_missingNodeRetryMap)try{const c=this.legacy_missingNodeRetryMap[a],u=At(c.node,this.mirror,this.virtualDom.mirror);ge(u,c.node,l,this.virtualDom.mirror),c.node=u}catch(c){this.config.showWarning&&console.warn(c)}this.constructedStyleMutations.forEach(a=>{this.applyStyleSheetMutation(a)}),this.constructedStyleMutations=[],this.adoptedStyleSheets.forEach(a=>{this.applyAdoptedStyleSheet(a)}),this.adoptedStyleSheets=[]}this.mousePos&&(this.moveAndHover(this.mousePos.x,this.mousePos.y,this.mousePos.id,!0,this.mousePos.debugData),this.mousePos=null),this.lastSelectionData&&(this.applySelection(this.lastSelectionData),this.lastSelectionData=null)}),this.emitter.on(V.PlayBack,()=>{this.firstFullSnapshot=null,this.mirror.reset(),this.styleMirror.reset()});const o=new ji([],{speed:this.config.speed,liveMode:this.config.liveMode});this.service=Xi({events:e.map(l=>r&&r.unpackFn?r.unpackFn(l):l).sort((l,a)=>l.timestamp-a.timestamp),timer:o,timeOffset:0,baselineTime:0,lastPlayedEvent:null},{getCastFn:this.getCastFn,applyEventsSynchronously:this.applyEventsSynchronously,emitter:this.emitter}),this.service.start(),this.service.subscribe(l=>{this.emitter.emit(V.StateChange,{player:l})}),this.speedService=Zi({normalSpeed:-1,timer:o}),this.speedService.start(),this.speedService.subscribe(l=>{this.emitter.emit(V.StateChange,{speed:l})});const s=this.service.state.context.events.find(l=>l.type===T.Meta),i=this.service.state.context.events.find(l=>l.type===T.FullSnapshot);if(s){const{width:l,height:a}=s.data;setTimeout(()=>{this.emitter.emit(V.Resize,{width:l,height:a})},0)}i&&setTimeout(()=>{var l;this.firstFullSnapshot||(this.firstFullSnapshot=i,this.rebuildFullSnapshot(i),(l=this.iframe.contentWindow)==null||l.scrollTo(i.data.initialOffset))},1),this.service.state.context.events.find(Cn)&&this.mouse.classList.add("touch-device")}get timer(){return this.service.state.context.timer}on(e,r){return this.emitter.on(e,r),this}off(e,r){return this.emitter.off(e,r),this}setConfig(e){Object.keys(e).forEach(r=>{e[r],this.config[r]=e[r]}),this.config.skipInactive||this.backToNormal(),typeof e.speed<"u"&&this.speedService.send({type:"SET_SPEED",payload:{speed:e.speed}}),typeof e.mouseTail<"u"&&(e.mouseTail===!1?this.mouseTail&&(this.mouseTail.style.display="none"):(this.mouseTail||(this.mouseTail=document.createElement("canvas"),this.mouseTail.width=Number.parseFloat(this.iframe.width),this.mouseTail.height=Number.parseFloat(this.iframe.height),this.mouseTail.classList.add("replayer-mouse-tail"),this.wrapper.insertBefore(this.mouseTail,this.iframe)),this.mouseTail.style.display="inherit"))}getMetaData(){const e=this.service.state.context.events[0],r=this.service.state.context.events[this.service.state.context.events.length-1];return{startTime:e.timestamp,endTime:r.timestamp,totalTime:r.timestamp-e.timestamp}}getCurrentTime(){return this.timer.timeOffset+this.getTimeOffset()}getTimeOffset(){const{baselineTime:e,events:r}=this.service.state.context;return e-r[0].timestamp}getMirror(){return this.mirror}play(e=0){var r,n;this.service.state.matches("paused")?this.service.send({type:"PLAY",payload:{timeOffset:e}}):(this.service.send({type:"PAUSE"}),this.service.send({type:"PLAY",payload:{timeOffset:e}})),(n=(r=this.iframe.contentDocument)==null?void 0:r.getElementsByTagName("html")[0])==null||n.classList.remove("rrweb-paused"),this.emitter.emit(V.Start)}pause(e){var r,n;e===void 0&&this.service.state.matches("playing")&&this.service.send({type:"PAUSE"}),typeof e=="number"&&(this.play(e),this.service.send({type:"PAUSE"})),(n=(r=this.iframe.contentDocument)==null?void 0:r.getElementsByTagName("html")[0])==null||n.classList.add("rrweb-paused"),this.emitter.emit(V.Pause)}resume(e=0){console.warn("The 'resume' was deprecated in 1.0. Please use 'play' method which has the same interface."),this.play(e),this.emitter.emit(V.Resume)}destroy(){this.pause(),this.config.root.removeChild(this.wrapper),this.emitter.emit(V.Destroy)}startLive(e){this.service.send({type:"TO_LIVE",payload:{baselineTime:e}})}addEvent(e){const r=this.config.unpackFn?this.config.unpackFn(e):e;Cn(r)&&this.mouse.classList.add("touch-device"),Promise.resolve().then(()=>this.service.send({type:"ADD_EVENT",payload:{event:r}}))}enableInteract(){this.iframe.setAttribute("scrolling","auto"),this.iframe.style.pointerEvents="auto"}disableInteract(){this.iframe.setAttribute("scrolling","no"),this.iframe.style.pointerEvents="none"}resetCache(){this.cache=mr()}setupDom(){this.wrapper=document.createElement("div"),this.wrapper.classList.add("replayer-wrapper"),this.config.root.appendChild(this.wrapper),this.mouse=document.createElement("div"),this.mouse.classList.add("replayer-mouse"),this.wrapper.appendChild(this.mouse),this.config.mouseTail!==!1&&(this.mouseTail=document.createElement("canvas"),this.mouseTail.classList.add("replayer-mouse-tail"),this.mouseTail.style.display="inherit",this.wrapper.appendChild(this.mouseTail)),this.iframe=document.createElement("iframe");const e=["allow-same-origin"];this.config.UNSAFE_replayCanvas&&e.push("allow-scripts"),this.iframe.style.display="none",this.iframe.setAttribute("sandbox",e.join(" ")),this.disableInteract(),this.wrapper.appendChild(this.iframe),this.iframe.contentWindow&&this.iframe.contentDocument&&(Ui(this.iframe.contentWindow,this.iframe.contentDocument),Ct(this.iframe.contentWindow))}rebuildFullSnapshot(e,r=!1){if(!this.iframe.contentDocument)return console.warn("Looks like your replayer has been destroyed.");Object.keys(this.legacy_missingNodeRetryMap).length&&console.warn("Found unresolved missing node map",this.legacy_missingNodeRetryMap),this.legacy_missingNodeRetryMap={};const n=[],o=(l,a)=>{this.collectIframeAndAttachDocument(n,l);for(const c of this.config.plugins||[])c.onBuild&&c.onBuild(l,{id:a,replayer:this})};bo(e.data.node,{doc:this.iframe.contentDocument,afterAppend:o,cache:this.cache,mirror:this.mirror}),o(this.iframe.contentDocument,e.data.node.id);for(const{mutationInQueue:l,builtNode:a}of n)this.attachDocumentToIframe(l,a),this.newDocumentQueue=this.newDocumentQueue.filter(c=>c!==l);const{documentElement:s,head:i}=this.iframe.contentDocument;this.insertStyleRules(s,i),this.service.state.matches("playing")||this.iframe.contentDocument.getElementsByTagName("html")[0].classList.add("rrweb-paused"),this.emitter.emit(V.FullsnapshotRebuilded,e),r||this.waitForStylesheetLoad(),this.config.UNSAFE_replayCanvas&&this.preloadAllImages()}insertStyleRules(e,r){var n;const o=Ki(this.config.blockClass).concat(this.config.insertStyleRules);if(this.config.pauseAnimation&&o.push("html.rrweb-paused *, html.rrweb-paused *:before, html.rrweb-paused *:after { animation-play-state: paused !important; }"),this.usingVirtualDom){const s=this.virtualDom.createElement("style");this.virtualDom.mirror.add(s,on(s,this.virtualDom.unserializedId)),e.insertBefore(s,r),s.rules.push({source:C.StyleSheetRule,adds:o.map((i,l)=>({rule:i,index:l}))})}else{const s=document.createElement("style");e.insertBefore(s,r);for(let i=0;i<o.length;i++)(n=s.sheet)==null||n.insertRule(o[i],i)}}attachDocumentToIframe(e,r){const n=this.usingVirtualDom?this.virtualDom.mirror:this.mirror,o=[],s=(i,l)=>{this.collectIframeAndAttachDocument(o,i);const a=n.getMeta(i);if(a?.type===F.Element&&a?.tagName.toUpperCase()==="HTML"){const{documentElement:c,head:u}=r.contentDocument;this.insertStyleRules(c,u)}for(const c of this.config.plugins||[])c.onBuild&&c.onBuild(i,{id:l,replayer:this})};Ge(e.node,{doc:r.contentDocument,mirror:n,hackCss:!0,skipChild:!1,afterAppend:s,cache:this.cache}),s(r.contentDocument,e.node.id);for(const{mutationInQueue:i,builtNode:l}of o)this.attachDocumentToIframe(i,l),this.newDocumentQueue=this.newDocumentQueue.filter(a=>a!==i)}collectIframeAndAttachDocument(e,r){if(Ae(r,this.mirror)){const n=this.newDocumentQueue.find(o=>o.parentId===this.mirror.getId(r));n&&e.push({mutationInQueue:n,builtNode:r})}}waitForStylesheetLoad(){var e;const r=(e=this.iframe.contentDocument)==null?void 0:e.head;if(r){const n=new Set;let o,s=this.service.state;const i=()=>{s=this.service.state};this.emitter.on(V.Start,i),this.emitter.on(V.Pause,i);const l=()=>{this.emitter.off(V.Start,i),this.emitter.off(V.Pause,i)};r.querySelectorAll('link[rel="stylesheet"]').forEach(a=>{a.sheet||(n.add(a),a.addEventListener("load",()=>{n.delete(a),n.size===0&&o!==-1&&(s.matches("playing")&&this.play(this.getCurrentTime()),this.emitter.emit(V.LoadStylesheetEnd),o&&clearTimeout(o),l())}))}),n.size>0&&(this.service.send({type:"PAUSE"}),this.emitter.emit(V.LoadStylesheetStart),o=setTimeout(()=>{s.matches("playing")&&this.play(this.getCurrentTime()),o=-1,l()},this.config.loadTimeout))}}preloadAllImages(){return Wt(this,null,function*(){this.service.state;const e=()=>{this.service.state};this.emitter.on(V.Start,e),this.emitter.on(V.Pause,e);const r=[];for(const n of this.service.state.context.events)n.type===T.IncrementalSnapshot&&n.data.source===C.CanvasMutation&&(r.push(this.deserializeAndPreloadCanvasEvents(n.data,n)),("commands"in n.data?n.data.commands:[n.data]).forEach(o=>{this.preloadImages(o,n)}));return Promise.all(r)})}preloadImages(e,r){if(e.property==="drawImage"&&typeof e.args[0]=="string"&&!this.imageMap.has(r)){const n=document.createElement("canvas"),o=n.getContext("2d"),s=o?.createImageData(n.width,n.height);s?.data,JSON.parse(e.args[0]),o?.putImageData(s,0,0)}}deserializeAndPreloadCanvasEvents(e,r){return Wt(this,null,function*(){if(!this.canvasEventMap.has(r)){const n={isUnchanged:!0};if("commands"in e){const o=yield Promise.all(e.commands.map(s=>Wt(this,null,function*(){const i=yield Promise.all(s.args.map(Ce(this.imageMap,null,n)));return $t(ut({},s),{args:i})})));n.isUnchanged===!1&&this.canvasEventMap.set(r,$t(ut({},e),{commands:o}))}else{const o=yield Promise.all(e.args.map(Ce(this.imageMap,null,n)));n.isUnchanged===!1&&this.canvasEventMap.set(r,$t(ut({},e),{args:o}))}}})}applyIncremental(e,r){var n,o,s;const{data:i}=e;switch(i.source){case C.Mutation:{try{this.applyMutation(i,r)}catch(l){this.warn(`Exception in mutation ${l.message||l}`,i)}break}case C.Drag:case C.TouchMove:case C.MouseMove:if(r){const l=i.positions[i.positions.length-1];this.mousePos={x:l.x,y:l.y,id:l.id,debugData:i}}else i.positions.forEach(l=>{const a={doAction:()=>{this.moveAndHover(l.x,l.y,l.id,r,i)},delay:l.timeOffset+e.timestamp-this.service.state.context.baselineTime};this.timer.addAction(a)}),this.timer.addAction({doAction(){},delay:e.delay-((n=i.positions[0])==null?void 0:n.timeOffset)});break;case C.MouseInteraction:{if(i.id===-1||r)break;const l=new Event(ie[i.type].toLowerCase()),a=this.mirror.getNode(i.id);if(!a)return this.debugNodeNotFound(i,i.id);this.emitter.emit(V.MouseInteraction,{type:i.type,target:a});const{triggerFocus:c}=this.config;switch(i.type){case ie.Blur:"blur"in a&&a.blur();break;case ie.Focus:c&&a.focus&&a.focus({preventScroll:!0});break;case ie.Click:case ie.TouchStart:case ie.TouchEnd:r?(i.type===ie.TouchStart?this.touchActive=!0:i.type===ie.TouchEnd&&(this.touchActive=!1),this.mousePos={x:i.x,y:i.y,id:i.id,debugData:i}):(i.type===ie.TouchStart&&(this.tailPositions.length=0),this.moveAndHover(i.x,i.y,i.id,r,i),i.type===ie.Click?(this.mouse.classList.remove("active"),this.mouse.offsetWidth,this.mouse.classList.add("active")):i.type===ie.TouchStart?(this.mouse.offsetWidth,this.mouse.classList.add("touch-active")):i.type===ie.TouchEnd&&this.mouse.classList.remove("touch-active"));break;case ie.TouchCancel:r?this.touchActive=!1:this.mouse.classList.remove("touch-active");break;default:a.dispatchEvent(l)}break}case C.Scroll:{if(i.id===-1)break;if(this.usingVirtualDom){const l=this.virtualDom.mirror.getNode(i.id);if(!l)return this.debugNodeNotFound(i,i.id);l.scrollData=i;break}this.applyScroll(i,r);break}case C.ViewportResize:this.emitter.emit(V.Resize,{width:i.width,height:i.height});break;case C.Input:{if(i.id===-1)break;if(this.usingVirtualDom){const l=this.virtualDom.mirror.getNode(i.id);if(!l)return this.debugNodeNotFound(i,i.id);l.inputData=i;break}this.applyInput(i);break}case C.MediaInteraction:{const l=this.usingVirtualDom?this.virtualDom.mirror.getNode(i.id):this.mirror.getNode(i.id);if(!l)return this.debugNodeNotFound(i,i.id);const a=l;try{i.currentTime&&(a.currentTime=i.currentTime),i.volume&&(a.volume=i.volume),i.muted&&(a.muted=i.muted),i.type===Se.Pause&&a.pause(),i.type===Se.Play&&a.play(),i.type===Se.RateChange&&(a.playbackRate=i.playbackRate)}catch(c){this.config.showWarning&&console.warn(`Failed to replay media interactions: ${c.message||c}`)}break}case C.StyleSheetRule:case C.StyleDeclaration:{this.usingVirtualDom?i.styleId?this.constructedStyleMutations.push(i):i.id&&((o=this.virtualDom.mirror.getNode(i.id))==null||o.rules.push(i)):this.applyStyleSheetMutation(i);break}case C.CanvasMutation:{if(!this.config.UNSAFE_replayCanvas)return;if(this.usingVirtualDom){const l=this.virtualDom.mirror.getNode(i.id);if(!l)return this.debugNodeNotFound(i,i.id);l.canvasMutations.push({event:e,mutation:i})}else{const l=this.mirror.getNode(i.id);if(!l)return this.debugNodeNotFound(i,i.id);wn({event:e,mutation:i,target:l,imageMap:this.imageMap,canvasEventMap:this.canvasEventMap,errorHandler:this.warnCanvasMutationFailed.bind(this)})}break}case C.Font:{try{const l=new FontFace(i.family,i.buffer?new Uint8Array(JSON.parse(i.fontSource)):i.fontSource,i.descriptors);(s=this.iframe.contentDocument)==null||s.fonts.add(l)}catch(l){this.config.showWarning&&console.warn(l)}break}case C.Selection:{if(r){this.lastSelectionData=i;break}this.applySelection(i);break}case C.AdoptedStyleSheet:{this.usingVirtualDom?this.adoptedStyleSheets.push(i):this.applyAdoptedStyleSheet(i);break}}}applyMutation(e,r){if(this.config.useVirtualDom&&!this.usingVirtualDom&&r&&(this.usingVirtualDom=!0,_i(this.iframe.contentDocument,this.mirror,this.virtualDom),Object.keys(this.legacy_missingNodeRetryMap).length))for(const c in this.legacy_missingNodeRetryMap)try{const u=this.legacy_missingNodeRetryMap[c],d=nn(u.node,this.virtualDom,this.mirror);d&&(u.node=d)}catch(u){this.config.showWarning&&console.warn(u)}const n=this.usingVirtualDom?this.virtualDom.mirror:this.mirror;e.removes.forEach(c=>{var u;const d=n.getNode(c.id);if(!d)return e.removes.find(p=>p.id===c.parentId)?void 0:this.warnNodeNotFound(e,c.id);let h=n.getNode(c.parentId);if(!h)return this.warnNodeNotFound(e,c.parentId);if(c.isShadow&&Ie(h)&&(h=h.shadowRoot),n.removeNodeFromMap(d),h)try{h.removeChild(d),this.usingVirtualDom&&d.nodeName==="#text"&&h.nodeName==="STYLE"&&((u=h.rules)==null?void 0:u.length)>0&&(h.rules=[])}catch(p){if(p instanceof DOMException)this.warn("parent could not remove child in mutation",h,d,e);else throw p}});const o=ut({},this.legacy_missingNodeRetryMap),s=[],i=c=>{let u=null;return c.nextId&&(u=n.getNode(c.nextId)),c.nextId!==null&&c.nextId!==void 0&&c.nextId!==-1&&!u},l=c=>{var u;if(!this.iframe.contentDocument)return console.warn("Looks like your replayer has been destroyed.");let d=n.getNode(c.parentId);if(!d)return c.node.type===F.Document?this.newDocumentQueue.push(c):s.push(c);c.node.isShadow&&(Ie(d)||d.attachShadow({mode:"open"}),d=d.shadowRoot);let h=null,p=null;if(c.previousId&&(h=n.getNode(c.previousId)),c.nextId&&(p=n.getNode(c.nextId)),i(c))return s.push(c);if(c.node.rootId&&!n.getNode(c.node.rootId))return;const m=c.node.rootId?n.getNode(c.node.rootId):this.usingVirtualDom?this.virtualDom:this.iframe.contentDocument;if(Ae(d,n)){this.attachDocumentToIframe(c,d);return}const y=(g,b)=>{for(const N of this.config.plugins||[])N.onBuild&&N.onBuild(g,{id:b,replayer:this})},v=Ge(c.node,{doc:m,mirror:n,skipChild:!0,hackCss:!0,cache:this.cache,afterAppend:y});if(c.previousId===-1||c.nextId===-1){o[c.node.id]={node:v,mutation:c};return}const f=n.getMeta(d);if(f&&f.type===F.Element&&f.tagName==="textarea"&&c.node.type===F.Text){const g=Array.isArray(d.childNodes)?d.childNodes:Array.from(d.childNodes);for(const b of g)b.nodeType===d.TEXT_NODE&&d.removeChild(b)}if(h&&h.nextSibling&&h.nextSibling.parentNode)d.insertBefore(v,h.nextSibling);else if(p&&p.parentNode)d.contains(p)?d.insertBefore(v,p):d.insertBefore(v,null);else{if(d===m)for(;m.firstChild;)m.removeChild(m.firstChild);d.appendChild(v)}if(y(v,c.node.id),this.usingVirtualDom&&v.nodeName==="#text"&&d.nodeName==="STYLE"&&((u=d.rules)==null?void 0:u.length)>0&&(d.rules=[]),Ae(v,this.mirror)){const g=this.mirror.getId(v),b=this.newDocumentQueue.find(N=>N.parentId===g);b&&(this.attachDocumentToIframe(b,v),this.newDocumentQueue=this.newDocumentQueue.filter(N=>N!==b))}(c.previousId||c.nextId)&&this.legacy_resolveMissingNode(o,d,v,c)};e.adds.forEach(c=>{l(c)});const a=Date.now();for(;s.length;){const c=gr(s);if(s.length=0,Date.now()-a>500){this.warn("Timeout in the loop, please check the resolve tree data:",c);break}for(const u of c)n.getNode(u.value.parentId)?Tt(u,d=>{l(d)}):this.debug("Drop resolve tree since there is no parent for the root node.",u)}Object.keys(o).length&&Object.assign(this.legacy_missingNodeRetryMap,o),yr(e.texts).forEach(c=>{var u;const d=n.getNode(c.id);if(!d)return e.removes.find(h=>h.id===c.id)?void 0:this.warnNodeNotFound(e,c.id);if(d.textContent=c.value,this.usingVirtualDom){const h=d.parentNode;((u=h?.rules)==null?void 0:u.length)>0&&(h.rules=[])}}),e.attributes.forEach(c=>{const u=n.getNode(c.id);if(!u)return e.removes.find(d=>d.id===c.id)?void 0:this.warnNodeNotFound(e,c.id);for(const d in c.attributes)if(typeof d=="string"){const h=c.attributes[d];if(h===null)u.removeAttribute(d);else if(typeof h=="string")try{if(d==="_cssText"&&(u.nodeName==="LINK"||u.nodeName==="STYLE"))try{const p=n.getMeta(u);Object.assign(p.attributes,c.attributes);const m=Ge(p,{doc:u.ownerDocument,mirror:n,skipChild:!0,hackCss:!0,cache:this.cache}),y=u.nextSibling,v=u.parentNode;if(m&&v){v.removeChild(u),v.insertBefore(m,y),n.replace(c.id,m);break}}catch{}u.setAttribute(d,h)}catch(p){this.config.showWarning&&console.warn("An error occurred may due to the checkout feature.",p)}else if(d==="style"){const p=h,m=u;for(const y in p)if(p[y]===!1)m.style.removeProperty(y);else if(p[y]instanceof Array){const v=p[y];m.style.setProperty(y,v[0],v[1])}else{const v=p[y];m.style.setProperty(y,v)}}}})}applyScroll(e,r){var n,o;const s=this.mirror.getNode(e.id);if(!s)return this.debugNodeNotFound(e,e.id);const i=this.mirror.getMeta(s);if(s===this.iframe.contentDocument)(n=this.iframe.contentWindow)==null||n.scrollTo({top:e.y,left:e.x,behavior:r?"auto":"smooth"});else if(i?.type===F.Document)(o=s.defaultView)==null||o.scrollTo({top:e.y,left:e.x,behavior:r?"auto":"smooth"});else try{s.scrollTo({top:e.y,left:e.x,behavior:r?"auto":"smooth"})}catch{}}applyInput(e){const r=this.mirror.getNode(e.id);if(!r)return this.debugNodeNotFound(e,e.id);try{r.checked=e.isChecked,r.value=e.text}catch{}}applySelection(e){try{const r=new Set,n=e.ranges.map(({start:o,startOffset:s,end:i,endOffset:l})=>{const a=this.mirror.getNode(o),c=this.mirror.getNode(i);if(!a||!c)return;const u=new Range;u.setStart(a,s),u.setEnd(c,l);const d=a.ownerDocument,h=d?.getSelection();return h&&r.add(h),{range:u,selection:h}});r.forEach(o=>o.removeAllRanges()),n.forEach(o=>{var s;return o&&((s=o.selection)==null?void 0:s.addRange(o.range))})}catch{}}applyStyleSheetMutation(e){var r;let n=null;e.styleId?n=this.styleMirror.getStyle(e.styleId):e.id&&(n=((r=this.mirror.getNode(e.id))==null?void 0:r.sheet)||null),n&&(e.source===C.StyleSheetRule?this.applyStyleSheetRule(e,n):e.source===C.StyleDeclaration&&this.applyStyleDeclaration(e,n))}applyStyleSheetRule(e,r){var n,o,s,i;if((n=e.adds)==null||n.forEach(({rule:l,index:a})=>{try{if(Array.isArray(a)){const{positions:c,index:u}=kt(a);Fe(r.cssRules,c).insertRule(l,u)}else{const c=a===void 0?void 0:Math.min(a,r.cssRules.length);r?.insertRule(l,c)}}catch{}}),(o=e.removes)==null||o.forEach(({index:l})=>{try{if(Array.isArray(l)){const{positions:a,index:c}=kt(l);Fe(r.cssRules,a).deleteRule(c||0)}else r?.deleteRule(l)}catch{}}),e.replace)try{(s=r.replace)==null||s.call(r,e.replace)}catch{}if(e.replaceSync)try{(i=r.replaceSync)==null||i.call(r,e.replaceSync)}catch{}}applyStyleDeclaration(e,r){e.set&&Fe(r.rules,e.index).style.setProperty(e.set.property,e.set.value,e.set.priority),e.remove&&Fe(r.rules,e.index).style.removeProperty(e.remove.property)}applyAdoptedStyleSheet(e){var r;const n=this.mirror.getNode(e.id);if(!n)return;(r=e.styles)==null||r.forEach(l=>{var a;let c=null,u=null;if(Ie(n)?u=((a=n.ownerDocument)==null?void 0:a.defaultView)||null:n.nodeName==="#document"&&(u=n.defaultView),!!u)try{c=new u.CSSStyleSheet,this.styleMirror.add(c,l.styleId),this.applyStyleSheetRule({source:C.StyleSheetRule,adds:l.rules},c)}catch{}});const o=10;let s=0;const i=(l,a)=>{const c=a.map(u=>this.styleMirror.getStyle(u)).filter(u=>u!==null);Ie(l)?l.shadowRoot.adoptedStyleSheets=c:l.nodeName==="#document"&&(l.adoptedStyleSheets=c),c.length!==a.length&&s<o&&(setTimeout(()=>i(l,a),0+100*s),s++)};i(n,e.styleIds)}legacy_resolveMissingNode(e,r,n,o){const{previousId:s,nextId:i}=o,l=s&&e[s],a=i&&e[i];if(l){const{node:c,mutation:u}=l;r.insertBefore(c,n),delete e[u.node.id],delete this.legacy_missingNodeRetryMap[u.node.id],(u.previousId||u.nextId)&&this.legacy_resolveMissingNode(e,r,c,u)}if(a){const{node:c,mutation:u}=a;r.insertBefore(c,n.nextSibling),delete e[u.node.id],delete this.legacy_missingNodeRetryMap[u.node.id],(u.previousId||u.nextId)&&this.legacy_resolveMissingNode(e,r,c,u)}}moveAndHover(e,r,n,o,s){const i=this.mirror.getNode(n);if(!i)return this.debugNodeNotFound(s,n);const l=It(i,this.iframe),a=e*l.absoluteScale+l.x,c=r*l.absoluteScale+l.y;this.mouse.style.left=`${a}px`,this.mouse.style.top=`${c}px`,o||this.drawMouseTail({x:a,y:c}),this.hoverElements(i)}drawMouseTail(e){if(!this.mouseTail)return;const{lineCap:r,lineWidth:n,strokeStyle:o,duration:s}=this.config.mouseTail===!0?jt:Object.assign({},jt,this.config.mouseTail),i=()=>{if(!this.mouseTail)return;const l=this.mouseTail.getContext("2d");!l||!this.tailPositions.length||(l.clearRect(0,0,this.mouseTail.width,this.mouseTail.height),l.beginPath(),l.lineWidth=n,l.lineCap=r,l.strokeStyle=o,l.moveTo(this.tailPositions[0].x,this.tailPositions[0].y),this.tailPositions.forEach(a=>l.lineTo(a.x,a.y)),l.stroke())};this.tailPositions.push(e),i(),setTimeout(()=>{this.tailPositions=this.tailPositions.filter(l=>l!==e),i()},s/this.speedService.state.context.timer.speed)}hoverElements(e){var r;(r=this.iframe.contentDocument)==null||r.querySelectorAll(".\\:hover").forEach(o=>{o.classList.remove(":hover")});let n=e;for(;n;)n.classList&&n.classList.add(":hover"),n=n.parentElement}isUserInteraction(e){return e.type!==T.IncrementalSnapshot?!1:e.data.source>C.Mutation&&e.data.source<=C.Input}backToNormal(){this.nextUserInteractionEvent=null,!this.speedService.state.matches("normal")&&(this.speedService.send({type:"BACK_TO_NORMAL"}),this.emitter.emit(V.SkipEnd,{speed:this.speedService.state.context.normalSpeed}))}warnNodeNotFound(e,r){this.warn(`Node with id '${r}' not found. `,e)}warnCanvasMutationFailed(e,r){this.warn("Has error on canvas update",r,"canvas mutation:",e)}debugNodeNotFound(e,r){this.debug(Ut,`Node with id '${r}' not found. `,e)}warn(...e){!this.config.showWarning||console.warn(Ut,...e)}debug(...e){!this.config.showDebug||console.log(Ut,...e)}}const{addCustomEvent:fs}=Re,{freezePage:gs}=Re;var se=Uint8Array,ue=Uint16Array,Ze=Uint32Array,dt=new se([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),ht=new se([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),Vt=new se([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Tn=function(t,e){for(var r=new ue(31),n=0;n<31;++n)r[n]=e+=1<<t[n-1];for(var o=new Ze(r[30]),n=1;n<30;++n)for(var s=r[n];s<r[n+1];++s)o[s]=s-r[n]<<5|n;return[r,o]},Mn=Tn(dt,2),In=Mn[0],Bt=Mn[1];In[28]=258,Bt[258]=28;for(var kn=Tn(ht,0),ys=kn[0],Rn=kn[1],Gt=new ue(32768),X=0;X<32768;++X){var Te=(X&43690)>>>1|(X&21845)<<1;Te=(Te&52428)>>>2|(Te&13107)<<2,Te=(Te&61680)>>>4|(Te&3855)<<4,Gt[X]=((Te&65280)>>>8|(Te&255)<<8)>>>1}for(var ye=function(t,e,r){for(var n=t.length,o=0,s=new ue(e);o<n;++o)++s[t[o]-1];var i=new ue(e);for(o=0;o<e;++o)i[o]=i[o-1]+s[o-1]<<1;var l;if(r){l=new ue(1<<e);var a=15-e;for(o=0;o<n;++o)if(t[o])for(var c=o<<4|t[o],u=e-t[o],d=i[t[o]-1]++<<u,h=d|(1<<u)-1;d<=h;++d)l[Gt[d]>>>a]=c}else for(l=new ue(n),o=0;o<n;++o)l[o]=Gt[i[t[o]-1]++]>>>15-t[o];return l},Me=new se(288),X=0;X<144;++X)Me[X]=8;for(var X=144;X<256;++X)Me[X]=9;for(var X=256;X<280;++X)Me[X]=7;for(var X=280;X<288;++X)Me[X]=8;for(var Ke=new se(32),X=0;X<32;++X)Ke[X]=5;var vs=ye(Me,9,0),bs=ye(Me,9,1),Ss=ye(Ke,5,0),ws=ye(Ke,5,1),zt=function(t){for(var e=t[0],r=1;r<t.length;++r)t[r]>e&&(e=t[r]);return e},fe=function(t,e,r){var n=e/8>>0;return(t[n]|t[n+1]<<8)>>>(e&7)&r},Ht=function(t,e){var r=e/8>>0;return(t[r]|t[r+1]<<8|t[r+2]<<16)>>>(e&7)},Yt=function(t){return(t/8>>0)+(t&7&&1)},Xt=function(t,e,r){(e==null||e<0)&&(e=0),(r==null||r>t.length)&&(r=t.length);var n=new(t instanceof ue?ue:t instanceof Ze?Ze:se)(r-e);return n.set(t.subarray(e,r)),n},Ns=function(t,e,r){var n=t.length,o=!e||r,s=!r||r.i;r||(r={}),e||(e=new se(n*3));var i=function(O){var te=e.length;if(O>te){var ne=new se(Math.max(te*2,O));ne.set(e),e=ne}},l=r.f||0,a=r.p||0,c=r.b||0,u=r.l,d=r.d,h=r.m,p=r.n,m=n*8;do{if(!u){r.f=l=fe(t,a,1);var y=fe(t,a+1,3);if(a+=3,y)if(y==1)u=bs,d=ws,h=9,p=5;else if(y==2){var b=fe(t,a,31)+257,N=fe(t,a+10,15)+4,R=b+fe(t,a+5,31)+1;a+=14;for(var k=new se(R),x=new se(19),E=0;E<N;++E)x[Vt[E]]=fe(t,a+E*3,7);a+=N*3;var H=zt(x),W=(1<<H)-1;if(!s&&a+R*(H+7)>m)break;for(var Z=ye(x,H,1),E=0;E<R;){var P=Z[fe(t,a,W)];a+=P&15;var v=P>>>4;if(v<16)k[E++]=v;else{var B=0,L=0;for(v==16?(L=3+fe(t,a,3),a+=2,B=k[E-1]):v==17?(L=3+fe(t,a,7),a+=3):v==18&&(L=11+fe(t,a,127),a+=7);L--;)k[E++]=B}}var U=k.subarray(0,b),A=k.subarray(b);h=zt(U),p=zt(A),u=ye(U,h,1),d=ye(A,p,1)}else throw"invalid block type";else{var v=Yt(a)+4,f=t[v-4]|t[v-3]<<8,g=v+f;if(g>n){if(s)throw"unexpected EOF";break}o&&i(c+f),e.set(t.subarray(v,g),c),r.b=c+=f,r.p=a=g*8;continue}if(a>m)throw"unexpected EOF"}o&&i(c+131072);for(var D=(1<<h)-1,z=(1<<p)-1,w=h+p+18;s||a+w<m;){var B=u[Ht(t,a)&D],S=B>>>4;if(a+=B&15,a>m)throw"unexpected EOF";if(!B)throw"invalid length/literal";if(S<256)e[c++]=S;else if(S==256){u=null;break}else{var M=S-254;if(S>264){var E=S-257,I=dt[E];M=fe(t,a,(1<<I)-1)+In[E],a+=I}var $=d[Ht(t,a)&z],G=$>>>4;if(!$)throw"invalid distance";a+=$&15;var A=ys[G];if(G>3){var I=ht[G];A+=Ht(t,a)&(1<<I)-1,a+=I}if(a>m)throw"unexpected EOF";o&&i(c+131072);for(var K=c+M;c<K;c+=4)e[c]=e[c-A],e[c+1]=e[c+1-A],e[c+2]=e[c+2-A],e[c+3]=e[c+3-A];c=K}}r.l=u,r.p=a,r.b=c,u&&(l=1,r.m=h,r.d=d,r.n=p)}while(!l);return c==e.length?e:Xt(e,0,c)},we=function(t,e,r){r<<=e&7;var n=e/8>>0;t[n]|=r,t[n+1]|=r>>>8},Je=function(t,e,r){r<<=e&7;var n=e/8>>0;t[n]|=r,t[n+1]|=r>>>8,t[n+2]|=r>>>16},Zt=function(t,e){for(var r=[],n=0;n<t.length;++n)t[n]&&r.push({s:n,f:t[n]});var o=r.length,s=r.slice();if(!o)return[new se(0),0];if(o==1){var i=new se(r[0].s+1);return i[r[0].s]=1,[i,1]}r.sort(function(R,k){return R.f-k.f}),r.push({s:-1,f:25001});var l=r[0],a=r[1],c=0,u=1,d=2;for(r[0]={s:-1,f:l.f+a.f,l,r:a};u!=o-1;)l=r[r[c].f<r[d].f?c++:d++],a=r[c!=u&&r[c].f<r[d].f?c++:d++],r[u++]={s:-1,f:l.f+a.f,l,r:a};for(var h=s[0].s,n=1;n<o;++n)s[n].s>h&&(h=s[n].s);var p=new ue(h+1),m=Kt(r[u-1],p,0);if(m>e){var n=0,y=0,v=m-e,f=1<<v;for(s.sort(function(k,x){return p[x.s]-p[k.s]||k.f-x.f});n<o;++n){var g=s[n].s;if(p[g]>e)y+=f-(1<<m-p[g]),p[g]=e;else break}for(y>>>=v;y>0;){var b=s[n].s;p[b]<e?y-=1<<e-p[b]++-1:++n}for(;n>=0&&y;--n){var N=s[n].s;p[N]==e&&(--p[N],++y)}m=e}return[new se(p),m]},Kt=function(t,e,r){return t.s==-1?Math.max(Kt(t.l,e,r+1),Kt(t.r,e,r+1)):e[t.s]=r},Dn=function(t){for(var e=t.length;e&&!t[--e];);for(var r=new ue(++e),n=0,o=t[0],s=1,i=function(a){r[n++]=a},l=1;l<=e;++l)if(t[l]==o&&l!=e)++s;else{if(!o&&s>2){for(;s>138;s-=138)i(32754);s>2&&(i(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(i(o),--s;s>6;s-=6)i(8304);s>2&&(i(s-3<<5|8208),s=0)}for(;s--;)i(o);s=1,o=t[l]}return[r.subarray(0,n),e]},Qe=function(t,e){for(var r=0,n=0;n<e.length;++n)r+=t[n]*e[n];return r},pt=function(t,e,r){var n=r.length,o=Yt(e+2);t[o]=n&255,t[o+1]=n>>>8,t[o+2]=t[o]^255,t[o+3]=t[o+1]^255;for(var s=0;s<n;++s)t[o+s+4]=r[s];return(o+4+n)*8},On=function(t,e,r,n,o,s,i,l,a,c,u){we(e,u++,r),++o[256];for(var d=Zt(o,15),h=d[0],p=d[1],m=Zt(s,15),y=m[0],v=m[1],f=Dn(h),g=f[0],b=f[1],N=Dn(y),R=N[0],k=N[1],x=new ue(19),E=0;E<g.length;++E)x[g[E]&31]++;for(var E=0;E<R.length;++E)x[R[E]&31]++;for(var H=Zt(x,7),W=H[0],Z=H[1],P=19;P>4&&!W[Vt[P-1]];--P);var B=c+5<<3,L=Qe(o,Me)+Qe(s,Ke)+i,U=Qe(o,h)+Qe(s,y)+i+14+3*P+Qe(x,W)+(2*x[16]+3*x[17]+7*x[18]);if(B<=L&&B<=U)return pt(e,u,t.subarray(a,a+c));var A,D,z,w;if(we(e,u,1+(U<L)),u+=2,U<L){A=ye(h,p,0),D=h,z=ye(y,v,0),w=y;var S=ye(W,Z,0);we(e,u,b-257),we(e,u+5,k-1),we(e,u+10,P-4),u+=14;for(var E=0;E<P;++E)we(e,u+3*E,W[Vt[E]]);u+=3*P;for(var M=[g,R],I=0;I<2;++I)for(var $=M[I],E=0;E<$.length;++E){var G=$[E]&31;we(e,u,S[G]),u+=W[G],G>15&&(we(e,u,$[E]>>>5&127),u+=$[E]>>>12)}}else A=vs,D=Me,z=Ss,w=Ke;for(var E=0;E<l;++E)if(n[E]>255){var G=n[E]>>>18&31;Je(e,u,A[G+257]),u+=D[G+257],G>7&&(we(e,u,n[E]>>>23&31),u+=dt[G]);var K=n[E]&31;Je(e,u,z[K]),u+=w[K],K>3&&(Je(e,u,n[E]>>>5&8191),u+=ht[K])}else Je(e,u,A[n[E]]),u+=D[n[E]];return Je(e,u,A[256]),u+D[256]},Es=new Ze([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Cs=new se(0),Ts=function(t,e,r,n,o,s){var i=t.length,l=new se(n+i+5*(1+Math.floor(i/7e3))+o),a=l.subarray(n,l.length-o),c=0;if(!e||i<8)for(var u=0;u<=i;u+=65535){var d=u+65535;d<i?c=pt(a,c,t.subarray(u,d)):(a[u]=s,c=pt(a,c,t.subarray(u,i)))}else{for(var h=Es[e-1],p=h>>>13,m=h&8191,y=(1<<r)-1,v=new ue(32768),f=new ue(y+1),g=Math.ceil(r/3),b=2*g,N=function(oe){return(t[oe]^t[oe+1]<<g^t[oe+2]<<b)&y},R=new Ze(25e3),k=new ue(288),x=new ue(32),E=0,H=0,u=0,W=0,Z=0,P=0;u<i;++u){var B=N(u),L=u&32767,U=f[B];if(v[L]=U,f[B]=L,Z<=u){var A=i-u;if((E>7e3||W>24576)&&A>423){c=On(t,a,0,R,k,x,H,W,P,u-P,c),W=E=H=0,P=u;for(var D=0;D<286;++D)k[D]=0;for(var D=0;D<30;++D)x[D]=0}var z=2,w=0,S=m,M=L-U&32767;if(A>2&&B==N(u-M))for(var I=Math.min(p,A)-1,$=Math.min(32767,u),G=Math.min(258,A);M<=$&&--S&&L!=U;){if(t[u+z]==t[u+z-M]){for(var K=0;K<G&&t[u+K]==t[u+K-M];++K);if(K>z){if(z=K,w=M,K>I)break;for(var O=Math.min(M,K-2),te=0,D=0;D<O;++D){var ne=u-M+D+32768&32767,J=v[ne],de=ne-J+32768&32767;de>te&&(te=de,U=ne)}}}L=U,U=v[L],M+=L-U+32768&32767}if(w){R[W++]=268435456|Bt[z]<<18|Rn[w];var j=Bt[z]&31,ce=Rn[w]&31;H+=dt[j]+ht[ce],++k[257+j],++x[ce],Z=u+z,++E}else R[W++]=t[u],++k[t[u]]}}c=On(t,a,s,R,k,x,H,W,P,u-P,c),s||(c=pt(a,c,Cs))}return Xt(l,0,n+Yt(c)+o)},Ms=function(){var t=1,e=0;return{p:function(r){for(var n=t,o=e,s=r.length,i=0;i!=s;){for(var l=Math.min(i+5552,s);i<l;++i)n+=r[i],o+=n;n%=65521,o%=65521}t=n,e=o},d:function(){return(t>>>8<<16|(e&255)<<8|e>>>8)+((t&255)<<23)*2}}},Is=function(t,e,r,n,o){return Ts(t,e.level==null?6:e.level,e.mem==null?Math.ceil(Math.max(8,Math.min(13,Math.log(t.length)))*1.5):12+e.mem,r,n,!o)},ks=function(t,e,r){for(;r;++e)t[e]=r,r>>>=8},Rs=function(t,e){var r=e.level,n=r==0?0:r<6?1:r==9?3:2;t[0]=120,t[1]=n<<6|(n?32-2*n:1)},Ds=function(t){if((t[0]&15)!=8||t[0]>>>4>7||(t[0]<<8|t[1])%31)throw"invalid zlib data";if(t[1]&32)throw"invalid zlib data: preset dictionaries not supported"};function Os(t,e){e===void 0&&(e={});var r=Ms();r.p(t);var n=Is(t,e,2,4);return Rs(n,e),ks(n,n.length-4,r.d()),n}function xs(t,e){return Ns((Ds(t),t.subarray(2,-4)),e)}function xn(t,e){var r=t.length;if(!e&&typeof TextEncoder<"u")return new TextEncoder().encode(t);for(var n=new se(t.length+(t.length>>>1)),o=0,s=function(c){n[o++]=c},i=0;i<r;++i){if(o+5>n.length){var l=new se(o+8+(r-i<<1));l.set(n),n=l}var a=t.charCodeAt(i);a<128||e?s(a):a<2048?(s(192|a>>>6),s(128|a&63)):a>55295&&a<57344?(a=65536+(a&1047552)|t.charCodeAt(++i)&1023,s(240|a>>>18),s(128|a>>>12&63),s(128|a>>>6&63),s(128|a&63)):(s(224|a>>>12),s(128|a>>>6&63),s(128|a&63))}return Xt(n,0,o)}function Ln(t,e){var r="";if(!e&&typeof TextDecoder<"u")return new TextDecoder().decode(t);for(var n=0;n<t.length;){var o=t[n++];o<128||e?r+=String.fromCharCode(o):o<224?r+=String.fromCharCode((o&31)<<6|t[n++]&63):o<240?r+=String.fromCharCode((o&15)<<12|(t[n++]&63)<<6|t[n++]&63):(o=((o&15)<<18|(t[n++]&63)<<12|(t[n++]&63)<<6|t[n++]&63)-65536,r+=String.fromCharCode(55296|o>>10,56320|o&1023))}return r}const Jt="v1";var Ls=Object.defineProperty,As=Object.defineProperties,Fs=Object.getOwnPropertyDescriptors,An=Object.getOwnPropertySymbols,_s=Object.prototype.hasOwnProperty,Ps=Object.prototype.propertyIsEnumerable,Fn=(t,e,r)=>e in t?Ls(t,e,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[e]=r,$s=(t,e)=>{for(var r in e||(e={}))_s.call(e,r)&&Fn(t,r,e[r]);if(An)for(var r of An(e))Ps.call(e,r)&&Fn(t,r,e[r]);return t},Ws=(t,e)=>As(t,Fs(e));const Us=t=>{const e=Ws($s({},t),{v:Jt});return Ln(Os(xn(JSON.stringify(e))),!0)},js=t=>{if(typeof t!="string")return t;try{const e=JSON.parse(t);if(e.timestamp)return e}catch{}try{const e=JSON.parse(Ln(xs(xn(t,!0))));if(e.v===Jt)return e;throw new Error(`These events were packed with packer ${e.v} which is incompatible with current packer ${Jt}.`)}catch(e){throw console.error(e),new Error("Unknown data format.")}};class Ue{constructor(e){this.fileName=e.fileName||"",this.functionName=e.functionName||"",this.lineNumber=e.lineNumber,this.columnNumber=e.columnNumber}toString(){const e=this.lineNumber||"",r=this.columnNumber||"";return this.functionName?`${this.functionName} (${this.fileName}:${e}:${r})`:`${this.fileName}:${e}:${r}`}}const Vs=/(^|@)\S+:\d+/,_n=/^\s*at .*(\S+:\d+|\(native\))/m,Bs=/^(eval@)?(\[native code])?$/,Pn={parse:function(t){if(!t)return[];if(typeof t.stacktrace<"u"||typeof t["opera#sourceloc"]<"u")return this.parseOpera(t);if(t.stack&&t.stack.match(_n))return this.parseV8OrIE(t);if(t.stack)return this.parseFFOrSafari(t);throw new Error("Cannot parse given Error object")},extractLocation:function(t){if(t.indexOf(":")===-1)return[t];const e=/(.+?)(?::(\d+))?(?::(\d+))?$/.exec(t.replace(/[()]/g,""));if(!e)throw new Error(`Cannot parse given url: ${t}`);return[e[1],e[2]||void 0,e[3]||void 0]},parseV8OrIE:function(t){return t.stack.split(`
`).filter(function(e){return!!e.match(_n)},this).map(function(e){e.indexOf("(eval ")>-1&&(e=e.replace(/eval code/g,"eval").replace(/(\(eval at [^()]*)|(\),.*$)/g,""));let r=e.replace(/^\s+/,"").replace(/\(eval code/g,"(");const n=r.match(/ (\((.+):(\d+):(\d+)\)$)/);r=n?r.replace(n[0],""):r;const o=r.split(/\s+/).slice(1),s=this.extractLocation(n?n[1]:o.pop()),i=o.join(" ")||void 0,l=["eval","<anonymous>"].indexOf(s[0])>-1?void 0:s[0];return new Ue({functionName:i,fileName:l,lineNumber:s[1],columnNumber:s[2]})},this)},parseFFOrSafari:function(t){return t.stack.split(`
`).filter(function(e){return!e.match(Bs)},this).map(function(e){if(e.indexOf(" > eval")>-1&&(e=e.replace(/ line (\d+)(?: > eval line \d+)* > eval:\d+:\d+/g,":$1")),e.indexOf("@")===-1&&e.indexOf(":")===-1)return new Ue({functionName:e});{const r=/((.*".+"[^@]*)?[^@]*)(?:@)/,n=e.match(r),o=n&&n[1]?n[1]:void 0,s=this.extractLocation(e.replace(r,""));return new Ue({functionName:o,fileName:s[0],lineNumber:s[1],columnNumber:s[2]})}},this)},parseOpera:function(t){return!t.stacktrace||t.message.indexOf(`
`)>-1&&t.message.split(`
`).length>t.stacktrace.split(`
`).length?this.parseOpera9(t):t.stack?this.parseOpera11(t):this.parseOpera10(t)},parseOpera9:function(t){const e=/Line (\d+).*script (?:in )?(\S+)/i,r=t.message.split(`
`),n=[];for(let o=2,s=r.length;o<s;o+=2){const i=e.exec(r[o]);i&&n.push(new Ue({fileName:i[2],lineNumber:parseFloat(i[1])}))}return n},parseOpera10:function(t){const e=/Line (\d+).*script (?:in )?(\S+)(?:: In function (\S+))?$/i,r=t.stacktrace.split(`
`),n=[];for(let o=0,s=r.length;o<s;o+=2){const i=e.exec(r[o]);i&&n.push(new Ue({functionName:i[3]||void 0,fileName:i[2],lineNumber:parseFloat(i[1])}))}return n},parseOpera11:function(t){return t.stack.split(`
`).filter(function(e){return!!e.match(Vs)&&!e.match(/^Error created at/)},this).map(function(e){const r=e.split("@"),n=this.extractLocation(r.pop()),o=(r.shift()||"").replace(/<anonymous function(: (\w+))?>/,"$2").replace(/\([^)]*\)/g,"")||void 0;return new Ue({functionName:o,fileName:n[0],lineNumber:n[1],columnNumber:n[2]})},this)}};function Gs(t){if(!t||!t.outerHTML)return"";let e="";for(;t.parentElement;){let r=t.localName;if(!r)break;r=r.toLowerCase();const n=t.parentElement,o=[];if(n.children&&n.children.length>0)for(let s=0;s<n.children.length;s++){const i=n.children[s];i.localName&&i.localName.toLowerCase&&i.localName.toLowerCase()===r&&o.push(i)}o.length>1&&(r+=`:eq(${o.indexOf(t)})`),e=r+(e?">"+e:""),t=n}return e}function Qt(t){return Object.prototype.toString.call(t)==="[object Object]"}function $n(t,e){if(e===0)return!0;const r=Object.keys(t);for(const n of r)if(Qt(t[n])&&$n(t[n],e-1))return!0;return!1}function qt(t,e){const r={numOfKeysLimit:50,depthOfLimit:4};Object.assign(r,e);const n=[],o=[];return JSON.stringify(t,function(l,a){if(n.length>0){const c=n.indexOf(this);~c?n.splice(c+1):n.push(this),~c?o.splice(c,1/0,l):o.push(l),~n.indexOf(a)&&(n[0]===a?a="[Circular ~]":a="[Circular ~."+o.slice(0,n.indexOf(a)).join(".")+"]")}else n.push(a);if(a===null)return a;if(a===void 0)return"undefined";if(s(a))return i(a);if(a instanceof Event){const c={};for(const u in a){const d=a[u];Array.isArray(d)?c[u]=Gs(d.length?d[0]:null):c[u]=d}return c}else{if(a instanceof Node)return a instanceof HTMLElement?a?a.outerHTML:"":a.nodeName;if(a instanceof Error)return a.stack?a.stack+`
End of stack for Error object`:a.name+": "+a.message}return a});function s(l){return!!(Qt(l)&&Object.keys(l).length>r.numOfKeysLimit||typeof l=="function"||Qt(l)&&$n(l,r.depthOfLimit))}function i(l){let a=l.toString();return r.stringLengthLimit&&a.length>r.stringLengthLimit&&(a=`${a.slice(0,r.stringLengthLimit)}...`),a}}const Wn={level:["assert","clear","count","countReset","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","table","time","timeEnd","timeLog","trace","warn"],lengthThreshold:1e3,logger:"console"};function zs(t,e,r){const n=r?Object.assign({},Wn,r):Wn,o=n.logger;if(!o)return()=>{};let s;typeof o=="string"?s=e[o]:s=o;let i=0;const l=[];if(n.level.includes("error")&&window){const c=u=>{const d=u.message,h=u.error,p=Pn.parse(h).map(y=>y.toString()),m=[qt(d,n.stringifyOptions)];t({level:"error",trace:p,payload:m})};window.addEventListener("error",c),l.push(()=>{window&&window.removeEventListener("error",c)})}for(const c of n.level)l.push(a(s,c));return()=>{l.forEach(c=>c())};function a(c,u){return c[u]?Ne(c,u,d=>(...h)=>{d.apply(this,h);try{const p=Pn.parse(new Error).map(y=>y.toString()).splice(1),m=h.map(y=>qt(y,n.stringifyOptions));i++,i<n.lengthThreshold?t({level:u,trace:p,payload:m}):i===n.lengthThreshold&&t({level:"warn",trace:[],payload:[qt("The number of log records reached the threshold.")]})}catch(p){d("rrweb logger error:",p,...h)}}):()=>{}}}const er="rrweb/console@1",Hs=t=>({name:er,observer:zs,options:t}),mt="__rrweb_original__",Ys={level:["assert","clear","count","countReset","debug","dir","dirxml","error","group","groupCollapsed","groupEnd","info","log","table","time","timeEnd","timeLog","trace","warn"],replayLogger:void 0};class Xs{constructor(e){this.config=Object.assign(Ys,e)}getConsoleLogger(){const e={};for(const r of this.config.level)r==="trace"?e[r]=n=>{(console.log[mt]?console.log[mt]:console.log)(...n.payload.map(o=>JSON.parse(o)),this.formatMessage(n))}:e[r]=n=>{(console[r][mt]?console[r][mt]:console[r])(...n.payload.map(o=>JSON.parse(o)),this.formatMessage(n))};return e}formatMessage(e){if(e.trace.length===0)return"";const r=`
	at `;let n=r;return n+=e.trace.join(r),n}}const Zs=t=>{const e=t?.replayLogger||new Xs(t).getConsoleLogger();return{handler(r,n,o){let s=null;if(r.type===T.IncrementalSnapshot&&r.data.source===C.Log?s=r.data:r.type===T.Plugin&&r.data.plugin===er&&(s=r.data.payload),s)try{typeof e[s.level]=="function"&&e[s.level](s)}catch(i){o.replayer.config.showWarning&&console.warn(i)}}}};return ee.EventType=T,ee.IncrementalSource=C,ee.MouseInteractions=ie,ee.PLUGIN_NAME=er,ee.Replayer=ms,ee.ReplayerEvents=V,ee.addCustomEvent=fs,ee.freezePage=gs,ee.getRecordConsolePlugin=Hs,ee.getReplayConsolePlugin=Zs,ee.pack=Us,ee.record=Re,ee.unpack=js,ee.utils=So,Object.defineProperty(ee,"__esModule",{value:!0}),ee}({});
//# sourceMappingURL=rrweb-all.min.js.map

<!--
 * @Descripttion: 
 * @Author: liuyue
 * @Date: 2023-08-09 09:11:10
 * @LastEditors: liuyue <EMAIL>
 * @LastEditTime: 2025-05-29 09:00:12
-->
<template>
  <div class="footer-content" style="height: 13mm;font-size: 4.8mm;line-height: 1.125;">
    <div v-if="position.left" class="locatePoint-l"></div>
    <div v-if="position.left" class="locatePoint-l-new"></div>
    <div v-if="position.right" class="locatePoint-r"></div>
    <div v-if="position.right" class="locatePoint-r-new"></div>
    <span class="page">{{ page }}</span
    >/<span class="total">{{ totalPage }}</span>
    <div class="page-tag">{{ pageText }}</div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted } from "vue";
export default defineComponent({
  props: {
    id: {
      type: String,
      default: "",
    },
    page: {
      type: Number,
      default: 1,
    },
    totalPage: {
      type: Number,
      default: 1,
    },
    pageText: {
      type: String,
      default: "第一张正面",
    },
    position: {
      type: Object,
      default: function () {
        return { left: true, right: true };
      },
    },
  },
  components: {},
  setup() {
    const state = reactive({});

    /**
     * 页面一开始加载
     */
    onMounted(async () => {});

    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang="scss" scoped>
.footer-content {
  text-align: center;
  color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  position: relative;
  .locatePoint-l {
    top: 1mm;
    left: -7mm;
    width: 3mm;
    height: 7mm;
    position: absolute;
    background-color: black;
    display: block;
  }
  .locatePoint-l-new {
    top: 4mm;
    left: -3mm;
    width: 4mm;
    height: 4mm;
    position: absolute;
    background-color: black;
    display: block;
  }
  .locatePoint-r {
    top: 1mm;
    right: -7mm;
    width: 3mm;
    height: 7mm;
    position: absolute;
    background-color: black;
    display: block;
  }
  .locatePoint-r-new {
    top: 4mm;
    right: -3mm;
    width: 4mm;
    height: 4mm;
    position: absolute;
    background-color: black;
    display: block;
  }
  .page-tag {
    // width: 78px;
    // height: 23px;
    // line-height: 23px;
    text-align: center;
    background: #d8d8d8;
    opacity: 1;
    margin-left: 3mm;
    padding: 1mm 2mm;
    border-radius: 4px 4px 4px 4px;
    font-size: 2.8mm;
  }
}
</style>

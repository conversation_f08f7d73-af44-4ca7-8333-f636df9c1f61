﻿<template>
  <template v-if="paginationType == 'noTotalCount'">
    <div class="mk-pagination-no-count" v-if="currentPageTableDataCount != 0">
      <el-button
        style="margin-right: 10px;"
        :disabled="page == 1"
        :color="bgColor"
        @click="pageClickLeft"
        >上一页</el-button
      >
      <el-button
        :disabled="isNoNextPage || currentPageTableDataCount < pageSize"
        :color="bgColor"
        @click="pageClickRight"
        >下一页</el-button
      >
    </div>
  </template>
  <template v-else>
    <el-row
      v-if="total > pageSize"
      class="mk-pagination"
      type="flex"
      align="middle"
      :justify="justify"
      :style="{ '--bgColor': bgColor }"
    >
      <el-pagination
        background
        :small="small"
        layout="prev, pager, next, total"
        :page-size="pageSize"
        :pager-count="pagerCount"
        :total="total"
        :current-page="currentPage"
        :hide-on-single-page="true"
        @current-change="switchPage"
      ></el-pagination>
    </el-row>
  </template>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from "vue";
import { ElPagination, ElRow } from "element-plus";

export default defineComponent({
  name: "mk-pagination",

  components: { ElPagination, ElRow },

  emits: ["switch-page"],

  props: {
    // 分页条数
    pageSize: {
      type: Number,
      default: 10
    },
    // 数据总数
    total: {
      type: Number,
      default: 0
    },
    // 当前页码
    currentPage: {
      type: Number,
      default: 1
    },
    // 页码C按钮的数量
    pagerCount: {
      type: Number,
      default: 7
    },
    // 是否使用小型分页样式
    small: {
      type: Boolean,
      default: false
    },
    // 水平位置
    justify: {
      type: String,
      default: "end",
      validator: (val: string) => {
        return (
          ["start", "end", "center", "space-around", "space-between"].indexOf(
            val
          ) !== -1
        );
      }
    },
    // 背景颜色
    bgColor: {
      type: String,
      default: "#3e73f6"
    },
    // 分页类型（带总数的分页和只需要请求下一页的分页）
    paginationType: {
      type: String,
      default: ""
    },
    //当前页数据数
    currentPageTableDataCount: {
      type: Number,
      default: 0
    },
    //当前页数据数
    isNoNextPage: {
      type: Boolean,
      default: false
    }
  },

  setup(props, ctx) {
    const state = reactive({
      page: props.currentPage
    });
    /**
     * @name: 页面切换
     * @param page 页码
     */
    const switchPage = (page: number) => {
      ctx.emit("switch-page", page);
    };
    /**
     * @name 切换上一页
     */
    const pageClickLeft = () => {
      state.page = state.page - 1 < 1 ? 1 : state.page - 1;
      switchPage(state.page);
    };
    /**
     * @name 切换下一页
     */
    const pageClickRight = () => {
      state.page = state.page + 1;
      switchPage(state.page);
    };

    return {
      ...toRefs(state),
      switchPage,
      pageClickLeft,
      pageClickRight
    };
  }
});
</script>

<style lang="scss" scoped>
.mk-pagination {
  width: 100%;
  padding: 10px 0;
}

.mk-pagination-no-count {
  text-align: right;
  padding: 10px 0;
}
</style>
<style lang="scss">
.mk-pagination .el-pagination__total {
  margin-left: 20px;
}

.mk-pagination {
  .is-background .el-pager li:not(.disabled).active {
    @include theme_background-color();
  }
  .el-pagination.is-background .el-pager li:not(.disabled):hover{
    @include theme_color();
  }
  .el-pagination.is-background .el-pager li:not(.disabled).active:hover{
    color: #fff !important;
  }
}
</style>

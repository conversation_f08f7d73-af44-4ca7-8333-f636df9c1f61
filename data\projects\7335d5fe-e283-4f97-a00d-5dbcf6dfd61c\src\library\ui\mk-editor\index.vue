<template>
  <div class="tinymce-editor">
    <editor v-model="myValue" :init="init" ref="myeditor"> </editor>
  </div>
</template>

<script>
import { defineComponent, onMounted, ref, watch } from 'vue';
import {
  generateUUID,
  get_suffix,
  addFsUrl
} from '@/library/src/utils/globalFunction';
import tinymce from 'tinymce/tinymce';
import 'tinymce/themes/silver/theme';
import Editor from '@tinymce/tinymce-vue';
import 'tinymce/plugins/image';
import 'tinymce/plugins/link';
import 'tinymce/plugins/code';
import 'tinymce/plugins/codesample';
import 'tinymce/plugins/table';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/contextmenu';
import 'tinymce/plugins/wordcount';
import 'tinymce/plugins/colorpicker';
import 'tinymce/plugins/textcolor';
import 'tinymce/icons/default';
import './wordlimit';
import { isTest } from '@/library/src/utils/valiteSite';
import store from '@/store';

export default defineComponent({
  name: 'mk-editor',
  components: {
    Editor
  },
  props: {
    // 绑定值
    value: {
      type: String,
      default: ''
    },
    // 最大字数限制,0时为不做限制
    maxlength: {
      type: Number,
      default: 0
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },
    // 插件
    plugins: {
      type: [String, Array],
      default:
        'image link code codesample table lists contextmenu wordcount colorpicker textcolor wordlimit'
    },
    // 工具栏
    toolbar: {
      type: [String, Array],
      default:
        'code undo redo restoredraft | cut copy paste pastetext | forecolor backcolor bold italic underline strikethrough link anchor | alignleft aligncenter alignright alignjustify outdent indent | styleselect fontselect fontsizeselect | bullist numlist | blockquote subscript superscript removeformat |  table image media charmap emoticons hr pagebreak insertdatetime print preview | fullscreen | bdmap indent2em lineheight formatpainter axupimgs importword'
    },
    //自定义上传地址
    pathUrl: {
      type: String,
      default: 'aliba/teacherStudio/info'
    }
  },
  setup(props, context) {
    const myeditor = ref(null);
    let myValue = ref(props.value);
    const init = {
      language_url: addFsUrl('aliba/plug/tinymce/langs/zh_CN.js'),
      language: 'zh_CN',
      skin_url: addFsUrl('aliba/plug/tinymce/skins/ui/oxide'),
      plugin_base_urls: addFsUrl('aliba/plug/tinymce/skins/ui/oxide'),
      content_css: addFsUrl(
        'aliba/plug/tinymce/skins/content/default/content.css'
      ),
      height: 500,
      plugins: props.plugins,
      toolbar: props.toolbar,
      branding: false,
      menubar: false,
      // 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
      images_upload_handler: (blobInfo, success, failure) => {
        // 此处可修改为返回ossupload上传之后回调地址
        const base64 = 'data:image/jpeg;base64,' + blobInfo.base64();
        let filePath = handleUploadPath(blobInfo.filename());
        minioUpload.uploadBase64File(base64, filePath, '').then((res) => {
          success(res[0]);
        });
      },
      wordlimit: {
        max: props.maxlength, // 最多可以输入多少字
        spaces: false, // 是否含空格
        isInput: false // 是否在超出后还可以输入
      },
      //设置默认字体 行间距
      content_style:
        '.mce-content-body {font-family:Microsoft YaHei;line-height:1.5}'
    };
    /**
     * 处理上传资源地址
     */
    function handleUploadPath(fileName) {
      let filePath = props.pathUrl + getDateForPath();
      let uuid = generateUUID();
      filePath = filePath + uuid + '/f' + get_suffix(fileName);
      return filePath;
    }
    /**
     * 根据当前时间拼接文件夹路径
     * @returns {string}
     */
    function getDateForPath() {
      let date = new Date();
      let y = date.getFullYear();
      let m = date.getMonth() + 1;
      let month = m < 10 ? '0' + m : m;
      let d = date.getDate();
      let day = d < 10 ? '0' + d : d;
      //当用户学校id为空时
      return '/' + y + '/' + month + '/' + day + '/';
    }
    /**
     * @name: 只读,设计模式切换
     */
    function toggleMode() {
      if (props.readonly) {
        tinymce.activeEditor.setMode('readonly');
      } else {
        tinymce.activeEditor.setMode('design');
      }
    }

    onMounted(() => {
      minioUpload.initConfig(
        isTest(),
        store.state.user.schoolId,
        'datedu',
        store.state.app.platFormInfo.id
      );
      tinymce.init({});
      toggleMode();
    });

    //监听父组件传过来的数据
    watch(
      () => props.value,
      () => {
        myValue.value = props.value;
      }
    );
    watch(
      () => props.toolbar,
      () => {
        init.toolbar = props.toolbar;
        tinymce.activeEditor.settings.toolbar = init.toolbar;
      }
    );
    watch(() => props.readonly, toggleMode);
    watch(myValue, (newValue, oldValue) => {
      context.emit('editor-change', newValue);
    });
    //获取纯文本
    function getText() {
      return myeditor.value.modelValue
        .replace(/<(style|script|iframe)[^>]*?>[\s\S]+?<\/\1\s*>/gi, '')
        .replace(/<[^>]+?>/g, '')
        .replace(/\s+/g, ' ')
        .replace(/ /g, ' ')
        .replace(/>/g, ' ')
        .replace(/&nbsp;/g, '')
        .replace(/&amp;/g, '&')
        .replace(/&ldquo;/g, '“')
        .replace(/&rdquo;/g, '”')
        .replace(/&lsquo;/g, '‘')
        .replace(/&rsquo;/g, '’');
    }
    return {
      init,
      myValue,
      myeditor,
      getText
    };
  }
});
</script>
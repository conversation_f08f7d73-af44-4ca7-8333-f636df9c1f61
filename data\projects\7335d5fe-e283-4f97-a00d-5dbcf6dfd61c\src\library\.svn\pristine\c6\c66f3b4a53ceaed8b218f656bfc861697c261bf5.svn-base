// /**
//  * @important 此文件废弃，引入下沉到各项目按需导入
//  * @name 导入样式
//  */
// import "../css/install.scss";
// import "../assets/icon/iconfont.css";
// // 使用symbol
// import "../assets/icon/iconfont.js";

// import MkDialog from "./mk-dialog/";
// import MkDeleteDialog from "./mk-delete-dialog/";
// import MkPagination from "./mk-pagination";
// import MkExportTable from "./mk-export-table";
// import MkPreviewSlider from "./mk-preview-slider";
// import MkTabs from "./mk-tabs";
// import MkNoData from "./mk-no-data";
// import MkCardTitle from "./mk-card-title";
// import MkSearch from "./mk-search";
// import MkBreadCrumb from "./mk-bread-crumb";
// import MkCourseCard from "./mk-course-card";
// import MkSchoolCard from "./mk-school-card";
// import MkLiveCard from "./mk-live-card";
// import MkInfoCard from "./mk-info-card";
// import MkLogout from "./mk-logout";
// import MkUpload from "./mk-upload";
// import MkSelect from "./mk-select";
// import MkNetresourceDialog from "./mk-netresource-dialog";
// import MkBanner from "./mk-banner";
// import MkSortCaret from "./mk-sort-caret";
// import MKEditor from "./mk-editor";
// import MKImage from "./mk-image";
// import MKRollingNumber from "./mk-rolling-number";
// import MkCarousel from "./mk-carousel";
// import MkLogin from "./mk-login";
// const components = [
//   MkDialog,
//   MkDeleteDialog,
//   MkPagination,
//   MkExportTable,
//   MkPreviewSlider,
//   MkTabs,
//   MkNoData,
//   MkSearch,
//   MkBreadCrumb,
//   MkCardTitle,
//   MkCourseCard,
//   MkSchoolCard,
//   MkLiveCard,
//   MkInfoCard,
//   MkLogout,
//   MkUpload,
//   MkSelect,
//   MkNetresourceDialog,
//   MkBanner,
//   MkSortCaret,
//   MKEditor,
//   MKImage,
//   MKRollingNumber,
//   MkCarousel,
//   MkLogin,
// ];

// const install = function(Vue: any) {
//   components.forEach((component) => {
//     Vue.component(component.name, component);
//   });
// };

// // if (typeof window !== "undefined" && window.Vue) {
// //   install(window.Vue);
// // }

// export {
//   install,
//   MkDialog,
//   MkDeleteDialog,
//   MkPagination,
//   MkExportTable,
//   MkPreviewSlider,
//   MkTabs,
//   MkNoData,
//   MkSearch,
//   MkBreadCrumb,
//   MkCardTitle,
//   MkCourseCard,
//   MkSchoolCard,
//   MkLiveCard,
//   MkInfoCard,
//   MkLogout,
//   MkUpload,
//   MkSelect,
//   MkNetresourceDialog,
//   MkBanner,
//   MkSortCaret,
//   MKEditor,
//   MKImage,
//   MKRollingNumber,
//   MkCarousel,
//   MkLogin,
// };
// export default {
//   install,
// };

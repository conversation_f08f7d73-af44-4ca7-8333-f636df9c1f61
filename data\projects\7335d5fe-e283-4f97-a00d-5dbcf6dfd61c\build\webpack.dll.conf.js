const path = require('path');
const webpack = require('webpack');
const { CleanWebpackPlugin } = require('clean-webpack-plugin')
const BuildConfig = require('../config/index');
const dllPath = BuildConfig.dll.path;

module.exports = {
    entry: BuildConfig.dll.entry,
    output: {
        path: path.resolve(dllPath),
        filename: 'dt.[name].dll.js',
        library: '[name]_[hash]'
    },
    plugins: [
        new CleanWebpackPlugin(),
        new webpack.DllPlugin({
            path: path.resolve(dllPath + '/[name]-manifest.json'),
            name: '[name]_[hash]',
            context: process.cwd()
        })
    ]
};

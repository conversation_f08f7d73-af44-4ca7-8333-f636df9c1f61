﻿<template>
  <div class="platform-label-box">
    <!--专题资源-->
    <template v-if="isSpecial">
      <!--不挂目录-->
      <template v-if="specialData.filterCategory === CATALOG_TYPE.NOTREE">
        <p
          v-if="judgeShowAll() == 0"
          style="color: #009bff; margin: 0 0 10px 20px; font-size: 14px"
        >
          全部
        </p>
        <el-form v-else label-width="100px" label-position="right">
          <platform-label-item
            ref="platformLabelItemRef"
            v-if="specialData.labelList.length"
            :labelList="specialData.labelList"
            :platFromId="platFromId"
            @switch-label="switchLabel"
            @switch-customlabel="switchCustomLabel"
          ></platform-label-item>
        </el-form>
      </template>
      <!--挂目录-->
      <template v-else>

        <!--专题只有自定义展开目录 无标签-->
        <bookmenu-tree
          class="bookmenu-tree"
          v-if="judgeExpandCatalog()"
          :datalist="catalogList"
          :highlight-current="true"
          @switch-catalog="switchExCatalog"
          :currentNodeKey="exCatalogId"
        ></bookmenu-tree>
        <el-form label-width="100px" label-position="right" v-else>
          <!--专题目录-->
          <el-form-item
            v-if="specialData.filterCategory === CATALOG_TYPE.CUSTOM"
            label="专题目录:"
            :required="true"
          >
            <!--有子级的父级无法选中,需保证dom构建完成,临时解决方案(后期更改)-->
            <el-cascader
              :options="options"
              :props="{ checkStrictly: true }"
              style="display: none"
            ></el-cascader>
            <el-cascader
              ref="catalogRef"
              popper-class="special-tree"
              v-model="specialCode"
              :options="catalogList"
              :props="{
                label: 'catalogueName',
                value: 'id',
                children: 'childCatalogues',
                checkStrictly: true
              }"
              @change="switchCatalog"
            ></el-cascader>
          </el-form-item>

          <!--知识点目录-->
          <el-form-item
            v-if="specialData.filterCategory === CATALOG_TYPE.KNOWLEDGE"
            label="知识点:"
            :required="true"
          >
            <span class="knowledge-count" @click="showKnowledgeDialog">
              已选知识点
              <b>({{ selectKnowledges.length }})</b> 个 >
            </span>
          </el-form-item>
          <platform-label-item
            ref="platformLabelItemRef"
            v-if="specialData.labelList.length"
            :labelList="specialData.labelList"
            :platFromId="platFromId"
            @switch-label="switchLabel"
            @switch-customlabel="switchCustomLabel"
          ></platform-label-item>
        </el-form>
      </template>
    </template>

    <!--教材同步-->
    <template v-else>

      <el-form
        label-width="100px"
        label-position="right"
        v-if="isShowPlatformLabel"
      >
        <!--教材目录-->
        <el-form-item label="教材目录:" :required="true">
          <!--有子级的父级无法选中,需保证dom构建完成,临时解决方案(后期更改)-->
          <el-cascader
            :options="options"
            :props="{ checkStrictly: true }"
            style="display: none"
          ></el-cascader>

          <el-cascader
            ref="catalogRef"
            v-model="catalogCode"
            :options="catalogList"
            :props="{
              label: 'title',
              value: 'code',
              children: 'children',
              checkStrictly: true
            }"
            @change="switchCatalog"
          ></el-cascader>
        </el-form-item>
        <platform-label-item
          ref="platformLabelItemRef"
          v-if="columnData.labelList.length"
          :labelList="columnData.labelList"
          :platFromId="platFromId"
          @switch-label="switchLabel"
          @switch-customlabel="switchCustomLabel"
        ></platform-label-item>
      </el-form>

      <bookmenu-tree
        v-else
        class="bookmenu-tree"
        :datalist="catalogList"
        :highlight-current="true"
        @switch-catalog="switchExCatalog"
        :currentNodeKey="exCatalogId"
      ></bookmenu-tree>
    </template>
    <p
      style="margin-top: 10px"
      v-if="isShowPlatformLabel || (isSpecial && !judgeExpandCatalog())"
    >
      提示：标签标有“<span style="color: red">*</span
      >”为必填项，未标为选填项，可不填哦~
    </p>
    <knowledge-dialog
      v-if="knowledgeDialogParams.isShow"
      @on-cancle="knowledgeDialogParams.isShow = false"
      :checkedKeys="knowledgeDialogParams.checkedKeys"
      :curSubObj="curSubject"
      @on-confirm="switchKnowledge"
    ></knowledge-dialog>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  watch,
  onMounted,
  ref
} from 'vue';
import {
  queryRegionLabelListAPI,
  queryThematicCatalogueAPI,
  queryThematicManagementByIdAPI,
  queryScreenThematicCatalogueAPI,
  queryColumnAPI,
  queryColumnListAPI
} from '@/library/src/service/API/tearesearch';
import { getStructureCatalogListAPI } from '@/library/src/service/API/resource';
import { ElMessage } from 'element-plus';
import PlatformLabelItem, {
  LABEL_TYPE
} from '@/library/ui/mk-share-res/modules/PlatformLabelItem.vue';
import KnowledgeDialog from '@/library/ui/mk-share-res/KnowledgeDialog/index.vue';
import { useStore } from 'vuex';
import { IGlobalState } from '@/library/src/store';
import { SHARE_TYPE } from '@/library/ui/mk-share-res/modules/PlatformBookDialog.vue';
import BookmenuTree from '@/library/ui/mk-share-res/modules/BookmenuTree.vue';
/**目录类别 */
export enum CATALOG_TYPE {
  /**自定义目录树*/
  CUSTOM = 1,
  /** 知识点目录树 */
  KNOWLEDGE = 2,
  /** 不挂目录 */
  NOTREE = 3
}

export default defineComponent({
  name: 'platform-label',

  components: { PlatformLabelItem, KnowledgeDialog, BookmenuTree },

  emits: [
    'switch-catalog',
    'switch-label',
    'switch-customlabel',
    'switch-knowledge'
  ],

  props: {
    // 书本编码
    bookCode: {
      type: String,
      default: ''
    },
    // 区域id
    platFromId: {
      type: String,
      default: ''
    },
    // 专题id
    specialId: {
      type: String,
      default: ''
    },
    // 目录code
    catalogCode: {
      type: String,
      default: ''
    },
    // 是否是专题
    isSpecial: {
      type: Boolean,
      default: false
    },
    //分享类型
    shareType: {
      type: String,
      default: ''
    }
  },

  setup(props, ctx) {
    const subjectList = require('@/library/ui/mk-share-res/SubjectSelect/subjectList.json');
    const store = useStore<IGlobalState>();
    const catalogRef = ref<any>(null);
    const platformLabelItemRef = ref<any>(null);
    const state = reactive({
      options: [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'daohang',
              label: '导航'
            }
          ]
        }
      ],
      // 目录列表
      catalogList: [] as any[],
      // 当前专题
      specialData: {
        labelList: []
      } as any,
      // 知识点弹窗参数
      knowledgeDialogParams: {
        isShow: false,
        checkedKeys: [] as any[]
      },
      //当前学科
      curSubject: store.state.user.subjectId ? {
            //学段
            phaseId: store.state.user.phase + '',
            //学科id
            subjectId: store.state.user.subjectId,
            id: store.state.user.subjectId,
            //学科名
            name: store.state.user.subjectName
          } : { phaseId: '1', subjectId: '24', id: '24', name: '小学语文' },
      //已选知识点列表
      selectKnowledges: [] as any[],
      //教材目录绑定值
      catalogCode: [] as any[],
      //专题目录绑定值
      specialCode: [] as any[],
      //展开目录选中的目录id
      exCatalogId: '',
      // 是否展示栏目标签
      isShowPlatformLabel: false,
      // 栏目(教材同步)数据
      columnData: {
        id: '',
        title: '',
        labelList: []
      } as any
    });

    /**
     * @name:专题资源无目录无标签时展示全部
     */
    const judgeShowAll = () => {
      const index = state.specialData.labelList.findIndex((item: any) => {
        return item.type == LABEL_TYPE.TYPE || item.type == LABEL_TYPE.FORMAT;
      });
      if (index !== -1) {
        state.specialData.labelList.splice(index, 1);
      }
      return state.specialData.labelList.length;
    };
    /**
     * @name:专题资源有自定义目录无标签时目录展开
     */
    const judgeExpandCatalog = () => {
      const index = state.specialData.labelList.findIndex((item: any) => {
        return item.type == LABEL_TYPE.CUSTOMTREE;
      });
      if (index !== -1) {
        state.specialData.labelList.splice(index, 1);
      }
      if (
        state.specialData.filterCategory === CATALOG_TYPE.CUSTOM &&
        state.specialData.labelList.length < 1
      ) {
        return true;
      } else {
        return false;
      }
    };
    /**
     * @name 表单校验
     */
    const validateForm = () => {
      if (props.isSpecial) {
        if (state.specialData.filterCategory === CATALOG_TYPE.CUSTOM) {
          //专题资源有自定义目录无标签时展开目录
          if (judgeExpandCatalog() && !state.exCatalogId) {
            ElMessage({ message: '请选择专题目录！', type: 'warning' });
            return false;
          }
          //专题资源有标签时
          if (
            !judgeExpandCatalog() &&
            (!state.specialCode || state.specialCode.length == 0)
          ) {
            ElMessage({ message: '请选择专题目录！', type: 'warning' });
            return false;
          }
        }
        //专题资源挂知识点目录
        if (
          state.specialData.filterCategory === CATALOG_TYPE.KNOWLEDGE &&
          state.selectKnowledges.length == 0
        ) {
          ElMessage({ message: '请选择知识点目录！', type: 'warning' });
          return false;
        }
        //专题资源挂目录自定义校验 存在自定义选项时
        if (
          state.specialData.labelList.length > 0 &&
          !platformLabelItemRef.value.validateForm()
        ) {
          return false;
        }
      } else {
        //存在标签时
        if (state.isShowPlatformLabel) {
          //教材同步挂教材目录
          if (!state.catalogCode || state.catalogCode.length == 0) {
            ElMessage({ message: '请选择教材目录！', type: 'warning' });
            return false;
          }
          // 教材同步自定义校验 存在自定义选项时
          if (
            state.columnData.labelList.length > 0 &&
            !platformLabelItemRef.value.validateForm()
          ) {
            return false;
          }
        } else {
          //无标签时
          if (!state.exCatalogId) {
            ElMessage({ message: '请选择教材目录！', type: 'warning' });
            return false;
          }
        }
      }
      return true;
    };
    /**
     * @name: 获取标签列表
     */
    const getLableList = async () => {
      const res = await queryRegionLabelListAPI({
        regionId: props.platFromId,
        isCustom: -1
      });
      if (res.code === 1) {
        for (let i = 0; i < state.columnData.labelList.length; i++) {
          let item = state.columnData.labelList[i];
          const newLabel = res.data.find((sitem: any) => {
            return sitem.id === item.labelId;
          });
          if (newLabel) {
            item.optionValue = newLabel.optionValue;
          }
        }
      }
    };
    /**
     * @name: 获取目录
     */
    const getCataLogList = async () => {
      if (!props.bookCode) {
        return;
      }
      const res = await getStructureCatalogListAPI({
        bookCode: props.bookCode
      }).catch(() => {
        state.catalogList = [];
      });
      if (res.code == 1) {
        let catalogList = []
        if (state.isShowPlatformLabel) {
          catalogList = [{
            code: '',
            expand: false,
            id: '',
            pid: '',
            sort: 0,
            title: '全部',
            children: []
          },...res.data];
          catalogList = catalogList.map((item: any) => {
            return handleCatalog(item);
          });
        } else {
          catalogList = [{
            code: '',
            expand: false,
            id: '0',
            pid: '0',
            sort: 0,
            title: '全部'
          },...res.data];
        }
        state.catalogList = catalogList;
        setCurCategoryId()
      } else {
        state.catalogList = [];
      }
    };
    /**
     * @name: 目录最后一级处理
     * @param item 当前目录节点
     */
    const handleCatalog = (item: any, children: string = 'children') => {
      item['leaf'] = item[children].length === 0 ? 'leaf' : '';
      if (item[children].length) {
        item[children] = item[children].map((sitem: any) => {
          return handleCatalog(sitem, children);
        });
      }
      return item;
    };
    /**
     * @name: 获取标签数据
     */
    const getLabelData = () => {
      const labelList = props.isSpecial
        ? state.specialData.labelList
        : state.columnData.labelList;
      for (let i = 0; i < labelList.length; i++) {
        const item = labelList[i];
        if (item.type === LABEL_TYPE.TEXTBOOK) {
          getCataLogList();
          break;
        }
      }
    };
    /**
     * @name: 切换标签
     * @param item 标签对象
     */
    const switchLabel = (item: any) => {
      ctx.emit('switch-label', item);
    };
    /**
     * @name: 切换目录
     * @param curCatalog 当前目录
     */
    const switchCatalog = (data: any, node: any) => {
      let result: any = [];
      const checkList = catalogRef && catalogRef.value.getCheckedNodes();
      if (checkList.length) {
        checkList[0].pathNodes.forEach((item: any) => {
          result.push({
            id: item.data.id,
            code: item.value,
            title: item.label
          });
        });
      }
      const item = [...result].pop()
      state.exCatalogId = item.id;
      ctx.emit('switch-catalog', item, result);
    };
    /**
     * @name 选中展开的目录
     */
    const switchExCatalog = (v: any, selectTreePathList: any[]) => {
      state.exCatalogId = v ? v.id : '';
      ctx.emit('switch-catalog', v, selectTreePathList);
    };
    /**
     * @name: 切换自定义标签
     * @param customLabelList 当前自定义标签
     */
    const switchCustomLabel = (customLabelList: any) => {
      ctx.emit('switch-customlabel', customLabelList);
    };
    /**
     * @name: 获取专题详情
     */
    const getSpecialDetail = async () => {
      if (!props.specialId) {
        return;
      }
      const res = await queryThematicManagementByIdAPI({
        id: props.specialId,
        regionId: props.platFromId
      });

      state.specialData = res.data;
      if (state.specialData.labelList.length) {
        await getLabelData();
      }
      if (state.specialData.filterCategory === CATALOG_TYPE.CUSTOM) {
        await getSpecialCatalog();
      }
    };
    /**
     * @name: 专题展开目录数据处理
     * @param data 专题目录数据
     */
    const handlerSpecialCataLog = (data: any[]) => {
      return data.map((item: any) => {
        item.title = item.catalogueName;
        item.children = item.childCatalogues;
        item.expand = false;
        item.pid = item.parentId || '0';
        item.code = item.id;
        if (item.children) {
          item.children = handlerSpecialCataLog(item.children);
        }
        return item;
      });
    };
    /**
     * @name: 获取专题目录
     */
    const getSpecialCatalog = async () => {
      let catalogList:any[] = [];
      const res = props.shareType == SHARE_TYPE.school
          ? await queryScreenThematicCatalogueAPI({
              thematicId: props.specialId,
              schoolId: store.state.user.schoolId
            })
          : await queryThematicCatalogueAPI({
              thematicId: props.specialId,
              regionId: props.platFromId
            });
      if (res.code == 1) {
        if (judgeExpandCatalog()) {
          //专题展开目录列表
          catalogList = handlerSpecialCataLog(res.data);
          catalogList = [{
              code: '',
              expand: false,
              id: '0',
              pid: '0',
              sort: 0,
              title: '全部'
            }, ...catalogList];
        } else {
          catalogList = [{
            catalogueName: '全部',
            childCatalogues: [],
            id: '',
            parentId: '',
            parentName: '',
            regionId: '',
            sort: 0,
            thematicId: ''
          },...res.data];
          catalogList = catalogList.map((item: any) => {
            return handleCatalog(item, 'childCatalogues');
          });
        }
        state.catalogList = catalogList;
        setCurCategoryId()
      } else {
        ElMessage.error(res.msg);
      }

    };
    /**
     * @name: 展示知识点弹窗
     */
    const showKnowledgeDialog = () => {
      const checkedKeys = state.selectKnowledges.map((item: any) => {
        return item.code;
      });
      state.knowledgeDialogParams = {
        isShow: true,
        checkedKeys
      };
    };
    /**
     * @name: 切换知识点
     * @param knowledge 知识点列表
     */
    const switchKnowledge = (knowledge: any[], selfResult: any[], curSubject: any) => {
      state.curSubject = curSubject;
      state.selectKnowledges = selfResult;
      const label = state.specialData.labelList.find((item: any) => {
        return item.type === LABEL_TYPE.KNOWLEDGE;
      });
      const labelId = label ? label.labelId : '';
      ctx.emit('switch-knowledge', knowledge, selfResult, curSubject, labelId);
    };
    /**
     * @name:判断知识点学科列表中是否当前学科
     */
    const handleSubject = () => {
      if (state.curSubject) {
        let item = subjectList.find((item: any) => item.subjectId === state.curSubject.subjectId)
        item = item || subjectList[0]
        if (item) {
          state.curSubject = {
            phaseId:item.phaseId,
            subjectId: item.subjectId,
            id: item.id,
            name: item.name
          };
        }
      }
    };
    /**
     * @name: 获取栏目列表
     */
    const getColumnList = async () => {
      const res = await queryColumnListAPI({
        keyWord: '教材',
        isPublish: 1,
        page: 1,
        limit: 1,
        regionId: props.platFromId
      });
      if (res.code === 1) {
        // 教材同步id
        const id = res.data.rows.find((item: any) => {
          return item.type == 0;
        })?.id;
        if (id) {
          getColumnDetail(id);
        } else {
          state.isShowPlatformLabel = false;
        }
      }
    };
    /**
     * @name: 获取栏目详情
     * @param id 栏目id
     */
    const getColumnDetail = async (id: string) => {
      const res = await queryColumnAPI({ id, regionId: props.platFromId });
      if (res.code === 1) {
        state.columnData = res.data;
        state.isShowPlatformLabel = res.data.labelList.length > 3;
        if (state.columnData.labelList.length) {
          await getLableList();
          await getLabelData();
        }
      } else {
        state.isShowPlatformLabel = false;
      }
    };
    /**
     * @name: 设置目录当前值
     */
    const setCurCategoryId = ()=>{
      let catagory:any = null
      for (let i = 0; i < state.catalogList.length; i++) {
        const item = state.catalogList[i]
        catagory =  getCatalogItem(item,props.catalogCode)
        if( catagory ){
          break
        }
      }
      state.exCatalogId = catagory ? catagory.id : ''
    }
    /**
     * @name: 递归获取当前目录
     * @param item 目录节点对象
     * @param value 当前值
     */
    const getCatalogItem = (item: any, value:string)=>{
      if( item.code === value || item.id === value ){
          return item
      }
      let result:any = null
      const children = item.children || item.childCatalogues
      if( children && children.length ){
        flag: for (let i = 0; i < children.length; i++) {
          result = getCatalogItem(children[i],value)
          if( result ){
              break flag
          }
        }
      }
      return result
    }

    /**
     * @name: 初始化
     */
    const init = async () => {
      if (props.shareType == SHARE_TYPE.platform) {
        await getColumnList();
      } else {
        getCataLogList();
      }
      if (props.isSpecial) {
        await getSpecialDetail();
      }
    };


    watch(
      () => props.bookCode,
      () => {
        !props.isSpecial && getCataLogList();
      }
    );
    watch(
      () => props.specialId,
      () => {
        getSpecialDetail();
      }
    );

    onMounted(() => {
      init();
      handleSubject();
    });

    return {
      ...toRefs(state),
      catalogRef,
      platformLabelItemRef,
      LABEL_TYPE,
      CATALOG_TYPE,
      SHARE_TYPE,
      switchLabel,
      switchCatalog,
      switchCustomLabel,
      switchKnowledge,
      showKnowledgeDialog,
      validateForm,
      judgeShowAll,
      judgeExpandCatalog,
      switchExCatalog
    };
  }
});
</script>

<style lang="scss" scoped>
.platform-label-box {
  .el-form-item {
    margin-bottom: 0px !important;
    .knowledge-count {
      display: inline-block;
      width: auto;
      height: 40px;
      line-height: 40px;
      cursor: pointer;
      color: #2574ff;
    }
  }
  .bookmenu-tree {
    ::v-deep(.el-tree-node__label) {
      font-size: 16px;
    }
  }
}
</style>
<style lang="scss">
.special-tree {
  .el-cascader-node__label {
    max-width: 260px !important;
  }
}
</style>

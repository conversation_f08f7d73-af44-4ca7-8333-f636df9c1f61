<!--
 * @Descripttion: 
 * @Author: 小圆
 * @Date: 2023-10-27 09:22:37
 * @LastEditors: liuyue <EMAIL>
-->
<template>
  <div contenteditable="false" id="notice-mark" :class="['full-wrap','m-b-4',isFlex ? 'flex-center':'']">
    <div v-if="showRight && !Paper.isABPaper" class="full-item flex-center">
      <span class="card-color">正确填涂</span>
      <div class="full-sec"></div>
    </div>
    <template v-if="Paper.isABPaper">
      <div class="full-item flex-center">
          <span class="card-color">A卷</span>
          <div class="full-example" id="ab-a-mark"></div>
        </div>
        <div class="full-item flex-center">
          <span class="card-color">B卷</span>
          <div class="full-example" id="ab-b-mark"></div>
        </div>
    </template>

    <div v-if="showMiss" class="full-item flex-center">
      <span class="card-color">缺考标记</span
      ><span class="full-example" id="miss-exam-mark"></span>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs } from "vue";
import Paper from "@/views/paper";

export default defineComponent({
  props: {
    showRight:{
      type:Boolean,
      default:true
    },
    showMiss:{
      type:Boolean,
      default:true
    },
    isFlex:{
      type:Boolean,
      default:true
    }
  },
  setup() {
    const state = reactive({
      Paper:Paper,
    })
    return {
      ...toRefs(state),
    };
  },
});
</script>

<style lang="scss" scoped>
.full-wrap {
  margin-top: 5mm;
  max-width: 90mm;
  width: 100%;
  line-height: 7mm;
  font-size: 3mm !important;
  border: 0.1mm solid #000;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.full-item {
  vertical-align: middle;

  span {
    margin-right: 1mm;
  }
}

.full-sec {
  display: inline-block;
  border-width: 1.5mm 2.5mm;
  border-style: solid;
  border-color: #000;
  width: 0;
  height: 0;
}

.full-example {
  margin: 0;
  padding: 0;
  width: 5mm;
  height: 3mm;
  border: 0.1mm solid #000;
  font-size: 5mm;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-pack: distribute;
  justify-content: space-around;
}

.card-color {
  color: #000;
}

.flex-end {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: end;
  -ms-flex-pack: end;
  justify-content: flex-end;
}

.flex-center {
  display: flex;
  align-items: center;
}
</style> 
/**
 * @Description:
 * @Author: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>@class30.com
 * @Date: 2023-11-24 13:40:37
 * @LastEditors: l<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>@class30.com
 * @LastEditTime: 2024-03-25 11:53:06
 */
/**
 * 自定义作文格
 */
CKEDITOR.plugins.add("FourLine", {
  requires: ["dialog"],
  init: function (editor) {
    editor.addCommand("FourLine", new CKEDITOR.dialogCommand("FourLine"));
    editor.ui.addButton("FourLine", {
      label: "插入四线格",
      command: "FourLine",
      cindex: 0,
      click: function (e) {
        editor.execCommand("FourLine");
      },
    });
    CKEDITOR.dialog.add("FourLine", this.path + "dialog/FourLine.js");
  },
});

<template>
  <div class="line-list" :style="{ lineHeight: lineHeight + 'mm' }">
    <div class="underline"
      :style="{ width: line.width, 'background': isFillLine ? 'rgb(233 233 233 / 50%)' : 'transparent' }">

      <div class="unline-writing" :style="{ lineHeight: wordLineHeight }" :data-tag="boxTag" contenteditable="true"
        @blur="updateContent($event, line)" @paste="handlePasteEvent" v-html="line.editContent"></div>

      <div class="line-btns nosave">
        <div class="line-btn click-element minus" title="删除线条" @click="changeLineNum('minus')"
          v-if="!isFillEva && ques.lineList.length > 1"></div>
        <div class="line-btn click-element plus" title="添加线条" @click="changeLineNum('plus')"
          v-if="!isFillEva && ques.lineList.length < 20"></div>
        <div class="line-btn click-element top" title="上移" @click="setLineFeed(false)"></div>
        <div class="line-btn click-element bottom" title="下移" @click="setLineFeed(true)"></div>
      </div>
      <div class="drag-button nosave" v-if="!isFillEva" @mousedown="mouseDown(index, $event)">
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  getCurrentInstance,
  ComponentInternalInstance,
  computed,
} from 'vue';
import {
  MARK_TYPE,
} from '@/typings/card';
import Paper from '@/views/paper';
import { pxConversionMm } from '@/utils/dom';

export default defineComponent({
  props: {
    //题干信息
    ques: {
      type: Object,
      default: () => {
        return {};
      },
    },
    line: {
      type: Object,
      default: () => {
        return {};
      },
    },
    //题目下标
    index: {
      type: Number,
      default: 0,
    },
    lineHeight: {
      type: String,
      default: '7mm',
    },
    isFillEva: {
      type: Boolean,
      default: false,
    },
  },
  setup(props: any, ctx: any) {
    const state = reactive({
      // 文字最大行高
      maxWordLineHeight: 7,
      Paper: Paper,
      isDrag: false,
      maxLineHeigt: 7,
      clientX: '' as any,
      startWidth: '' as any,
      startX: '' as any,
      boxTag: 'subjectbox_' + props.ques.id,
      MARK_TYPE: MARK_TYPE
    });
    const instance = getCurrentInstance() as ComponentInternalInstance | null;

    // 计算填空题文字的行高
    const wordLineHeight = computed(() => {
      const lineHeight = props.lineHeight >= state.maxWordLineHeight ? state.maxWordLineHeight : props.lineHeight
      return lineHeight + 'mm';
    });

    const isFillLine = computed(() => {
      return props.ques.markType == MARK_TYPE.ONLINE;
    });

    /* 改变线条数量 */
    const changeLineNum = (type: 'plus' | 'minus') => {
      ctx.emit('changeLineNum', { ques: props.ques, type, index: props.index });
    };

    /* 设置线条换行 */
    const setLineFeed = (bool: boolean) => {
      props.line.isLineFeed = bool;
      ctx.emit('setLineFeed', props.ques);
    };

    const mouseDown = (index: any, e: any) => {
      console.log('mouseDown', state.isDrag);

      state.isDrag = true;
      state.startX = e.clientX;
      state.startWidth = props.ques.lineList[props.index].width.replace('mm', '');
      document.addEventListener('pointermove', mouseMove);
      document.addEventListener('pointerup', mouseUp);
    };

    const mouseMove = (e: any) => {
      if (state.isDrag) {
        state.clientX = `${pxConversionMm(Number(e.clientX) - Number(state.startX))}`;
        props.ques.lineList[props.index].width =
          Math.trunc(Number(state.startWidth) + Number(state.clientX)) + 'mm';
        instance!.proxy!.$forceUpdate();
      }
    };

    const mouseUp = () => {
      state.isDrag = false;
      let el: any = document.getElementsByClassName('page-box')[0];
      let pageWidth = el.style.width;
      if (
        Number(props.ques.lineList[props.index].width.replace('mm', '')) >
        Number(pageWidth.replace('mm', ''))
      ) {
        props.ques.lineList[props.index].width = pageWidth.replace('mm', '') - 10 + 'mm';
      }
      if (props.ques.lineList[props.index].width.replace('mm', '') < 10) {
        props.ques.lineList[props.index].width = '10mm';
      }

      ctx.emit('changeLineWidth', props.ques);
      document.removeEventListener('pointermove', mouseMove);
      document.removeEventListener('pointerup', mouseUp);
    };

    const updateContent = (event: any, line: any) => {
      props.ques.editContent = line.editContent = event.target.innerHTML;
      ctx.emit('updateContent', props.ques);
    };

    const handlePasteEvent = event => {
      let clipboardData = event.clipboardData;
      let items = clipboardData.items;
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          event.preventDefault();

          let file = items[i].getAsFile();
          ctx.emit('pasteImage', {
            file,
            withCredentials: false,
            method: 'post',
            onProgress() { },
          });
          return;
        }
      }
    };

    return {
      MARK_TYPE,
      ...toRefs(state),
      handlePasteEvent,
      wordLineHeight,
      isFillLine,
      changeLineNum,
      updateContent,
      setLineFeed,
      mouseDown,
      mouseMove,
      mouseUp,
    };
  },
});
</script>

<style lang="scss" scoped>
.fill-container {
  vertical-align: bottom;
  margin: 0;
  padding: 0;
  min-height: 9.8mm;
  border-bottom: 0.1mm solid #000;
  overflow: hidden;
  font-size: 3.6mm;
}

.line-list {
  // margin-left: 2mm;

  ::v-deep p {
    line-height: unset;
  }
}

.underline {
  height: 100%;
  position: relative;
  vertical-align: bottom;
  margin: 0;
  padding: 0;
  // min-height: 9.8mm;
  border-bottom: 0.1mm solid #000;
  font-size: 3.6mm;
  // margin-right: 1.5mm;
  display: inline-block;
  overflow: hidden;

  +.underline {
    margin-right: 7mm;
  }

  .line-btns {
    display: none;
  }

  &:hover,
  &:focus {
    outline: 1px dashed;

    .line-btns {
      display: block;
    }
  }

  .unline-writing {
    display: inline-block;
    vertical-align: bottom;
    width: 100%;
    position: relative;
    padding: 0 1mm 0;
    // line-height: 7mm !important;
    outline: none;
  }
}

.line-btns {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 100;
  padding: 2px;
}

.line-btn {
  width: 15px;
  height: 15px;
  float: left;
  margin-left: 5px;
  background-repeat: no-repeat;

  &.plus {
    background-image: url('../../assets/iconfont-plus.png');
  }

  &.minus {
    background-image: url('../../assets/iconfont-minus.png');
  }

  &.top {
    background-image: url('../../assets/iconfont-top.png');
  }

  &.bottom {
    background-image: url('../../assets/iconfont-bottom.png');
  }
}

.drag-button {
  position: absolute;
  width: 20px;
  height: 18px;

  background: {
    image: url('../../assets/icon_drag.svg');
    size: cover;
    repeat: no-repeat;
  }

  cursor: ew-resize;
  bottom: 2px;
  right: 7px;
  display: inline-block;
  z-index: 1000;
}
</style>
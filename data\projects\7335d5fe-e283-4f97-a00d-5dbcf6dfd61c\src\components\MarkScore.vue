<template>
  <!-- 简答题 -->
  <template v-if="typeId == QUES_TYPE.subject">

    <!-- 分数格 表格-->
    <template v-if="markType == MARK_TYPE.NUMBER">
      <table class="score-table"
        style="font-size: 3.7mm; line-height: 7mm; font-family: 宋体, SimSun">
        <template v-if="isSplit">
          <tr class="custom-tr">
            <template v-for="(item, index) in scoreList" :key="index">
              <td :colspan="scoreList.length" class="custom-td" v-if="(isDecimal && item == 0.5) ||
    (Number(score) > tdNum && item == 9)
    " style="border-left-width: 1mm">
                {{ item }}
              </td>
              <td :colspan="scoreList.length" v-else class="custom-td">
                {{ item }}
              </td>
            </template>
          </tr>
        </template>

        <template v-else>
          <tr class="custom-tr" v-for="line in scoreList">
            <template v-for="(item, index) in line" :key="index">
              <td :colspan="line.length" class="custom-td" v-if="(isDecimal && item == 0.5)"
                style="border-left-width: 1mm">
                {{ item }}
              </td>
              <td :colspan="line.length" v-else class="custom-td">
                {{ item }}
              </td>
            </template>
          </tr>
        </template>
      </table>
    </template>

    <template v-else-if="markType == MARK_TYPE.WRITE">
      <div class="write-score-box">
        <div class="write-score-item" v-for="(item, index) in String(Math.floor(score)).length" :key="index">
        </div>
        <template v-if="isDecimal">
          <div class="score-dot"></div>
          <div class="write-score-item">
          </div>
        </template>
      </div>
    </template>

    <!-- 对错 -->
    <template v-else>
      <template v-if="Paper.cardType == ICARDMODEL.QUESCARD">
        <div class="mark-list">
          <div class="mark-item"><span class="iconfont">&#xe953;</span></div>
          <div class="mark-item"><span class="iconfont">&#xe653;</span></div>
          <div class="mark-item"><span class="iconfont">&#xe952;</span></div>
        </div>
      </template>

      <template v-else>
        <div class="mark-item"><span class="iconfont">&#xe953;</span></div>
        <div class="mark-item"><span class="iconfont">&#xe653;</span></div>
        <div class="mark-item"><span class="iconfont">&#xe952;</span></div>
      </template>

    </template>

  </template>

  <!-- 填空题 -->

  <template v-else>
    <span class="noeditsave nbsp">&ZeroWidthSpace;</span>

    <!-- 分数格 -->
    <template v-if="markType == MARK_TYPE.NUMBER && typeId != QUES_TYPE.judge">
      <span class="score-option-item noeditsave" style="border-left: 0.2mm solid #000">
        {{ score }}
      </span>
      <template v-for="idx of scoreList" :key="idx">
        <span class="score-option-item noeditsave">
          {{ idx }}
        </span>
      </template>
    </template>
    <!-- 仅显示错误 -->

    <template v-else-if="markType == MARK_TYPE.ONLYNO && typeId != QUES_TYPE.judge">
      <span class="score-option-item noeditsave click-element"
        :class="{ active: data.answer === 1 }" title="错误" style="border-left: 0.2mm solid #000">
        <span class="opttion-txt iconfont" v-html="TFNameList[JUDGE_TYPE.MARK][1]">
        </span>
      </span>
    </template>
    <template v-else-if="markType == MARK_TYPE.ONLINE && typeId != QUES_TYPE.judge">
    </template>

    <template v-else>
      <span class="score-option-item noeditsave click-element"
        :class="{ active: data.answer === TFAnswers[0] }" title="正确"
        style="border-left: 0.2mm solid #000" :style="{ lineHeight: '5mm' }">
        <span class="opttion-txt iconfont" v-html="TFNameList[judgeType][0]">
        </span>
      </span>
      <span class="score-option-item noeditsave click-element"
        :class="{ active: data.answer === TFAnswers[1] }" title="错误" :style="{ lineHeight: '5mm' }">
        <span class="opttion-txt iconfont" v-html="TFNameList[judgeType][1]">
        </span>
      </span>
    </template>

    <span class="noeditsave nbsp">&ZeroWidthSpace;</span>
  </template>
</template>

<script lang="ts">
import { defineComponent, reactive, toRefs, onMounted, ref, watch, computed } from 'vue';
import { ICARDMODEL, JUDGE_TYPE, MARK_TYPE, QUES_TYPE } from '@/typings/card';
import { PaperConstant } from '@/views/paper.constant';
import Paper from '@/views/paper';
export default defineComponent({
  props: {
    // 题干信息
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
    judgeType: {
      type: Number,
      default: JUDGE_TYPE.MARK,
    },
    //题目类型
    typeId: {
      type: Number,
      default: QUES_TYPE.fill,
    },
    //打分模式
    markType: {
      type: Number,
      default: MARK_TYPE.YESORNO,
    },
    //题目分数
    score: {
      type: Number,
      default: 1,
    },
    //步进
    step: {
      type: Number,
      default: 1,
    },
    //是否有小数
    isDecimal: {
      type: Boolean,
      default: false,
    },
    //十个位分开
    isSplit: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
  },
  components: {},
  emits: ['change'],
  setup(props: any, ctx: any) {
    const state = reactive({
      Paper: Paper,
      ICARDMODEL: ICARDMODEL,
      QUES_TYPE: QUES_TYPE,
      MARK_TYPE: MARK_TYPE,
      JUDGE_TYPE: JUDGE_TYPE,
      tableScore: {} as any,
      scoreList: [] as Array<any>,
      tdNum: 20,
      lineNum: 16,
      rowNum: 1,
      rowKeyList: [] as Array<string>,
      TFNameList: {
        1: ['&#xe953;', '&#xe952;'], // √ ×
        2: ['T', 'F'],
        3: ['A', 'B'],
      },
      TFAnswers: PaperConstant.TFAnswers,
    });

    const markRef = ref<any>(null);
    /**
     * @name:根据分数、步进、是否有小数生成打分框数据
     */
    const renderScoreTable = () => {
      if (props.typeId == QUES_TYPE.subject) {
        const maxScore = Number(props.score);
        let scoreList = [];
        let score = 0;
        let lineScore = [];
        if (props.isDecimal) {
          if (props.isSplit) {
            scoreList.push(0.5);
          } else {
            lineScore.push(0.5);
          }
        }

        while (score <= maxScore) {
          if (props.isSplit) {
            if (maxScore > state.tdNum && score > 9) {
              score % 10 || scoreList.push(score);
            } else {
              scoreList.push(score);
            }
            score++;
          } else {
            lineScore.push(score);
            if (lineScore.length % state.lineNum == 0) {
              lineScore = lineScore.concat(new Array(state.lineNum - lineScore.length).fill(''));
              lineScore = lineScore.reverse();
              scoreList.push(lineScore);
              lineScore = [];
            }
            let step = Number(props.step);
            if(props.isDecimal){
              step = 1;
            }
            score = (score * 10 + step * 10) / 10;
            if (score > maxScore && (score*10 - step*10)/10 != maxScore) {
              lineScore.push(maxScore);
            }
          }
        }
        if (lineScore.length != 0) {
          lineScore = lineScore.concat(new Array(state.lineNum - lineScore.length).fill(''));
          lineScore = lineScore.reverse();
          scoreList.push(lineScore);
        }
        state.scoreList = scoreList.reverse();
      } else {
        let scoreList = [0];
        let score = parseFloat(props.step);
        while (score < props.score) {
          scoreList.push(score);
          score = (score * 10 + parseFloat(props.step) * 10) / 10;
        }
        state.scoreList = scoreList.reverse();
      }
    };

    watch(
      () => props.score,
      () => {
        renderScoreTable();
      }
    );
    watch(
      () => props.step,
      () => {
        renderScoreTable();
      }
    );
    watch(
      () => props.isDecimal,
      () => {
        renderScoreTable();
      }
    );
    /**
     * 页面一开始加载
     */
    onMounted(async () => {
      renderScoreTable();
    });

    return {
      ...toRefs(state),
      markRef,
    };
  },
});
</script>

<style lang="scss" scoped>
.score-option-item {
  border: 0.2mm solid #000;
  border-left: 0.2mm;
  display: inline-block;
  width: 8mm;
  height: 6mm;
  font-size: 3mm;
  text-align: center;
  line-height: 6mm;
  margin: 0 0 0 0;
  vertical-align: bottom;

  &.active {
    background-color: #3399ff;
    color: #fff;
  }
}

.mark-list {
  // float: right;
  position: absolute;
  bottom: 3mm;
  background: white;
  right: 3mm;
  margin: 0 0 0 1mm !important;
  z-index: 100;

  .mark-item {
    display: inline-block;
    border-top: 0.1mm solid #000;
    border-left: 0.1mm solid #000;
    border-bottom: 0.1mm solid #000;
    padding: 0 1mm !important;
    width: 8mm;
    height: 7mm;
    line-height: 7mm;
    text-align: center;

    &:last-child {
      border: 0.1mm solid #000;
    }
  }
}

.mark-item {
  display: inline-block;
  border-top: 0.1mm solid #000;
  border-left: 0.1mm solid #000;
  border-bottom: 0.1mm solid #000;
  padding: 0 1mm !important;
  width: 8mm;
  height: 7mm;
  line-height: 7mm;
  text-align: center;

  &:last-child {
    border: 0.1mm solid #000;
  }
}

.write-score-box{
  float: right;
  .write-score-item{
    width: 10mm;
    height: 12mm;
    margin-top: 1mm;
    margin-right: 1mm;
    border: 1px dashed #000;
    display: inline-block;
  }
  .score-dot{
    width: 2mm;
    display: inline-block;
    vertical-align: top;
    margin-top: 6mm;
  }
  .score-dot:before{
    content: "";
    display: inline-block;
    width: 1mm;
    height: 1mm;
    background: #000;
    border-radius: 50%;
  }
}

table {
  border-collapse: collapse;
}

.page-box {
  &.onlycard {
    .score-table {
      border: none;
      // border-bottom: 0.1mm solid #000;
    }
  }
}

.score-table {
  width: 100%;
  // border: 0.1mm solid #000;

  td {
    &:first-child {
      border-left: none;
    }

    // vertical-align: inherit;
    border-left: 0.1mm solid #000;
    border-bottom: 0.1mm solid #000;
    // border-right: 0.1mm solid #000;
    // border-top: 0.1mm solid #000;
    width: calc(100% / 22);
    // border-collapse: collapse;
    text-align: center;
    line-height: 6mm;

    &:last-child {
      border-right: unset;
    }
  }
}
</style>

<template>
    <div class="q-opt merge-img" v-if="data.get(qid)?.list?.length">
        <div class="img-list">
            <div class="img-item" v-for="img in data.get(qid).list" @click="handleClick(img)">
                <img :src="img.src" :height="data.get(qid).h" />
                <div style="text-align: center;">第{{ img.quesName }}题图{{ img.sort }}</div>
                <div class="mask nosave">还原</div>
            </div>
        </div>
        <div class="pop down nosave">
            <div class="pop-icon pop-icon-free" @mousedown="startDrag" title="拖动鼠标调整高度">
                <span class="free"></span>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent, toRefs, reactive, nextTick } from 'vue'
import bus from '@/utils/bus';
import Paper from '@/views/paper';
export default defineComponent({
    props: {
        qid: {
            type: String,
            default: () => {
                return "";
            },
        },
        data: {
            type: Object,
            default: () => {
                return {};
            },
        },
    },
    setup(props, ctx) {
        const state = reactive({});
        let startY = 0;
        let startHeight = 0;
        let newHeight = 0;
        function handleDrag(e) {
            const deltaY = e.clientY - startY;
            newHeight = startHeight + deltaY;
            newHeight = newHeight < 60 ? 60 : newHeight;
            newHeight = newHeight > 260 ? 260 : newHeight;
            props.data.get(props.qid).h = newHeight;
        }

        function stopDrag() {
            document.removeEventListener('mousemove', handleDrag);
            document.removeEventListener('mouseup', stopDrag);
            bus.emit('renderAllCard');
        }

        const startDrag = (e) => {
            startY = e.clientY;
            startHeight = props.data.get(props.qid).h;
            document.addEventListener('mousemove', handleDrag);
            document.addEventListener('mouseup', stopDrag);
        };

        const handleClick = (img) => {
            //过滤掉当前的图片
            props.data.get(props.qid).list = props.data.get(props.qid).list.filter(item => {
                return item.src != img.src;
            })
            //获取当前图片对应的题目是否还有其他图片
            const curQuesImgs = props.data.get(props.qid).list.filter(item => {
                return item.quesId == img.quesId
            })
            if (!curQuesImgs.length) {
                //如果当前题目不存在其他图片，修改题目标识属性isMergeImg
                let ques = Paper.findSmallQuesByQuesId(img.quesId);
                ques.isMergeImg = false;
            } else {
                //如果当前题目存在其他图片，则修改对应sort
                props.data.get(props.qid).list.forEach((item, index) => {
                    if (item.quesId == img.quesId) {
                        if (curQuesImgs.length > 1) {
                            item.sort = index + 1;
                        } else {
                            item.sort = ''
                        }
                    }
                })
            }
            nextTick(() => {
                let imgEle = document.createElement('img');
                imgEle.src = img.src;
                imgEle.width = img.width;
                imgEle.height = img.height;
                let els = document.querySelectorAll(`[id="${img.quesId}"]`);
                //还原原题图片
                let content = $('.tmpcontent,.hascontent,.ques-content',els[0]);
                if(!content.length){
                    content = $('.tmpcontent,.hascontent,.ques-content',els[1]);
                }
                try{
                    content[content.length-1].append(imgEle);
                }catch(e){
                    content = $('.ques-box',els[0]).append(imgEle);
                }
                
                // $('img[src="' + img.src + '"]').show();
                Paper.notifyUpdateData('right');
                bus.emit('orderQuesInfos');
            })
        }

        return {
            ...toRefs(state),
            startDrag,
            handleClick
        }
    },
})
</script>

<style lang="scss" scoped>
.merge-img {
    .img-list {
        display: flex;
        justify-content: space-around;

        .img-item {
            margin: auto;
            position: relative;
            .mask{
                display: none;
            }
            &:hover {
                .mask {
                    display: flex;
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    background: #5252527d;
                    text-align: center;
                    color: #fff;
                    font-size: 20px;
                    font-weight: bold;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;
                }
            }

        }
    }

    &:hover {
        outline: 1px solid;

        .pop {
            display: block;
        }
    }
}
</style>
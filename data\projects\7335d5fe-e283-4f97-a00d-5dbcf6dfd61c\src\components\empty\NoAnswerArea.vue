<!-- 非作答区域 -->
<template>
  <div :id="qId" disable-spread class="q-opt card ques-noanswerarea" style="padding-top:0;">
    <div class="ques-box" :id="`areabox_${qId}`"
      :style="{ height: item.height, lineHeight: item.height }">
      非作答区域

      <div class="btn-tool btn-delete click-element" title="点击删除非作答区" @click="deleteArea"></div>
      <!-- 调整区块尺寸 -->
      <div class="btn-tool pop down nosave">
        <div class="pop-icon click-element" title="点击调整至页面底部" @click="setHeightFull">
          <span class="bottom"></span>
        </div>
        <div class="pop-icon pop-icon-free click-element" @pointerdown="startDrag($event, qId)" title="拖动鼠标调整高度">
          <span class="free"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, nextTick, reactive, toRefs } from 'vue';
import Paper from '../../views/paper';
import bus from '@/utils/bus';
import { pxConversionMm } from '@/utils/dom';
import { footerKeepHeight, getHeaderInfoHeight, pageHeight } from '@/utils/config';
import { IPAGELAYOUT } from '@/typings/card';

export default defineComponent({
  props: {
    qId: {
      type: String,
      default: '',
    },
    // 题干信息
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 题目下标
    index: {
      type: Number,
      default: 0,
    },
    // 大题目下标
    bigIndex: {
      type: Number,
      default: 0,
    },
  },
  setup(props: any, ctx: any) {
    const state = reactive({
      isA33Layout: Paper.pageLayout == IPAGELAYOUT.A33,
    });

    /* 删除区域 */
    const deleteArea = () => {
      let quesInfos = Paper.removeBigQuesByQuesId(props.qId);
      Paper.updateAndMergeQuestions(quesInfos);
      Paper.trigger('updateQustions');
      bus.emit('orderQuesInfos');
    };

    let startY = 0;
    let startHeight = 0;
    const MIN_HEIGHT = 75; //px

    const startDrag = (e: MouseEvent, qId: string) => {
      let $dom = document.getElementById(qId);
      startY = e.clientY;
      startHeight = $dom.offsetHeight;
      document.addEventListener('pointermove', handleDrag);
      document.addEventListener('pointerup', stopDrag);
    };

    let newHeight = 0;
    function handleDrag(e: MouseEvent) {
      const deltaY = e.clientY - startY;
      newHeight = startHeight + deltaY;
      newHeight = Math.max(MIN_HEIGHT, newHeight);
      props.item.height = pxConversionMm(newHeight) + 'mm';
    }

    async function stopDrag() {
      document.removeEventListener('pointermove', handleDrag);
      document.removeEventListener('pointerup', stopDrag);

      setTimeout(function () {
        bus.emit('renderAllCard');
      }, 10);
    }

    const getConetntHeight = () => {
      //页面高度 - 首尾高度
      let height = pageHeight - footerKeepHeight * 2;
      const dom = document.getElementById(props.qId);
      const page = parseInt(dom?.getAttribute('page') || '1');
      //当前页是否包含头部信息
      let hasInfo = true;
      if (Paper.pageLayout == IPAGELAYOUT.A4) {
        hasInfo = page % 2 == 1;
      } else if (Paper.pageLayout == IPAGELAYOUT.A3) {
        hasInfo = page % 4 == 1;
      } else {
        hasInfo = page % 6 == 1;
      }
      //减去头部信息高度
      if (hasInfo) {
        height -= getHeaderInfoHeight();
      }
      return height;
    };

    const setHeightFull = () => {
      let $dom = document.getElementById(props.qId);
      let height = getConetntHeight();
      let _height = 0;

      const getHeight = (_dom: any) => {
        if (_dom && _dom.className.indexOf('header-box') < 0) {
          _height += Math.round(pxConversionMm(_dom.offsetHeight));
          getHeight(_dom?.previousElementSibling);
        }
      };
      getHeight($dom?.previousElementSibling);

      let offsetTop = pxConversionMm($dom.offsetTop);
      height = pageHeight * Math.ceil(offsetTop / pageHeight) - footerKeepHeight * 2 - _height - 1;
      // height += 0.3;
      props.item.height = `${height}mm`;

      nextTick(() => {
        bus.emit('renderAllCard');
      });
    };

    return {
      ...toRefs(state),
      deleteArea,
      startDrag,
      setHeightFull,
    };
  },
});
</script>

<style lang="scss" scoped>
.ques-noanswerarea {
  .ques-box {
    position: relative;
    border: 0.1mm solid #000;
    text-align: center;
    font-size: 12mm;
    font-family: '微软雅黑, Microsoft YaHei, Verdana, Arial, Helvetica, sans-serif';
    color: rgba(0, 0, 0, 0.2);
    font-weight: bold;
    letter-spacing: 10px;
    white-space: nowrap;

    &:hover {
      .btn-delete,
      .pop {
        display: block;
      }
    }
  }

  .btn-delete {
    display: none;
    position: absolute;
    top: 10px;
    right: 6px;
    z-index: 10;
    width: 24px;
    height: 24px;
    background: url('../../assets/icon_delete.png') no-repeat 0 0;
    opacity: 0.6;

    &:hover {
      opacity: 1;
    }
  }
}

.pop {
  right: 0;
  bottom: -30px;
  line-height: normal;
}
</style>

<style lang="scss">
.a33 .ques-box > .background {
  border-left-width: 130mm !important;
}
</style>
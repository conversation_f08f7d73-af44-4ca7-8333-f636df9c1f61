!(function (e) {
    var t = {};
    function n(i) {
      if (t[i]) return t[i].exports;
      var o = (t[i] = { i: i, l: !1, exports: {} });
      return e[i].call(o.exports, o, o.exports, n), (o.l = !0), o.exports;
    }
    (n.m = e),
      (n.c = t),
      (n.d = function (e, t, i) {
        n.o(e, t) || Object.defineProperty(e, t, { enumerable: !0, get: i });
      }),
      (n.r = function (e) {
        "undefined" != typeof Symbol &&
          Symbol.toStringTag &&
          Object.defineProperty(e, Symbol.toStringTag, { value: "Module" }),
          Object.defineProperty(e, "__esModule", { value: !0 });
      }),
      (n.t = function (e, t) {
        if ((1 & t && (e = n(e)), 8 & t)) return e;
        if (4 & t && "object" == typeof e && e && e.__esModule) return e;
        var i = Object.create(null);
        if (
          (n.r(i),
          Object.defineProperty(i, "default", { enumerable: !0, value: e }),
          2 & t && "string" != typeof e)
        )
          for (var o in e)
            n.d(
              i,
              o,
              function (t) {
                return e[t];
              }.bind(null, o)
            );
        return i;
      }),
      (n.n = function (e) {
        var t =
          e && e.__esModule
            ? function () {
                return e.default;
              }
            : function () {
                return e;
              };
        return n.d(t, "a", t), t;
      }),
      (n.o = function (e, t) {
        return Object.prototype.hasOwnProperty.call(e, t);
      }),
      (n.p = ""),
      n((n.s = 18));
  })([
    function (e) {
      e.exports = JSON.parse(
        '{"en":{"latex": "公式编辑器","cancel": "取消","accept": "确定","manual": "手册","insert_math": "插入数学公式 - MathType","insert_chem": "插入化学公式 - ChemType","minimize": "最小化","maximize": "最大化","fullscreen": "全屏","exit_fullscreen": "退出全屏","close": "关闭","mathtype": "公式编辑器","title_modalwindow": "公式编辑器窗口","close_modal_warning": "你确定要离开吗？您所做的更改将会丢失。","latex_name_label": "Latex Formula","browser_no_compatible": "Your browser is not compatible with AJAX technology. Please, use the latest version of Mozilla Firefox.","error_convert_accessibility": "Error converting from MathML to accessible text.","exception_cross_site": "Cross site scripting is only allowed for HTTP.","exception_high_surrogate": "High surrogate not followed by low surrogate in fixedCharCodeAt()","exception_string_length": "Invalid string. Length must be a multiple of 4","exception_key_nonobject": "Object.keys called on non-object","exception_null_or_undefined": " this is null or not defined","exception_not_function": " is not a function","exception_invalid_date_format": "Invalid date format : ","exception_casting": "Cannot cast ","exception_casting_to": " to "},"":{}}'
      );
    },
    function (module, __webpack_exports__, __webpack_require__) {
      "use strict";
      function _typeof(e) {
        return (_typeof =
          "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
            ? function (e) {
                return typeof e;
              }
            : function (e) {
                return e &&
                  "function" == typeof Symbol &&
                  e.constructor === Symbol &&
                  e !== Symbol.prototype
                  ? "symbol"
                  : typeof e;
              })(e);
      }
      var md5,
        _unused_webpack_default_export = md5;
      !(function () {
        var HxOverrides = function () {};
        (HxOverrides.__name__ = !0),
          (HxOverrides.dateStr = function (e) {
            var t = e.getMonth() + 1,
              n = e.getDate(),
              i = e.getHours(),
              o = e.getMinutes(),
              r = e.getSeconds();
            return (
              e.getFullYear() +
              "-" +
              (t < 10 ? "0" + t : "" + t) +
              "-" +
              (n < 10 ? "0" + n : "" + n) +
              " " +
              (i < 10 ? "0" + i : "" + i) +
              ":" +
              (o < 10 ? "0" + o : "" + o) +
              ":" +
              (r < 10 ? "0" + r : "" + r)
            );
          }),
          (HxOverrides.strDate = function (e) {
            switch (e.length) {
              case 8:
                var t = e.split(":"),
                  n = new Date();
                return (
                  n.setTime(0),
                  n.setUTCHours(t[0]),
                  n.setUTCMinutes(t[1]),
                  n.setUTCSeconds(t[2]),
                  n
                );
              case 10:
                t = e.split("-");
                return new Date(t[0], t[1] - 1, t[2], 0, 0, 0);
              case 19:
                var i = (t = e.split(" "))[0].split("-"),
                  o = t[1].split(":");
                return new Date(i[0], i[1] - 1, i[2], o[0], o[1], o[2]);
              default:
                throw "Invalid date format : " + e;
            }
          }),
          (HxOverrides.cca = function (e, t) {
            var n = e.charCodeAt(t);
            if (n == n) return n;
          }),
          (HxOverrides.substr = function (e, t, n) {
            return null != t && 0 != t && null != n && n < 0
              ? ""
              : (null == n && (n = e.length),
                t < 0
                  ? (t = e.length + t) < 0 && (t = 0)
                  : n < 0 && (n = e.length + n - t),
                e.substr(t, n));
          }),
          (HxOverrides.remove = function (e, t) {
            for (var n = 0, i = e.length; n < i; ) {
              if (e[n] == t) return e.splice(n, 1), !0;
              n++;
            }
            return !1;
          }),
          (HxOverrides.iter = function (e) {
            return {
              cur: 0,
              arr: e,
              hasNext: function () {
                return this.cur < this.arr.length;
              },
              next: function () {
                return this.arr[this.cur++];
              },
            };
          });
        var IntIter = function (e, t) {
          (this.min = e), (this.max = t);
        };
        (IntIter.__name__ = !0),
          (IntIter.prototype = {
            next: function () {
              return this.min++;
            },
            hasNext: function () {
              return this.min < this.max;
            },
            __class__: IntIter,
          });
        var Std = function () {};
        (Std.__name__ = !0),
          (Std.is = function (e, t) {
            return js.Boot.__instanceof(e, t);
          }),
          (Std.string = function (e) {
            return js.Boot.__string_rec(e, "");
          }),
          (Std.int = function (e) {
            return 0 | e;
          }),
          (Std.parseInt = function (e) {
            var t = parseInt(e, 10);
            return (
              0 != t ||
                (120 != HxOverrides.cca(e, 1) && 88 != HxOverrides.cca(e, 1)) ||
                (t = parseInt(e)),
              isNaN(t) ? null : t
            );
          }),
          (Std.parseFloat = function (e) {
            return parseFloat(e);
          }),
          (Std.random = function (e) {
            return Math.floor(Math.random() * e);
          });
        var com = com || {};
        com.wiris || (com.wiris = {}),
          com.wiris.js || (com.wiris.js = {}),
          (com.wiris.js.JsPluginTools = function () {
            this.tryReady();
          }),
          (com.wiris.js.JsPluginTools.__name__ = !0),
          (com.wiris.js.JsPluginTools.main = function () {
            var e;
            (e = com.wiris.js.JsPluginTools.getInstance()),
              haxe.Timer.delay($bind(e, e.tryReady), 100);
          }),
          (com.wiris.js.JsPluginTools.getInstance = function () {
            return (
              null == com.wiris.js.JsPluginTools.instance &&
                (com.wiris.js.JsPluginTools.instance =
                  new com.wiris.js.JsPluginTools()),
              com.wiris.js.JsPluginTools.instance
            );
          }),
          (com.wiris.js.JsPluginTools.bypassEncapsulation = function () {
            null == window.com && (window.com = {}),
              null == window.com.wiris && (window.com.wiris = {}),
              null == window.com.wiris.js && (window.com.wiris.js = {}),
              null == window.com.wiris.js.JsPluginTools &&
                (window.com.wiris.js.JsPluginTools =
                  com.wiris.js.JsPluginTools.getInstance());
          }),
          (com.wiris.js.JsPluginTools.prototype = {
            md5encode: function (e) {
              return haxe.Md5.encode(e);
            },
            doLoad: function () {
              (this.ready = !0),
                (com.wiris.js.JsPluginTools.instance = this),
                com.wiris.js.JsPluginTools.bypassEncapsulation();
            },
            tryReady: function () {
              (this.ready = !1),
                js.Lib.document.readyState && (this.doLoad(), (this.ready = !0)),
                this.ready || haxe.Timer.delay($bind(this, this.tryReady), 100);
            },
            __class__: com.wiris.js.JsPluginTools,
          });
        var haxe = haxe || {};
        (haxe.Log = function () {}),
          (haxe.Log.__name__ = !0),
          (haxe.Log.trace = function (e, t) {
            js.Boot.__trace(e, t);
          }),
          (haxe.Log.clear = function () {
            js.Boot.__clear_trace();
          }),
          (haxe.Md5 = function () {}),
          (haxe.Md5.__name__ = !0),
          (haxe.Md5.encode = function (e) {
            return new haxe.Md5().doEncode(e);
          }),
          (haxe.Md5.prototype = {
            doEncode: function (e) {
              for (
                var t = this.str2blks(e),
                  n = 1732584193,
                  i = -271733879,
                  o = -1732584194,
                  r = 271733878,
                  a = 0;
                a < t.length;
  
              ) {
                var s = n,
                  l = i,
                  c = o,
                  u = r;
                0,
                  (n = this.ff(n, i, o, r, t[a], 7, -680876936)),
                  (r = this.ff(r, n, i, o, t[a + 1], 12, -389564586)),
                  (o = this.ff(o, r, n, i, t[a + 2], 17, 606105819)),
                  (i = this.ff(i, o, r, n, t[a + 3], 22, -1044525330)),
                  (n = this.ff(n, i, o, r, t[a + 4], 7, -176418897)),
                  (r = this.ff(r, n, i, o, t[a + 5], 12, 1200080426)),
                  (o = this.ff(o, r, n, i, t[a + 6], 17, -1473231341)),
                  (i = this.ff(i, o, r, n, t[a + 7], 22, -45705983)),
                  (n = this.ff(n, i, o, r, t[a + 8], 7, 1770035416)),
                  (r = this.ff(r, n, i, o, t[a + 9], 12, -1958414417)),
                  (o = this.ff(o, r, n, i, t[a + 10], 17, -42063)),
                  (i = this.ff(i, o, r, n, t[a + 11], 22, -1990404162)),
                  (n = this.ff(n, i, o, r, t[a + 12], 7, 1804603682)),
                  (r = this.ff(r, n, i, o, t[a + 13], 12, -40341101)),
                  (o = this.ff(o, r, n, i, t[a + 14], 17, -1502002290)),
                  (i = this.ff(i, o, r, n, t[a + 15], 22, 1236535329)),
                  (n = this.gg(n, i, o, r, t[a + 1], 5, -165796510)),
                  (r = this.gg(r, n, i, o, t[a + 6], 9, -1069501632)),
                  (o = this.gg(o, r, n, i, t[a + 11], 14, 643717713)),
                  (i = this.gg(i, o, r, n, t[a], 20, -373897302)),
                  (n = this.gg(n, i, o, r, t[a + 5], 5, -701558691)),
                  (r = this.gg(r, n, i, o, t[a + 10], 9, 38016083)),
                  (o = this.gg(o, r, n, i, t[a + 15], 14, -660478335)),
                  (i = this.gg(i, o, r, n, t[a + 4], 20, -405537848)),
                  (n = this.gg(n, i, o, r, t[a + 9], 5, 568446438)),
                  (r = this.gg(r, n, i, o, t[a + 14], 9, -1019803690)),
                  (o = this.gg(o, r, n, i, t[a + 3], 14, -187363961)),
                  (i = this.gg(i, o, r, n, t[a + 8], 20, 1163531501)),
                  (n = this.gg(n, i, o, r, t[a + 13], 5, -1444681467)),
                  (r = this.gg(r, n, i, o, t[a + 2], 9, -51403784)),
                  (o = this.gg(o, r, n, i, t[a + 7], 14, 1735328473)),
                  (i = this.gg(i, o, r, n, t[a + 12], 20, -1926607734)),
                  (n = this.hh(n, i, o, r, t[a + 5], 4, -378558)),
                  (r = this.hh(r, n, i, o, t[a + 8], 11, -2022574463)),
                  (o = this.hh(o, r, n, i, t[a + 11], 16, 1839030562)),
                  (i = this.hh(i, o, r, n, t[a + 14], 23, -35309556)),
                  (n = this.hh(n, i, o, r, t[a + 1], 4, -1530992060)),
                  (r = this.hh(r, n, i, o, t[a + 4], 11, 1272893353)),
                  (o = this.hh(o, r, n, i, t[a + 7], 16, -155497632)),
                  (i = this.hh(i, o, r, n, t[a + 10], 23, -1094730640)),
                  (n = this.hh(n, i, o, r, t[a + 13], 4, 681279174)),
                  (r = this.hh(r, n, i, o, t[a], 11, -358537222)),
                  (o = this.hh(o, r, n, i, t[a + 3], 16, -722521979)),
                  (i = this.hh(i, o, r, n, t[a + 6], 23, 76029189)),
                  (n = this.hh(n, i, o, r, t[a + 9], 4, -640364487)),
                  (r = this.hh(r, n, i, o, t[a + 12], 11, -421815835)),
                  (o = this.hh(o, r, n, i, t[a + 15], 16, 530742520)),
                  (i = this.hh(i, o, r, n, t[a + 2], 23, -995338651)),
                  (n = this.ii(n, i, o, r, t[a], 6, -198630844)),
                  (r = this.ii(r, n, i, o, t[a + 7], 10, 1126891415)),
                  (o = this.ii(o, r, n, i, t[a + 14], 15, -1416354905)),
                  (i = this.ii(i, o, r, n, t[a + 5], 21, -57434055)),
                  (n = this.ii(n, i, o, r, t[a + 12], 6, 1700485571)),
                  (r = this.ii(r, n, i, o, t[a + 3], 10, -1894986606)),
                  (o = this.ii(o, r, n, i, t[a + 10], 15, -1051523)),
                  (i = this.ii(i, o, r, n, t[a + 1], 21, -2054922799)),
                  (n = this.ii(n, i, o, r, t[a + 8], 6, 1873313359)),
                  (r = this.ii(r, n, i, o, t[a + 15], 10, -30611744)),
                  (o = this.ii(o, r, n, i, t[a + 6], 15, -1560198380)),
                  (i = this.ii(i, o, r, n, t[a + 13], 21, 1309151649)),
                  (n = this.ii(n, i, o, r, t[a + 4], 6, -145523070)),
                  (r = this.ii(r, n, i, o, t[a + 11], 10, -1120210379)),
                  (o = this.ii(o, r, n, i, t[a + 2], 15, 718787259)),
                  (i = this.ii(i, o, r, n, t[a + 9], 21, -343485551)),
                  (n = this.addme(n, s)),
                  (i = this.addme(i, l)),
                  (o = this.addme(o, c)),
                  (r = this.addme(r, u)),
                  (a += 16);
              }
              return this.rhex(n) + this.rhex(i) + this.rhex(o) + this.rhex(r);
            },
            ii: function (e, t, n, i, o, r, a) {
              return this.cmn(this.bitXOR(n, this.bitOR(t, ~i)), e, t, o, r, a);
            },
            hh: function (e, t, n, i, o, r, a) {
              return this.cmn(this.bitXOR(this.bitXOR(t, n), i), e, t, o, r, a);
            },
            gg: function (e, t, n, i, o, r, a) {
              return this.cmn(
                this.bitOR(this.bitAND(t, i), this.bitAND(n, ~i)),
                e,
                t,
                o,
                r,
                a
              );
            },
            ff: function (e, t, n, i, o, r, a) {
              return this.cmn(
                this.bitOR(this.bitAND(t, n), this.bitAND(~t, i)),
                e,
                t,
                o,
                r,
                a
              );
            },
            cmn: function (e, t, n, i, o, r) {
              return this.addme(
                this.rol(this.addme(this.addme(t, e), this.addme(i, r)), o),
                n
              );
            },
            rol: function (e, t) {
              return (e << t) | (e >>> (32 - t));
            },
            str2blks: function (e) {
              for (
                var t = 1 + ((e.length + 8) >> 6),
                  n = new Array(),
                  i = 0,
                  o = 16 * t;
                i < o;
  
              ) {
                n[(r = i++)] = 0;
              }
              for (var r = 0; r < e.length; )
                (n[r >> 2] |=
                  HxOverrides.cca(e, r) << (((8 * e.length + r) % 4) * 8)),
                  r++;
              n[r >> 2] |= 128 << (((8 * e.length + r) % 4) * 8);
              var a = 8 * e.length,
                s = 16 * t - 2;
              return (
                (n[s] = 255 & a),
                (n[s] |= ((a >>> 8) & 255) << 8),
                (n[s] |= ((a >>> 16) & 255) << 16),
                (n[s] |= ((a >>> 24) & 255) << 24),
                n
              );
            },
            rhex: function (e) {
              for (var t = "", n = 0; n < 4; ) {
                var i = n++;
                t +=
                  "0123456789abcdef".charAt((e >> (8 * i + 4)) & 15) +
                  "0123456789abcdef".charAt((e >> (8 * i)) & 15);
              }
              return t;
            },
            addme: function (e, t) {
              var n = (65535 & e) + (65535 & t);
              return (((e >> 16) + (t >> 16) + (n >> 16)) << 16) | (65535 & n);
            },
            bitAND: function (e, t) {
              return (((e >>> 1) & (t >>> 1)) << 1) | (1 & e & t);
            },
            bitXOR: function (e, t) {
              return (((e >>> 1) ^ (t >>> 1)) << 1) | ((1 & e) ^ (1 & t));
            },
            bitOR: function (e, t) {
              return (((e >>> 1) | (t >>> 1)) << 1) | ((1 & e) | (1 & t));
            },
            __class__: haxe.Md5,
          }),
          (haxe.Timer = function (e) {
            var t = this;
            this.id = window.setInterval(function () {
              t.run();
            }, e);
          }),
          (haxe.Timer.__name__ = !0),
          (haxe.Timer.delay = function (e, t) {
            var n = new haxe.Timer(t);
            return (
              (n.run = function () {
                n.stop(), e();
              }),
              n
            );
          }),
          (haxe.Timer.measure = function (e, t) {
            var n = haxe.Timer.stamp(),
              i = e();
            return haxe.Log.trace(haxe.Timer.stamp() - n + "s", t), i;
          }),
          (haxe.Timer.stamp = function () {
            return new Date().getTime() / 1e3;
          }),
          (haxe.Timer.prototype = {
            run: function () {},
            stop: function () {
              null != this.id &&
                (window.clearInterval(this.id), (this.id = null));
            },
            __class__: haxe.Timer,
          });
        var js = js || {},
          $_;
        function $bind(e, t) {
          var n = function e() {
            return e.method.apply(e.scope, arguments);
          };
          return (n.scope = e), (n.method = t), n;
        }
        (js.Boot = function () {}),
          (js.Boot.__name__ = !0),
          (js.Boot.__unhtml = function (e) {
            return e
              .split("&")
              .join("&amp;")
              .split("<")
              .join("&lt;")
              .split(">")
              .join("&gt;");
          }),
          (js.Boot.__trace = function (e, t) {
            var n,
              i = null != t ? t.fileName + ":" + t.lineNumber + ": " : "";
            (i += js.Boot.__string_rec(e, "")),
              "undefined" != typeof document &&
              null != (n = document.getElementById("haxe:trace"))
                ? (n.innerHTML += js.Boot.__unhtml(i) + "<br/>")
                : "undefined" != typeof console &&
                  null != console.log &&
                  console.log(i);
          }),
          (js.Boot.__clear_trace = function () {
            var e = document.getElementById("haxe:trace");
            null != e && (e.innerHTML = "");
          }),
          (js.Boot.isClass = function (e) {
            return e.__name__;
          }),
          (js.Boot.isEnum = function (e) {
            return e.__ename__;
          }),
          (js.Boot.getClass = function (e) {
            return e.__class__;
          }),
          (js.Boot.__string_rec = function (e, t) {
            if (null == e) return "null";
            if (t.length >= 5) return "<...>";
            var n = _typeof(e);
            switch (
              ("function" == n && (e.__name__ || e.__ename__) && (n = "object"),
              n)
            ) {
              case "object":
                if (e instanceof Array) {
                  if (e.__enum__) {
                    if (2 == e.length) return e[0];
                    var i = e[0] + "(";
                    t += "\t";
                    for (var o = 2, r = e.length; o < r; ) {
                      i +=
                        2 != (a = o++)
                          ? "," + js.Boot.__string_rec(e[a], t)
                          : js.Boot.__string_rec(e[a], t);
                    }
                    return i + ")";
                  }
                  var a,
                    s = e.length;
                  i = "[";
                  t += "\t";
                  for (r = 0; r < s; ) {
                    var l = r++;
                    i += (l > 0 ? "," : "") + js.Boot.__string_rec(e[l], t);
                  }
                  return (i += "]");
                }
                var c;
                try {
                  c = e.toString;
                } catch (e) {
                  return "???";
                }
                if (null != c && c != Object.toString) {
                  var u = e.toString();
                  if ("[object Object]" != u) return u;
                }
                var d = null;
                i = "{\n";
                t += "\t";
                var h = null != e.hasOwnProperty;
                for (var d in e)
                  (h && !e.hasOwnProperty(d)) ||
                    ("prototype" != d &&
                      "__class__" != d &&
                      "__super__" != d &&
                      "__interfaces__" != d &&
                      "__properties__" != d &&
                      (2 != i.length && (i += ", \n"),
                      (i += t + d + " : " + js.Boot.__string_rec(e[d], t))));
                return (i += "\n" + (t = t.substring(1)) + "}");
              case "function":
                return "<function>";
              case "string":
                return e;
              default:
                return String(e);
            }
          }),
          (js.Boot.__interfLoop = function (e, t) {
            if (null == e) return !1;
            if (e == t) return !0;
            var n = e.__interfaces__;
            if (null != n)
              for (var i = 0, o = n.length; i < o; ) {
                var r = n[i++];
                if (r == t || js.Boot.__interfLoop(r, t)) return !0;
              }
            return js.Boot.__interfLoop(e.__super__, t);
          }),
          (js.Boot.__instanceof = function (e, t) {
            try {
              if (e instanceof t) return t != Array || null == e.__enum__;
              if (js.Boot.__interfLoop(e.__class__, t)) return !0;
            } catch (e) {
              if (null == t) return !1;
            }
            switch (t) {
              case Int:
                return Math.ceil(e % 2147483648) === e;
              case Float:
                return "number" == typeof e;
              case Bool:
                return !0 === e || !1 === e;
              case String:
                return "string" == typeof e;
              case Dynamic:
                return !0;
              default:
                return (
                  null != e &&
                  ((t == Class && null != e.__name__) ||
                    (t == Enum && null != e.__ename__) ||
                    e.__enum__ == t)
                );
            }
          }),
          (js.Boot.__cast = function (e, t) {
            if (js.Boot.__instanceof(e, t)) return e;
            throw "Cannot cast " + Std.string(e) + " to " + Std.string(t);
          }),
          (js.Lib = function () {}),
          (js.Lib.__name__ = !0),
          (js.Lib.debug = function () {}),
          (js.Lib.alert = function (e) {
            alert(js.Boot.__string_rec(e, ""));
          }),
          (js.Lib.eval = function (code) {
            return eval(code);
          }),
          (js.Lib.setErrorHandler = function (e) {
            js.Lib.onerror = e;
          }),
          Array.prototype.indexOf &&
            (HxOverrides.remove = function (e, t) {
              var n = e.indexOf(t);
              return -1 != n && (e.splice(n, 1), !0);
            }),
          (Math.__name__ = ["Math"]),
          (Math.NaN = Number.NaN),
          (Math.NEGATIVE_INFINITY = Number.NEGATIVE_INFINITY),
          (Math.POSITIVE_INFINITY = Number.POSITIVE_INFINITY),
          (Math.isFinite = function (e) {
            return isFinite(e);
          }),
          (Math.isNaN = function (e) {
            return isNaN(e);
          }),
          (String.prototype.__class__ = String),
          (String.__name__ = !0),
          (Array.prototype.__class__ = Array),
          (Array.__name__ = !0),
          (Date.prototype.__class__ = Date),
          (Date.__name__ = ["Date"]);
        var Int = { __name__: ["Int"] },
          Dynamic = { __name__: ["Dynamic"] },
          Float = Number;
        Float.__name__ = ["Float"];
        var Bool = Boolean;
        Bool.__ename__ = ["Bool"];
        var Class = { __name__: ["Class"] },
          Enum = {},
          Void = { __ename__: ["Void"] };
        "undefined" != typeof document && (js.Lib.document = document),
          "undefined" != typeof window &&
            ((js.Lib.window = window),
            (js.Lib.window.onerror = function (e, t, n) {
              var i = js.Lib.onerror;
              return null != i && i(e, [t + ":" + n]);
            })),
          com.wiris.js.JsPluginTools.main(),
          delete Array.prototype.__class__;
      })(),
        (function () {
          var HxOverrides = function () {};
          (HxOverrides.__name__ = !0),
            (HxOverrides.dateStr = function (e) {
              var t = e.getMonth() + 1,
                n = e.getDate(),
                i = e.getHours(),
                o = e.getMinutes(),
                r = e.getSeconds();
              return (
                e.getFullYear() +
                "-" +
                (t < 10 ? "0" + t : "" + t) +
                "-" +
                (n < 10 ? "0" + n : "" + n) +
                " " +
                (i < 10 ? "0" + i : "" + i) +
                ":" +
                (o < 10 ? "0" + o : "" + o) +
                ":" +
                (r < 10 ? "0" + r : "" + r)
              );
            }),
            (HxOverrides.strDate = function (e) {
              switch (e.length) {
                case 8:
                  var t = e.split(":"),
                    n = new Date();
                  return (
                    n.setTime(0),
                    n.setUTCHours(t[0]),
                    n.setUTCMinutes(t[1]),
                    n.setUTCSeconds(t[2]),
                    n
                  );
                case 10:
                  t = e.split("-");
                  return new Date(t[0], t[1] - 1, t[2], 0, 0, 0);
                case 19:
                  var i = (t = e.split(" "))[0].split("-"),
                    o = t[1].split(":");
                  return new Date(i[0], i[1] - 1, i[2], o[0], o[1], o[2]);
                default:
                  throw "Invalid date format : " + e;
              }
            }),
            (HxOverrides.cca = function (e, t) {
              var n = e.charCodeAt(t);
              if (n == n) return n;
            }),
            (HxOverrides.substr = function (e, t, n) {
              return null != t && 0 != t && null != n && n < 0
                ? ""
                : (null == n && (n = e.length),
                  t < 0
                    ? (t = e.length + t) < 0 && (t = 0)
                    : n < 0 && (n = e.length + n - t),
                  e.substr(t, n));
            }),
            (HxOverrides.remove = function (e, t) {
              for (var n = 0, i = e.length; n < i; ) {
                if (e[n] == t) return e.splice(n, 1), !0;
                n++;
              }
              return !1;
            }),
            (HxOverrides.iter = function (e) {
              return {
                cur: 0,
                arr: e,
                hasNext: function () {
                  return this.cur < this.arr.length;
                },
                next: function () {
                  return this.arr[this.cur++];
                },
              };
            });
          var IntIter = function (e, t) {
            (this.min = e), (this.max = t);
          };
          (IntIter.__name__ = !0),
            (IntIter.prototype = {
              next: function () {
                return this.min++;
              },
              hasNext: function () {
                return this.min < this.max;
              },
              __class__: IntIter,
            });
          var Std = function () {};
          (Std.__name__ = !0),
            (Std.is = function (e, t) {
              return js.Boot.__instanceof(e, t);
            }),
            (Std.string = function (e) {
              return js.Boot.__string_rec(e, "");
            }),
            (Std.int = function (e) {
              return 0 | e;
            }),
            (Std.parseInt = function (e) {
              var t = parseInt(e, 10);
              return (
                0 != t ||
                  (120 != HxOverrides.cca(e, 1) && 88 != HxOverrides.cca(e, 1)) ||
                  (t = parseInt(e)),
                isNaN(t) ? null : t
              );
            }),
            (Std.parseFloat = function (e) {
              return parseFloat(e);
            }),
            (Std.random = function (e) {
              return Math.floor(Math.random() * e);
            });
          var com = com || {};
          com.wiris || (com.wiris = {}),
            com.wiris.js || (com.wiris.js = {}),
            (com.wiris.js.JsPluginTools = function () {
              this.tryReady();
            }),
            (com.wiris.js.JsPluginTools.__name__ = !0),
            (com.wiris.js.JsPluginTools.main = function () {
              var e;
              (e = com.wiris.js.JsPluginTools.getInstance()),
                haxe.Timer.delay($bind(e, e.tryReady), 100);
            }),
            (com.wiris.js.JsPluginTools.getInstance = function () {
              return (
                null == com.wiris.js.JsPluginTools.instance &&
                  (com.wiris.js.JsPluginTools.instance =
                    new com.wiris.js.JsPluginTools()),
                com.wiris.js.JsPluginTools.instance
              );
            }),
            (com.wiris.js.JsPluginTools.bypassEncapsulation = function () {
              null == window.com && (window.com = {}),
                null == window.com.wiris && (window.com.wiris = {}),
                null == window.com.wiris.js && (window.com.wiris.js = {}),
                null == window.com.wiris.js.JsPluginTools &&
                  (window.com.wiris.js.JsPluginTools =
                    com.wiris.js.JsPluginTools.getInstance());
            }),
            (com.wiris.js.JsPluginTools.prototype = {
              md5encode: function (e) {
                return haxe.Md5.encode(e);
              },
              doLoad: function () {
                (this.ready = !0),
                  (com.wiris.js.JsPluginTools.instance = this),
                  com.wiris.js.JsPluginTools.bypassEncapsulation();
              },
              tryReady: function () {
                (this.ready = !1),
                  js.Lib.document.readyState &&
                    (this.doLoad(), (this.ready = !0)),
                  this.ready || haxe.Timer.delay($bind(this, this.tryReady), 100);
              },
              __class__: com.wiris.js.JsPluginTools,
            });
          var haxe = haxe || {};
          (haxe.Log = function () {}),
            (haxe.Log.__name__ = !0),
            (haxe.Log.trace = function (e, t) {
              js.Boot.__trace(e, t);
            }),
            (haxe.Log.clear = function () {
              js.Boot.__clear_trace();
            }),
            (haxe.Md5 = function () {}),
            (haxe.Md5.__name__ = !0),
            (haxe.Md5.encode = function (e) {
              return new haxe.Md5().doEncode(e);
            }),
            (haxe.Md5.prototype = {
              doEncode: function (e) {
                for (
                  var t = this.str2blks(e),
                    n = 1732584193,
                    i = -271733879,
                    o = -1732584194,
                    r = 271733878,
                    a = 0;
                  a < t.length;
  
                ) {
                  var s = n,
                    l = i,
                    c = o,
                    u = r;
                  0,
                    (n = this.ff(n, i, o, r, t[a], 7, -680876936)),
                    (r = this.ff(r, n, i, o, t[a + 1], 12, -389564586)),
                    (o = this.ff(o, r, n, i, t[a + 2], 17, 606105819)),
                    (i = this.ff(i, o, r, n, t[a + 3], 22, -1044525330)),
                    (n = this.ff(n, i, o, r, t[a + 4], 7, -176418897)),
                    (r = this.ff(r, n, i, o, t[a + 5], 12, 1200080426)),
                    (o = this.ff(o, r, n, i, t[a + 6], 17, -1473231341)),
                    (i = this.ff(i, o, r, n, t[a + 7], 22, -45705983)),
                    (n = this.ff(n, i, o, r, t[a + 8], 7, 1770035416)),
                    (r = this.ff(r, n, i, o, t[a + 9], 12, -1958414417)),
                    (o = this.ff(o, r, n, i, t[a + 10], 17, -42063)),
                    (i = this.ff(i, o, r, n, t[a + 11], 22, -1990404162)),
                    (n = this.ff(n, i, o, r, t[a + 12], 7, 1804603682)),
                    (r = this.ff(r, n, i, o, t[a + 13], 12, -40341101)),
                    (o = this.ff(o, r, n, i, t[a + 14], 17, -1502002290)),
                    (i = this.ff(i, o, r, n, t[a + 15], 22, 1236535329)),
                    (n = this.gg(n, i, o, r, t[a + 1], 5, -165796510)),
                    (r = this.gg(r, n, i, o, t[a + 6], 9, -1069501632)),
                    (o = this.gg(o, r, n, i, t[a + 11], 14, 643717713)),
                    (i = this.gg(i, o, r, n, t[a], 20, -373897302)),
                    (n = this.gg(n, i, o, r, t[a + 5], 5, -701558691)),
                    (r = this.gg(r, n, i, o, t[a + 10], 9, 38016083)),
                    (o = this.gg(o, r, n, i, t[a + 15], 14, -660478335)),
                    (i = this.gg(i, o, r, n, t[a + 4], 20, -405537848)),
                    (n = this.gg(n, i, o, r, t[a + 9], 5, 568446438)),
                    (r = this.gg(r, n, i, o, t[a + 14], 9, -1019803690)),
                    (o = this.gg(o, r, n, i, t[a + 3], 14, -187363961)),
                    (i = this.gg(i, o, r, n, t[a + 8], 20, 1163531501)),
                    (n = this.gg(n, i, o, r, t[a + 13], 5, -1444681467)),
                    (r = this.gg(r, n, i, o, t[a + 2], 9, -51403784)),
                    (o = this.gg(o, r, n, i, t[a + 7], 14, 1735328473)),
                    (i = this.gg(i, o, r, n, t[a + 12], 20, -1926607734)),
                    (n = this.hh(n, i, o, r, t[a + 5], 4, -378558)),
                    (r = this.hh(r, n, i, o, t[a + 8], 11, -2022574463)),
                    (o = this.hh(o, r, n, i, t[a + 11], 16, 1839030562)),
                    (i = this.hh(i, o, r, n, t[a + 14], 23, -35309556)),
                    (n = this.hh(n, i, o, r, t[a + 1], 4, -1530992060)),
                    (r = this.hh(r, n, i, o, t[a + 4], 11, 1272893353)),
                    (o = this.hh(o, r, n, i, t[a + 7], 16, -155497632)),
                    (i = this.hh(i, o, r, n, t[a + 10], 23, -1094730640)),
                    (n = this.hh(n, i, o, r, t[a + 13], 4, 681279174)),
                    (r = this.hh(r, n, i, o, t[a], 11, -358537222)),
                    (o = this.hh(o, r, n, i, t[a + 3], 16, -722521979)),
                    (i = this.hh(i, o, r, n, t[a + 6], 23, 76029189)),
                    (n = this.hh(n, i, o, r, t[a + 9], 4, -640364487)),
                    (r = this.hh(r, n, i, o, t[a + 12], 11, -421815835)),
                    (o = this.hh(o, r, n, i, t[a + 15], 16, 530742520)),
                    (i = this.hh(i, o, r, n, t[a + 2], 23, -995338651)),
                    (n = this.ii(n, i, o, r, t[a], 6, -198630844)),
                    (r = this.ii(r, n, i, o, t[a + 7], 10, 1126891415)),
                    (o = this.ii(o, r, n, i, t[a + 14], 15, -1416354905)),
                    (i = this.ii(i, o, r, n, t[a + 5], 21, -57434055)),
                    (n = this.ii(n, i, o, r, t[a + 12], 6, 1700485571)),
                    (r = this.ii(r, n, i, o, t[a + 3], 10, -1894986606)),
                    (o = this.ii(o, r, n, i, t[a + 10], 15, -1051523)),
                    (i = this.ii(i, o, r, n, t[a + 1], 21, -2054922799)),
                    (n = this.ii(n, i, o, r, t[a + 8], 6, 1873313359)),
                    (r = this.ii(r, n, i, o, t[a + 15], 10, -30611744)),
                    (o = this.ii(o, r, n, i, t[a + 6], 15, -1560198380)),
                    (i = this.ii(i, o, r, n, t[a + 13], 21, 1309151649)),
                    (n = this.ii(n, i, o, r, t[a + 4], 6, -145523070)),
                    (r = this.ii(r, n, i, o, t[a + 11], 10, -1120210379)),
                    (o = this.ii(o, r, n, i, t[a + 2], 15, 718787259)),
                    (i = this.ii(i, o, r, n, t[a + 9], 21, -343485551)),
                    (n = this.addme(n, s)),
                    (i = this.addme(i, l)),
                    (o = this.addme(o, c)),
                    (r = this.addme(r, u)),
                    (a += 16);
                }
                return this.rhex(n) + this.rhex(i) + this.rhex(o) + this.rhex(r);
              },
              ii: function (e, t, n, i, o, r, a) {
                return this.cmn(this.bitXOR(n, this.bitOR(t, ~i)), e, t, o, r, a);
              },
              hh: function (e, t, n, i, o, r, a) {
                return this.cmn(this.bitXOR(this.bitXOR(t, n), i), e, t, o, r, a);
              },
              gg: function (e, t, n, i, o, r, a) {
                return this.cmn(
                  this.bitOR(this.bitAND(t, i), this.bitAND(n, ~i)),
                  e,
                  t,
                  o,
                  r,
                  a
                );
              },
              ff: function (e, t, n, i, o, r, a) {
                return this.cmn(
                  this.bitOR(this.bitAND(t, n), this.bitAND(~t, i)),
                  e,
                  t,
                  o,
                  r,
                  a
                );
              },
              cmn: function (e, t, n, i, o, r) {
                return this.addme(
                  this.rol(this.addme(this.addme(t, e), this.addme(i, r)), o),
                  n
                );
              },
              rol: function (e, t) {
                return (e << t) | (e >>> (32 - t));
              },
              str2blks: function (e) {
                for (
                  var t = 1 + ((e.length + 8) >> 6),
                    n = new Array(),
                    i = 0,
                    o = 16 * t;
                  i < o;
  
                ) {
                  n[(r = i++)] = 0;
                }
                for (var r = 0; r < e.length; )
                  (n[r >> 2] |=
                    HxOverrides.cca(e, r) << (((8 * e.length + r) % 4) * 8)),
                    r++;
                n[r >> 2] |= 128 << (((8 * e.length + r) % 4) * 8);
                var a = 8 * e.length,
                  s = 16 * t - 2;
                return (
                  (n[s] = 255 & a),
                  (n[s] |= ((a >>> 8) & 255) << 8),
                  (n[s] |= ((a >>> 16) & 255) << 16),
                  (n[s] |= ((a >>> 24) & 255) << 24),
                  n
                );
              },
              rhex: function (e) {
                for (var t = "", n = 0; n < 4; ) {
                  var i = n++;
                  t +=
                    "0123456789abcdef".charAt((e >> (8 * i + 4)) & 15) +
                    "0123456789abcdef".charAt((e >> (8 * i)) & 15);
                }
                return t;
              },
              addme: function (e, t) {
                var n = (65535 & e) + (65535 & t);
                return (((e >> 16) + (t >> 16) + (n >> 16)) << 16) | (65535 & n);
              },
              bitAND: function (e, t) {
                return (((e >>> 1) & (t >>> 1)) << 1) | (1 & e & t);
              },
              bitXOR: function (e, t) {
                return (((e >>> 1) ^ (t >>> 1)) << 1) | ((1 & e) ^ (1 & t));
              },
              bitOR: function (e, t) {
                return (((e >>> 1) | (t >>> 1)) << 1) | ((1 & e) | (1 & t));
              },
              __class__: haxe.Md5,
            }),
            (haxe.Timer = function (e) {
              var t = this;
              this.id = window.setInterval(function () {
                t.run();
              }, e);
            }),
            (haxe.Timer.__name__ = !0),
            (haxe.Timer.delay = function (e, t) {
              var n = new haxe.Timer(t);
              return (
                (n.run = function () {
                  n.stop(), e();
                }),
                n
              );
            }),
            (haxe.Timer.measure = function (e, t) {
              var n = haxe.Timer.stamp(),
                i = e();
              return haxe.Log.trace(haxe.Timer.stamp() - n + "s", t), i;
            }),
            (haxe.Timer.stamp = function () {
              return new Date().getTime() / 1e3;
            }),
            (haxe.Timer.prototype = {
              run: function () {},
              stop: function () {
                null != this.id &&
                  (window.clearInterval(this.id), (this.id = null));
              },
              __class__: haxe.Timer,
            });
          var js = js || {},
            $_;
          function $bind(e, t) {
            var n = function e() {
              return e.method.apply(e.scope, arguments);
            };
            return (n.scope = e), (n.method = t), n;
          }
          (js.Boot = function () {}),
            (js.Boot.__name__ = !0),
            (js.Boot.__unhtml = function (e) {
              return e
                .split("&")
                .join("&amp;")
                .split("<")
                .join("&lt;")
                .split(">")
                .join("&gt;");
            }),
            (js.Boot.__trace = function (e, t) {
              var n,
                i = null != t ? t.fileName + ":" + t.lineNumber + ": " : "";
              (i += js.Boot.__string_rec(e, "")),
                "undefined" != typeof document &&
                null != (n = document.getElementById("haxe:trace"))
                  ? (n.innerHTML += js.Boot.__unhtml(i) + "<br/>")
                  : "undefined" != typeof console &&
                    null != console.log &&
                    console.log(i);
            }),
            (js.Boot.__clear_trace = function () {
              var e = document.getElementById("haxe:trace");
              null != e && (e.innerHTML = "");
            }),
            (js.Boot.isClass = function (e) {
              return e.__name__;
            }),
            (js.Boot.isEnum = function (e) {
              return e.__ename__;
            }),
            (js.Boot.getClass = function (e) {
              return e.__class__;
            }),
            (js.Boot.__string_rec = function (e, t) {
              if (null == e) return "null";
              if (t.length >= 5) return "<...>";
              var n = _typeof(e);
              switch (
                ("function" == n && (e.__name__ || e.__ename__) && (n = "object"),
                n)
              ) {
                case "object":
                  if (e instanceof Array) {
                    if (e.__enum__) {
                      if (2 == e.length) return e[0];
                      var i = e[0] + "(";
                      t += "\t";
                      for (var o = 2, r = e.length; o < r; ) {
                        i +=
                          2 != (a = o++)
                            ? "," + js.Boot.__string_rec(e[a], t)
                            : js.Boot.__string_rec(e[a], t);
                      }
                      return i + ")";
                    }
                    var a,
                      s = e.length;
                    i = "[";
                    t += "\t";
                    for (r = 0; r < s; ) {
                      var l = r++;
                      i += (l > 0 ? "," : "") + js.Boot.__string_rec(e[l], t);
                    }
                    return (i += "]");
                  }
                  var c;
                  try {
                    c = e.toString;
                  } catch (e) {
                    return "???";
                  }
                  if (null != c && c != Object.toString) {
                    var u = e.toString();
                    if ("[object Object]" != u) return u;
                  }
                  var d = null;
                  i = "{\n";
                  t += "\t";
                  var h = null != e.hasOwnProperty;
                  for (var d in e)
                    (h && !e.hasOwnProperty(d)) ||
                      ("prototype" != d &&
                        "__class__" != d &&
                        "__super__" != d &&
                        "__interfaces__" != d &&
                        "__properties__" != d &&
                        (2 != i.length && (i += ", \n"),
                        (i += t + d + " : " + js.Boot.__string_rec(e[d], t))));
                  return (i += "\n" + (t = t.substring(1)) + "}");
                case "function":
                  return "<function>";
                case "string":
                  return e;
                default:
                  return String(e);
              }
            }),
            (js.Boot.__interfLoop = function (e, t) {
              if (null == e) return !1;
              if (e == t) return !0;
              var n = e.__interfaces__;
              if (null != n)
                for (var i = 0, o = n.length; i < o; ) {
                  var r = n[i++];
                  if (r == t || js.Boot.__interfLoop(r, t)) return !0;
                }
              return js.Boot.__interfLoop(e.__super__, t);
            }),
            (js.Boot.__instanceof = function (e, t) {
              try {
                if (e instanceof t) return t != Array || null == e.__enum__;
                if (js.Boot.__interfLoop(e.__class__, t)) return !0;
              } catch (e) {
                if (null == t) return !1;
              }
              switch (t) {
                case Int:
                  return Math.ceil(e % 2147483648) === e;
                case Float:
                  return "number" == typeof e;
                case Bool:
                  return !0 === e || !1 === e;
                case String:
                  return "string" == typeof e;
                case Dynamic:
                  return !0;
                default:
                  return (
                    null != e &&
                    ((t == Class && null != e.__name__) ||
                      (t == Enum && null != e.__ename__) ||
                      e.__enum__ == t)
                  );
              }
            }),
            (js.Boot.__cast = function (e, t) {
              if (js.Boot.__instanceof(e, t)) return e;
              throw "Cannot cast " + Std.string(e) + " to " + Std.string(t);
            }),
            (js.Lib = function () {}),
            (js.Lib.__name__ = !0),
            (js.Lib.debug = function () {}),
            (js.Lib.alert = function (e) {
              alert(js.Boot.__string_rec(e, ""));
            }),
            (js.Lib.eval = function (code) {
              return eval(code);
            }),
            (js.Lib.setErrorHandler = function (e) {
              js.Lib.onerror = e;
            }),
            Array.prototype.indexOf &&
              (HxOverrides.remove = function (e, t) {
                var n = e.indexOf(t);
                return -1 != n && (e.splice(n, 1), !0);
              }),
            (Math.__name__ = ["Math"]),
            (Math.NaN = Number.NaN),
            (Math.NEGATIVE_INFINITY = Number.NEGATIVE_INFINITY),
            (Math.POSITIVE_INFINITY = Number.POSITIVE_INFINITY),
            (Math.isFinite = function (e) {
              return isFinite(e);
            }),
            (Math.isNaN = function (e) {
              return isNaN(e);
            }),
            (String.prototype.__class__ = String),
            (String.__name__ = !0),
            (Array.prototype.__class__ = Array),
            (Array.__name__ = !0),
            (Date.prototype.__class__ = Date),
            (Date.__name__ = ["Date"]);
          var Int = { __name__: ["Int"] },
            Dynamic = { __name__: ["Dynamic"] },
            Float = Number;
          Float.__name__ = ["Float"];
          var Bool = Boolean;
          Bool.__ename__ = ["Bool"];
          var Class = { __name__: ["Class"] },
            Enum = {},
            Void = { __ename__: ["Void"] };
          "undefined" != typeof document && (js.Lib.document = document),
            "undefined" != typeof window &&
              ((js.Lib.window = window),
              (js.Lib.window.onerror = function (e, t, n) {
                var i = js.Lib.onerror;
                return null != i && i(e, [t + ":" + n]);
              })),
            com.wiris.js.JsPluginTools.main();
        })(),
        delete Array.prototype.__class__;
    },
    function (e, t, n) {
      var i = n(3);
      "string" == typeof i && (i = [[e.i, i, ""]]);
      var o = { hmr: !0, transform: void 0, insertInto: void 0 };
      n(16)(i, o);
      i.locals && (e.exports = i.locals);
    },
    function (e, t, n) {
      var i = n(4);
      (e.exports = n(5)(!1)).push([
        e.i,
        ".wrs_modal_overlay {\n    position: fixed;\n    font-family: arial, sans-serif;\n    top: 0;\n    right: 0;\n    left: 0;\n    bottom: 0;\n    background: rgba(0, 0, 0, 0.8);\n    z-index: 999998;\n    opacity: 0.65;\n    pointer-events: auto;\n}\n\n.wrs_modal_overlay.wrs_modal_ios {\n    visibility: hidden;\n    display: none;\n}\n\n.wrs_modal_overlay.wrs_modal_android {\n    visibility: hidden;\n    display: none;\n}\n\n.wrs_modal_overlay.wrs_modal_ios.moodle {\n    position: fixed;\n}\n\n.wrs_modal_overlay.wrs_modal_desktop.wrs_stack {\n    background: rgba(0, 0, 0, 0);\n    display: none;\n}\n\n.wrs_modal_overlay.wrs_modal_desktop.wrs_maximized {\n    background: rgba(0, 0, 0, 0.8);\n}\n\n.wrs_modal_overlay.wrs_modal_desktop.wrs_minimized {\n    background: rgba(0, 0, 0, 0);\n    display: none;\n}\n\n.wrs_modal_overlay.wrs_modal_desktop.wrs_closed {\n    background: rgba(0, 0, 0, 0);\n    display: none;\n}\n\n.wrs_modal_title {\n    color: #fff;\n    padding: 5px 0 5px 10px;\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n    text-align: left;\n}\n\n.wrs_modal_close_button {\n    float: right;\n    cursor: pointer;\n    color: #fff;\n    padding: 5px 10px 5px 0;\n    background-image: url(" +
          i(n(6)) +
          ");\n    background-size: 10px;\n    margin: 10px 7px 0 0;\n    background-repeat: no-repeat;\n}\n\n.wrs_modal_close_button:hover {\n    background-image: url(" +
          i(n(7)) +
          ");\n}\n\n.wrs_modal_minimize_button {\n    float: right;\n    cursor: pointer;\n    color: #fff;\n    padding: 5px 10px 5px 0;\n    top: inherit;\n    margin: 10px 7px 0 0;\n    background-size: 10px;\n    background-image: url(" +
          i(n(8)) +
          ");\n    background-repeat: no-repeat;\n}\n\n.wrs_modal_minimize_button:hover {\n    background-image: url(" +
          i(n(9)) +
          ");\n}\n\n.wrs_modal_minimize_button.wrs_minimized {\n    background-image: url(" +
          i(n(10)) +
          ");\n}\n\n.wrs_modal_minimize_button.wrs_minimized:hover {\n    background-image: url(" +
          i(n(11)) +
          ");\n}\n\n.wrs_modal_stack_button {\n    float: right;\n    cursor: pointer;\n    color: #fff;\n    margin: 10px 7px 0 0;\n    padding: 5px 10px 5px 0;\n    top: inherit;\n    background-image: url(" +
          i(n(12)) +
          ");\n    background-size: 10px;\n}\n\n.wrs_modal_stack_button.wrs_stack {\n    visibility: hidden;\n    margin: 0;\n    padding: 0;\n}\n\n.wrs_modal_stack_button.wrs_minimized {\n    visibility: hidden;\n    margin: 0;\n    padding: 0;\n}\n\n.wrs_modal_stack_button:hover {\n    background-image: url(" +
          i(n(13)) +
          ");\n}\n\n.wrs_modal_maximize_button {\n    float: right;\n    cursor: pointer;\n    color: #fff;\n    margin: 10px 7px 0 0;\n    padding: 5px 10px 5px 0;\n    top: inherit;\n    background-image: url(" +
          i(n(14)) +
          ");\n    background-size: 10px;\n    background-repeat: no-repeat;\n}\n\n.wrs_modal_maximize_button.wrs_maximized {\n    visibility: hidden;\n    margin: 0;\n    padding: 0;\n}\n\n.wrs_modal_maximize_button:hover {\n    background-image: url(" +
          i(n(15)) +
          ");\n}\n\n.wrs_modal_wrapper {\n    display: block;\n    margin: 6px;\n}\n\n.wrs_modal_title_bar {\n    display: block;\n    background-color: #778e9a;\n}\n\n.wrs_modal_dialogContainer {\n    border: none;\n    background: #fafafa;\n    z-index: 999999;\n}\n\n.wrs_modal_dialogContainer.wrs_modal_desktop {\n    font-size: 14px;\n}\n\n.wrs_modal_dialogContainer.wrs_modal_desktop.wrs_maximized {\n    position: fixed;\n}\n\n.wrs_modal_dialogContainer.wrs_modal_desktop.wrs_minimized {\n    position: fixed;\n    top: inherit;\n    margin: 0;\n    margin-right: 10px;\n}\n\n.wrs_modal_dialogContainer.wrs_closed {\n    visibility: hidden;\n    display: none;\n    opacity: 0;\n}\n\n\n/* Class that exists but hasn't got css properties defined\n.wrs_modal_dialogContainer.wrs_modal_desktop.wrs_minimized.wrs_drag {} */\n\n.wrs_modal_dialogContainer.wrs_modal_desktop.wrs_stack {\n    position: fixed;\n    bottom: 0;\n    right: 0;\n    box-shadow: rgba(0, 0, 0, 0.5) 0 2px 8px;\n}\n\n.wrs_modal_dialogContainer.wrs_drag {\n    box-shadow: rgba(0, 0, 0, 0.5) 0 2px 8px;\n}\n\n.wrs_modal_dialogContainer.wrs_modal_desktop.wrs_drag {\n    box-shadow: rgba(0, 0, 0, 0.5) 0 2px 8px;\n}\n\n.wrs_modal_dialogContainer.wrs_modal_android {\n    margin: auto;\n    position: fixed;\n    width: 99%;\n    height: 99%;\n    overflow: hidden;\n    transform: translate(50%, -50%);\n    top: 50%;\n    right: 50% !important;\n    position: fixed;\n}\n\n.wrs_modal_dialogContainer.wrs_modal_ios {\n    margin: auto;\n    position: fixed;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    transform: translate(50%, -50%);\n    top: 50%;\n    right: 50% !important;\n    position: fixed;\n}\n\n\n/* Class that exists but hasn't got css properties defined\n.wrs_content_container.wrs_maximized {} */\n\n.wrs_content_container.wrs_minimized {\n    display: none;\n}\n\n/* .wrs_editor {\n    flex-grow: 1;\n} */\n\n.wrs_content_container.wrs_modal_android {\n    width: 100%;\n    flex-grow: 1;\n    display: flex;\n    flex-direction: column;\n}\n\n.wrs_content_container.wrs_modal_android > div:first-child {\n    flex-grow: 1;\n}\n\n.wrs_content_container.wrs_modal_ios > div:first-child {\n    flex-grow: 1;\n}\n\n.wrs_content_container.wrs_modal_desktop > div:first-child {\n    flex-grow: 1;\n}\n\n.wrs_modal_wrapper.wrs_modal_android {\n    margin: auto;\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    width: 100%;\n}\n\n.wrs_content_container.wrs_modal_desktop {\n    width: 100%;\n    flex-grow: 1;\n    display: flex;\n    flex-direction: column;\n}\n\n.wrs_content_container.wrs_modal_ios {\n    width: 100%;\n    flex-grow: 1;\n    display: flex;\n    flex-direction: column;\n}\n\n.wrs_modal_wrapper.wrs_modal_ios {\n    margin: auto;\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    width: 100%;\n}\n\n.wrs_virtual_keyboard {\n    height: 100%;\n    width: 100%;\n    top: 0;\n    left: 50%;\n    transform: translate(-50%, 0%);\n}\n\n@media all and (orientation: portrait) {\n    .wrs_modal_dialogContainer.wrs_modal_mobile {\n        width: 100vmin;\n        height: 100vmin;\n        margin: auto;\n        border-width: 0;\n    }\n    .wrs_modal_wrapper.wrs_modal_mobile {\n        width: 100vmin;\n        height: 100vmin;\n        margin: auto;\n    }\n}\n\n@media all and (orientation: landscape) {\n    .wrs_modal_dialogContainer.wrs_modal_mobile {\n        width: 100vmin;\n        height: 100vmin;\n        margin: auto;\n        border-width: 0;\n    }\n    .wrs_modal_wrapper.wrs_modal_mobile {\n        width: 100vmin;\n        height: 100vmin;\n        margin: auto;\n    }\n}\n\n.wrs_modal_dialogContainer.wrs_modal_badStock {\n    width: 100%;\n    height: 280px;\n    margin: 0 auto;\n    border-width: 0;\n}\n\n.wrs_modal_wrapper.wrs_modal_badStock {\n    width: 100%;\n    height: 280px;\n    margin: 0 auto;\n    border-width: 0;\n}\n.wrs_noselect {\n    -moz-user-select: none;\n    -khtml-user-select: none;\n    -webkit-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n}\n.wrs_bottom_right_resizer {\n    width: 10px;\n    height: 10px;\n    color: #778e9a;\n    position: absolute;\n    right: 4px;\n    bottom: 8px;\n    cursor: se-resize;\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n}\n.wrs_bottom_left_resizer {\n    width: 15px;\n    height: 15px;\n    color: #778e9a;\n    position: absolute;\n    left: 0;\n    top: 0;\n    cursor: se-resize;\n}\n\n.wrs_modal_controls {\n    height: 42px;\n    margin: 3px 0;\n    overflow: hidden;\n    line-height: normal;\n}\n\n.wrs_modal_links {\n    margin: 10px auto;\n    margin-bottom: 0;\n    font-family: arial, sans-serif;\n    padding: 6px;\n    display: inline;\n    float: right;\n    text-align: right;\n}\n\n.wrs_modal_links > a {\n    text-decoration: none;\n    color: #778e9a;\n    font-size: 16px;\n}\n\n.wrs_modal_button_cancel,\n.wrs_modal_button_cancel:hover,\n.wrs_modal_button_cancel:visited,\n.wrs_modal_button_cancel:active,\n.wrs_modal_button_cancel:focus {\n    min-width: 80px;\n    font-size: 14px;\n    border-radius: 3px;\n    border: 1px solid #778e9a;\n    padding: 6px 8px;\n    margin: 10px auto;\n    margin-left: 5px;\n    margin-bottom: 0;\n    cursor: pointer;\n    font-family: arial, sans-serif;\n    background-color: #DDDDDD;\n    height: 32px;\n}\n\n.wrs_modal_button_accept,\n.wrs_modal_button_accept:hover,\n.wrs_modal_button_accept:visited,\n.wrs_modal_button_accept:active,\n.wrs_modal_button_accept:focus {\n    min-width: 80px;\n    font-size: 14px;\n    border-radius: 3px;\n    border: 1px solid #778e9a;\n    padding: 6px 8px;\n    margin: 10px auto;\n    margin-right: 5px;\n    margin-bottom: 0;\n    color: #fff;\n    background: #778e9a;\n    cursor: pointer;\n    font-family: arial, sans-serif;\n    height: 32px;\n}\n\n.wrs_editor_vertical_bar {\n    height: 20px;\n    float: right;\n    background: none;\n    width: 20px;\n    cursor: pointer;\n}\n\n.wrs_modal_buttons_container {\n    display: inline;\n    float: left;\n}\n\n.wrs_modal_buttons_container.wrs_modalAndroid {\n    padding-left: 6px;\n}\n\n.wrs_modal_buttons_container.wrs_modalDesktop {\n    padding-left: 0;\n}\n\n.wrs_modal_buttons_container > button {\n    line-height: normal;\n    background-image: none;\n}\n\n.wrs_modal_wrapper {\n    margin: 6px;\n    display: flex;\n    flex-direction: column;\n}\n\n.wrs_modal_wrapper.wrs_modal_desktop.wrs_minimized {\n    display: none;\n}\n\n@media only screen and (max-device-width: 480px) and (orientation: portrait) {\n    #wrs_modal_wrapper {\n        width: 140%;\n    }\n}\n\n.wrs_popupmessage_overlay_envolture {\n    display: none;\n    width: 100%;\n}\n.wrs_popupmessage_overlay {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.5);\n    z-index: 4;\n    cursor: pointer;\n}\n.wrs_popupmessage_panel {\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    position: absolute;\n    background: white;\n    max-width: 500px;\n    width: 75%;\n    border-radius: 2px;\n    padding: 20px;\n    font-family: sans-serif;\n    font-size: 15px;\n    text-align: left;\n    color: #2e2e2e;\n    z-index: 5;\n    max-height: 75%;\n    overflow: auto;\n}\n\n.wrs_popupmessage_button_area {\n    margin: 10px 0 0 0;\n}\n\n.wrs_panelContainer * {\n    border: 0;\n}\n\n.wrs_button_cancel,\n.wrs_button_cancel:hover,\n.wrs_button_cancel:visited,\n.wrs_button_cancel:active,\n.wrs_button_cancel:focus {\n    min-width: 80px;\n    font-size: 14px;\n    border-radius: 3px;\n    border: 1px solid #778e9a;\n    padding: 6px 8px;\n    margin: 10px auto;\n    margin-left: 5px;\n    margin-bottom: 0;\n    cursor: pointer;\n    font-family: arial, sans-serif;\n    background-color: #DDDDDD;\n    background-image: none;\n    height: 32px;\n}\n\n.wrs_button_accept,\n.wrs_button_accept:hover,\n.wrs_button_accept:visited,\n.wrs_button_accept:active,\n.wrs_button_accept:focus {\n    min-width: 80px;\n    font-size: 14px;\n    border-radius: 3px;\n    border: 1px solid #778e9a;\n    padding: 6px 8px;\n    margin: 10px auto;\n    margin-right: 5px;\n    margin-bottom: 0;\n    color: #fff;\n    background: #778e9a;\n    cursor: pointer;\n    font-family: arial, sans-serif;\n    height: 32px;\n}\n\n.wrs_editor button{\n    box-shadow: none;\n}\n\n.wrs_editor .wrs_header button{\n    border-bottom: none;\n    border-bottom-left-radius: 0;\n    border-bottom-right-radius: 0;\n}\n\n.wrs_modal_overlay.wrs_modal_desktop.wrs_stack.wrs_overlay_active {\n    display: block;\n}\n/* Fix selection in drupal style */\n.wrs_toolbar tr:focus{\n    background: none;\n}\n.wrs_toolbar tr:hover{\n    background: none;\n}\n/* End of fix drupal */\n.wrs_modal_rtl .wrs_modal_button_cancel {\n    margin-right: 5px;\n    margin-left: 0;\n}\n.wrs_modal_rtl .wrs_modal_button_accept {\n    margin-right: 0;\n    margin-left: 5px;\n}\n.wrs_modal_rtl .wrs_button_cancel {\n    margin-right: 5px;\n    margin-left: 0;\n}\n.wrs_modal_rtl .wrs_button_accept {\n    margin-right: 0;\n    margin-left: 5px;\n}\n",
        "",
      ]);
    },
    function (e, t) {
      e.exports = function (e) {
        return "string" != typeof e
          ? e
          : (/^['"].*['"]$/.test(e) && (e = e.slice(1, -1)),
            /["'() \t\n]/.test(e)
              ? '"' + e.replace(/"/g, '\\"').replace(/\n/g, "\\n") + '"'
              : e);
      };
    },
    function (e, t) {
      e.exports = function (e) {
        var t = [];
        return (
          (t.toString = function () {
            return this.map(function (t) {
              var n = (function (e, t) {
                var n = e[1] || "",
                  i = e[3];
                if (!i) return n;
                if (t && "function" == typeof btoa) {
                  var o = (function (e) {
                      return (
                        "/*# sourceMappingURL=data:application/json;charset=utf-8;base64," +
                        btoa(unescape(encodeURIComponent(JSON.stringify(e)))) +
                        " */"
                      );
                    })(i),
                    r = i.sources.map(function (e) {
                      return "/*# sourceURL=" + i.sourceRoot + e + " */";
                    });
                  return [n].concat(r).concat([o]).join("\n");
                }
                return [n].join("\n");
              })(t, e);
              return t[2] ? "@media " + t[2] + "{" + n + "}" : n;
            }).join("");
          }),
          (t.i = function (e, n) {
            "string" == typeof e && (e = [[null, e, ""]]);
            for (var i = {}, o = 0; o < this.length; o++) {
              var r = this[o][0];
              "number" == typeof r && (i[r] = !0);
            }
            for (o = 0; o < e.length; o++) {
              var a = e[o];
              ("number" == typeof a[0] && i[a[0]]) ||
                (n && !a[2]
                  ? (a[2] = n)
                  : n && (a[2] = "(" + a[2] + ") and (" + n + ")"),
                t.push(a));
            }
          }),
          t
        );
      };
    },
    function (e, t) {
      e.exports =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAArCAYAAADhXXHAAAAACXBIWXMAAC4jAAAuIwF4pT92AAABvklEQVRYw83Z23GDMBAF0AsNhBIowSVQgjuISnAJKSEdZNOBS6CDOBUkqSC4gs2PyGhAQg92se4M4w8bccYW2hVumBmRdAB6ADfopQcw2SOYNoIkAL8APgB8AzgLI0/2S/iy1xkt3B9m9h0dM9/YHxM4J/c4MfPkGX+y763OyYVKgUPQTXAJdC84Bg2CS6Gl4FSoF7wHmgvOhbrgzsW+8L4YJegccrEj749Rgs7ZXGdz8wbAeNbREcDTzrHvblEgBbAUFACuy6JALJeL0E/P9sbvmBnNojcgAM+oJ58AhrlnWM5ZA+C9RmiokakBvIJuNTLSc7hojqY0Mo8EB6Ep2CPBm9BU7BHgKDQHqwlOguZiNcDJ0JLe4FV4iaLYJjF16dLqnoob+EdDs8A1QJPBtUCTwDVBo+DaoJvgNvBIR6rDl9wirbA1QIPgVgl6VwHb+dAr7JkkS/Pg3mCkVOslxxV9yBFqSqTA/3N2Utkzye3pftw5OxzQ5tHeddcdzGj3o4VgClUwowgtAVOs3BpFaA6YUnsDowhNAVNu12UUoVtgCn2+ifxp1wO42Ner4KPR5dJ2tsse2ZLvTQxbVf4AmC2z7WnSvpIAAAAASUVORK5CYII=";
    },
    function (e, t) {
      e.exports =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAArCAYAAADhXXHAAAAACXBIWXMAAC4jAAAuIwF4pT92AAAB2ElEQVRYw9XZoXPCMBTH8S+5KfDzQ29606CH3/SmQTO96aGHHn/F0Himh8eDZSblQknSJH2F0DtEQw8+12vyfulr7XY7LuW4qvj+DugD18AC+AE2woa+/mz07y9cF7Y8d7YPDEtjK2AsCB4BvdLYHPi0XawioAA3wAfQaQiKHhuFYl1QSbAL6gWrSKgEuArqBKsEaB1wKNQKVsasHybcpRhwLNQED0zsoMbzFwJOhWL6Cmzd2e0D14Wi1/k9di2wFNnAEtBifd9jv4GtIPgaeBOCAkzLFayr/6idWSSY6DJ8sHT96VK6zRFqKwo5gQ+grnKbA/gI6gsy5wRboT7sucBOaBX21GAvNAR7KnAlNBTbNDgIGoMtwO/C0GkoNBZbN525tk+dJrAj4F4YGxXgVQS019DkCgarM0OjwCoDaDBYZQINAquMoJVglRnUC1YZQp1g1RB0Jryn65jYJ0HoRGPHguDX8hsZ6VAiGX4eUrJBbHqSArdN7LLBmCcBnpvYWfHWo6E8Wge8Ar7Kj8E4ARwcnBPBB20BE7uJBMdAU8BH/YvyBAsFp0BjwNZGi201qALXgYaAnR0hX2upAzwDj/p8raFL5I4u8ALc6vNfvc+ztq5al9Rh/AfwZZ/LmlMllAAAAABJRU5ErkJggg==";
    },
    function (e, t) {
      e.exports =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAArCAYAAADhXXHAAAAACXBIWXMAAC4jAAAuIwF4pT92AAAAnUlEQVRYw+3Z0QnCMBSF4T/FATqCG1g3cISO0NE6iiPoCE5gneD40ohPvgkJ/AcC9/EjHELgliT0koGOIlasWLFixYoVK1asWLFixYoVK1bsjxy+5hlYgLEx47ofSEKSJW1nTUJJMgLPDlpwHoCpk8rOvgZixf4Zu3Vi3cq+WroBp4ahL+BYa3AB7o1CH7vvc7M1U4N/g2sdSk8bxjfDaMNdr+hmAQAAAABJRU5ErkJggg==";
    },
    function (e, t) {
      e.exports =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAArCAYAAADhXXHAAAAACXBIWXMAAC4jAAAuIwF4pT92AAAApklEQVRYw+3ZLQ4CMRCG4bcbFOvXg99T7FG4BafAw1VALx7dWyy2mIoGgSOZJu/n6p70ZybppFIKvWSgo4gVK1asWLFixYoVK1asWLFixYoV+yO7r/UMHIAxiO8FZGBrsUfgDEwBN/QNXIA11S/PW1BoCz4N9ein4Nd1Dyw9PbDR0iVW7J+xudax6HkOtZVdg0MfQE7N0G4GlmANYgNW4A6QepowfgDMXB26b1V6LAAAAABJRU5ErkJggg==";
    },
    function (e, t) {
      e.exports =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAArCAYAAAAOnxr+AAAACXBIWXMAAC4jAAAuIwF4pT92AAAAvElEQVRYw+3ZSw0CMRSF4b8T9iAFB4wDkDAWcICEkTA4GAeAA3AADurgsCkbAgsSMrmFczZNd1/a3vSVJFFDGipJNdBZaRdAB2wC2TIwAgNAkrQEjsA86GBegDZJGoF18JnfJtVR9idXvaGGGmrod/b6V9kD14k9LbD6FDqUM8CU2b2Deo0aaqihhhpqqKGGGhr1hH/wiP469FaBMzflEhc9PZKQ1CtmsqROEunpHbeNNN3A+dFJ/mf6V+gduGPIoUgKLbAAAAAASUVORK5CYII=";
    },
    function (e, t) {
      e.exports =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAArCAYAAAAOnxr+AAAACXBIWXMAAC4jAAAuIwF4pT92AAAAvUlEQVRYw+3ZsQ3CMBCF4d8WFekZgBqWIDUDZACmYBQWYIn0pGYAegZIexROERHRIBTdhXeVy08+yT4/JzMjQmWCVBjoarSugK0z3/0degKODjeyBy5Am8ysARrnnT8nM7sCa+fQLgdAAlQ6ngQVVFBBfzeUTK6t8VAwU328ztV6QQUVVFBBBRVUUEG9Ds41sJvZs/8GelDrlw7tAjhvmZLo9o6RD4bEGUp+X1My/I0T4HN4rrcASf9M/wp9ASNzIKYYz2hAAAAAAElFTkSuQmCC";
    },
    function (e, t) {
      e.exports =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAArCAYAAADhXXHAAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAALiMAAC4jAXilP3YAAAHOSURBVFhH1ZiLUcMwEEQNDcQl0AEuISVABZhOUkroICVAB6ECoINQgdmVfR5FlmQrkZzjzezEzsc8NPqcdNd1XfVfuB9ec3NAmv4yiRo5ImzBlm+cwZYtEHJCGsT3eSgHxKZFxs/tL+aMkCK8R3yMwu4PcsVmiXBIVDDCvh/miEtMeE5UaEsNMJcN8o64g26PvPSXs9S+/zRHQtgtvLRFCb9blZpnYw/9Rb6RR3M3zxtiprFbyKYwipK1+uwlnIkSrbITUaJR1itKtMkGRYk2WRZAQbTNBpzWtggrrwnaWja00hk0DrCgsEZZ4hXWKksmwjLAHobkgOv+V3+ZhXHQiWxKqXYLKNyILDdqbPKlldASPhA+Mxc7uwatkSOSix1iP//q2APshLBvfJo7hbizgQj/mDtl+KYuCj8h7NSqCM2zXJvZwqqEY4uCOuGYLKEwJ3kVzMlyscg5915FTFbdqhaSVbn8+mTV1gmurOqCxpZNEeUu9BlZd1obioTkQ7IhPGTjYZuPIoUMK/GUFrX39asuHJTlH3w1d3FCBxCrCUufZX+NCUdPSsAqwu4A8wnPiQrFhW1Z4govFRWKCoeOjzjoZF92CdwpZy6AquoPvJRHJxB8bJ8AAAAASUVORK5CYII=";
    },
    function (e, t) {
      e.exports =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAArCAYAAADhXXHAAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAALiMAAC4jAXilP3YAAAG/SURBVFhH1ZgxUsMwEEUNJRyAGmp6qKGn5xRQQ08NNfRQQw11DpAaanIAWrMv8WaELSlexhLLm/mRnImiF48jr7zVtm3zX9ju2ik5llxLdpdHNg4kT5I7yWB8Cdl9yZHkRmIRRpQxOxK+YzC+hKwSnTBBKKoMxpeUBSbkksgRE1V+CJeWhUPJ5ao7ICeqrIVryMKJpC88RlTZk1SThVDYIvoluZIsSqyz511SfEg4UxbRdw5qnlmFa9AsCn8hO4aBKHiUjYqCN9mkKHiSzYqCJ9lPSVIUPMmySqTudEu8XbOxO90ab7KQFPYoC1Fhr7IwENbagMLCUtXnoCTM1QZW3iS3dFT2mRfHvEjuVfZUckFnQh67dgqo1GYqC1MLn3XtZIR/sFcJW2C39FcD18KxpcutcGqddSmcuykg/LDq+iAnC/OudUFOVrfLbkjJWvb11YjJuhSFvqxbUQhlXYuCylpE2YXy2SkLlVEgaxVluzyTIEutWQ1kKZYtouF2maK4mjCyFN6bJsw9gKgmrNdsbsKNT0qEKsIqC7EJx4gqxYVDWQgntIgqRYXDbY3CLpcVgmdPC974BYy3/MgRNM03hR9ubFTHT48AAAAASUVORK5CYII=";
    },
    function (e, t) {
      e.exports =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAArCAYAAADhXXHAAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAALiMAAC4jAXilP3YAAAG4SURBVFhHvZnhUYNAEEbRBkwH2oGUkA40FWgJKSEdaAmxA0vQDmIHKSFWgPuAHZkEAnd8y5v5kuNHMm+WY1mSm6qqCiGlZdUspXzxopY9Wu6bpZQfSxlRWapwVx9p2dy2CxUHy9ryWx9pKdWyECYcIQshwlGyIBeOlAWpcLQsyISXkAWEX5tlPkvJwnP7ns1SsnvLS7PMZwlZiShEy8pEIVJWKgpRsnJRiJBNFf2wbCzjfZgRUZi9JYWDxT9bWk6WIXbKym4tKRVloObO5oze6ZClWX9a5jyOcOrfmuUkXPRUH/1zVRhZpvsnCxN+jnDqHh0SdQaFu9vg0ZIqrBZ1eoXP92yKcJSocyHcd4FNEY4WdbrCR1rGrukMF9BWVhZvLZ7U9rS2nH9HVvoq63iFu+RUlOpIuCYLCCPIqVjq1A9j5R3aBnMY2kKzMlbZHPQVbVHLhomCUjZUFFSy35ZQUVDIMo+Gi4JCltFwERSy75Y54+VkFLLcKHLHyyRUF1jOeJmMShbChZWy0Df8yFDLgg8/cpCN6I9cdHJhZHmy7X2anAnCtDUZ/j/Yg2X2j709MHhTDAFF8QdK9SRpUl2yFgAAAABJRU5ErkJggg==";
    },
    function (e, t) {
      e.exports =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAArCAYAAADhXXHAAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAALiMAAC4jAXilP3YAAAGMSURBVFhHvdk7TsNAFIVhQ0l6elLDJqCGngXQU7MA6rALahZATQ81C6APrXP/jEaKHD/i8TnzS1eaICF/2I4f4qxt20bYOmaVlrK2Mb8s1Nj3mIu0lPYZszlPa1kvMf9pKe02Zq3Gcrhc4JUaSzawA0sWsAtLcrATS1KwG0sycA0sAd6kZXm1sNzVHtOyvBpYoK8xV/tPC3JjZVByYqVQcmHlUHJgLVBSY0ugPP7xO5PXYSW2FMr19ytm8sahxD7ElEBzk3c6sFysn/afymKPvsXMueh3oblRMNibmPuYZ34wsyWHfqhB8OFpwKvDHLADmusFd8/ZU8FOaO4I3PcFmwLXgOYOwVtexdnwdUy3vg2UQPnD2eji+vZsrruHS/eoBEpjWMpgrhi1Dv1gY6fBkuRQmtqzJVmgpMbaoKTEWqGkwtqhpMBWgZICWwVKCuwpzxKSFNi5T2vFqb5gVcAqLNnBSixZwWos2cBg/9JSmgUM9iMt5QFe8tZ8VP6n3WXMHQtxPzHfabm0ptkBwWhpthzMp7YAAAAASUVORK5CYII=";
    },
    function (e, t, n) {
      var i = {},
        o = (function (e) {
          var t;
          return function () {
            return void 0 === t && (t = e.apply(this, arguments)), t;
          };
        })(function () {
          return window && document && document.all && !window.atob;
        }),
        r = (function (e) {
          var t = {};
          return function (e, n) {
            if ("function" == typeof e) return e();
            if (void 0 === t[e]) {
              var i = function (e, t) {
                return t ? t.querySelector(e) : document.querySelector(e);
              }.call(this, e, n);
              if (
                window.HTMLIFrameElement &&
                i instanceof window.HTMLIFrameElement
              )
                try {
                  i = i.contentDocument.head;
                } catch (e) {
                  i = null;
                }
              t[e] = i;
            }
            return t[e];
          };
        })(),
        a = null,
        s = 0,
        l = [],
        c = n(17);
      function u(e, t) {
        for (var n = 0; n < e.length; n++) {
          var o = e[n],
            r = i[o.id];
          if (r) {
            r.refs++;
            for (var a = 0; a < r.parts.length; a++) r.parts[a](o.parts[a]);
            for (; a < o.parts.length; a++) r.parts.push(p(o.parts[a], t));
          } else {
            var s = [];
            for (a = 0; a < o.parts.length; a++) s.push(p(o.parts[a], t));
            i[o.id] = { id: o.id, refs: 1, parts: s };
          }
        }
      }
      function d(e, t) {
        for (var n = [], i = {}, o = 0; o < e.length; o++) {
          var r = e[o],
            a = t.base ? r[0] + t.base : r[0],
            s = { css: r[1], media: r[2], sourceMap: r[3] };
          i[a] ? i[a].parts.push(s) : n.push((i[a] = { id: a, parts: [s] }));
        }
        return n;
      }
      function h(e, t) {
        var n = r(e.insertInto);
        if (!n)
          throw new Error(
            "Couldn't find a style target. This probably means that the value for the 'insertInto' parameter is invalid."
          );
        var i = l[l.length - 1];
        if ("top" === e.insertAt)
          i
            ? i.nextSibling
              ? n.insertBefore(t, i.nextSibling)
              : n.appendChild(t)
            : n.insertBefore(t, n.firstChild),
            l.push(t);
        else if ("bottom" === e.insertAt) n.appendChild(t);
        else {
          if ("object" != typeof e.insertAt || !e.insertAt.before)
            throw new Error(
              "[Style Loader]\n\n Invalid value for parameter 'insertAt' ('options.insertAt') found.\n Must be 'top', 'bottom', or Object.\n (https://github.com/webpack-contrib/style-loader#insertat)\n"
            );
          var o = r(e.insertAt.before, n);
          n.insertBefore(t, o);
        }
      }
      function m(e) {
        if (null === e.parentNode) return !1;
        e.parentNode.removeChild(e);
        var t = l.indexOf(e);
        t >= 0 && l.splice(t, 1);
      }
      function f(e) {
        var t = document.createElement("style");
        if (
          (void 0 === e.attrs.type && (e.attrs.type = "text/css"),
          void 0 === e.attrs.nonce)
        ) {
          var i = (function () {
            0;
            return n.nc;
          })();
          i && (e.attrs.nonce = i);
        }
        return g(t, e.attrs), h(e, t), t;
      }
      function g(e, t) {
        Object.keys(t).forEach(function (n) {
          e.setAttribute(n, t[n]);
        });
      }
      function p(e, t) {
        var n, i, o, r;
        if (t.transform && e.css) {
          if (
            !(r =
              "function" == typeof t.transform
                ? t.transform(e.css)
                : t.transform.default(e.css))
          )
            return function () {};
          e.css = r;
        }
        if (t.singleton) {
          var l = s++;
          (n = a || (a = f(t))),
            (i = v.bind(null, n, l, !1)),
            (o = v.bind(null, n, l, !0));
        } else
          e.sourceMap &&
          "function" == typeof URL &&
          "function" == typeof URL.createObjectURL &&
          "function" == typeof URL.revokeObjectURL &&
          "function" == typeof Blob &&
          "function" == typeof btoa
            ? ((n = (function (e) {
                var t = document.createElement("link");
                return (
                  void 0 === e.attrs.type && (e.attrs.type = "text/css"),
                  (e.attrs.rel = "stylesheet"),
                  g(t, e.attrs),
                  h(e, t),
                  t
                );
              })(t)),
              (i = function (e, t, n) {
                var i = n.css,
                  o = n.sourceMap,
                  r = void 0 === t.convertToAbsoluteUrls && o;
                (t.convertToAbsoluteUrls || r) && (i = c(i));
                o &&
                  (i +=
                    "\n/*# sourceMappingURL=data:application/json;base64," +
                    btoa(unescape(encodeURIComponent(JSON.stringify(o)))) +
                    " */");
                var a = new Blob([i], { type: "text/css" }),
                  s = e.href;
                (e.href = URL.createObjectURL(a)), s && URL.revokeObjectURL(s);
              }.bind(null, n, t)),
              (o = function () {
                m(n), n.href && URL.revokeObjectURL(n.href);
              }))
            : ((n = f(t)),
              (i = function (e, t) {
                var n = t.css,
                  i = t.media;
                i && e.setAttribute("media", i);
                if (e.styleSheet) e.styleSheet.cssText = n;
                else {
                  for (; e.firstChild; ) e.removeChild(e.firstChild);
                  e.appendChild(document.createTextNode(n));
                }
              }.bind(null, n)),
              (o = function () {
                m(n);
              }));
        return (
          i(e),
          function (t) {
            if (t) {
              if (
                t.css === e.css &&
                t.media === e.media &&
                t.sourceMap === e.sourceMap
              )
                return;
              i((e = t));
            } else o();
          }
        );
      }
      e.exports = function (e, t) {
        if ("undefined" != typeof DEBUG && DEBUG && "object" != typeof document)
          throw new Error(
            "The style-loader cannot be used in a non-browser environment"
          );
        ((t = t || {}).attrs = "object" == typeof t.attrs ? t.attrs : {}),
          t.singleton || "boolean" == typeof t.singleton || (t.singleton = o()),
          t.insertInto || (t.insertInto = "head"),
          t.insertAt || (t.insertAt = "bottom");
        var n = d(e, t);
        return (
          u(n, t),
          function (e) {
            for (var o = [], r = 0; r < n.length; r++) {
              var a = n[r];
              (s = i[a.id]).refs--, o.push(s);
            }
            e && u(d(e, t), t);
            for (r = 0; r < o.length; r++) {
              var s;
              if (0 === (s = o[r]).refs) {
                for (var l = 0; l < s.parts.length; l++) s.parts[l]();
                delete i[s.id];
              }
            }
          }
        );
      };
      var _ = (function () {
        var e = [];
        return function (t, n) {
          return (e[t] = n), e.filter(Boolean).join("\n");
        };
      })();
      function v(e, t, n, i) {
        var o = n ? "" : i.css;
        if (e.styleSheet) e.styleSheet.cssText = _(t, o);
        else {
          var r = document.createTextNode(o),
            a = e.childNodes;
          a[t] && e.removeChild(a[t]),
            a.length ? e.insertBefore(r, a[t]) : e.appendChild(r);
        }
      }
    },
    function (e, t) {
      e.exports = function (e) {
        var t = "undefined" != typeof window && window.location;
        if (!t) throw new Error("fixUrls requires window.location");
        if (!e || "string" != typeof e) return e;
        var n = t.protocol + "//" + t.host,
          i = n + t.pathname.replace(/\/[^\/]*$/, "/");
        return e.replace(
          /url\s*\(((?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)\)/gi,
          function (e, t) {
            var o,
              r = t
                .trim()
                .replace(/^"(.*)"$/, function (e, t) {
                  return t;
                })
                .replace(/^'(.*)'$/, function (e, t) {
                  return t;
                });
            return /^(#|data:|http:\/\/|https:\/\/|file:\/\/\/|\s*$)/i.test(r)
              ? e
              : ((o =
                  0 === r.indexOf("//")
                    ? r
                    : 0 === r.indexOf("/")
                    ? n + r
                    : i + r.replace(/^\.\//, "")),
                "url(" + JSON.stringify(o) + ")");
          }
        );
      };
    },
    function (e, t, n) {
      "use strict";
      function i(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      n.r(t);
      var o = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e);
        }
        return (
          (function (e, t, n) {
            t && i(e.prototype, t), n && i(e, n);
          })(e, null, [
            {
              key: "safeXmlCharactersEntities",
              get: function () {
                return {
                  tagOpener: "&laquo;",
                  tagCloser: "&raquo;",
                  doubleQuote: "&uml;",
                  realDoubleQuote: "&quot;",
                };
              },
            },
            {
              key: "safeBadBlackboardCharacters",
              get: function () {
                return {
                  ltElement: "«mo»<«/mo»",
                  gtElement: "«mo»>«/mo»",
                  ampElement: "«mo»&«/mo»",
                };
              },
            },
            {
              key: "safeGoodBlackboardCharacters",
              get: function () {
                return {
                  ltElement: "«mo»§lt;«/mo»",
                  gtElement: "«mo»§gt;«/mo»",
                  ampElement: "«mo»§amp;«/mo»",
                };
              },
            },
            {
              key: "xmlCharacters",
              get: function () {
                return {
                  id: "xmlCharacters",
                  tagOpener: "<",
                  tagCloser: ">",
                  doubleQuote: '"',
                  ampersand: "&",
                  quote: "'",
                };
              },
            },
            {
              key: "safeXmlCharacters",
              get: function () {
                return {
                  id: "safeXmlCharacters",
                  tagOpener: "«",
                  tagCloser: "»",
                  doubleQuote: "¨",
                  ampersand: "§",
                  quote: "`",
                  realDoubleQuote: "¨",
                };
              },
            },
          ]),
          e
        );
      })();
      function r(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var a = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e);
        }
        return (
          (function (e, t, n) {
            t && r(e.prototype, t), n && r(e, n);
          })(e, null, [
            {
              key: "isMathmlInAttribute",
              value: function (e, t) {
                var n = "[\\s]*(".concat(
                    "\"[^\"]*\"|'[^']*'",
                    ")[\\s]*=[\\s]*[\\w-]+[\\s]*"
                  ),
                  i = "('".concat(n, "')*"),
                  o = "^"
                    .concat("['\"][\\s]*=[\\s]*[\\w-]+")
                    .concat(i, "[\\s]+gmi<"),
                  r = new RegExp(o),
                  a = e.substring(0, t).split("").reverse().join("");
                return r.test(a);
              },
            },
            {
              key: "safeXmlDecode",
              value: function (e) {
                var t = o.safeXmlCharactersEntities.tagOpener,
                  n = o.safeXmlCharactersEntities.tagCloser,
                  i = o.safeXmlCharactersEntities.doubleQuote,
                  r = o.safeXmlCharactersEntities.realDoubleQuote;
                e = (e = (e = (e = e.split(t).join(o.safeXmlCharacters.tagOpener))
                  .split(n)
                  .join(o.safeXmlCharacters.tagCloser))
                  .split(i)
                  .join(o.safeXmlCharacters.doubleQuote))
                  .split(r)
                  .join(o.safeXmlCharacters.realDoubleQuote);
                var a = o.safeBadBlackboardCharacters.ltElement,
                  s = o.safeBadBlackboardCharacters.gtElement,
                  l = o.safeBadBlackboardCharacters.ampElement;
                "_wrs_blackboard" in window &&
                  window._wrs_blackboard &&
                  (e = (e = (e = e
                    .split(a)
                    .join(o.safeGoodBlackboardCharacters.ltElement))
                    .split(s)
                    .join(o.safeGoodBlackboardCharacters.gtElement))
                    .split(l)
                    .join(o.safeGoodBlackboardCharacters.ampElement)),
                  (t = o.safeXmlCharacters.tagOpener),
                  (n = o.safeXmlCharacters.tagCloser),
                  (i = o.safeXmlCharacters.doubleQuote),
                  (r = o.safeXmlCharacters.realDoubleQuote);
                var c = o.safeXmlCharacters.ampersand,
                  u = o.safeXmlCharacters.quote;
                e = (e = (e = (e = (e = e
                  .split(t)
                  .join(o.xmlCharacters.tagOpener))
                  .split(n)
                  .join(o.xmlCharacters.tagCloser))
                  .split(i)
                  .join(o.xmlCharacters.doubleQuote))
                  .split(c)
                  .join(o.xmlCharacters.ampersand))
                  .split(u)
                  .join(o.xmlCharacters.quote);
                for (var d = "", h = null, m = 0; m < e.length; m += 1) {
                  var f = e.charAt(m);
                  null == h
                    ? "$" === f
                      ? (h = "")
                      : (d += f)
                    : ";" === f
                    ? ((d += "&".concat(h)), (h = null))
                    : f.match(/([a-zA-Z0-9#._-] | '-')/)
                    ? (h += f)
                    : ((d += "$".concat(h)), (h = null), (m -= 1));
                }
                return d;
              },
            },
            {
              key: "safeXmlEncode",
              value: function (e) {
                var t = o.xmlCharacters.tagOpener,
                  n = o.xmlCharacters.tagCloser,
                  i = o.xmlCharacters.doubleQuote,
                  r = o.xmlCharacters.ampersand,
                  a = o.xmlCharacters.quote;
                return (e = (e = (e = (e = (e = e
                  .split(t)
                  .join(o.safeXmlCharacters.tagOpener))
                  .split(n)
                  .join(o.safeXmlCharacters.tagCloser))
                  .split(i)
                  .join(o.safeXmlCharacters.doubleQuote))
                  .split(r)
                  .join(o.safeXmlCharacters.ampersand))
                  .split(a)
                  .join(o.safeXmlCharacters.quote));
              },
            },
            {
              key: "mathMLEntities",
              value: function (e) {
                for (var t = "", n = 0; n < e.length; n += 1) {
                  var i = e.charAt(n);
                  if (e.codePointAt(n) > 128)
                    (t += "&#".concat(e.codePointAt(n), ";")),
                      e.codePointAt(n) > 65535 && (n += 1);
                  else if ("&" === i) {
                    var o = e.indexOf(";", n + 1);
                    if (o >= 0) {
                      var r = document.createElement("span");
                      (r.innerHTML = e.substring(n, o + 1)),
                        (t += "&#".concat(
                          w.fixedCharCodeAt(r.textContent || r.innerText, 0),
                          ";"
                        )),
                        (n = o);
                    } else t += i;
                  } else t += i;
                }
                return t;
              },
            },
            {
              key: "addCustomEditorClassAttribute",
              value: function (e, t) {
                var n = "",
                  i = e.indexOf("<math");
                if (0 === i) {
                  var o = e.indexOf(">");
                  if (-1 === e.indexOf("class"))
                    return (
                      (n = ""
                        .concat(e.substr(i, o), ' class="wrs_')
                        .concat(t, '">')),
                      (n += e.substr(o + 1, e.length))
                    );
                }
                return e;
              },
            },
            {
              key: "removeCustomEditorClassAttribute",
              value: function (e, t) {
                return -1 === e.indexOf("class") ||
                  -1 === e.indexOf("wrs_".concat(t))
                  ? e
                  : -1 !== e.indexOf('class="wrs_'.concat(t, '"'))
                  ? e.replace('class="wrs_'.concat(t, '"'), "")
                  : e.replace("wrs_".concat(t), "");
              },
            },
            {
              key: "addAnnotation",
              value: function (t, n, i) {
                var o = "";
                if (-1 !== t.indexOf("<annotation")) {
                  var r = t.indexOf("</semantics>");
                  o = ""
                    .concat(t.substring(0, r), '<annotation encoding="')
                    .concat(i, '">')
                    .concat(n, "</annotation>")
                    .concat(t.substring(r));
                } else if (e.isEmpty(t)) {
                  var a = t.indexOf("/>"),
                    s = t.indexOf(">"),
                    l = s === a ? a : s;
                  o = ""
                    .concat(
                      t.substring(0, l),
                      '><semantics><annotation encoding="'
                    )
                    .concat(i, '">')
                    .concat(n, "</annotation></semantics></math>");
                } else {
                  var c = t.indexOf(">") + 1,
                    u = t.lastIndexOf("</math>"),
                    d = t.substring(c, u);
                  o = ""
                    .concat(t.substring(0, c), "<semantics>")
                    .concat(d, '<annotation encoding="')
                    .concat(i, '">')
                    .concat(n, "</annotation></semantics></math>");
                }
                return o;
              },
            },
            {
              key: "removeAnnotation",
              value: function (t, n) {
                var i = t,
                  o = '<annotation encoding="'.concat(n, '">'),
                  r = t.indexOf(o);
                if (-1 !== r) {
                  for (var a = !1, s = t.indexOf("<annotation"); -1 !== s; )
                    s !== r && (a = !0), (s = t.indexOf("<annotation", s + 1));
                  if (a) {
                    var l =
                      t.indexOf("</annotation>", r) + "</annotation>".length;
                    i = t.substring(0, r) + t.substring(l);
                  } else i = e.removeSemantics(t);
                }
                return i;
              },
            },
            {
              key: "removeSemantics",
              value: function (e) {
                var t = e,
                  n = e.indexOf("<semantics>");
                if (-1 !== n) {
                  var i = e.indexOf("<annotation", n + "<semantics>".length);
                  -1 !== i &&
                    (t =
                      e.substring(0, n) +
                      e.substring(n + "<semantics>".length, i) +
                      "</math>");
                }
                return t;
              },
            },
            {
              key: "removeSemanticsOcurrences",
              value: function (e) {
                for (
                  var t =
                      arguments.length > 1 && void 0 !== arguments[1]
                        ? arguments[1]
                        : o.xmlCharacters,
                    n = "".concat(t.tagOpener, "math"),
                    i = "".concat(t.tagOpener, "/math").concat(t.tagCloser),
                    r = "/".concat(t.tagCloser),
                    a = t.tagCloser,
                    s = "".concat(t.tagOpener, "semantics").concat(t.tagCloser),
                    l = "".concat(t.tagOpener, "annotation encoding="),
                    c = "",
                    u = e.indexOf(n),
                    d = 0;
                  -1 !== u;
  
                ) {
                  c += e.substring(d, u);
                  var h = e.indexOf(i, u),
                    m = e.indexOf(r, u),
                    f = e.indexOf(a, u);
                  -1 !== h ? (d = h) : m === f - 1 && (d = m);
                  var g = e.indexOf(s, u);
                  if (-1 !== g) {
                    var p = e.substring(u, g),
                      _ = e.indexOf(l, u);
                    if (-1 !== _) {
                      var v = g + s.length;
                      (c += p + e.substring(v, _) + i),
                        (u = e.indexOf(n, u + n.length)),
                        (d += i.length);
                    } else (d = u), (u = e.indexOf(n, u + n.length));
                  } else (d = u), (u = e.indexOf(n, u + n.length));
                }
                return (c += e.substring(d, e.length));
              },
            },
            {
              key: "containClass",
              value: function (e, t) {
                var n = e.indexOf("class");
                if (-1 === n) return !1;
                var i = e.indexOf(">", n);
                return -1 !== e.substring(n, i).indexOf(t);
              },
            },
            {
              key: "isEmpty",
              value: function (e) {
                var t = e.indexOf(">"),
                  n = e.indexOf("/>"),
                  i = !1;
                if ((-1 !== n && n === t - 1 && (i = !0), !i)) {
                  var o = new RegExp("</(.+:)?math>").exec(e);
                  o && (i = t + 1 === o.index);
                }
                return i;
              },
            },
            {
              key: "encodeProperties",
              value: function (e) {
                return e.replace(/\w+=".*?"/g, function (e) {
                  var t = e.indexOf('"'),
                    n = e.substring(t + 1, e.length - 1),
                    i = w.htmlEntities(n);
                  return "".concat(e.substring(0, t + 1)).concat(i, '"');
                });
              },
            },
          ]),
          e
        );
      })();
      function s(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var l = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e);
        }
        return (
          (function (e, t, n) {
            t && s(e.prototype, t), n && s(e, n);
          })(e, null, [
            {
              key: "addConfiguration",
              value: function (t) {
                Object.assign(e.properties, t);
              },
            },
            {
              key: "get",
              value: function (t) {
                return Object.prototype.hasOwnProperty.call(e.properties, t)
                  ? e.properties[t]
                  : !!Object.prototype.hasOwnProperty.call(
                      e.properties,
                      "_wrs_conf_"
                    ) && e.properties["_wrs_conf_".concat(t)];
              },
            },
            {
              key: "set",
              value: function (t, n) {
                e.properties[t] = n;
              },
            },
            {
              key: "update",
              value: function (t, n) {
                if (e.get(t)) {
                  var i = Object.assign(e.get(t), n);
                  e.set(t, i);
                } else e.set(t, n);
              },
            },
            {
              key: "properties",
              get: function () {
                return e._properties;
              },
              set: function (t) {
                e._properties = t;
              },
            },
          ]),
          e
        );
      })();
      function c(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      l._properties = {};
      var u = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e),
            (this.cache = []);
        }
        return (
          (function (e, t, n) {
            t && c(e.prototype, t), n && c(e, n);
          })(e, [
            {
              key: "populate",
              value: function (e, t) {
                this.cache[e] = t;
              },
            },
            {
              key: "get",
              value: function (e) {
                return (
                  !!Object.prototype.hasOwnProperty.call(this.cache, e) &&
                  this.cache[e]
                );
              },
            },
          ]),
          e
        );
      })();
      function d(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var h = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e),
            (this.listeners = []);
        }
        return (
          (function (e, t, n) {
            t && d(e.prototype, t), n && d(e, n);
          })(
            e,
            [
              {
                key: "add",
                value: function (e) {
                  this.listeners.push(e);
                },
              },
              {
                key: "fire",
                value: function (e, t) {
                  for (
                    var n = 0;
                    n < this.listeners.length && !t.cancelled;
                    n += 1
                  )
                    this.listeners[n].eventName === e &&
                      this.listeners[n].callback(t);
                  return t.defaultPrevented;
                },
              },
            ],
            [
              {
                key: "newListener",
                value: function (e, t) {
                  var n = {};
                  return (n.eventName = e), (n.callback = t), n;
                },
              },
            ]
          ),
          e
        );
      })();
      function m(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var f = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e);
        }
        return (
          (function (e, t, n) {
            t && m(e.prototype, t), n && m(e, n);
          })(e, null, [
            {
              key: "addListener",
              value: function (t) {
                e.listeners.add(t);
              },
            },
            {
              key: "fireEvent",
              value: function (t, n) {
                e.listeners.fire(t, n);
              },
            },
            {
              key: "setServicePath",
              value: function (t, n) {
                e.servicePaths[t] = n;
              },
            },
            {
              key: "getServicePath",
              value: function (t) {
                return e.servicePaths[t];
              },
            },
            {
              key: "getServerURL",
              value: function () {
                var e = window.location.href.split("/");
                return "".concat(e[0], "//").concat(e[2]);
              },
            },
            {
              key: "init",
              value: function (t) {
                e.parameters = t;
                var n = e.createServiceURI("configurationjs"),
                  i = e.createServiceURI("createimage"),
                  o = e.createServiceURI("showimage"),
                  r = e.createServiceURI("getmathml"),
                  a = e.createServiceURI("service");
                if (0 === e.parameters.URI.indexOf("/")) {
                  var s = e.getServerURL();
                  (n = s + n), (o = s + o), (i = s + i), (r = s + r), (a = s + a);
                }
                e.setServicePath("configurationjs", n),
                  e.setServicePath("showimage", o),
                  e.setServicePath("createimage", i),
                  e.setServicePath("service", a),
                  e.setServicePath("getmathml", r),
                  e.setServicePath("configurationjs", n),
                  e.listeners.fire("onInit", {});
              },
            },
            {
              key: "getUrl",
              value: function (e, t) {
                var n = window.location
                    .toString()
                    .substr(0, window.location.toString().lastIndexOf("/") + 1),
                  i = w.createHttpRequest();
                return i
                  ? (void 0 === t || void 0 === t
                      ? i.open("GET", e, !1)
                      : "/" === e.substr(0, 1) ||
                        "http://" === e.substr(0, 7) ||
                        "https://" === e.substr(0, 8)
                      ? i.open("POST", e, !1)
                      : i.open("POST", n + e, !1),
                    void 0 !== t && t
                      ? (i.setRequestHeader(
                          "Content-type",
                          "application/x-www-form-urlencoded; charset=UTF-8"
                        ),
                        i.send(w.httpBuildQuery(t)))
                      : i.send(null),
                    i.responseText)
                  : "";
              },
            },
            {
              key: "getService",
              value: function (t, n, i) {
                var o;
                if (!0 === i) {
                  var r = "".concat(e.getServicePath(t), "?").concat(n);
                  o = e.getUrl(r);
                } else {
                  var a = e.getServicePath(t);
                  o = e.getUrl(a, n);
                }
                return o;
              },
            },
            {
              key: "getServerLanguageFromService",
              value: function (e) {
                return -1 !== e.indexOf(".php")
                  ? "php"
                  : -1 !== e.indexOf(".aspx")
                  ? "aspx"
                  : -1 !== e.indexOf("wirispluginengine")
                  ? "ruby"
                  : "java";
              },
            },
            {
              key: "createServiceURI",
              value: function (t) {
                var n = e.serverExtension();
                return w.concatenateUrl(e.parameters.URI, t) + n;
              },
            },
            {
              key: "serverExtension",
              value: function () {
                return -1 !== e.parameters.server.indexOf("php")
                  ? ".php"
                  : -1 !== e.parameters.server.indexOf("aspx")
                  ? ".aspx"
                  : "";
              },
            },
            {
              key: "listeners",
              get: function () {
                return e._listeners;
              },
            },
            {
              key: "parameters",
              get: function () {
                return e._parameters;
              },
              set: function (t) {
                e._parameters = t;
              },
            },
            {
              key: "servicePaths",
              get: function () {
                return e._servicePaths;
              },
              set: function (t) {
                e._servicePaths = t;
              },
            },
            {
              key: "integrationPath",
              get: function () {
                return e._integrationPath;
              },
              set: function (t) {
                e._integrationPath = t;
              },
            },
          ]),
          e
        );
      })();
      function g(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      (f._servicePaths = {}),
        (f._integrationPath = ""),
        (f._listeners = new h()),
        (f._parameters = {});
      var p = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e);
        }
        return (
          (function (e, t, n) {
            t && g(e.prototype, t), n && g(e, n);
          })(e, null, [
            {
              key: "getLatexFromMathML",
              value: function (t) {
                var n = a.removeSemantics(t),
                  i = e.cache,
                  o = { service: "mathml2latex", mml: n },
                  r = JSON.parse(f.getService("service", o)),
                  s = "";
                if ("ok" === r.status) {
                  s = r.result.text;
                  var l = w.htmlEntities(s),
                    c = a.addAnnotation(t, l, "LaTeX");
                  i.populate(s, c);
                }else{
                  s = MathMLToLaTeX.MathMLToLaTeX.convert(n.replace(/mml:/g, ''));
                  var l = w.htmlEntities(s),
                    c = a.addAnnotation(t, l, "LaTeX");
                  i.populate(s, c);
                }
                return s;
              },
            },
            {
              key: "getMathMLFromLatex",
              value: function (t, n) {
                var i = e.cache;
                if (e.cache.get(t)) return e.cache.get(t);
                var o = { service: "latex2mathml", latex: t };
                n && (o.saveLatex = "");
                var r,
                  s = JSON.parse(f.getService("service", o));
                if ("ok" === s.status) {
                  var l = s.result.text;
                  (r =
                    -1 ===
                      (l = l.split("\r").join("").split("\n").join(" ")).indexOf(
                        "semantics"
                      ) && -1 === l.indexOf("annotation")
                      ? (l = a.addAnnotation(l, t, "LaTeX"))
                      : l),
                    i.get(t) || i.populate(t, l);
                } else r = "$$".concat(t, "$$");
                return r;
              },
            },
            {
              key: "parseMathmlToLatex",
              value: function (t, n) {
                for (
                  var i,
                    r,
                    s,
                    l = "",
                    c = "".concat(n.tagOpener, "math"),
                    u = "".concat(n.tagOpener, "/math").concat(n.tagCloser),
                    d = ""
                      .concat(n.tagOpener, "annotation encoding=")
                      .concat(n.doubleQuote, "LaTeX")
                      .concat(n.doubleQuote)
                      .concat(n.tagCloser),
                    h = "".concat(n.tagOpener, "/annotation").concat(n.tagCloser),
                    m = t.indexOf(c),
                    f = 0;
                  -1 !== m;
  
                ) {
                  if (
                    ((l += t.substring(f, m)),
                    -1 === (f = t.indexOf(u, m))
                      ? (f = t.length - 1)
                      : (f += u.length),
                    -1 !== (r = (i = t.substring(m, f)).indexOf(d)))
                  ) {
                    (r += d.length), (s = i.indexOf(h));
                    var g = i.substring(r, s);
                    n === o.safeXmlCharacters && (g = a.safeXmlDecode(g)),
                      (l += "$$".concat(g, "$$")),
                      e.cache.populate(g, i);
                  } else l += i;
                  m = t.indexOf(c, f);
                }
                return (l += t.substring(f, t.length));
              },
            },
            {
              key: "getLatexFromTextNode",
              value: function (e, t, n) {
                (void 0 !== n && null != n) || (n = { open: "$$", close: "$$" });
                for (
                  var i, o = e;
                  o.previousSibling && 3 === o.previousSibling.nodeType;
  
                )
                  o = o.previousSibling;
                function r(e, t, i) {
                  for (var o = e.nodeValue.indexOf(i, t); -1 === o; ) {
                    if (!(e = e.nextSibling)) return null;
                    o = e.nodeValue ? e.nodeValue.indexOf(n.close) : -1;
                  }
                  return { node: e, position: o };
                }
                function a(e, t, n, i) {
                  if (e === n) return t <= i;
                  for (; e && e !== n; ) e = e.nextSibling;
                  return e === n;
                }
                var s,
                  l = { node: o, position: 0 },
                  c = n.open.length;
                do {
                  if (
                    null == (i = r(l.node, l.position, n.open)) ||
                    a(e, t, i.node, i.position)
                  )
                    return null;
                  if (null == (l = r(i.node, i.position + c, n.close)))
                    return null;
                  l.position += c;
                } while (a(l.node, l.position, e, t));
                if (i.node === l.node)
                  s = i.node.nodeValue.substring(i.position + c, l.position - c);
                else {
                  var u = i.position + c;
                  s = i.node.nodeValue.substring(u, i.node.nodeValue.length);
                  var d = i.node;
                  do {
                    (d = d.nextSibling) === l.node
                      ? (s += l.node.nodeValue.substring(0, l.position - c))
                      : (s += d.nodeValue ? d.nodeValue : "");
                  } while (d !== l.node);
                }
                return {
                  latex: s,
                  startNode: i.node,
                  startPosition: i.position,
                  endNode: l.node,
                  endPosition: l.position,
                };
              },
            },
            {
              key: "cache",
              get: function () {
                return e._cache;
              },
              set: function (t) {
                e._cache = t;
              },
            },
          ]),
          e
        );
      })();
      p._cache = new u();
      var _ = n(0);
      function v(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var b = (function () {
        function e() {
          throw (
            ((function (e, t) {
              if (!(e instanceof t))
                throw new TypeError("Cannot call a class as a function");
            })(this, e),
            new Error("Static class StringManager can not be instantiated."))
          );
        }
        return (
          (function (e, t, n) {
            t && v(e.prototype, t), n && v(e, n);
          })(e, null, [
            {
              key: "get",
              value: function (e) {
                var t = this.language;
                return (
                  t in this.strings ||
                    (console.warn(
                      "Unknown language ".concat(t, " set in StringManager.")
                    ),
                    (t = "en")),
                  e in this.strings[t]
                    ? this.strings[t][e]
                    : (console.warn(
                        "Unknown key ".concat(e, " in StringManager.")
                      ),
                      e)
                );
              },
            },
          ]),
          e
        );
      })();
      function y(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      (b.strings = _), (b.language = "en");
      var w = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e);
        }
        return (
          (function (e, t, n) {
            t && y(e.prototype, t), n && y(e, n);
          })(e, null, [
            {
              key: "fireEvent",
              value: function (e, t) {
                if (document.createEvent) {
                  var n = document.createEvent("HTMLEvents");
                  return n.initEvent(t, !0, !0), !e.dispatchEvent(n);
                }
                var i = document.createEventObject();
                return e.fireEvent("on".concat(t), i);
              },
            },
            {
              key: "addEvent",
              value: function (e, t, n) {
                e.addEventListener
                  ? e.addEventListener(t, n, !0)
                  : e.attachEvent && e.attachEvent("on".concat(t), n);
              },
            },
            {
              key: "removeEvent",
              value: function (e, t, n) {
                e.removeEventListener
                  ? e.removeEventListener(t, n, !0)
                  : e.detachEvent && e.detachEvent("on".concat(t), n);
              },
            },
            {
              key: "addElementEvents",
              value: function (t, n, i, o) {
                n &&
                  e.addEvent(t, "dblclick", function (e) {
                    var t = e || window.event,
                      i = t.srcElement ? t.srcElement : t.target;
                    n(i, t);
                  }),
                  i &&
                    e.addEvent(t, "mousedown", function (e) {
                      var t = e || window.event,
                        n = t.srcElement ? t.srcElement : t.target;
                      i(n, t);
                    }),
                  o &&
                    e.addEvent(t, "mouseup", function (e) {
                      var t = e || window.event,
                        n = t.srcElement ? t.srcElement : t.target;
                      o(n, t);
                    });
              },
            },
            {
              key: "addClass",
              value: function (t, n) {
                e.containsClass(t, n) || (t.className += " ".concat(n));
              },
            },
            {
              key: "containsClass",
              value: function (e, t) {
                if (null == e || !("className" in e)) return !1;
                for (
                  var n = e.className.split(" "), i = n.length - 1;
                  i >= 0;
                  i -= 1
                )
                  if (n[i] === t) return !0;
                return !1;
              },
            },
            {
              key: "removeClass",
              value: function (e, t) {
                for (
                  var n = "", i = e.className.split(" "), o = 0;
                  o < i.length;
                  o += 1
                )
                  i[o] !== t && (n += "".concat(i[o], " "));
                e.className = n.trim();
              },
            },
            {
              key: "convertOldXmlinitialtextAttribute",
              value: function (e) {
                var t = "value=",
                  n = e.indexOf("xmlinitialtext"),
                  i = e.indexOf(t, n),
                  o = e.charAt(i + t.length),
                  r = i + t.length + 1,
                  a = e.indexOf(o, r),
                  s = e.substring(r, a),
                  l = s.split("«").join("§lt;");
                return (
                  (l = (l = (l = l.split("»").join("§gt;")).split("&").join("§"))
                    .split("¨")
                    .join("§quot;")),
                  (e = e.split(s).join(l))
                );
              },
            },
            {
              key: "createElement",
              value: function (t, n, i) {
                var o;
                void 0 === n && (n = {}), void 0 === i && (i = document);
                try {
                  var r = "<".concat(t);
                  Object.keys(n).forEach(function (t) {
                    r += " ".concat(t, '="').concat(e.htmlEntities(n[t]), '"');
                  }),
                    (r += ">"),
                    (o = i.createElement(r));
                } catch (e) {
                  (o = i.createElement(t)),
                    Object.keys(n).forEach(function (e) {
                      o.setAttribute(e, n[e]);
                    });
                }
                return o;
              },
            },
            {
              key: "createObject",
              value: function (t, n) {
                void 0 === n && (n = document),
                  (t = (t = (t = (t = t
                    .split("<applet ")
                    .join('<span wirisObject="WirisApplet" ')
                    .split("<APPLET ")
                    .join('<span wirisObject="WirisApplet" '))
                    .split("</applet>")
                    .join("</span>")
                    .split("</APPLET>")
                    .join("</span>"))
                    .split("<param ")
                    .join('<br wirisObject="WirisParam" ')
                    .split("<PARAM ")
                    .join('<br wirisObject="WirisParam" '))
                    .split("</param>")
                    .join("</br>")
                    .split("</PARAM>")
                    .join("</br>"));
                var i = e.createElement("div", {}, n);
                return (
                  (i.innerHTML = t),
                  (function t(i) {
                    if (
                      i.getAttribute &&
                      "WirisParam" === i.getAttribute("wirisObject")
                    ) {
                      for (var o = {}, r = 0; r < i.attributes.length; r += 1)
                        null !== i.attributes[r].nodeValue &&
                          (o[i.attributes[r].nodeName] =
                            i.attributes[r].nodeValue);
                      var a = e.createElement("param", o, n);
                      a.NAME && ((a.name = a.NAME), (a.value = a.VALUE)),
                        a.removeAttribute("wirisObject"),
                        i.parentNode.replaceChild(a, i);
                    } else if (
                      i.getAttribute &&
                      "WirisApplet" === i.getAttribute("wirisObject")
                    ) {
                      for (var s = {}, l = 0; l < i.attributes.length; l += 1)
                        null !== i.attributes[l].nodeValue &&
                          (s[i.attributes[l].nodeName] =
                            i.attributes[l].nodeValue);
                      var c = e.createElement("applet", s, n);
                      c.removeAttribute("wirisObject");
                      for (var u = 0; u < i.childNodes.length; u += 1)
                        t(i.childNodes[u]),
                          "param" === i.childNodes[u].nodeName.toLowerCase() &&
                            (c.appendChild(i.childNodes[u]), (u -= 1));
                      i.parentNode.replaceChild(c, i);
                    } else
                      for (var d = 0; d < i.childNodes.length; d += 1)
                        t(i.childNodes[d]);
                  })(i),
                  i.firstChild
                );
              },
            },
            {
              key: "createObjectCode",
              value: function (t) {
                if (void 0 === t || null === t) return null;
                if (1 === t.nodeType) {
                  for (
                    var n = "<".concat(t.tagName), i = 0;
                    i < t.attributes.length;
                    i += 1
                  )
                    t.attributes[i].specified &&
                      (n += " "
                        .concat(t.attributes[i].name, '="')
                        .concat(e.htmlEntities(t.attributes[i].value), '"'));
                  if (t.childNodes.length > 0) {
                    n += ">";
                    for (var o = 0; o < t.childNodes.length; o += 1)
                      n += e.createObject(t.childNodes[o]);
                    n += "</".concat(t.tagName, ">");
                  } else
                    "DIV" === t.nodeName || "SCRIPT" === t.nodeName
                      ? (n += "></".concat(t.tagName, ">"))
                      : (n += "/>");
                  return n;
                }
                return 3 === t.nodeType ? e.htmlEntities(t.nodeValue) : "";
              },
            },
            {
              key: "concatenateUrl",
              value: function (e, t) {
                var n = "";
                return (
                  e.indexOf("/") !== e.length &&
                    0 !== t.indexOf("/") &&
                    (n = "/"),
                  (e + n + t).replace(/([^:]\/)\/+/g, "$1")
                );
              },
            },
            {
              key: "htmlEntities",
              value: function (e) {
                return e
                  .split("&")
                  .join("&amp;")
                  .split("<")
                  .join("&lt;")
                  .split(">")
                  .join("&gt;")
                  .split('"')
                  .join("&quot;");
              },
            },
            {
              key: "htmlEntitiesDecode",
              value: function (e) {
                var t = document.createElement("textarea");
                return (t.innerHTML = e), t.value;
              },
            },
            {
              key: "createHttpRequest",
              value: function () {
                if (
                  "file://" ===
                  window.location
                    .toString()
                    .substr(0, window.location.toString().lastIndexOf("/") + 1)
                    .substr(0, 7)
                )
                  throw b.get("exception_cross_site");
                if ("undefined" != typeof XMLHttpRequest)
                  return new XMLHttpRequest();
                try {
                  return new ActiveXObject("Msxml2.XMLHTTP");
                } catch (e) {
                  try {
                    return new ActiveXObject("Microsoft.XMLHTTP");
                  } catch (e) {
                    return null;
                  }
                }
              },
            },
            {
              key: "httpBuildQuery",
              value: function (t) {
                var n = "";
                return (
                  Object.keys(t).forEach(function (i) {
                    null != t[i] &&
                      (n += ""
                        .concat(e.urlEncode(i), "=")
                        .concat(e.urlEncode(t[i]), "&"));
                  }),
                  "&" === n.substring(n.length - 1) &&
                    (n = n.substring(0, n.length - 1)),
                  n
                );
              },
            },
            {
              key: "propertiesToString",
              value: function (t) {
                var n = [];
                Object.keys(t).forEach(function (e) {
                  Object.prototype.hasOwnProperty.call(t, e) && n.push(e);
                });
                for (var i = n.length, o = 0; o < i; o += 1)
                  for (var r = o + 1; r < i; r += 1) {
                    var a = n[o],
                      s = n[r];
                    e.compareStrings(a, s) > 0 && ((n[o] = s), (n[r] = a));
                  }
                for (var l = "", c = 0; c < i; c += 1) {
                  var u = n[c];
                  (l += u), (l += "=");
                  var d = t[u];
                  (l += d =
                    (d = (d = (d = d.replace("\\", "\\\\")).replace(
                      "\n",
                      "\\n"
                    )).replace("\r", "\\r")).replace("\t", "\\t")),
                    (l += "\n");
                }
                return l;
              },
            },
            {
              key: "compareStrings",
              value: function (t, n) {
                var i,
                  o = t.length,
                  r = n.length,
                  a = o > r ? r : o;
                for (i = 0; i < a; i += 1) {
                  var s = e.fixedCharCodeAt(t, i) - e.fixedCharCodeAt(n, i);
                  if (0 !== s) return s;
                }
                return t.length - n.length;
              },
            },
            {
              key: "fixedCharCodeAt",
              value: function (e, t) {
                t = t || 0;
                var n,
                  i,
                  o = e.charCodeAt(t);
                if (o >= 55296 && o <= 56319) {
                  if (((n = o), (i = e.charCodeAt(t + 1)), Number.isNaN(i)))
                    throw b.get("exception_high_surrogate");
                  return 1024 * (n - 55296) + (i - 56320) + 65536;
                }
                return !(o >= 56320 && o <= 57343) && o;
              },
            },
            {
              key: "urlToAssArray",
              value: function (e) {
                var t;
                if ((t = e.indexOf("?")) > 0) {
                  var n = e.substring(t + 1).split("&"),
                    i = {};
                  for (t = 0; t < n.length; t += 1) {
                    var o = n[t].split("=");
                    o.length > 1 &&
                      (i[o[0]] = decodeURIComponent(o[1].replace(/\+/g, " ")));
                  }
                  return i;
                }
                return {};
              },
            },
            {
              key: "urlEncode",
              value: function (e) {
                return encodeURIComponent(e);
              },
            },
            {
              key: "getWIRISImageOutput",
              value: function (t, n, i) {
                var o = e.createObject(t);
                if (
                  o &&
                  (o.className === l.get("imageClassName") ||
                    o.getAttribute(l.get("imageMathmlAttribute")))
                ) {
                  if (!n) return t;
                  var r = o.getAttribute(l.get("imageMathmlAttribute")),
                    s = a.safeXmlDecode(r);
                  return (
                    l.get("saveHandTraces") ||
                      (s = a.removeAnnotation(s, "application/json")),
                    null == s && (s = o.getAttribute("alt")),
                    i ? a.safeXmlEncode(s) : s
                  );
                }
                return t;
              },
            },
            {
              key: "getNodeLength",
              value: function (t) {
                if (3 === t.nodeType) return t.nodeValue.length;
                if (1 === t.nodeType) {
                  var n = { IMG: 1, BR: 1 }[t.nodeName.toUpperCase()];
                  void 0 === n && (n = 0);
                  for (var i = 0; i < t.childNodes.length; i += 1)
                    n += e.getNodeLength(t.childNodes[i]);
                  return n;
                }
                return 0;
              },
            },
            {
              key: "getSelectedItem",
              value: function (t, n, i) {
                var o;
                if (
                  (n ? (o = t.contentWindow).focus() : ((o = window), t.focus()),
                  document.selection && !i)
                ) {
                  var r = o.document.selection.createRange();
                  if (r.parentElement) {
                    if (r.htmlText.length > 0)
                      return 0 === r.text.length
                        ? e.getSelectedItem(t, n, !0)
                        : null;
                    o.document.execCommand("InsertImage", !1, "#");
                    var a,
                      s,
                      l = r.parentElement();
                    return (
                      "IMG" !== l.nodeName.toUpperCase() &&
                        (r.pasteHTML(
                          '<span id="wrs_openEditorWindow_temporalObject"></span>'
                        ),
                        (l = o.document.getElementById(
                          "wrs_openEditorWindow_temporalObject"
                        ))),
                      l.nextSibling && 3 === l.nextSibling.nodeType
                        ? ((a = l.nextSibling), (s = 0))
                        : l.previousSibling && 3 === l.previousSibling.nodeType
                        ? (s = (a = l.previousSibling).nodeValue.length)
                        : ((a = o.document.createTextNode("")),
                          l.parentNode.insertBefore(a, l),
                          (s = 0)),
                      l.parentNode.removeChild(l),
                      { node: a, caretPosition: s }
                    );
                  }
                  return r.length > 1 ? null : { node: r.item(0) };
                }
                if (o.getSelection) {
                  var c,
                    u = o.getSelection();
                  try {
                    c = u.getRangeAt(0);
                  } catch (e) {
                    c = o.document.createRange();
                  }
                  var d = c.startContainer;
                  if (3 === d.nodeType)
                    return { node: d, caretPosition: c.startOffset };
                  if (d !== c.endContainer) return null;
                  if (1 === d.nodeType) {
                    var h = c.startOffset;
                    if (d.childNodes[h]) return { node: d.childNodes[h] };
                  }
                }
                return null;
              },
            },
            {
              key: "getSelectedItemOnTextarea",
              value: function (e) {
                var t = document.createTextNode(e.value),
                  n = p.getLatexFromTextNode(t, e.selectionStart);
                return null === n
                  ? null
                  : {
                      node: t,
                      caretPosition: e.selectionStart,
                      startPosition: n.startPosition,
                      endPosition: n.endPosition,
                    };
              },
            },
            {
              key: "getElementsByNameFromString",
              value: function (e, t, n) {
                var i = [];
                (e = e.toLowerCase()), (t = t.toLowerCase());
                for (var o = e.indexOf("<".concat(t, " ")); -1 !== o; ) {
                  var r = void 0;
                  r = n ? ">" : "</".concat(t, ">");
                  var a = e.indexOf(r, o);
                  -1 !== a
                    ? ((a += r.length), i.push({ start: o, end: a }))
                    : (a = o + 1),
                    (o = e.indexOf("<".concat(t, " "), a));
                }
                return i;
              },
            },
            {
              key: "decode64",
              value: function (e) {
                var t = "+".charCodeAt(0),
                  n = "/".charCodeAt(0),
                  i = "0".charCodeAt(0),
                  o = "a".charCodeAt(0),
                  r = "A".charCodeAt(0),
                  a = "-".charCodeAt(0),
                  s = "_".charCodeAt(0),
                  l = e.charCodeAt(0);
                return l === t || l === a
                  ? 62
                  : l === n || l === s
                  ? 63
                  : l < i
                  ? -1
                  : l < i + 10
                  ? l - i + 26 + 26
                  : l < r + 26
                  ? l - r
                  : l < o + 26
                  ? l - o + 26
                  : null;
              },
            },
            {
              key: "b64ToByteArray",
              value: function (t, n) {
                var i;
                if (t.length % 4 > 0)
                  throw new Error(
                    "Invalid string. Length must be a multiple of 4"
                  );
                var o,
                  r,
                  a,
                  s = [];
                for (
                  o =
                    n ||
                    ((r =
                      "=" === t.charAt(t.length - 2)
                        ? 2
                        : "=" === t.charAt(t.length - 1)
                        ? 1
                        : 0) > 0
                      ? t.length - 4
                      : t.length),
                    a = 0;
                  a < o;
                  a += 4
                )
                  (i =
                    (e.decode64(t.charAt(a)) << 18) |
                    (e.decode64(t.charAt(a + 1)) << 12) |
                    (e.decode64(t.charAt(a + 2)) << 6) |
                    e.decode64(t.charAt(a + 3))),
                    s.push((i >> 16) & 255),
                    s.push((i >> 8) & 255),
                    s.push(255 & i);
                return (
                  r &&
                    (2 === r
                      ? ((i =
                          (e.decode64(t.charAt(a)) << 2) |
                          (e.decode64(t.charAt(a + 1)) >> 4)),
                        s.push(255 & i))
                      : 1 === r &&
                        ((i =
                          (e.decode64(t.charAt(a)) << 10) |
                          (e.decode64(t.charAt(a + 1)) << 4) |
                          (e.decode64(t.charAt(a + 2)) >> 2)),
                        s.push((i >> 8) & 255),
                        s.push(255 & i))),
                  s
                );
              },
            },
            {
              key: "readInt32",
              value: function (e) {
                if (e.length < 4) return !1;
                var t = e.splice(0, 4);
                return (t[0] << 24) | (t[1] << 16) | (t[2] << 8) | (t[3] << 0);
              },
            },
            {
              key: "readByte",
              value: function (e) {
                return e.shift() << 0;
              },
            },
            {
              key: "readBytes",
              value: function (e, t, n) {
                return e.splice(t, n);
              },
            },
            {
              key: "updateTextArea",
              value: function (e, t) {
                if (e && t)
                  if ((e.focus(), null != e.selectionStart)) {
                    var n = e.selectionEnd,
                      i = e.value.substring(0, e.selectionStart),
                      o = e.value.substring(n, e.value.length);
                    (e.value = i + t + o), (e.selectionEnd = n + t.length);
                  } else {
                    document.selection.createRange().text = t;
                  }
              },
            },
            {
              key: "updateExistingTextOnTextarea",
              value: function (e, t, n, i) {
                e.focus();
                var o = e.value.substring(0, n);
                (e.value = o + t + e.value.substring(i, e.value.length)),
                  (e.selectionEnd = n + t.length);
              },
            },
            {
              key: "addArgument",
              value: function (e, t, n) {
                var i;
                return (
                  (i = e.indexOf("?") > 0 ? "&" : "?"),
                  "".concat(e + i + t, "=").concat(n)
                );
              },
            },
          ]),
          e
        );
      })();
      function x(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var A = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e);
        }
        return (
          (function (e, t, n) {
            t && x(e.prototype, t), n && x(e, n);
          })(e, null, [
            {
              key: "removeImgDataAttributes",
              value: function (e) {
                var t = [],
                  n = e.attributes;
                Object.keys(n).forEach(function (e) {
                  var i = n[e];
                  0 === i.name.indexOf("data-") && t.push(i.name);
                }),
                  t.forEach(function (t) {
                    e.removeAttribute(t);
                  });
              },
            },
            {
              key: "clone",
              value: function (e, t) {
                var n = l.get("imageCustomEditorName");
                e.hasAttribute(n) || t.removeAttribute(n),
                  [
                    l.get("imageMathmlAttribute"),
                    n,
                    "alt",
                    "height",
                    "width",
                    "style",
                    "src",
                    "role",
                  ].forEach(function (n) {
                    var i = e.getAttribute(n);
                    i && t.setAttribute(n, i);
                  });
              },
            },
            {
              key: "setImgSize",
              value: function (t, n, i) {
                var o, r, a, s;
                if (i)
                  if ("svg" === l.get("imageFormat"))
                    if ("base64" !== l.get("saveMode"))
                      o = e.getMetricsFromSvgString(n);
                    else {
                      (r = t.src.substr(
                        t.src.indexOf("base64,") + 7,
                        t.src.length
                      )),
                        (s = ""),
                        (a = w.b64ToByteArray(r, r.length));
                      for (var c = 0; c < a.length; c += 1)
                        s += String.fromCharCode(a[c]);
                      o = e.getMetricsFromSvgString(s);
                    }
                  else
                    (r = t.src.substr(
                      t.src.indexOf("base64,") + 7,
                      t.src.length
                    )),
                      (a = w.b64ToByteArray(r, 88)),
                      (o = e.getMetricsFromBytes(a));
                else o = w.urlToAssArray(n);
                var u = o.cw;
                if (u) {
                  var d = o.ch,
                    h = o.cb,
                    m = o.dpi;
                  m &&
                    ((u = (96 * u) / m), (d = (96 * d) / m), (h = (96 * h) / m)),
                    (t.width = u),
                    (t.height = d),
                    (t.style.verticalAlign = "-".concat(d - h, "px"));
                }
              },
            },
            {
              key: "fixAfterResize",
              value: function (t) {
                if (
                  (t.removeAttribute("style"),
                  t.removeAttribute("width"),
                  t.removeAttribute("height"),
                  (t.style.maxWidth = "none"),
                  -1 !== t.src.indexOf("data:image"))
                )
                  if ("svg" === l.get("imageFormat")) {
                    var n = decodeURIComponent(t.src.substring(32, t.src.length));
                    e.setImgSize(t, n, !0);
                  } else {
                    var i = t.src.substring(22, t.src.length);
                    e.setImgSize(t, i, !0);
                  }
                else e.setImgSize(t, t.src);
              },
            },
            {
              key: "getMetricsFromSvgString",
              value: function (e) {
                var t = e.indexOf('height="'),
                  n = e.indexOf('"', t + 8, e.length),
                  i = e.substring(t + 8, n);
                (t = e.indexOf('width="')), (n = e.indexOf('"', t + 7, e.length));
                var o = e.substring(t + 7, n);
                (t = e.indexOf('wrs:baseline="')),
                  (n = e.indexOf('"', t + 14, e.length));
                var r = e.substring(t + 14, n);
                if (void 0 !== o) {
                  var a = [];
                  return (a.cw = o), (a.ch = i), void 0 !== r && (a.cb = r), a;
                }
                return [];
              },
            },
            {
              key: "getMetricsFromBytes",
              value: function (e) {
                var t, n, i, o, r;
                for (w.readBytes(e, 0, 8); e.length >= 4; )
                  1229472850 === (i = w.readInt32(e))
                    ? ((t = w.readInt32(e)),
                      (n = w.readInt32(e)),
                      w.readInt32(e),
                      w.readByte(e))
                    : 1650545477 === i
                    ? (o = w.readInt32(e))
                    : 1883789683 === i &&
                      ((r = w.readInt32(e)),
                      (r = Math.round(r / 39.37)),
                      w.readInt32(e),
                      w.readByte(e)),
                    w.readInt32(e);
                if (void 0 !== t) {
                  var a = [];
                  return (a.cw = t), (a.ch = n), (a.dpi = r), o && (a.cb = o), a;
                }
                return [];
              },
            },
          ]),
          e
        );
      })();
      function C(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var M = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e);
        }
        return (
          (function (e, t, n) {
            t && C(e.prototype, t), n && C(e, n);
          })(e, null, [
            {
              key: "mathMLToAccessible",
              value: function (t, n, i) {
                void 0 === n && (n = "en"),
                  a.containClass(t, "wrs_chemistry") && (i.mode = "chemistry");
                var o = "";
                if (e.cache.get(t)) o = e.cache.get(t);
                else {
                  (i.service = "mathml2accessible"), (i.lang = n);
                  var r = JSON.parse(f.getService("service", i));
                  "error" !== r.status
                    ? ((o = r.result.text), e.cache.populate(t, o))
                    : (o = b.get("error_convert_accessibility"));
                }
                return o;
              },
            },
            {
              key: "cache",
              get: function () {
                return e._cache;
              },
              set: function (t) {
                e._cache = t;
              },
            },
          ]),
          e
        );
      })();
      M._cache = new u();
      n(1);
      function k(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var E = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e);
        }
        return (
          (function (e, t, n) {
            t && k(e.prototype, t), n && k(e, n);
          })(e, null, [
            {
              key: "mathmlToImgObject",
              value: function (t, n, i, o) {
                var r = t.createElement("img");
                (r.align = "middle"), (r.style.maxWidth = "none");
                var s = i || {};
                if (
                  ((s.mml = n),
                  (s.lang = o),
                  (s.metrics = "true"),
                  (s.centerbaseline = "false"),
                  "base64" === l.get("saveMode") &&
                    "default" === l.get("base64savemode") &&
                    (s.base64 = !0),
                  (r.className = l.get("imageClassName")),
                  -1 !== n.indexOf('class="'))
                ) {
                  var c = n.substring(
                    n.indexOf('class="') + 'class="'.length,
                    n.length
                  );
                  (c = (c = c.substring(0, c.indexOf('"'))).substring(
                    4,
                    c.length
                  )),
                    r.setAttribute(l.get("imageCustomEditorName"), c);
                }
                if (
                  !l.get("wirisPluginPerformance") ||
                  ("xml" !== l.get("saveMode") && "safeXml" !== l.get("saveMode"))
                ) {
                  var u = e.createImageSrc(n, s);
                  r.setAttribute(
                    l.get("imageMathmlAttribute"),
                    a.safeXmlEncode(n)
                  ),
                    (r.src = u),
                    A.setImgSize(
                      r,
                      u,
                      "base64" === l.get("saveMode") &&
                        "default" === l.get("base64savemode")
                    ),
                    l.get("enableAccessibility") &&
                      (r.alt = M.mathMLToAccessible(n, o, s));
                } else {
                  var d = JSON.parse(e.createShowImageSrc(s, o));
                  if ("warning" === d.status)
                    try {
                      d = JSON.parse(f.getService("showimage", s));
                    } catch (e) {
                      return null;
                    }
                  "png" === (d = d.result).format
                    ? (r.src = "data:image/png;base64,".concat(d.content))
                    : (r.src = "data:image/svg+xml;charset=utf8,".concat(
                        w.urlEncode(d.content)
                      )),
                    r.setAttribute(
                      l.get("imageMathmlAttribute"),
                      a.safeXmlEncode(n)
                    ),
                    A.setImgSize(r, d.content, !0),
                    l.get("enableAccessibility") &&
                      (void 0 === d.alt
                        ? (r.alt = M.mathMLToAccessible(n, o, s))
                        : (r.alt = d.alt));
                }
                return (
                  void 0 !== e.observer && e.observer.observe(r),
                  r.setAttribute("role", "math"),
                  r
                );
              },
            },
            {
              key: "createImageSrc",
              value: function (e, t) {
                "base64" === l.get("saveMode") &&
                  "default" === l.get("base64savemode") &&
                  (t.base64 = !0);
                var n = f.getService("createimage", t);
                if (-1 !== n.indexOf("@BASE@")) {
                  var i = f.getServicePath("createimage").split("/");
                  i.pop(), (n = n.split("@BASE@").join(i.join("/")));
                }
                return n;
              },
            },
            {
              key: "initParse",
              value: function (t, n) {
                return (t = e.initParseSaveMode(t, n)), e.initParseEditMode(t);
              },
            },
            {
              key: "initParseSaveMode",
              value: function (t, n) {
                return (
                  l.get("saveMode") &&
                    ((t = p.parseMathmlToLatex(t, o.safeXmlCharacters)),
                    (t = p.parseMathmlToLatex(t, o.xmlCharacters)),
                    (t = e.parseMathmlToImg(t, o.safeXmlCharacters, n)),
                    (t = e.parseMathmlToImg(t, o.xmlCharacters, n)),
                    "base64" === l.get("saveMode") &&
                      "image" === l.get("base64savemode") &&
                      (t = e.codeImgTransform(t, "base642showimage"))),
                  t
                );
              },
            },
            {
              key: "initParseEditMode",
              value: function (e) {
                if (-1 !== l.get("parseModes").indexOf("latex"))
                  for (
                    var t = w.getElementsByNameFromString(e, "img", !0),
                      n = 'encoding="LaTeX">',
                      i = 0,
                      o = 0;
                    o < t.length;
                    o += 1
                  ) {
                    var r = e.substring(t[o].start + i, t[o].end + i);
                    if (
                      -1 !==
                      r.indexOf(' class="'.concat(l.get("imageClassName"), '"'))
                    ) {
                      var s = " ".concat(l.get("imageMathmlAttribute"), '="'),
                        c = r.indexOf(s);
                      if (
                        (-1 === c && ((s = ' alt="'), (c = r.indexOf(s))),
                        -1 !== c)
                      ) {
                        c += s.length;
                        var u = r.indexOf('"', c),
                          d = a.safeXmlDecode(r.substring(c, u)),
                          h = d.indexOf(n);
                        if (-1 !== h) {
                          h += n.length;
                          var m = d.indexOf("</annotation>", h),
                            f = d.substring(h, m),
                            g = "$$".concat(w.htmlEntitiesDecode(f), "$$");
                          (e =
                            e.substring(0, t[o].start + i) +
                            g +
                            e.substring(t[o].end + i)),
                            (i += g.length - (t[o].end - t[o].start));
                        }
                      }
                    }
                  }
                return e;
              },
            },
            {
              key: "endParse",
              value: function (t) {
                var n = e.endParseEditMode(t);
                return e.endParseSaveMode(n);
              },
            },
            {
              key: "endParseEditMode",
              value: function (e) {
                if (-1 !== l.get("parseModes").indexOf("latex")) {
                  for (var t = "", n = 0, i = e.indexOf("$$"); -1 !== i; ) {
                    if (
                      ((t += e.substring(n, i)),
                      -1 !== (n = e.indexOf("$$", i + 2)))
                    ) {
                      var o = e.substring(i + 2, n),
                        r = w.htmlEntitiesDecode(o),
                        s = p.getMathMLFromLatex(r, !0);
                      l.get("saveHandTraces") ||
                        (s = a.removeAnnotation(s, "application/json")),
                        (t += s),
                        (n += 2);
                    } else (t += "$$"), (n = i + 2);
                    i = e.indexOf("$$", n);
                  }
                  e = t += e.substring(n, e.length);
                }
                return e;
              },
            },
            {
              key: "endParseSaveMode",
              value: function (t) {
                return (
                  l.get("saveMode") &&
                    ("safeXml" === l.get("saveMode")
                      ? (t = e.codeImgTransform(t, "img2mathml"))
                      : "xml" === l.get("saveMode")
                      ? (t = e.codeImgTransform(t, "img2mathml"))
                      : "base64" === l.get("saveMode") &&
                        "image" === l.get("base64savemode") &&
                        (t = e.codeImgTransform(t, "img264"))),
                  t
                );
              },
            },
            {
              key: "createShowImageSrc",
              value: function (e, t) {
                var n = [],
                  i = [
                    "mml",
                    "color",
                    "centerbaseline",
                    "zoom",
                    "dpi",
                    "fontSize",
                    "fontFamily",
                    "defaultStretchy",
                    "backgroundColor",
                    "format",
                  ];
                i.forEach(function (t) {
                  var o = i[t];
                  void 0 !== e[o] && (n[o] = e[o]);
                });
                var o = {};
                return (
                  Object.keys(e).forEach(function (t) {
                    "mml" !== t && (o[t] = e[t]);
                  }),
                  (o.formula = com.wiris.js.JsPluginTools.md5encode(
                    w.propertiesToString(n)
                  )),
                  (o.lang = void 0 === t ? "en" : t),
                  (o.version = l.get("version")),
                  f.getService("showimage", w.httpBuildQuery(o), !0)
                );
              },
            },
            {
              key: "codeImgTransform",
              value: function (t, n) {
                for (
                  var i = "", o = 0, r = /<img/gi, s = r.source.length;
                  r.test(t);
  
                ) {
                  var c = r.lastIndex - s;
                  i += t.substring(o, c);
                  for (var u = c + 1; u < t.length && o <= c; ) {
                    var d = t.charAt(u);
                    if ('"' === d || "'" === d) {
                      var h = t.indexOf(d, u + 1);
                      u = -1 === h ? t.length : h;
                    } else ">" === d && (o = u + 1);
                    u += 1;
                  }
                  if (o < c) return (i += t.substring(c, t.length));
                  var m = t.substring(c, o),
                    f = w.createObject(m),
                    g = f.getAttribute(l.get("imageMathmlAttribute")),
                    p = void 0,
                    _ = void 0;
                  if ("base642showimage" === n)
                    null == g && (g = f.getAttribute("alt")),
                      (g = a.safeXmlDecode(g)),
                      (m = e.mathmlToImgObject(document, g, null, null)),
                      (i += w.createObjectCode(m));
                  else if ("img2mathml" === n)
                    l.get("saveMode") &&
                      ("safeXml" === l.get("saveMode")
                        ? ((p = !0), (_ = !0))
                        : "xml" === l.get("saveMode") && ((p = !0), (_ = !1))),
                      (i += w.getWIRISImageOutput(m, p, _));
                  else if ("img264" === n) {
                    null === g && (g = f.getAttribute("alt")),
                      (g = a.safeXmlDecode(g));
                    var v = { base64: "true" };
                    (m = e.mathmlToImgObject(document, g, v, null)),
                      A.setImgSize(m, m.src, !0),
                      (i += w.createObjectCode(m));
                  }
                }
                return (i += t.substring(o, t.length));
              },
            },
            {
              key: "parseMathmlToImg",
              value: function (t, n, i) {
                for (
                  var r = "",
                    s = "".concat(n.tagOpener, "math"),
                    c = "".concat(n.tagOpener, "/math").concat(n.tagCloser),
                    u = t.indexOf(s),
                    d = 0;
                  -1 !== u;
  
                ) {
                  r += t.substring(d, u);
                  var h = t.indexOf(l.get("imageMathmlAttribute"));
                  if (
                    (-1 === (d = t.indexOf(c, u))
                      ? (d = t.length - 1)
                      : (d += -1 !== h ? t.indexOf("/>", u) : c.length),
                    a.isMathmlInAttribute(t, u) || -1 !== h)
                  )
                    r += t.substring(u, d);
                  else {
                    var m = t.substring(u, d);
                    (m =
                      n.id === o.safeXmlCharacters.id
                        ? a.safeXmlDecode(m)
                        : a.mathMLEntities(m)),
                      (r += w.createObjectCode(
                        e.mathmlToImgObject(document, m, null, i)
                      ));
                  }
                  u = t.indexOf(s, d);
                }
                return (r += t.substring(d, t.length));
              },
            },
          ]),
          e
        );
      })();
      if ("undefined" != typeof MutationObserver) {
        var T = new MutationObserver(function (e) {
          e.forEach(function (e) {
            e.oldValue === l.get("imageClassName") &&
              "class" === e.attributeName &&
              -1 === e.target.className.indexOf(l.get("imageClassName")) &&
              (e.target.className = l.get("imageClassName"));
          });
        });
        (E.observer = Object.create(T)),
          (E.observer.Config = { attributes: !0, attributeOldValue: !0 }),
          (E.observer.observe = function (e) {
            Object.getPrototypeOf(this).observe(e, this.Config);
          });
      }
      function I(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var O = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e),
            (this.isContentChanged = !1),
            (this.waitingForChanges = !1);
        }
        return (
          (function (e, t, n) {
            t && I(e.prototype, t), n && I(e, n);
          })(e, [
            {
              key: "setIsContentChanged",
              value: function (e) {
                this.isContentChanged = e;
              },
            },
            {
              key: "getIsContentChanged",
              value: function () {
                return this.isContentChanged;
              },
            },
            {
              key: "setWaitingForChanges",
              value: function (e) {
                this.waitingForChanges = e;
              },
            },
            { key: "caretPositionChanged", value: function (e) {} },
            { key: "clipboardChanged", value: function (e) {} },
            {
              key: "contentChanged",
              value: function (e) {
                !0 === this.waitingForChanges &&
                  !1 === this.isContentChanged &&
                  (this.isContentChanged = !0);
              },
            },
            { key: "styleChanged", value: function (e) {} },
            { key: "transformationReceived", value: function (e) {} },
          ]),
          e
        );
      })();
      function P(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var j = (function () {
        function e(t) {
          if (
            ((function (e, t) {
              if (!(e instanceof t))
                throw new TypeError("Cannot call a class as a function");
            })(this, e),
            (this.editorAttributes = {}),
            !("editorAttributes" in t))
          )
            throw new Error(
              "ContentManager constructor error: editorAttributes property missed."
            );
          if (
            ((this.editorAttributes = t.editorAttributes),
            (this.customEditors = null),
            "customEditors" in t && (this.customEditors = t.customEditors),
            (this.environment = {}),
            !("environment" in t))
          )
            throw new Error(
              "ContentManager constructor error: environment property missed"
            );
          if (
            ((this.environment = t.environment),
            (this.language = ""),
            !("language" in t))
          )
            throw new Error(
              "ContentManager constructor error: language property missed"
            );
          (this.language = t.language),
            (this.editorListener = new O()),
            (this.editor = null),
            (this.ua = navigator.userAgent.toLowerCase()),
            (this.deviceProperties = {}),
            (this.deviceProperties.isAndroid = this.ua.indexOf("android") > -1),
            (this.deviceProperties.isIOS =
              this.ua.indexOf("ipad") > -1 || this.ua.indexOf("iphone") > -1),
            (this.toolbar = null),
            (this.modalDialogInstance = null),
            (this.listeners = new h()),
            (this.mathML = null),
            (this.isNewElement = !0),
            (this.integrationModel = null),
            (this.isEditorLoaded = !1);
        }
        return (
          (function (e, t, n) {
            t && P(e.prototype, t), n && P(e, n);
          })(e, [
            {
              key: "addListener",
              value: function (e) {
                this.listeners.add(e);
              },
            },
            {
              key: "setIntegrationModel",
              value: function (e) {
                this.integrationModel = e;
              },
            },
            {
              key: "setModalDialogInstance",
              value: function (e) {
                this.modalDialogInstance = e;
              },
            },
            {
              key: "insert",
              value: function () {
                this.updateTitle(this.modalDialogInstance),
                  this.insertEditor(this.modalDialogInstance);
              },
            },
            {
              key: "insertEditor",
              value: function () {
                if (
                  window.com &&
                  window.com.wiris &&
                  window.com.wiris.jsEditor &&
                  window.com.wiris.jsEditor.JsEditor &&
                  window.com.wiris.jsEditor.JsEditor.newInstance
                ) {
                  if (
                    ((this.editor =
                      window.com.wiris.jsEditor.JsEditor.newInstance(
                        this.editorAttributes
                      )),
                    this.editor.insertInto(
                      this.modalDialogInstance.contentContainer
                    ),
                    this.editor.focus(),
                    this.modalDialogInstance.rtl && this.editor.action("rtl"),
                    this.editor.getEditorModel().isRTL() &&
                      (this.editor.element.style.direction = "rtl"),
                    this.editor
                      .getEditorModel()
                      .addEditorListener(this.editorListener),
                    this.modalDialogInstance.deviceProperties.isIOS)
                  ) {
                    setTimeout(function () {
                      this.modalDialogInstance.hideKeyboard();
                    }, 400);
                    var t =
                      document.getElementsByClassName("wrs_formulaDisplay")[0];
                    w.addEvent(
                      t,
                      "focus",
                      this.modalDialogInstance.handleOpenedIosSoftkeyboard
                    ),
                      w.addEvent(
                        t,
                        "blur",
                        this.modalDialogInstance.handleClosedIosSoftkeyboard
                      );
                  }
                  this.listeners.fire("onLoad", {}), (this.isEditorLoaded = !0);
                } else setTimeout(e.prototype.insertEditor.bind(this), 100);
              },
            },
            {
              key: "init",
              value: function () {
                var e = document.createElement("script");
                e.type = "text/javascript";
                var t = l.get("editorUrl"),
                  n = document.createElement("a");
                (n.href = t),
                  0 === window.location.href.indexOf("https://") &&
                    "http:" === n.protocol &&
                    (n.protocol = "https:"),
                  (t =
                    "80" === n.port || "443" === n.port
                      ? ""
                          .concat(n.protocol, "//")
                          .concat(n.hostname, "/")
                          .concat(n.pathname)
                      : ""
                          .concat(n.protocol, "//")
                          .concat(n.hostname, ":")
                          .concat(n.port, "/")
                          .concat(n.pathname));
                var i = {};
                "editor" in this.environment
                  ? (i.editor = this.environment.editor)
                  : (i.editor = "unknown"),
                  "mode" in this.environment
                    ? (i.mode = this.environment.mode)
                    : (i.mode = l.get("saveMode")),
                  "version" in this.environment
                    ? (i.version = this.environment.version)
                    : (i.version = l.get("version")),
                  (e.src = ""
                    .concat(t, "?lang=")
                    .concat(this.language, "&stats-editor=")
                    .concat(i.editor, "&stats-mode=")
                    .concat(i.mode, "&stats-version=")
                    .concat(i.version)),
                  document.getElementsByTagName("head")[0].appendChild(e);
              },
            },
            {
              key: "setInitialContent",
              value: function () {
                this.isNewElement || this.setMathML(this.mathML);
              },
            },
            {
              key: "setMathML",
              value: function (e, t) {
                var n = this;
                void 0 === t && (t = !1),
                  this.editor.setMathMLWithCallback(e, function () {
                    n.editorListener.setWaitingForChanges(!0);
                  }),
                  setTimeout(function () {
                    n.editorListener.setIsContentChanged(!1);
                  }, 500),
                  t || this.onFocus();
              },
            },
            {
              key: "onFocus",
              value: function () {
                void 0 !== this.editor &&
                  null != this.editor &&
                  this.editor.focus();
              },
            },
            {
              key: "submitAction",
              value: function () {
                if (this.editor.isFormulaEmpty())
                  this.integrationModel.updateFormula(null);
                else {
                  var e = this.editor.getMathMLWithSemantics();
                  if (null !== this.customEditors.getActiveEditor()) {
                    var t = this.customEditors.getActiveEditor().toolbar;
                    e = a.addCustomEditorClassAttribute(e, t);
                  } else
                    Object.keys(this.customEditors.editors).forEach(function (t) {
                      e = a.removeCustomEditorClassAttribute(e, t);
                    });
                  var n = a.mathMLEntities(e);
                  this.integrationModel.updateFormula(n);
                }
                this.customEditors.disable(),
                  this.integrationModel.notifyWindowClosed(),
                  this.setEmptyMathML(),
                  this.customEditors.disable();
              },
            },
            {
              key: "setEmptyMathML",
              value: function () {
                this.deviceProperties.isAndroid || this.deviceProperties.isIOS
                  ? this.editor.getEditorModel().isRTL()
                    ? this.setMathML(
                        '<math dir="rtl"><semantics><annotation encoding="application/json">[]</annotation></semantics></math>',
                        !0
                      )
                    : this.setMathML(
                        '<math><semantics><annotation encoding="application/json">[]</annotation></semantics></math>',
                        !0
                      )
                  : this.editor.getEditorModel().isRTL()
                  ? this.setMathML('<math dir="rtl"/>', !0)
                  : this.setMathML("<math/>", !0);
              },
            },
            {
              key: "onOpen",
              value: function () {
                this.isNewElement
                  ? this.setEmptyMathML()
                  : this.setMathML(this.mathML),
                  this.updateToolbar(),
                  this.onFocus();
              },
            },
            {
              key: "updateToolbar",
              value: function () {
                this.updateTitle(this.modalDialogInstance);
                var e = this.customEditors.getActiveEditor();
                if (e) {
                  var t = e.toolbar
                    ? e.toolbar
                    : _wrs_int_wirisProperties.toolbar;
                  (null != this.toolbar && this.toolbar === t) ||
                    this.setToolbar(t);
                } else {
                  var n = this.getToolbar();
                  (null != this.toolbar && this.toolbar === n) ||
                    (this.setToolbar(n), this.customEditors.disable());
                }
              },
            },
            {
              key: "updateTitle",
              value: function () {
                var e = this.customEditors.getActiveEditor();
                e
                  ? this.modalDialogInstance.setTitle(e.title)
                  : this.modalDialogInstance.setTitle("MathType");
              },
            },
            {
              key: "getToolbar",
              value: function () {
                var e = "general";
                return (
                  "toolbar" in this.editorAttributes &&
                    (e = this.editorAttributes.toolbar),
                  "general" === e &&
                    (e =
                      "undefined" == typeof _wrs_int_wirisProperties ||
                      void 0 === _wrs_int_wirisProperties.toolbar
                        ? "general"
                        : _wrs_int_wirisProperties.toolbar),
                  e
                );
              },
            },
            {
              key: "setToolbar",
              value: function (e) {
                (this.toolbar = e),
                  this.editor.setParams({ toolbar: this.toolbar });
              },
            },
            {
              key: "hasChanges",
              value: function () {
                return (
                  !this.editor.isFormulaEmpty() &&
                  this.editorListener.getIsContentChanged()
                );
              },
            },
            {
              key: "onKeyDown",
              value: function (e) {
                if (void 0 !== e.key && !1 === e.repeat)
                  if ("Escape" === e.key || "Esc" === e.key) {
                    var t = document.getElementsByClassName(
                      "wrs_expandButton wrs_expandButtonFor3RowsLayout wrs_pressed"
                    );
                    0 === t.length &&
                      0 ===
                        (t = document.getElementsByClassName(
                          "wrs_expandButton wrs_expandButtonFor2RowsLayout wrs_pressed"
                        )).length &&
                      0 ===
                        (t = document.getElementsByClassName(
                          "wrs_select wrs_pressed"
                        )).length &&
                      (this.modalDialogInstance.cancelAction(),
                      e.stopPropagation(),
                      e.preventDefault());
                  } else if (e.shiftKey && "Tab" === e.key)
                    if (
                      document.activeElement ===
                      this.modalDialogInstance.submitButton
                    )
                      this.editor.focus(),
                        e.stopPropagation(),
                        e.preventDefault();
                    else {
                      var n = document.querySelector('[title="Manual"]');
                      document.activeElement === n &&
                        (this.modalDialogInstance.cancelButton.focus(),
                        e.stopPropagation(),
                        e.preventDefault());
                    }
                  else if ("Tab" === e.key) {
                    if (
                      document.activeElement ===
                      this.modalDialogInstance.cancelButton
                    )
                      document.querySelector('[title="Manual"]').focus(),
                        e.stopPropagation(),
                        e.preventDefault();
                    else
                      "wrs_formulaDisplay wrs_focused" ===
                        document
                          .getElementsByClassName("wrs_formulaDisplay")[0]
                          .getAttribute("class") &&
                        (this.modalDialogInstance.submitButton.focus(),
                        e.stopPropagation(),
                        e.preventDefault());
                  }
              },
            },
          ]),
          e
        );
      })();
      function L(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var S = (function () {
          function e() {
            !(function (e, t) {
              if (!(e instanceof t))
                throw new TypeError("Cannot call a class as a function");
            })(this, e),
              (this.editors = []),
              (this.activeEditor = "default");
          }
          return (
            (function (e, t, n) {
              t && L(e.prototype, t), n && L(e, n);
            })(e, [
              {
                key: "addEditor",
                value: function (e, t) {
                  var n = {};
                  (n.name = t.name),
                    (n.toolbar = t.toolbar),
                    (n.icon = t.icon),
                    (n.confVariable = t.confVariable),
                    (n.title = t.title),
                    (n.tooltip = t.tooltip),
                    (this.editors[e] = n);
                },
              },
              {
                key: "enable",
                value: function (e) {
                  this.activeEditor = e;
                },
              },
              {
                key: "disable",
                value: function () {
                  this.activeEditor = "default";
                },
              },
              {
                key: "getActiveEditor",
                value: function () {
                  return "default" !== this.activeEditor
                    ? this.editors[this.activeEditor]
                    : null;
                },
              },
            ]),
            e
          );
        })(),
        z = {
          imageCustomEditorName: "data-custom-editor",
          imageClassName: "Wirisformula",
          CASClassName: "Wiriscas",
        };
      function B(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var N = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e),
            (this.cancelled = !1),
            (this.defaultPrevented = !1);
        }
        return (
          (function (e, t, n) {
            t && B(e.prototype, t), n && B(e, n);
          })(e, [
            {
              key: "cancel",
              value: function () {
                this.cancelled = !0;
              },
            },
            {
              key: "preventDefault",
              value: function () {
                this.defaultPrevented = !0;
              },
            },
          ]),
          e
        );
      })();
      function D(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var F = (function () {
        function e(t) {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e),
            (this.overlayElement = t.overlayElement),
            (this.callbacks = t.callbacks),
            (this.overlayWrapper = this.overlayElement.appendChild(
              document.createElement("div")
            )),
            this.overlayWrapper.setAttribute(
              "class",
              "wrs_popupmessage_overlay_envolture"
            ),
            (this.message = this.overlayWrapper.appendChild(
              document.createElement("div")
            )),
            (this.message.id = "wrs_popupmessage"),
            this.message.setAttribute("class", "wrs_popupmessage_panel"),
            this.message.setAttribute("role", "dialog"),
            this.message.setAttribute("aria-describedby", "description_txt");
          var n = document.createElement("p"),
            i = document.createTextNode(t.strings.message);
          n.appendChild(i),
            (n.id = "description_txt"),
            this.message.appendChild(n);
          var o = this.overlayWrapper.appendChild(document.createElement("div"));
          o.setAttribute("class", "wrs_popupmessage_overlay"),
            o.addEventListener("click", this.cancelAction.bind(this)),
            (this.buttonArea = this.message.appendChild(
              document.createElement("div")
            )),
            this.buttonArea.setAttribute("class", "wrs_popupmessage_button_area"),
            (this.buttonArea.id = "wrs_popup_button_area");
          var r = {
            class: "wrs_button_accept",
            innerHTML: t.strings.submitString,
            id: "wrs_popup_accept_button",
          };
          (this.closeButton = this.createButton(r, this.closeAction.bind(this))),
            this.buttonArea.appendChild(this.closeButton);
          var a = {
            class: "wrs_button_cancel",
            innerHTML: t.strings.cancelString,
            id: "wrs_popup_cancel_button",
          };
          (this.cancelButton = this.createButton(
            a,
            this.cancelAction.bind(this)
          )),
            this.buttonArea.appendChild(this.cancelButton);
        }
        return (
          (function (e, t, n) {
            t && D(e.prototype, t), n && D(e, n);
          })(e, [
            {
              key: "createButton",
              value: function (e, t) {
                var n = {};
                return (
                  (n = document.createElement("button")).setAttribute("id", e.id),
                  n.setAttribute("class", e.class),
                  (n.innerHTML = e.innerHTML),
                  n.addEventListener("click", t),
                  n
                );
              },
            },
            {
              key: "show",
              value: function () {
                "block" !== this.overlayWrapper.style.display
                  ? (document.activeElement.blur(),
                    (this.overlayWrapper.style.display = "block"),
                    this.closeButton.focus())
                  : ((this.overlayWrapper.style.display = "none"),
                    _wrs_modalWindow.focus());
              },
            },
            {
              key: "cancelAction",
              value: function () {
                (this.overlayWrapper.style.display = "none"),
                  void 0 !== this.callbacks.cancelCallback &&
                    this.callbacks.cancelCallback();
              },
            },
            {
              key: "closeAction",
              value: function () {
                this.cancelAction(),
                  void 0 !== this.callbacks.closeCallback &&
                    this.callbacks.closeCallback();
              },
            },
            {
              key: "onKeyDown",
              value: function (e) {
                void 0 !== e.key &&
                  ("Escape" === e.key || "Esc" === e.key
                    ? (this.cancelAction(),
                      e.stopPropagation(),
                      e.preventDefault())
                    : "Tab" === e.key &&
                      (document.activeElement === this.closeButton
                        ? this.cancelButton.focus()
                        : this.closeButton.focus(),
                      e.stopPropagation(),
                      e.preventDefault()));
              },
            },
          ]),
          e
        );
      })();
      function R(e, t) {
        if (!(e instanceof t))
          throw new TypeError("Cannot call a class as a function");
      }
      function H(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      function X(e, t, n) {
        return t && H(e.prototype, t), n && H(e, n), e;
      }
      var U = (function () {
        function e(t) {
          var n = this;
          R(this, e), (this.attributes = t);
          var i = navigator.userAgent.toLowerCase(),
            o = i.indexOf("android") > -1,
            r = i.indexOf("ipad") > -1 || i.indexOf("iphone") > -1;
          (this.iosSoftkeyboardOpened = !1),
            (this.iosMeasureUnit = -1 === i.indexOf("crios") ? "%" : "vh"),
            (this.iosDivHeight = "100%".concat(this.iosMeasureUnit));
          var a = window.outerWidth,
            s = window.outerHeight,
            l = a > s,
            c = a < s,
            u = l && this.attributes.height > s,
            d = c && this.attributes.width > a,
            h = u || d;
          (this.instanceId = document.getElementsByClassName(
            "wrs_modal_dialogContainer"
          ).length),
            (this.deviceProperties = {
              orientation: l ? "landscape" : "portait",
              isAndroid: o,
              isIOS: r,
              isMobile: h,
              isDesktop: !h && !r && !o,
            }),
            (this.properties = {
              created: !1,
              state: "",
              previousState: "",
              position: { bottom: (document.body.clientHeight  - 338)/2, right: (document.body.clientWidth  - 580)/2 },
              size: { height: 338, width: 580 },
            }),
            (this.websiteBeforeLockParameters = null);
          var m = { class: "wrs_modal_overlay" };
          (m.id = this.getElementId(m.class)),
            (this.overlay = w.createElement("div", m)),
            ((m = {}).class = "wrs_modal_title_bar"),
            (m.id = this.getElementId(m.class)),
            (this.titleBar = w.createElement("div", m)),
            ((m = {}).class = "wrs_modal_title"),
            (m.id = this.getElementId(m.class)),
            (this.title = w.createElement("div", m)),
            (this.title.innerHTML = ""),
            ((m = {}).class = "wrs_modal_close_button"),
            (m.id = this.getElementId(m.class)),
            (m.title = b.get("close")),
            (this.closeDiv = w.createElement("a", m)),
            this.closeDiv.setAttribute("role", "button"),
            ((m = {}).class = "wrs_modal_stack_button"),
            (m.id = this.getElementId(m.class)),
            (m.title = b.get("exit_fullscreen")),
            (this.stackDiv = w.createElement("a", m)),
            this.stackDiv.setAttribute("role", "button"),
            ((m = {}).class = "wrs_modal_maximize_button"),
            (m.id = this.getElementId(m.class)),
            (m.title = b.get("fullscreen")),
            (this.maximizeDiv = w.createElement("a", m)),
            this.maximizeDiv.setAttribute("role", "button"),
            ((m = {}).class = "wrs_modal_minimize_button"),
            (m.id = this.getElementId(m.class)),
            (m.title = b.get("minimize")),
            (this.minimizeDiv = w.createElement("a", m)),
            this.minimizeDiv.setAttribute("role", "button"),
            ((m = {}).class = "wrs_modal_dialogContainer"),
            (m.id = this.getElementId(m.class)),
            (m.role = "dialog"),
            (this.container = w.createElement("div", m)),
            this.container.setAttribute("aria-labeledby", "wrs_modal_title[0]"),
            ((m = {}).class = "wrs_modal_wrapper"),
            (m.id = this.getElementId(m.class)),
            (this.wrapper = w.createElement("div", m)),
            ((m = {}).class = "wrs_content_container"),
            (m.id = this.getElementId(m.class)),
            (this.contentContainer = w.createElement("div", m)),
            ((m = {}).class = "wrs_modal_controls"),
            (m.id = this.getElementId(m.class)),
            (this.controls = w.createElement("div", m)),
            ((m = {}).class = "wrs_modal_buttons_container"),
            (m.id = this.getElementId(m.class)),
            (this.buttonContainer = w.createElement("div", m)),
            (this.submitButton = this.createSubmitButton(
              {
                id: this.getElementId("wrs_modal_button_accept"),
                class: "wrs_modal_button_accept",
                innerHTML: b.get("accept"),
              },
              this.submitAction.bind(this)
            )),
            (this.cancelButton = this.createSubmitButton(
              {
                id: this.getElementId("wrs_modal_button_cancel"),
                class: "wrs_modal_button_cancel",
                innerHTML: b.get("cancel"),
              },
              this.cancelAction.bind(this)
            )),
            (this.contentManager = null);
          var f = {
              cancelString: b.get("cancel"),
              submitString: b.get("close"),
              message: b.get("close_modal_warning"),
            },
            g = {
              closeCallback: function () {
                n.close();
              },
              cancelCallback: function () {
                n.focus();
              },
            },
            p = { overlayElement: this.container, callbacks: g, strings: f };
          (this.popup = new F(p)),
            (this.rtl = !1),
            "rtl" in this.attributes && (this.rtl = this.attributes.rtl),
            (this.handleOpenedIosSoftkeyboard =
              this.handleOpenedIosSoftkeyboard.bind(this)),
            (this.handleClosedIosSoftkeyboard =
              this.handleClosedIosSoftkeyboard.bind(this));
        }
        return (
          X(e, [
            {
              key: "setContentManager",
              value: function (e) {
                this.contentManager = e;
              },
            },
            {
              key: "getContentManager",
              value: function () {
                return this.contentManager;
              },
            },
            {
              key: "submitAction",
              value: function () {
                void 0 !== this.contentManager.submitAction &&
                  this.contentManager.submitAction(),
                  this.close();
              },
            },
            {
              key: "cancelAction",
              value: function () {
                void 0 === this.contentManager.hasChanges
                  ? this.close()
                  : this.contentManager.hasChanges()
                  ? this.showPopUpMessage()
                  : this.close();
              },
            },
            {
              key: "createSubmitButton",
              value: function (e, t) {
                return new ((function () {
                  function n() {
                    R(this, n),
                      (this.element = document.createElement("button")),
                      (this.element.id = e.id),
                      (this.element.className = e.class),
                      (this.element.innerHTML = e.innerHTML),
                      w.addEvent(this.element, "click", t);
                  }
                  return (
                    X(n, [
                      {
                        key: "getElement",
                        value: function () {
                          return this.element;
                        },
                      },
                    ]),
                    n
                  );
                })())(e, t).getElement();
              },
            },
            {
              key: "create",
              value: function () {
                this.titleBar.appendChild(this.closeDiv),
                  this.titleBar.appendChild(this.stackDiv),
                  this.titleBar.appendChild(this.maximizeDiv),
                  this.titleBar.appendChild(this.minimizeDiv),
                  this.titleBar.appendChild(this.title),
                  this.deviceProperties.isDesktop &&
                    this.container.appendChild(this.titleBar),
                  this.wrapper.appendChild(this.contentContainer),
                  this.wrapper.appendChild(this.controls),
                  this.controls.appendChild(this.buttonContainer),
                  this.buttonContainer.appendChild(this.submitButton),
                  this.buttonContainer.appendChild(this.cancelButton),
                  this.container.appendChild(this.wrapper),
                  this.recalculateScrollBar(),
                  document.body.appendChild(this.container),
                  document.body.appendChild(this.overlay),
                  this.deviceProperties.isDesktop
                    ? (this.createModalWindowDesktop(),
                      this.createResizeButtons(),
                      this.addListeners(),
                      l.get("modalWindowFullScreen") && this.maximize())
                    : this.deviceProperties.isAndroid
                    ? this.createModalWindowAndroid()
                    : this.deviceProperties.isIOS &&
                      !this.deviceProperties.isMobile &&
                      this.createModalWindowIos(),
                  null != this.contentManager && this.contentManager.insert(this),
                  (this.properties.open = !0),
                  (this.properties.created = !0),
                  this.isRTL() &&
                    ((this.container.style.right = "".concat(
                      window.innerWidth -
                        this.scrollbarWidth -
                        this.container.offsetWidth,
                      "px"
                    )),
                    (this.container.className += " wrs_modal_rtl"));
              },
            },
            {
              key: "createResizeButtons",
              value: function () {
                (this.resizerBR = document.createElement("div")),
                  (this.resizerBR.className = "wrs_bottom_right_resizer"),
                  (this.resizerBR.innerHTML = "◢"),
                  (this.resizerTL = document.createElement("div")),
                  (this.resizerTL.className = "wrs_bottom_left_resizer"),
                  this.container.appendChild(this.resizerBR),
                  this.titleBar.appendChild(this.resizerTL),
                  w.addEvent(
                    this.resizerBR,
                    "mousedown",
                    this.activateResizeStateBR.bind(this)
                  ),
                  w.addEvent(
                    this.resizerTL,
                    "mousedown",
                    this.activateResizeStateTL.bind(this)
                  );
              },
            },
            {
              key: "activateResizeStateBR",
              value: function (e) {
                this.initializeResizeProperties(e, !1);
              },
            },
            {
              key: "activateResizeStateTL",
              value: function (e) {
                this.initializeResizeProperties(e, !0);
              },
            },
            {
              key: "initializeResizeProperties",
              value: function (e, t) {
                w.addClass(document.body, "wrs_noselect"),
                  w.addClass(this.overlay, "wrs_overlay_active"),
                  (this.resizeDataObject = {
                    x: this.eventClient(e).X,
                    y: this.eventClient(e).Y,
                  }),
                  (this.initialWidth = parseInt(this.container.style.width, 10)),
                  (this.initialHeight = parseInt(
                    this.container.style.height,
                    10
                  )),
                  t
                    ? (this.leftScale = !0)
                    : ((this.initialRight = parseInt(
                        this.container.style.right,
                        10
                      )),
                      (this.initialBottom = parseInt(
                        this.container.style.bottom,
                        10
                      ))),
                  this.initialRight || (this.initialRight = 0),
                  this.initialBottom || (this.initialBottom = 0),
                  (document.body.style["user-select"] = "none");
              },
            },
            {
              key: "open",
              value: function () {
                var e = this;
                this.removeClass("wrs_closed");
                var t = this.deviceProperties.isIOS,
                  n = this.deviceProperties.isAndroid,
                  i = this.deviceProperties.isMobile;
                if (
                  ((t || n || i) &&
                    (this.restoreWebsiteScale(),
                    this.lockWebsiteScroll(),
                    setTimeout(function () {
                      e.hideKeyboard();
                    }, 400)),
                  this.properties.created
                    ? (this.properties.open ||
                        ((this.properties.open = !0),
                        this.deviceProperties.isAndroid ||
                          this.deviceProperties.isIOS ||
                          this.restoreState()),
                      this.deviceProperties.isDesktop &&
                        l.get("modalWindowFullScreen") &&
                        this.maximize(),
                      this.deviceProperties.isIOS &&
                        ((this.iosSoftkeyboardOpened = !1),
                        this.setContainerHeight(
                          "".concat(100 + this.iosMeasureUnit)
                        )))
                    : this.create(),
                  !1 === this.contentManager.isEditorLoaded)
                ) {
                  var o = h.newListener("onLoad", function () {
                    e.contentManager.onOpen(e);
                  });
                  this.contentManager.addListener(o);
                } else this.contentManager.onOpen(this);
              },
            },
            {
              key: "close",
              value: function () {
                this.removeClass("wrs_maximized"),
                  this.removeClass("wrs_minimized"),
                  this.removeClass("wrs_stack"),
                  this.addClass("wrs_closed"),
                  this.saveModalProperties(),
                  this.unlockWebsiteScroll(),
                  (this.properties.open = !1);
              },
            },
            {
              key: "restoreWebsiteScale",
              value: function () {
                var e = document.querySelector("meta[name=viewport]"),
                  t = ["initial-scale=", "minimum-scale=", "maximum-scale="],
                  n = ["1.0", "1.0", "1.0"],
                  i = function (e, t) {
                    var i = e.getAttribute("content");
                    if (i) {
                      for (
                        var o = i.split(","), r = "", a = [], s = 0;
                        s < o.length;
                        s += 1
                      ) {
                        for (var l = !1, c = 0; !l && c < t.length; )
                          o[s].indexOf(t[c]) && (l = !0), (c += 1);
                        l || a.push(o[s]);
                      }
                      for (var u = 0; u < t.length; u += 1) {
                        var d = t[u] + n[u];
                        r += 0 === u ? d : ",".concat(d);
                      }
                      for (var h = 0; h < a.length; h += 1) r += ",".concat(a[h]);
                      e.setAttribute("content", r),
                        e.setAttribute("content", ""),
                        e.setAttribute("content", i);
                    } else
                      e.setAttribute(
                        "content",
                        "initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0"
                      ),
                        e.removeAttribute("content");
                  };
                e
                  ? i(e, t)
                  : ((e = document.createElement("meta")),
                    document.getElementsByTagName("head")[0].appendChild(e),
                    i(e, t),
                    e.remove());
              },
            },
            {
              key: "lockWebsiteScroll",
              value: function () {
                this.websiteBeforeLockParameters = {
                  bodyStylePosition: document.body.style.position
                    ? document.body.style.position
                    : "",
                  bodyStyleOverflow: document.body.style.overflow
                    ? document.body.style.overflow
                    : "",
                  htmlStyleOverflow: document.documentElement.style.overflow
                    ? document.documentElement.style.overflow
                    : "",
                  windowScrollX: window.scrollX,
                  windowScrollY: window.scrollY,
                };
              },
            },
            {
              key: "unlockWebsiteScroll",
              value: function () {
                if (this.websiteBeforeLockParameters) {
                  (document.body.style.position =
                    this.websiteBeforeLockParameters.bodyStylePosition),
                    (document.body.style.overflow =
                      this.websiteBeforeLockParameters.bodyStyleOverflow),
                    (document.documentElement.style.overflow =
                      this.websiteBeforeLockParameters.htmlStyleOverflow);
                  var e = this.websiteBeforeLockParameters.windowScrollX,
                    t = this.websiteBeforeLockParameters.windowScrollY;
                  window.scrollTo(e, t),
                    (this.websiteBeforeLockParameters = null);
                }
              },
            },
            {
              key: "isIE11",
              value: function () {
                return (
                  navigator.userAgent.search("Msie/") >= 0 ||
                  navigator.userAgent.search("Trident/") >= 0 ||
                  navigator.userAgent.search("Edge/") >= 0
                );
              },
            },
            {
              key: "isRTL",
              value: function () {
                return (
                  "ar" === this.attributes.language ||
                  "he" === this.attributes.language ||
                  this.rtl
                );
              },
            },
            {
              key: "addClass",
              value: function (e) {
                w.addClass(this.overlay, e),
                  w.addClass(this.titleBar, e),
                  w.addClass(this.overlay, e),
                  w.addClass(this.container, e),
                  w.addClass(this.contentContainer, e),
                  w.addClass(this.stackDiv, e),
                  w.addClass(this.minimizeDiv, e),
                  w.addClass(this.maximizeDiv, e),
                  w.addClass(this.wrapper, e);
              },
            },
            {
              key: "removeClass",
              value: function (e) {
                w.removeClass(this.overlay, e),
                  w.removeClass(this.titleBar, e),
                  w.removeClass(this.overlay, e),
                  w.removeClass(this.container, e),
                  w.removeClass(this.contentContainer, e),
                  w.removeClass(this.stackDiv, e),
                  w.removeClass(this.minimizeDiv, e),
                  w.removeClass(this.maximizeDiv, e),
                  w.removeClass(this.wrapper, e);
              },
            },
            {
              key: "createModalWindowDesktop",
              value: function () {
                this.addClass("wrs_modal_desktop"), this.stack();
              },
            },
            {
              key: "createModalWindowAndroid",
              value: function () {
                this.addClass("wrs_modal_android"),
                  window.addEventListener(
                    "resize",
                    this.orientationChangeAndroidSoftkeyboard.bind(this)
                  );
              },
            },
            {
              key: "createModalWindowIos",
              value: function () {
                this.addClass("wrs_modal_ios"),
                  window.addEventListener(
                    "resize",
                    this.orientationChangeIosSoftkeyboard.bind(this)
                  );
              },
            },
            {
              key: "restoreState",
              value: function () {
                "maximized" === this.properties.state
                  ? this.maximize()
                  : "minimized" === this.properties.state
                  ? ((this.properties.state = this.properties.previousState),
                    (this.properties.previousState = ""),
                    this.minimize())
                  : this.stack();
              },
            },
            {
              key: "stack",
              value: function () {
                (this.properties.previousState = this.properties.state),
                  (this.properties.state = "stack"),
                  this.removeClass("wrs_maximized"),
                  (this.minimizeDiv.title = b.get("minimize")),
                  this.removeClass("wrs_minimized"),
                  this.addClass("wrs_stack"),
                  this.restoreModalProperties(),
                  void 0 !== this.resizerBR &&
                    void 0 !== this.resizerTL &&
                    this.setResizeButtonsVisibility(),
                  this.recalculateScrollBar(),
                  this.recalculatePosition(),
                  this.recalculateScale(),
                  this.focus();
              },
            },
            {
              key: "minimize",
              value: function () {
                this.saveModalProperties(),
                  "minimized" === this.properties.state &&
                  "stack" === this.properties.previousState
                    ? this.stack()
                    : "minimized" === this.properties.state &&
                      "maximized" === this.properties.previousState
                    ? this.maximize()
                    : ((this.container.style.height = "30px"),
                      (this.container.style.width = "250px"),
                      (this.container.style.bottom = "0px"),
                      (this.container.style.right = "10px"),
                      this.removeListeners(),
                      (this.properties.previousState = this.properties.state),
                      (this.properties.state = "minimized"),
                      this.setResizeButtonsVisibility(),
                      (this.minimizeDiv.title = b.get("maximize")),
                      w.containsClass(this.overlay, "wrs_stack")
                        ? this.removeClass("wrs_stack")
                        : this.removeClass("wrs_maximized"),
                      this.addClass("wrs_minimized"));
              },
            },
            {
              key: "maximize",
              value: function () {
                this.saveModalProperties(),
                  "maximized" !== this.properties.state &&
                    ((this.properties.previousState = this.properties.state),
                    (this.properties.state = "maximized")),
                  this.setResizeButtonsVisibility(),
                  w.containsClass(this.overlay, "wrs_minimized")
                    ? ((this.minimizeDiv.title = b.get("minimize")),
                      this.removeClass("wrs_minimized"))
                    : w.containsClass(this.overlay, "wrs_stack") &&
                      ((this.container.style.left = null),
                      (this.container.style.top = null),
                      this.removeClass("wrs_stack")),
                  this.addClass("wrs_maximized"),
                  this.setSize(
                    parseInt(0.8 * window.innerHeight, 10),
                    parseInt(0.8 * window.innerWidth, 10)
                  ),
                  this.container.clientHeight > 700 &&
                    (this.container.style.height = "700px"),
                  this.container.clientWidth > 1200 &&
                    (this.container.style.width = "1200px");
                var e = window.innerHeight,
                  t = window.innerWidth,
                  n = e / 2 - this.container.offsetHeight / 2,
                  i = t / 2 - this.container.offsetWidth / 2;
                this.setPosition(n, i),
                  this.recalculateScale(),
                  this.recalculatePosition(),
                  this.recalculateSize(),
                  this.focus();
              },
            },
            {
              key: "setSize",
              value: function (e, t) {
                (this.container.style.height = "".concat(e, "px")),
                  (this.container.style.width = "".concat(t, "px")),
                  this.recalculateSize();
              },
            },
            {
              key: "setPosition",
              value: function (e, t) {
                (this.container.style.bottom = "".concat(e, "px")),
                  (this.container.style.right = "".concat(t, "px"));
              },
            },
            {
              key: "saveModalProperties",
              value: function () {
                "stack" === this.properties.state &&
                  ((this.properties.position.bottom = parseInt(
                    this.container.style.bottom,
                    10
                  )),
                  (this.properties.position.right = parseInt(
                    this.container.style.right,
                    10
                  )),
                  (this.properties.size.width = parseInt(
                    this.container.style.width,
                    10
                  )),
                  (this.properties.size.height = parseInt(
                    this.container.style.height,
                    10
                  )));
              },
            },
            {
              key: "restoreModalProperties",
              value: function () {
                "stack" === this.properties.state &&
                  (this.setPosition(
                    this.properties.position.bottom,
                    this.properties.position.right
                  ),
                  this.setSize(
                    this.properties.size.height,
                    this.properties.size.width
                  ));
              },
            },
            {
              key: "recalculateSize",
              value: function () {
                (this.wrapper.style.width = "".concat(
                  this.container.clientWidth - 12,
                  "px"
                )),
                  (this.wrapper.style.height = "".concat(
                    this.container.clientHeight - 38,
                    "px"
                  )),
                  (this.contentContainer.style.height = "".concat(
                    parseInt(this.wrapper.offsetHeight - 50, 10),
                    "px"
                  ));
              },
            },
            {
              key: "setResizeButtonsVisibility",
              value: function () {
                "stack" === this.properties.state
                  ? ((this.resizerTL.style.visibility = "visible"),
                    (this.resizerBR.style.visibility = "visible"))
                  : ((this.resizerTL.style.visibility = "hidden"),
                    (this.resizerBR.style.visibility = "hidden"));
              },
            },
            {
              key: "addListeners",
              value: function () {
                this.maximizeDiv.addEventListener(
                  "click",
                  this.maximize.bind(this),
                  !0
                ),
                  this.stackDiv.addEventListener(
                    "click",
                    this.stack.bind(this),
                    !0
                  ),
                  this.minimizeDiv.addEventListener(
                    "click",
                    this.minimize.bind(this),
                    !0
                  ),
                  this.closeDiv.addEventListener(
                    "click",
                    this.cancelAction.bind(this)
                  ),
                  this.overlay.addEventListener(
                    "click",
                    this.cancelAction.bind(this)
                  ),
                  w.addEvent(window, "mousedown", this.startDrag.bind(this)),
                  w.addEvent(window, "mouseup", this.stopDrag.bind(this)),
                  w.addEvent(window, "mousemove", this.drag.bind(this)),
                  w.addEvent(window, "resize", this.onWindowResize.bind(this)),
                  w.addEvent(
                    this.container,
                    "keydown",
                    this.onKeyDown.bind(this)
                  );
              },
            },
            {
              key: "removeListeners",
              value: function () {
                w.removeEvent(window, "mousedown", this.startDrag),
                  w.removeEvent(window, "mouseup", this.stopDrag),
                  w.removeEvent(window, "mousemove", this.drag),
                  w.removeEvent(window, "resize", this.onWindowResize),
                  w.removeEvent(this.container, "keydown", this.onKeyDown);
              },
            },
            {
              key: "eventClient",
              value: function (e) {
                return void 0 === e.clientX && e.changedTouches
                  ? {
                      X: e.changedTouches[0].clientX,
                      Y: e.changedTouches[0].clientY,
                    }
                  : { X: e.clientX, Y: e.clientY };
              },
            },
            {
              key: "startDrag",
              value: function (e) {
                "minimized" !== this.properties.state &&
                  e.target === this.title &&
                  ((void 0 !== this.dragDataObject &&
                    null !== this.dragDataObject) ||
                    ((this.dragDataObject = {
                      x: this.eventClient(e).X,
                      y: this.eventClient(e).Y,
                    }),
                    (this.lastDrag = { x: "0px", y: "0px" }),
                    "" === this.container.style.right &&
                      (this.container.style.right = "0px"),
                    "" === this.container.style.bottom &&
                      (this.container.style.bottom = "0px"),
                    this.isIE11(),
                    w.addClass(document.body, "wrs_noselect"),
                    w.addClass(this.overlay, "wrs_overlay_active"),
                    (this.limitWindow = this.getLimitWindow())));
              },
            },
            {
              key: "drag",
              value: function (e) {
                if (this.dragDataObject) {
                  e.preventDefault();
                  var t = Math.min(
                    this.eventClient(e).Y,
                    this.limitWindow.minPointer.y
                  );
                  t = Math.max(this.limitWindow.maxPointer.y, t);
                  var n = Math.min(
                    this.eventClient(e).X,
                    this.limitWindow.minPointer.x
                  );
                  n = Math.max(this.limitWindow.maxPointer.x, n);
                  var i = "".concat(n - this.dragDataObject.x, "px"),
                    o = "".concat(t - this.dragDataObject.y, "px");
                  (this.lastDrag = { x: i, y: o }),
                    (this.container.style.transform = "translate3d("
                      .concat(i, ",")
                      .concat(o, ",0)"));
                }
                if (this.resizeDataObject) {
                  var r,
                    a = window.innerWidth,
                    s = window.innerHeight,
                    l = Math.min(
                      this.eventClient(e).X,
                      a - this.scrollbarWidth - 7
                    ),
                    c = Math.min(this.eventClient(e).Y, s - 7);
                  l < 0 && (l = 0),
                    c < 0 && (c = 0),
                    (r = this.leftScale ? -1 : 1),
                    (this.container.style.width = "".concat(
                      this.initialWidth + r * (l - this.resizeDataObject.x),
                      "px"
                    )),
                    (this.container.style.height = "".concat(
                      this.initialHeight + r * (c - this.resizeDataObject.y),
                      "px"
                    )),
                    this.leftScale ||
                      (this.resizeDataObject.x - l - this.initialWidth < -580
                        ? (this.container.style.right = "".concat(
                            this.initialRight - (l - this.resizeDataObject.x),
                            "px"
                          ))
                        : ((this.container.style.right = "".concat(
                            this.initialRight + this.initialWidth - 580,
                            "px"
                          )),
                          (this.container.style.width = "580px")),
                      this.resizeDataObject.y - c < this.initialHeight - 338
                        ? (this.container.style.bottom = "".concat(
                            this.initialBottom - (c - this.resizeDataObject.y),
                            "px"
                          ))
                        : ((this.container.style.bottom = "".concat(
                            this.initialBottom + this.initialHeight - 338,
                            "px"
                          )),
                          (this.container.style.height = "338px"))),
                    this.recalculateScale(),
                    this.recalculatePosition();
                }
              },
            },
            {
              key: "getLimitWindow",
              value: function () {
                var e = window.innerWidth,
                  t = window.innerHeight,
                  n = this.container.offsetHeight,
                  i = parseInt(this.container.style.bottom, 10),
                  o = parseInt(this.container.style.right, 10),
                  r = window.pageXOffset,
                  a = this.dragDataObject.y,
                  s = this.dragDataObject.x,
                  l = n + i - (t - (a - r)),
                  c = e - this.scrollbarWidth - (s - r) - o,
                  u = t - this.container.offsetHeight + l,
                  d = this.title.offsetHeight - (this.title.offsetHeight - l);
                return {
                  minPointer: { x: e - c - this.scrollbarWidth, y: u },
                  maxPointer: { x: this.container.offsetWidth - c, y: d },
                };
              },
            },
            {
              key: "getScrollBarWidth",
              value: function () {
                var e = document.createElement("p");
                (e.style.width = "100%"), (e.style.height = "200px");
                var t = document.createElement("div");
                (t.style.position = "absolute"),
                  (t.style.top = "0px"),
                  (t.style.left = "0px"),
                  (t.style.visibility = "hidden"),
                  (t.style.width = "200px"),
                  (t.style.height = "150px"),
                  (t.style.overflow = "hidden"),
                  t.appendChild(e),
                  document.body.appendChild(t);
                var n = e.offsetWidth;
                t.style.overflow = "scroll";
                var i = e.offsetWidth;
                return (
                  n === i && (i = t.clientWidth),
                  document.body.removeChild(t),
                  n - i
                );
              },
            },
            {
              key: "stopDrag",
              value: function () {
                (this.dragDataObject || this.resizeDataObject) &&
                  ((this.container.style.transform = ""),
                  this.dragDataObject &&
                    ((this.container.style.right = "".concat(
                      parseInt(this.container.style.right, 10) -
                        parseInt(this.lastDrag.x, 10),
                      "px"
                    )),
                    (this.container.style.bottom = "".concat(
                      parseInt(this.container.style.bottom, 10) -
                        parseInt(this.lastDrag.y, 10),
                      "px"
                    ))),
                  this.focus(),
                  (document.body.style["user-select"] = ""),
                  this.isIE11(),
                  w.removeClass(document.body, "wrs_noselect"),
                  w.removeClass(this.overlay, "wrs_overlay_active")),
                  (this.dragDataObject = null),
                  (this.resizeDataObject = null),
                  (this.initialWidth = null),
                  (this.leftScale = null);
              },
            },
            {
              key: "onWindowResize",
              value: function () {
                this.recalculateScrollBar(),
                  this.recalculatePosition(),
                  this.recalculateScale();
              },
            },
            {
              key: "onKeyDown",
              value: function (e) {
                void 0 !== e.key &&
                  ("block" !== this.popup.overlayWrapper.style.display
                    ? "Escape" === e.key || "Esc" === e.key
                      ? this.properties.open && this.contentManager.onKeyDown(e)
                      : e.shiftKey && "Tab" === e.key
                      ? document.activeElement === this.cancelButton
                        ? (this.submitButton.focus(),
                          e.stopPropagation(),
                          e.preventDefault())
                        : this.contentManager.onKeyDown(e)
                      : "Tab" === e.key &&
                        (document.activeElement === this.submitButton
                          ? (this.cancelButton.focus(),
                            e.stopPropagation(),
                            e.preventDefault())
                          : this.contentManager.onKeyDown(e))
                    : this.popup.onKeyDown(e));
              },
            },
            {
              key: "recalculatePosition",
              value: function () {
                (this.container.style.right = "".concat(
                  Math.min(
                    parseInt(this.container.style.right, 10),
                    window.innerWidth -
                      this.scrollbarWidth -
                      this.container.offsetWidth
                  ),
                  "px"
                )),
                  parseInt(this.container.style.right, 10) < 0 &&
                    (this.container.style.right = "0px"),
                  (this.container.style.bottom = "".concat(
                    Math.min(
                      parseInt(this.container.style.bottom, 10),
                      window.innerHeight - this.container.offsetHeight
                    ),
                    "px"
                  )),
                  parseInt(this.container.style.bottom, 10) < 0 &&
                    (this.container.style.bottom = "0px");
              },
            },
            {
              key: "recalculateScale",
              value: function () {
                var e = !1;
                parseInt(this.container.style.width, 10) > 580
                  ? ((this.container.style.width = "".concat(
                      Math.min(
                        parseInt(this.container.style.width, 10),
                        window.innerWidth - this.scrollbarWidth
                      ),
                      "px"
                    )),
                    (e = !0))
                  : ((this.container.style.width = "580px"), (e = !0)),
                  parseInt(this.container.style.height, 10) > 338
                    ? ((this.container.style.height = "".concat(
                        Math.min(
                          parseInt(this.container.style.height, 10),
                          window.innerHeight
                        ),
                        "px"
                      )),
                      (e = !0))
                    : ((this.container.style.height = "338px"), (e = !0)),
                  e && this.recalculateSize();
              },
            },
            {
              key: "recalculateScrollBar",
              value: function () {
                (this.hasScrollBar =
                  window.innerWidth > document.documentElement.clientWidth),
                  this.hasScrollBar
                    ? (this.scrollbarWidth = this.getScrollBarWidth())
                    : (this.scrollbarWidth = 0);
              },
            },
            {
              key: "hideKeyboard",
              value: function () {
                var e = document.createElement("input");
                this.container.appendChild(e), e.focus(), e.blur(), e.remove();
              },
            },
            {
              key: "focus",
              value: function () {
                null != this.contentManager &&
                  void 0 !== this.contentManager.onFocus &&
                  this.contentManager.onFocus();
              },
            },
            {
              key: "portraitMode",
              value: function () {
                return window.innerHeight > window.innerWidth;
              },
            },
            {
              key: "handleOpenedIosSoftkeyboard",
              value: function () {
                this.iosSoftkeyboardOpened ||
                  null == this.iosDivHeight ||
                  this.iosDivHeight !== "100".concat(this.iosMeasureUnit) ||
                  (this.portraitMode()
                    ? this.setContainerHeight("63".concat(this.iosMeasureUnit))
                    : this.setContainerHeight("40".concat(this.iosMeasureUnit))),
                  (this.iosSoftkeyboardOpened = !0);
              },
            },
            {
              key: "handleClosedIosSoftkeyboard",
              value: function () {
                (this.iosSoftkeyboardOpened = !1),
                  this.setContainerHeight("100".concat(this.iosMeasureUnit));
              },
            },
            {
              key: "orientationChangeIosSoftkeyboard",
              value: function () {
                this.iosSoftkeyboardOpened
                  ? this.portraitMode()
                    ? this.setContainerHeight("63".concat(this.iosMeasureUnit))
                    : this.setContainerHeight("40".concat(this.iosMeasureUnit))
                  : this.setContainerHeight("100".concat(this.iosMeasureUnit));
              },
            },
            {
              key: "orientationChangeAndroidSoftkeyboard",
              value: function () {
                this.setContainerHeight("100%");
              },
            },
            {
              key: "setContainerHeight",
              value: function (e) {
                (this.iosDivHeight = e), (this.wrapper.style.height = e);
              },
            },
            {
              key: "showPopUpMessage",
              value: function () {
                "minimized" === this.properties.state && this.stack(),
                  this.popup.show();
              },
            },
            {
              key: "setTitle",
              value: function (e) {
                this.title.innerHTML = e;
              },
            },
            {
              key: "getElementId",
              value: function (e) {
                return "".concat(e, "[").concat(this.instanceId, "]");
              },
            },
          ]),
          e
        );
      })();
      /*! http://mths.be/codepointat v0.1.0 by @mathias */
      String.prototype.codePointAt ||
        (function () {
          var e = function (e) {
            if (null == this) throw TypeError();
            var t = String(this),
              n = t.length,
              i = e ? Number(e) : 0;
            if ((i != i && (i = 0), !(i < 0 || i >= n))) {
              var o,
                r = t.charCodeAt(i);
              return r >= 55296 &&
                r <= 56319 &&
                n > i + 1 &&
                (o = t.charCodeAt(i + 1)) >= 56320 &&
                o <= 57343
                ? 1024 * (r - 55296) + o - 56320 + 65536
                : r;
            }
          };
          Object.defineProperty
            ? Object.defineProperty(String.prototype, "codePointAt", {
                value: e,
                configurable: !0,
                writable: !0,
              })
            : (String.prototype.codePointAt = e);
        })(),
        "function" != typeof Object.assign &&
          Object.defineProperty(Object, "assign", {
            value: function (e, t) {
              if (null == e)
                throw new TypeError("Cannot convert undefined or null to object");
              for (var n = Object(e), i = 1; i < arguments.length; i++) {
                var o = arguments[i];
                if (null != o)
                  for (var r in o)
                    Object.prototype.hasOwnProperty.call(o, r) && (n[r] = o[r]);
              }
              return n;
            },
            writable: !0,
            configurable: !0,
          });
      n(2);
      function W(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      var J = (function () {
        function e(t) {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e),
            (this.language = "en"),
            (this.editMode = "latex"),
            (this.modalDialog = null),
            (this.customEditors = new S());
          if (
            (this.customEditors.addEditor("chemistry", {
              name: "Chemistry",
              toolbar: "chemistry",
              icon: "chem.png",
              confVariable: "chemEnabled",
              title: "ChemType",
              tooltip: "Insert a chemistry formula - ChemType",
            }),
            (this.environment = {}),
            (this.editionProperties = {}),
            (this.editionProperties.isNewElement = !0),
            (this.editionProperties.temporalImage = null),
            (this.editionProperties.latexRange = null),
            (this.editionProperties.range = null),
            (this.integrationModel = null),
            (this.contentManager = null),
            (this.browser = (function () {
              var e = navigator.userAgent,
                t = "none";
              return (
                e.search("Edge/") >= 0
                  ? (t = "EDGE")
                  : e.search("Chrome/") >= 0
                  ? (t = "CHROME")
                  : e.search("Trident/") >= 0
                  ? (t = "IE")
                  : e.search("Firefox/") >= 0
                  ? (t = "FIREFOX")
                  : e.search("Safari/") >= 0 && (t = "SAFARI"),
                t
              );
            })()),
            (this.listeners = new h()),
            (this.serviceProviderProperties = {}),
            !("serviceProviderProperties" in t))
          )
            throw new Error("serviceProviderProperties property missing.");
          this.serviceProviderProperties = t.serviceProviderProperties;
        }
        return (
          (function (e, t, n) {
            t && W(e.prototype, t), n && W(e, n);
          })(
            e,
            [
              {
                key: "setIntegrationModel",
                value: function (e) {
                  this.integrationModel = e;
                },
              },
              {
                key: "setEnvironment",
                value: function (e) {
                  "editor" in e && (this.environment.editor = e.editor),
                    "mode" in e && (this.environment.mode = e.mode),
                    "version" in e && (this.environment.version = e.version);
                },
              },
              {
                key: "getModalDialog",
                value: function () {
                  return this.modalDialog;
                },
              },
              {
                key: "init",
                value: function () {
                  var t = this;
                  if (e.initialized) this.listeners.fire("onLoad", {});
                  else {
                    var n = h.newListener("onInit", function () {
                      var e = '{"versionPlatform":"unknown","editorParameters":{},"imageFormat":"svg","CASEnabled":false,"customHeaders":"","parseModes":["latex"],"editorToolbar":"","editorAttributes":"width=570, height=450, scroll=no, resizable=yes","base64savemode":"default","modalWindow":true,"version":"8.8.2.1481","enableAccessibility":true,"saveMode":"xml","saveHandTraces":false,"editorUrl":"https://fs.iclass30.com/aliba/plug/wiris/demo/editor/editor","editorEnabled":true,"chemEnabled":true,"CASMathmlAttribute":"alt","CASAttributes":"width=640, height=480, scroll=no, resizable=yes","modalWindowFullScreen":false,"imageMathmlAttribute":"data-mathml","hostPlatform":"unknown","wirisPluginPerformance":true}',//f.getService("configurationjs", "", "get"),
                        n = JSON.parse(e);
                      l.addConfiguration(n),
                        l.addConfiguration(z),
                        (b.language = t.language),
                        t.listeners.fire("onLoad", {});
                    });
                    f.addListener(n),
                      f.init(this.serviceProviderProperties),
                      (e.initialized = !0);
                  }
                },
              },
              {
                key: "addListener",
                value: function (e) {
                  this.listeners.add(e);
                },
              },
              {
                key: "beforeUpdateFormula",
                value: function (t, n) {
                  var i = new N();
                  return (
                    (i.mathml = t),
                    (i.wirisProperties = {}),
                    null != n &&
                      Object.keys(n).forEach(function (e) {
                        i.wirisProperties[e] = n[e];
                      }),
                    (i.language = this.language),
                    (i.editMode = this.editMode),
                    this.listeners.fire("onBeforeFormulaInsertion", i)
                      ? {}
                      : e.globalListeners.fire("onBeforeFormulaInsertion", i)
                      ? {}
                      : { mathml: i.mathml, wirisProperties: i.wirisProperties }
                  );
                },
              },
              {
                key: "insertFormula",
                value: function (e, t, n, i) {
                  var o = {};
                  if (n)
                    if ("latex" === this.editMode) {
                      if (
                        ((o.latex = p.getLatexFromMathML(n)),
                        this.integrationModel.fillNonLatexNode && !o.latex)
                      ) {
                        var r = new N();
                        (r.editMode = this.editMode),
                          (r.windowTarget = t),
                          (r.focusElement = e),
                          (r.latex = o.latex),
                          this.integrationModel.fillNonLatexNode(r, t, n);
                      } else{
                        o.node = t.document.createElement('span'),
                        o.node.className='ck-math-tex',
                        o.node.title='双击编辑公式',
                        o.node.setAttribute('tex','\\(' + o.latex.replace('<',"< ") + '\\)'),
                        o.node.setAttribute('contenteditable','false'),
                        o.node.innerHTML = '\\(' + o.latex.replace('<',"< ") + '\\)'
                      }
                      this.insertElementOnSelection(o.node, e, t);
                    } else
                      (o.node = E.mathmlToImgObject(
                        t.document,
                        n,
                        i,
                        this.language
                      )),
                        this.insertElementOnSelection(o.node, e, t);
                  else this.insertElementOnSelection(null, e, t);
                  return o;
                },
              },
              {
                key: "afterUpdateFormula",
                value: function (t, n, i, o) {
                  var r = new N();
                  return (
                    (r.editMode = this.editMode),
                    (r.windowTarget = n),
                    (r.focusElement = t),
                    (r.node = i),
                    (r.latex = o),
                    this.listeners.fire("onAfterFormulaInsertion", r)
                      ? {}
                      : (e.globalListeners.fire("onAfterFormulaInsertion", r), {})
                  );
                },
              },
              {
                key: "placeCaretAfterNode",
                value: function (e) {
                  this.integrationModel.getSelection();
                  var t = e.ownerDocument;
                  if (void 0 !== t.getSelection && e.parentElement) {
                    var n = t.createRange();
                    n.setStartAfter(e), n.collapse(!0);
                    var i = t.getSelection();
                    i.removeAllRanges(), i.addRange(n), t.body.focus();
                  }
                },
              },
              {
                key: "insertElementOnSelection",
                value: function (e, t, n) {
                  if (this.editionProperties.isNewElement)
                    if (e)
                      if ("textarea" === t.type)
                        w.updateTextArea(t, e.textContent);
                      else if (
                        document.selection &&
                        0 === document.getSelection
                      ) {
                        var i = n.document.selection.createRange();
                        if (
                          (n.document.execCommand("InsertImage", !1, e.src),
                          "parentElement" in i ||
                            (n.document.execCommand("delete", !1),
                            (i = n.document.selection.createRange()),
                            n.document.execCommand("InsertImage", !1, e.src)),
                          "parentElement" in i)
                        ) {
                          var o = i.parentElement();
                          "IMG" === o.nodeName.toUpperCase()
                            ? o.parentNode.replaceChild(e, o)
                            : i.pasteHTML(w.createObjectCode(e));
                        }
                      } else {
                        var r = this.integrationModel.getSelection(),
                          a = null;
                        this.editionProperties.range
                          ? ((a = this.editionProperties.range),
                            (this.editionProperties.range = null))
                          : (a = r.getRangeAt(0)),
                          a.deleteContents();
                        var s = a.startContainer,
                          l = a.startOffset;
                        3 === s.nodeType
                          ? (s = s.splitText(l)).parentNode.insertBefore(e, s)
                          : 1 === s.nodeType &&
                            s.insertBefore(e, s.childNodes[l]),
                          this.placeCaretAfterNode(e);
                      }
                    else if ("textarea" === t.type) t.focus();
                    else {
                      var c = this.integrationModel.getSelection();
                      if ((c.removeAllRanges(), this.editionProperties.range)) {
                        var u = this.editionProperties.range;
                        (this.editionProperties.range = null), c.addRange(u);
                      }
                    }
                  else if (this.editionProperties.latexRange)
                    document.selection && 0 === document.getSelection
                      ? ((this.editionProperties.isNewElement = !0),
                        this.editionProperties.latexRange.select(),
                        this.insertElementOnSelection(e, t, n))
                      : (this.editionProperties.latexRange.deleteContents(),
                        this.editionProperties.latexRange.insertNode(e),
                        this.placeCaretAfterNode(e));
                  else if ("textarea" === t.type) {
                    var d;
                    (d =
                      void 0 !== this.integrationModel.getSelectedItem
                        ? this.integrationModel.getSelectedItem(t, !1)
                        : w.getSelectedItemOnTextarea(t)),
                      w.updateExistingTextOnTextarea(
                        t,
                        e.textContent,
                        d.startPosition,
                        d.endPosition
                      );
                  } else
                    e && "img" === e.nodeName.toLowerCase()
                      ? (A.removeImgDataAttributes(
                          this.editionProperties.temporalImage
                        ),
                        A.clone(e, this.editionProperties.temporalImage))
                      : 
                      this.editionProperties.temporalImage.parentElement.className == 'ck-math-tex' ? 
                      this.editionProperties.temporalImage.parentNode.parentNode.replaceChild(e, this.editionProperties.temporalImage.parentElement):
                      this.editionProperties.temporalImage.parentNode.replaceChild(e, this.editionProperties.temporalImage),
                      this.placeCaretAfterNode(e)
                      MathJax.Hub.Queue(["Typeset", MathJax.Hub, e.parentElement]);
                },
              },
              {
                key: "openModalDialog",
                value: function (e, t,isOpen=true) {
                  var n,
                    i = this;
                  this.editMode = "latex";
                  try {
                    if (t) {
                      e.contentWindow.focus();
                      var o = e.contentWindow.getSelection();
                      this.editionProperties.range = o.getRangeAt(0);
                    } else {
                      e.focus();
                      var r = getSelection();
                      this.editionProperties.range = r.getRangeAt(0);
                    }
                  } catch (e) {
                    this.editionProperties.range = null;
                  }
                  if (
                    (void 0 === t && (t = !0),
                    (this.editionProperties.latexRange = null),
                    e)
                  )
                    if (
                      (n =
                        void 0 !== this.integrationModel.getSelectedItem
                          ? this.integrationModel.getSelectedItem(e, t)
                          : w.getSelectedItem(e, t))
                    ) {
                      if (
                        !n.caretPosition &&
                        w.containsClass(n.node, l.get("imageClassName"))
                      )
                        (this.editionProperties.temporalImage = n.node),
                          (this.editionProperties.isNewElement = !1);
                      else if (3 === n.node.nodeType)
                        if (this.integrationModel.getMathmlFromTextNode) {
                          var s = this.integrationModel.getMathmlFromTextNode(
                            n.node,
                            n.caretPosition
                          );
                          s &&
                            ((this.editMode = "latex"),
                            (this.editionProperties.isNewElement = !1),
                            (this.editionProperties.temporalImage =
                              document.createElement("img")),
                            this.editionProperties.temporalImage.setAttribute(
                              l.get("imageMathmlAttribute"),
                              a.safeXmlEncode(s)
                            ));
                        } else {
                          var c = p.getLatexFromTextNode(n.node, n.caretPosition);
                          if (c) {
                            var u = p.getMathMLFromLatex(c.latex);
                            (this.editMode = "latex"),
                              (this.editionProperties.isNewElement = !1),
                              (this.editionProperties.temporalImage =
                                document.createElement("img")),
                              this.editionProperties.temporalImage.setAttribute(
                                l.get("imageMathmlAttribute"),
                                a.safeXmlEncode(u)
                              );
                            var d = t ? e.contentWindow : window;
                            if ("textarea" !== e.tagName.toLowerCase())
                              if (document.selection) {
                                for (
                                  var m = 0, f = c.startNode.previousSibling;
                                  f;
  
                                )
                                  (m += w.getNodeLength(f)),
                                    (f = f.previousSibling);
                                (this.editionProperties.latexRange =
                                  d.document.selection.createRange()),
                                  this.editionProperties.latexRange.moveToElementText(
                                    c.startNode.parentNode
                                  ),
                                  this.editionProperties.latexRange.move(
                                    "character",
                                    m + c.startPosition
                                  ),
                                  this.editionProperties.latexRange.moveEnd(
                                    "character",
                                    c.latex.length + 4
                                  );
                              } else
                                (this.editionProperties.latexRange =
                                  d.document.createRange()),
                                  this.editionProperties.latexRange.setStart(
                                    c.startNode,
                                    c.startPosition
                                  ),
                                  this.editionProperties.latexRange.setEnd(
                                    c.endNode,
                                    c.endPosition
                                  );
                          }
                        }
                    } else
                      "textarea" === e.tagName.toLowerCase() &&
                        (this.editMode = "latex");
                  for (
                    var g = l.get("editorAttributes").split(", "),
                      _ = {},
                      v = 0,
                      b = g.length;
                    v < b;
                    v += 1
                  ) {
                    var y = g[v].split("="),
                      x = y[0],
                      A = y[1];
                    _[x] = A;
                  }
                  var C = {},
                    M = l.get("editorParameters"),
                    k = this.integrationModel.editorParameters;
                  Object.assign(C, _, M),
                    Object.assign(C, _, k),
                    (C.language = this.language),
                    (C.rtl = this.integrationModel.rtl);
                  var E = {};
                  if (
                    ((E.editorAttributes = C),
                    (E.language = this.language),
                    (E.customEditors = this.customEditors),
                    (E.environment = this.environment),
                    null == this.modalDialog)
                  ) {
                    (this.modalDialog = new U(C)),
                      (this.contentManager = new j(E));
                    var T = h.newListener("onLoad", function () {
                      if (
                        ((i.contentManager.isNewElement =
                          i.editionProperties.isNewElement),
                        null != i.editionProperties.temporalImage)
                      ) {
                        var e = a.safeXmlDecode(
                          i.editionProperties.temporalImage.getAttribute(
                            l.get("imageMathmlAttribute")
                          )
                        );
                        i.contentManager.mathML = e;
                      }
                    });
                    this.contentManager.addListener(T),
                      this.contentManager.init(),
                      this.modalDialog.setContentManager(this.contentManager),
                      this.contentManager.setModalDialogInstance(
                        this.modalDialog
                      );
                  } else if (
                    ((this.contentManager.isNewElement =
                      this.editionProperties.isNewElement),
                    null != this.editionProperties.temporalImage)
                  ) {
                    var I = a.safeXmlDecode(
                      this.editionProperties.temporalImage.getAttribute(
                        l.get("imageMathmlAttribute")
                      )
                    );
                    this.contentManager.mathML = I;
                  }
                  this.contentManager.setIntegrationModel(this.integrationModel);
                  this.modalDialog.open();
                  !isOpen && this.modalDialog.close();
                },
              },
              {
                key: "getCustomEditors",
                value: function () {
                  return this.customEditors;
                },
              },
            ],
            [
              {
                key: "addGlobalListener",
                value: function (t) {
                  e.globalListeners.add(t);
                },
              },
              {
                key: "globalListeners",
                get: function () {
                  return e._globalListeners;
                },
                set: function (t) {
                  e._globalListeners = t;
                },
              },
              {
                key: "initialized",
                get: function () {
                  return e._initialized;
                },
                set: function (t) {
                  e._initialized = t;
                },
              },
            ]
          ),
          e
        );
      })();
      (J._globalListeners = new h()), (J._initialized = !1);
      function V(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      (window.wrs_addPluginListener = function (e) {
        var t, n;
        console.warn("Deprecated method"), (n = e[(t = Object.keys(e)[0])]);
        var i = h.newListener(t, n);
        J.addGlobalListener(i);
      }),
        (window.wrs_initParse = function (e, t) {
          return (
            console.warn("Deprecated method. Use Parser.endParse instead."),
            E.initParse(e, t)
          );
        }),
        (window.wrs_endParse = function (e, t, n) {
          return (
            console.warn("Deprecated method. Use Parser.endParse instead."),
            E.endParse(e, t, n)
          );
        });
      var Y = (function () {
        function e(t) {
          var n = this;
          if (
            ((function (e, t) {
              if (!(e instanceof t))
                throw new TypeError("Cannot call a class as a function");
            })(this, e),
            (this.language = "en"),
            (this.serviceProviderProperties = {}),
            "serviceProviderProperties" in t &&
              (this.serviceProviderProperties = t.serviceProviderProperties),
            (this.configurationService = ""),
            "configurationService" in t &&
              ((this.serviceProviderProperties.URI = t.configurationService),
              console.warn(
                "Deprecated property configurationService. Use serviceParameters on instead.",
                [t.configurationService]
              )),
            (this.version = "version" in t ? t.version : ""),
            (this.target = null),
            !("target" in t))
          )
            throw new Error(
              "IntegrationModel constructor error: target property missed."
            );
          (this.target = t.target),
            "scriptName" in t && (this.scriptName = t.scriptName),
            (this.callbackMethodArguments = {}),
            "callbackMethodArguments" in t &&
              (this.callbackMethodArguments = t.callbackMethodArguments),
            (this.environment = {}),
            "environment" in t && (this.environment = t.environment),
            (this.isIframe = !1),
            null != this.target &&
              (this.isIframe = "IFRAME" === this.target.tagName.toUpperCase()),
            (this.editorObject = null),
            "editorObject" in t && (this.editorObject = t.editorObject),
            (this.rtl = !1),
            "rtl" in t && (this.rtl = t.rtl),
            (this.managesLanguage = !1),
            "managesLanguage" in t && (this.managesLanguage = t.managesLanguage),
            (this.temporalImageResizing = !1),
            (this.core = null),
            (this.listeners = new h()),
            "integrationParameters" in t &&
              e.integrationParameters.forEach(function (e) {
                if (e in t.integrationParameters) {
                  var i = t.integrationParameters[e];
                  0 !== Object.keys(i).length && (n[e] = i);
                }
              });
        }
        return (
          (function (e, t, n) {
            t && V(e.prototype, t), n && V(e, n);
          })(e, [
            {
              key: "init",
              value: function () {
                var e = this;
                this.language = this.getLanguage();
                var t = h.newListener("onLoad", function () {
                  e.callbackFunction(e.callbackMethodArguments);
                });
                if (
                  -1 !==
                  this.serviceProviderProperties.URI.indexOf("configuration")
                ) {
                  var n = this.serviceProviderProperties.URI,
                    i = f.getServerLanguageFromService(n);
                  this.serviceProviderProperties.server = i;
                  var o =
                      this.serviceProviderProperties.URI.indexOf("configuration"),
                    r = this.serviceProviderProperties.URI.substring(0, o);
                  this.serviceProviderProperties.URI = r;
                }
                var a = this.serviceProviderProperties.URI;
                (a =
                  0 === a.indexOf("/") || 0 === a.indexOf("http")
                    ? a
                    : w.concatenateUrl(this.getPath(), a)),
                  (this.serviceProviderProperties.URI = a);
                var s = {};
                (s.serviceProviderProperties = this.serviceProviderProperties),
                  this.setCore(new J(s)),
                  this.core.addListener(t),
                  (this.core.language = this.language),
                  this.core.init(),
                  this.core.setEnvironment(this.environment);
              },
            },
            {
              key: "getPath",
              value: function () {
                if (void 0 === this.scriptName)
                  throw new Error("scriptName property needed for getPath.");
                for (
                  var e = document.getElementsByTagName("script"), t = "", n = 0;
                  n < e.length;
                  n += 1
                ) {
                  var i = e[n].src.lastIndexOf(this.scriptName);
                  i >= 0 && (t = e[n].src.substr(0, i - 1));
                }
                return t;
              },
            },
            {
              key: "setLanguage",
              value: function (e) {
                this.language = e;
              },
            },
            {
              key: "setCore",
              value: function (e) {
                (this.core = e), e.setIntegrationModel(this);
              },
            },
            {
              key: "getCore",
              value: function () {
                return this.core;
              },
            },
            {
              key: "setTarget",
              value: function (e) {
                (this.target = e),
                  (this.isIframe =
                    "IFRAME" === this.target.tagName.toUpperCase());
              },
            },
            {
              key: "setEditorObject",
              value: function (e) {
                this.editorObject = e;
              },
            },
            {
              key: "openNewFormulaEditor",
              value: function (isopen=true) {
                (this.core.editionProperties.isNewElement = !0),
                  this.core.openModalDialog(this.target, this.isIframe, isopen);
              },
            },
            {
              key: "openExistingFormulaEditor",
              value: function () {
                (this.core.editionProperties.isNewElement = !1),
                  this.core.openModalDialog(this.target, this.isIframe);
              },
            },
            {
              key: "updateFormula",
              value: function (e) {
                var t, n;
                this.editorParameters &&
                  (e = com.wiris.editor.util.EditorUtils.addAnnotation(
                    e,
                    "application/vnd.wiris.mtweb-params+json",
                    JSON.stringify(this.editorParameters)
                  ));
                this.isIframe
                  ? ((t = this.target.contentWindow),
                    (n = this.target.contentWindow))
                  : ((t = this.target), (n = window));
                var i = this.core.beforeUpdateFormula(e, null);
                return i &&
                  (i = this.insertFormula(t, n, i.mathml, i.wirisProperties))
                  ? this.core.afterUpdateFormula(
                      i.focusElement,
                      i.windowTarget,
                      i.node,
                      i.latex
                    )
                  : "";
              },
            },
            {
              key: "insertFormula",
              value: function (e, t, n, i) {
                return this.core.insertFormula(e, t, n, i);
              },
            },
            {
              key: "getSelection",
              value: function () {
                return this.isIframe
                  ? (this.target.contentWindow.focus(),
                    this.target.contentWindow.getSelection())
                  : (this.target.focus(), window.getSelection());
              },
            },
            {
              key: "addEvents",
              value: function () {
                var e = this,
                  t = this.isIframe
                    ? this.target.contentWindow.document
                    : this.target;
                w.addElementEvents(
                  t,
                  function (t, n) {
                    e.doubleClickHandler(t, n);
                  },
                  function (t, n) {
                    e.mousedownHandler(t, n);
                  },
                  function (t, n) {
                    e.mouseupHandler(t, n);
                  }
                );
              },
            },
            {
              key: "doubleClickHandler",
              value: function (e) {
                if ("img" === e.nodeName.toLowerCase()) {
                  this.core.getCustomEditors().disable();
                  var t = l.get("imageCustomEditorName");
                  if (e.hasAttribute(t)) {
                    var n = e.getAttribute(t);
                    this.core.getCustomEditors().enable(n);
                  }
                  w.containsClass(e, l.get("imageClassName")) &&
                    ((this.core.editionProperties.temporalImage = e),
                    (this.core.editionProperties.isNewElement = !0),
                    this.openExistingFormulaEditor());
                }
              },
            },
            {
              key: "mouseupHandler",
              value: function () {
                var e = this;
                this.temporalImageResizing &&
                  setTimeout(function () {
                    A.fixAfterResize(e.temporalImageResizing);
                  }, 10);
              },
            },
            {
              key: "mousedownHandler",
              value: function (e) {
                "img" === e.nodeName.toLowerCase() &&
                  w.containsClass(e, l.get("imageClassName")) &&
                  (this.temporalImageResizing = e);
              },
            },
            {
              key: "getLanguage",
              value: function () {
                return this.getBrowserLanguage();
              },
            },
            {
              key: "getBrowserLanguage",
              value: function () {
                return navigator.userLanguage
                  ? navigator.userLanguage.substring(0, 2)
                  : navigator.language
                  ? navigator.language.substring(0, 2)
                  : "en";
              },
            },
            {
              key: "callbackFunction",
              value: function () {
                var e = this,
                  t = h.newListener("onTargetReady", function () {
                    e.addEvents(e.target);
                  });
                this.listeners.add(t);
              },
            },
            { key: "notifyWindowClosed", value: function () {} },
            { key: "getMathmlFromTextNode", value: function (e, t) {} },
            { key: "fillNonLatexNode", value: function (e, t, n) {} },
            { key: "getSelectedItem", value: function (e, t) {} },
          ]),
          e
        );
      })();
      function K(e) {
        return (K =
          "function" == typeof Symbol && "symbol" == typeof Symbol.iterator
            ? function (e) {
                return typeof e;
              }
            : function (e) {
                return e &&
                  "function" == typeof Symbol &&
                  e.constructor === Symbol &&
                  e !== Symbol.prototype
                  ? "symbol"
                  : typeof e;
              })(e);
      }
      function Q(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      function Z(e, t) {
        return !t || ("object" !== K(t) && "function" != typeof t)
          ? (function (e) {
              if (void 0 === e)
                throw new ReferenceError(
                  "this hasn't been initialised - super() hasn't been called"
                );
              return e;
            })(e)
          : t;
      }
      function q(e, t, n) {
        return (q =
          "undefined" != typeof Reflect && Reflect.get
            ? Reflect.get
            : function (e, t, n) {
                var i = (function (e, t) {
                  for (
                    ;
                    !Object.prototype.hasOwnProperty.call(e, t) &&
                    null !== (e = G(e));
  
                  );
                  return e;
                })(e, t);
                if (i) {
                  var o = Object.getOwnPropertyDescriptor(i, t);
                  return o.get ? o.get.call(n) : o.value;
                }
              })(e, t, n || e);
      }
      function G(e) {
        return (G = Object.setPrototypeOf
          ? Object.getPrototypeOf
          : function (e) {
              return e.__proto__ || Object.getPrototypeOf(e);
            })(e);
      }
      function $(e, t) {
        return ($ =
          Object.setPrototypeOf ||
          function (e, t) {
            return (e.__proto__ = t), e;
          })(e, t);
      }
      (Y.prototype.getMathmlFromTextNode = void 0),
        (Y.prototype.fillNonLatexNode = void 0),
        (Y.prototype.getSelectedItem = void 0),
        (Y.integrationParameters = [
          "serviceProviderProperties",
          "editorParameters",
        ]);
      var ee = (function (e) {
        function t(e) {
          var n;
          return (
            (function (e, t) {
              if (!(e instanceof t))
                throw new TypeError("Cannot call a class as a function");
            })(this, t),
            ((n = Z(this, G(t).call(this, e))).integrationFolderName =
              "ckeditor_wiris"),
            n
          );
        }
        return (
          (function (e, t) {
            if ("function" != typeof t && null !== t)
              throw new TypeError(
                "Super expression must either be null or a function"
              );
            (e.prototype = Object.create(t && t.prototype, {
              constructor: { value: e, writable: !0, configurable: !0 },
            })),
              t && $(e, t);
          })(t, Y),
          (function (e, t, n) {
            t && Q(e.prototype, t), n && Q(e, n);
          })(t, [
            {
              key: "init",
              value: function () {
                q(G(t.prototype), "init", this).call(this);
                var e = this.editorObject;
                "wiriseditorparameters" in e.config &&
                  l.update("editorParameters", e.config.wiriseditorparameters);
              },
            },
            {
              key: "getLanguage",
              value: function () {
                return this.editorObject.langCode;
              },
            },
            {
              key: "getPath",
              value: function () {
                return this.editorObject.plugins.ckeditor_wiris.path;
              },
            },
            {
              key: "addEditorListeners",
              value: function () {
                var e = this.editorObject;
                void 0 !== e.config.wirislistenersdisabled &&
                e.config.wirislistenersdisabled
                  ? (e.on(
                      "instanceReady",
                      function (e) {
                        this.checkElement();
                      }.bind(this)
                    ),
                    e.resetDirty())
                  : (e.setData(E.initParse(e.getData())),
                    e.on("focus", function (e) {
                      WirisPlugin.currentInstance =
                        WirisPlugin.instances[e.editor.name];
                    }),
                    e.on(
                      "contentDom",
                      function () {
                        e.on(
                          "doubleclick",
                          function (e) {
                            (("img" == e.data.element.$.nodeName.toLowerCase() &&
                              w.containsClass(
                                e.data.element.$,
                                l.get("imageClassName")
                              )) ||
                              w.containsClass(
                                e.data.element.$,
                                l.get("CASClassName")
                              )) &&
                              (e.data.dialog = null);
                          }.bind(this)
                        );
                      }.bind(this)
                    ),
                    e.on(
                      "setData",
                      function (e) {
                        e.data.dataValue = E.initParse(e.data.dataValue || "");
                      }.bind(this)
                    ),
                    e.on(
                      "afterSetData",
                      function (e) {
                        void 0 !== E.observer &&
                          Array.prototype.forEach.call(
                            document.getElementsByClassName("Wirisformula"),
                            function (e) {
                              E.observer.observe(e);
                            }
                          );
                      }.bind(this)
                    ),
                    e.on(
                      "getData",
                      function (e) {
                        e.data.dataValue = E.endParse(e.data.dataValue || "");
                      }.bind(this)
                    ),
                    e.on(
                      "mode",
                      function (e) {
                        this.checkElement();
                      }.bind(this)
                    ),
                    this.checkElement());
              },
            },
            {
              key: "checkElement",
              value: function () {
                var e,
                  t = this.editorObject,
                  n = document.getElementById("cke_contents_" + t.name)
                    ? document.getElementById("cke_contents_" + t.name)
                    : document.getElementById("cke_" + t.name),
                  i = !1;
                if (
                  !(e =
                    t.elementMode == CKEDITOR.ELEMENT_MODE_INLINE
                      ? t.container.$
                      : n.getElementsByTagName("iframe")[0])
                ) {
                  var o;
                  for (var r in n.classList) {
                    var a = n.classList[r];
                    if (-1 != a.search("cke_\\d")) {
                      o = a;
                      break;
                    }
                  }
                  o && ((e = document.getElementById(o + "_contents")), (i = !0));
                }
                if (!e.wirisActive)
                  if (t.elementMode === CKEDITOR.ELEMENT_MODE_INLINE) {
                    if ("TEXTAREA" === e.tagName) {
                      var s = document.getElementsByClassName(
                        "cke_textarea_inline"
                      );
                      Array.prototype.forEach.call(s, function (e) {
                        this.setTarget(e), this.addEvents();
                      });
                    } else this.setTarget(e), this.addEvents();
                    e.wirisActive = !0;
                  } else
                    (e.contentWindow || i) &&
                      (this.setTarget(e), this.addEvents(), (e.wirisActive = !0));
              },
            },
            {
              key: "doubleClickHandler",
              value: function (e, t) {
                if (
                  "img" == e.nodeName.toLowerCase() &&
                  w.containsClass(e, l.get("imageClassName"))
                ) {
                  void 0 !== t.stopPropagation
                    ? t.stopPropagation()
                    : (t.returnValue = !1),
                    this.core.getCustomEditors().disable();
                  var n = e.getAttribute(l.get("imageCustomEditorName"));
                  n && this.core.getCustomEditors().enable(n),
                    (this.core.editionProperties.temporalImage = e),
                    this.openExistingFormulaEditor();
                }else if("span" == e.nodeName.toLowerCase() && e.isMathJax){
                  this.editMode = "latex";
                  var _d = e;
                  while (_d.className.indexOf("MathJax")<0) {
                      _d = _d.parentElement
                  }
                  void 0 !== t.stopPropagation ? t.stopPropagation() : t.returnValue = !1,
                  this.core.getCustomEditors().disable();
                  var n = _d.getAttribute(l.get("imageCustomEditorName"));
                  n && this.core.getCustomEditors().enable(n),
                  this.core.editionProperties.temporalImage = _d,
                  this.openExistingFormulaEditor()
              }
              },
            },
            {
              key: "getCorePath",
              value: function () {
                return CKEDITOR.plugins.getPath(this.integrationFolderName);
              },
            },
            {
              key: "getSelection",
              value: function () {
                return (
                  this.editorObject.editable().$.focus(),
                  this.editorObject.getSelection().getNative()
                );
              },
            },
            {
              key: "callbackFunction",
              value: function () {
                q(G(t.prototype), "callbackFunction", this).call(this),
                  this.addEditorListeners();
              },
            },
          ]),
          t
        );
      })();
      function te(e, t) {
        for (var n = 0; n < t.length; n++) {
          var i = t[n];
          (i.enumerable = i.enumerable || !1),
            (i.configurable = !0),
            "value" in i && (i.writable = !0),
            Object.defineProperty(e, i.key, i);
        }
      }
      CKEDITOR.plugins.add("ckeditor_wiris", {
        init: function (e) {
          e.ui.addButton("ckeditor_wiris_formulaEditor", {
            label: "Insert a math equation - MathType",
            command: "ckeditor_wiris_openFormulaEditor",
            icon:
              CKEDITOR.plugins.getPath("ckeditor_wiris") + "./icons/formula.png",
          }),
            e.ui.addButton("ckeditor_wiris_formulaEditorChemistry", {
              label: "Insert a chemistry formula - ChemType",
              command: "ckeditor_wiris_openFormulaEditorChemistry",
              icon:
                CKEDITOR.plugins.getPath("ckeditor_wiris") + "./icons/chem.png",
            });
          var t = "img[align,";
          (t += l.get("imageMathmlAttribute")),
            (t += ",src,alt](!Wirisformula)"),
            e.addCommand("ckeditor_wiris_openFormulaEditor", {
              async: !1,
              canUndo: !0,
              editorFocus: !0,
              allowedContent: t,
              requiredContent: t,
              exec: function (e) {
                var t = WirisPlugin.instances[e.name];
                t.core.getCustomEditors().disable(), t.openNewFormulaEditor();
              },
            }),
            e.addCommand("ckeditor_wiris_openFormulaEditorChemistry", {
              async: !1,
              canUndo: !0,
              editorFocus: !0,
              allowedContent: t,
              requiredContent: t,
              exec: function (e) {
                var t = WirisPlugin.instances[e.name];
                t.core.getCustomEditors().enable("chemistry"),
                  t.openNewFormulaEditor();
              },
            }),
            e.on("instanceReady", function () {
              var t = {};
              (t.editorObject = e),
                (t.target = e.container.$.querySelector("*[class^=cke_wysiwyg]")),
                (t.serviceProviderProperties = {}),
                (t.serviceProviderProperties.URI =
                  "https://kklservice.iclass30.com/pluginwiris_engine/app"),
                (t.serviceProviderProperties.server = "java"),
                (t.version = "7.16.1.1426"),
                (t.scriptName = "plugin.js"),
                (t.langFolderName = "languages"),
                (t.environment = {}),
                (t.environment.editor = "CKEditor4"),
                "wiriscontextpath" in e.config &&
                  ((t.configurationService =
                    e.config.wiriscontextpath + t.configurationService),
                  console.warn(
                    "Deprecated property wiriscontextpath. Use mathTypeParameters on instead.",
                    e.config.wiriscontextpath
                  )),
                "mathTypeParameters" in e.config &&
                  (t.integrationParameters = e.config.mathTypeParameters);
              var n = new ee(t);
              n.init(),
                n.listeners.fire("onTargetReady", {}),
                (WirisPlugin.instances[e.name] = n),
                (WirisPlugin.currentInstance = n);
            });
        },
      });
      var ne = (function () {
        function e() {
          !(function (e, t) {
            if (!(e instanceof t))
              throw new TypeError("Cannot call a class as a function");
          })(this, e);
        }
        return (
          (function (e, t, n) {
            t && te(e.prototype, t), n && te(e, n);
          })(e, null, [
            {
              key: "init",
              value: function () {
                e.testServices();
              },
            },
            {
              key: "testServices",
              value: function () {
                var e;
                console.log("Testing configuration service..."),
                  console.log(f.getService("configurationjs", "", "get")),
                  console.log("Testing showimage service..."),
                  ((e = []).mml =
                    '<math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>x</mi><mn>2</mn></msup></math>'),
                  console.log(f.getService("showimage", e)),
                  console.log("Testing createimage service..."),
                  ((e = []).mml =
                    '<math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>x</mi><mn>2</mn></msup></math>'),
                  console.log(f.getService("createimage", e, "post")),
                  console.log("Testing MathML2Latex service..."),
                  ((e = []).service = "mathml2latex"),
                  (e.mml =
                    '<math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>x</mi><mn>2</mn></msup></math>'),
                  console.log(f.getService("service", e)),
                  console.log("Testing Latex2MathML service..."),
                  ((e = []).service = "latex2mathml"),
                  (e.latex = "x^2"),
                  console.log(f.getService("service", e)),
                  console.log("Testing Mathml2Accesible service..."),
                  ((e = []).service = "mathml2accessible"),
                  (e.mml =
                    '<math xmlns="http://www.w3.org/1998/Math/MathML"><msup><mi>x</mi><mn>2</mn></msup></math>'),
                  console.log(f.getService("service", e));
              },
            },
          ]),
          e
        );
      })();
      window.WirisPlugin = {
        Core: J,
        Parser: E,
        Image: A,
        Util: w,
        Configuration: l,
        Listeners: h,
        IntegrationModel: Y,
        currentInstance: null,
        instances: {},
        CKEditor4Integration: ee,
        Latex: p,
        Test: ne,
      };
    },
  ]);
  
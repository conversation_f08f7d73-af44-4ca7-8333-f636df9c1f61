import { API } from "../index";
const SYURL =
  process.env.NODE_ENV == "development"
    ? "https://test.xckj.net:9391"
    : "https://www.syjy.cn";
const PXURL = process.env.VUE_APP_PXURL
  ? "https://pxtest.iclass30.com/gatewayApi"
  : "https://px.iclass30.com/gatewayApi";

/****************************** user *****************************************/
/**
 * @name 获取Authorization
 **/
export const loginByTokenAPI = (params: any) => {
  return API.GET(
    "/user/portal/loginByToken",
    params,
    process.env.VUE_APP_PXURL
  );
};

/****************************** website *****************************************/
/**
 * @name 获取省市区
 **/
export const getRegionAPI = (params: { type: number; id: string }) => {
  return API.GET("/website/wisdom/getRegion", params);
};

/****************************** operag *****************************************/
/**
 * @name: 获取用户身份角色
 * @param {params} 接口参数
 * @userId 用户id
 */
export const getUserRoleIdList = (params: { userId: string }) => {
  return API.GET("/operag/operator/getUserRoleIdList", params);
};

/****************************** platformManage *****************************************/
/**
 * @name 根据域名获取平台信息
 **/
export const getPlatFormInfoByPlatformDomainAPI = (params: {
  platformDomain: string;
}) => {
  return API.POST("/platformManage/getPlatFormInfoByPlatformDomain", params);
};
/**
 * @name: 获取区域学校
 */
export const querySchoolPlatformListAPI = (params: object) => {
  return API.GET("/platformManage/querySchoolPlatformList", params);
};

/****************************** webconfig *****************************************/
/**
 * @name 根据domainName获取布局json内容
 **/
export const getContentByDomainAPI = (params: object) => {
  return API.GET("/webconfig/website/getContentByDomain", params);
};

/****************************** xcoffice *****************************************/
/**
 * @name 获取学校
 **/
export const getUnitListAPI = (params: {}) => {
  return API.GET("/xcoffice/api/scanLogin/getUnitList", params, SYURL);
};
/**
 * @name  获取二维码地址
 * @param type 类型
 * @param domainId 学校id
 **/
export const getQrCodeAPI = (params: { type: string; domainId: string }) => {
  return API.GET("/xcoffice/api/scanLogin/getQrCode", params, SYURL);
};
/**
 * @name 获取二维码状态
 * @param scanId 二维码id
 **/
export const getScanStatusAPI = (params: { scanId: string }) => {
  return API.GET("/xcoffice/api/scanLogin/getScanStatus", params, SYURL);
};

/****************************** base *****************************************/
/**
 * @name 【注册】[第四步]登录
 **/
export const loginByMobileCodeAPI = (params: object) => {
  return API.GET("/base/baselogin/loginByMobileCode", params);
};
/**
 * @name 管理员登录
 **/
export const adminLoginAPI = (params: {
  account: string;
  passWord: string;
  logintype: string;
  adminTypes: string;
}) => {
  return API.GET("/base/baselogin/adminLogin", params);
};

/**
 * @name 教师登录，不包含校管
 **/
export const teacherLoginAPI = (params: {
  account: string;
  passWord: string;
  logintype: string;
}) => {
  return API.GET("/base/baselogin/teacherLogin", params);
};

/****************************** userMgr *****************************************/
/**
 * @name: 获取用户信息
 * @param {params} 接口参数
 * @userId 用户id
 */
export const getUserInfoAPI = (params: { userId: string }) => {
  return API.POST("/userMgr/baselogin/getLoginUserInfo", params);
};
/**
 * @name: 根据获取教师的带课班级
 * @param {params} 接口参数ApI
 * @userId 用户id
 */
export const getClassListBySubjectId = (params: { userId: string }) => {
  return API.GET("/userMgr/baseclass/getClassListBySubjectId", params);
};
/**
 * @name: 获取用户信息
 * @param {params} 接口参数
 * @key 字段名(和数据库一样，例如（user_name，mobile，email)
 * @keyvalue 字段值
 * @userType 用户类型：1：教师2：学生 3：家长 0：注册用户 4：代理商 5:运营 6:区域用户
 */
export const getUserAPI = (params: {
  key: string;
  keyvalue: string;
  userType: string;
}) => {
  return API.POST("/userMgr/baselogin/getUser", params);
};
/**
 * @name: 年级-根据学校ID获取年级列表
 * @param {params} 接口参数
 * @userId schoolId 学校id
 */
export const getGradeArrayBySchoolId = (params: { schoolId: string }) => {
  return API.GET("/userMgr/basegrade/getSchoolGrade", params);
};
/**
 * @name: 年级-根据学校ID获取学科列表
 * @param {params} 接口参数
 * @userId schoolId 学校id
 */
export const getSubjectBySchoolId = (params: { schoolId: string }) => {
  return API.GET("/userMgr/basesubject/getSubject", params);
};
/**
 * @name: 绑定快捷登录账号
 */
export const bindQuickLoginAPI = (params: object) => {
  return API.POST("/userMgr/loginoauth/bindQuickLogin", params);
};
/**
 * @name: 获取腾讯验证码应用id
 */
export const findAppId = (params: object) => {
  return API.POST("/userMgr/verificationCode/getAppId", params);
};
/**
 * @name: 【注册】[第一步]获取安全码
 */
export const findsafetyid = (params: object) => {
  return API.GET("/userMgr/register/getsafetyid", params);
};
/**
 * @name: 【注册】[第二步]获取短信验证码
 */
export const findsmscodebymobile = (params: object) => {
  return API.GET("/userMgr/register/getNewSMSCodeByMobile", params);
};
/**
 * @name: 【注册】[第三步]校验短信验证码
 */
export const checkMobileCodeAPI = (params: object) => {
  return API.GET("/userMgr/register/checkMobileCode", params);
};
/**
 * @name 上虞账号密码登录
 * @param username 账号
 * @param password 密码
 **/
export const shangYuLoginAPI = (params: object) => {
  return API.GET("/userMgr/baselogin/shangYuLogin", params);
};
/**
 * @name: 平台登录
 */
export const platformLoginAPI = (params: object) => {
  return API.POST("/base/baselogin/platformLogin", params);
};

/**
 * @name: 普通登录
 */
export const loginAPI = (params: object) => {
  return API.POST("/base/baselogin/login", params);
};
/**
 * @name: 获取相同手机号账号
 */
export const getSameAccountAPI = (params: object) => {
  return API.GET("/userMgr/baselogin/getSameAccount", params);
};
/**
 * @name 上虞钉钉登录
 * @param ddId 钉钉id
 **/
export const shangYuDingScanCodeLoginAPI = (params: { ddId: string }) => {
  return API.GET("/userMgr/baselogin/shangYuDingScanCodeLogin", params);
};
/**
 * @name 【字典】获取字典列表
 **/
export const getDictListAPI = (params: {
  typecode: number | string;
  keyWord?: string;
  page?: number;
  limit?: number;
}) => {
  return API.POST("/userMgr/basedict/getDictList", params);
};
/**
 * @name 获取所有年级列表
 **/
export const getAllGradeListAPI = (params: object) => {
  return API.GET("/userMgr/basegrade/getAllGradeList", params);
};
export const userListAPI = (params: object) => {
  return API.GET("/userMgr/baselogin/userList", params);
};
/**
 * @name 根据学段获取学科
 **/
export const getSubjectListByPhaseAPI = (params: {
  phase?: string;
  schoolId?: string;
}) => {
  return API.GET("/userMgr/basesubject/getSubjectListByPhase", params);
};
/**
 * @name 获取部门列表
 **/
export const getDepartmentListAPI = (params: object) => {
  return API.GET("/userMgr/baseDepartment/getDepartmentList", params);
};
/**
 * @name 获取部门用户列表
 **/
export const getDepartmentUserListAPI = (params: object) => {
  return API.GET("/userMgr/baseDepartmentUser/getDepartmentUserList", params);
};
/**
 * @name 获取区域教师列表(支持学科 、 年级筛选 、 支持区域查全部学校老师 )
 **/
export const getRegionTeacherList = (params: object) => {
  return API.GET("/userMgr/baselogin/getRegionTeacherList", params);
};
/**
 * @name 获取教师列表(支持学科 、 年级筛选)
 **/
export const getTeacherListSupportSubjectAndGradeAPI = (params: object) => {
  return API.GET(
    "/userMgr/baselogin/getTeacherListSupportSubjectAndGrade",
    params
  );
};
/**
 * @name 获取教研管理组列表（教研组，备课组）
 * @param keyWord 组名称关键字（非必传）
 * @param schoolId 学校ID（必传）
 * @param phase 学段（非必传）
 * @param type 教研类型：1：教研组2：备课组，默认为1（非必传）
 * @param parentId 父级id，type=2查询教研组下的备课组时传教研组id（非必传）
 * @param page 当前页（必传）
 * @param limit 每页多少条（必传）
 * @param subjectId 学科id（非必传）
 **/
export const getTeachGroupListAPI = (params: {
  keyWord?: string;
  schoolId: string;
  phase?: string;
  type?: string;
  parentId?: string;
  page: number;
  limit: number;
  subjectId?: string;
}) => {
  return API.GET('/userMgr/teachManage/getTeachGroupList', params);
};
/**
 * @name: 根据学校id获取学校信息
 */
export const getSchoolByIdAPI = (params: object) => {
  return API.GET('/userMgr/baseschool/getSchoolById', params);
};

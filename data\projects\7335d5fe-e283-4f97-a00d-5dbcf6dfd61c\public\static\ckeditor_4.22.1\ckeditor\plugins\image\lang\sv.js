/*
Copyright (c) 2003-2023, CKSource Holding sp. z o.o. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'image', 'sv', {
	alt: 'Alternativ text',
	border: 'Kant',
	btnUpload: 'Skicka till server',
	button2Img: 'Vill du omvandla den valda bildknappen på en enkel bild?',
	hSpace: 'Horis. marginal',
	img2Button: 'Vill du omvandla den valda bildknappen på en enkel bild?',
	infoTab: 'Bildinformation',
	linkTab: 'Länk',
	lockRatio: 'L<PERSON>s höjd/bredd förhållanden',
	menu: 'Bildegenskaper',
	resetSize: '<PERSON>terställ storlek',
	title: 'Bildegenskaper',
	titleButton: 'Egenskaper för bildknapp',
	upload: 'Ladda upp',
	urlMissing: 'Bildkällans URL saknas.',
	vSpace: 'Vert. marginal',
	validateBorder: 'Kantlinje måste vara ett heltal.',
	validateHSpace: 'HSpace måste vara ett heltal.',
	validateVSpace: 'VSpace måste vara ett heltal.'
} );

<template>
  <div ref="quesCardRef" class="q-opt card card--fill is-static-ques" :id="qId" :data-typeid="item.typeId"
    :style="{ fontSize: Paper.fontSize, fontFamily: Paper.fontFamily }"
    :class="{ onlycard: isOnlyCard, web: !isHandCorrent, hand: isHandCorrent, over: isOver, 'layout-a33': isA33Layout, 'is-small-mixin': isMixinSmallQues }">

    <!-- 调整编辑工具 -->
    <div v-drag class="edit-button-tools btn-tool nosave">
      <div class="btn-lineheight">
        <span class="label">行高(mm)</span>
        <el-input-number class="btn-change-linenum" @change="handleLineHeightChange" v-model="item.fillDistance" :min="3"
          :max="30" size="small" />
      </div>
      <div class="add-picture click-element" title="添加图片">
        <el-upload multiple accept="image/png, image/jpeg" action="#" :show-file-list="false"
          :http-request="handleHttpUpload">
          <el-icon>
            <Picture />
          </el-icon>
        </el-upload>
      </div>
      <div class="split click-element" @click="splitSubjectiveQuestions" title="点击拆分"></div>
      <div class="edit  click-element" @click="editQues"></div>
    </div>

    <div class="ques-content" :class="{ 'is-small-mixin': isMixinSmallQues }" ref="qWrapRef">
      <!-- 调整高度工具 -->
      <object-height-tool class="object-height-tools" ref="objectHeightToolRef" :qId="qId" :splitNodes="splitNodes"
        :refreshKey="refreshKey" @setQuesCardHeight="setQuesCardHeight" v-if="isOnlyCard"></object-height-tool>

      <!-- 大题标题 -->
      <bigques-name :item="item" v-if="showBigTitle"></bigques-name>

      <!-- 这个div很重要，隔离分段 -->
      <div class="splitnode-container">
        <template v-for="(spItem, spIndex) in splitNodes" :key="spItem.id">
          <div class="ques-box-container" :data-index="spIndex" :style="{ width: spItem.width }"
            v-if="spItem.type === 'ques'">
            <!-- 混合题小问标题 -->
            <div class="ques-content-name--small" contenteditable="true"
              v-if="spIndex === 0 && isMixinSmallQues && questionCard.showName === '1' && !item.hideSmallName">
              <span v-if="item.quesFullName">{{ item.quesFullName }}</span>
              <span v-else>{{ item.quesName }}.({{ item.score }}分)</span>
            </div>

            <div class="ques-box-wrap split-card" :class="{ 'no-items': spItem.data && !spItem.data.length }"
              :data-id="spItem.id" :data-index="spIndex" :style="{ height: isOnlyCard ? spItem.height : 'auto' }">

              <!-- 填空题 -->
              <div class="ques-box">
                <div class="water-mark" v-if="questionCard.typeId == QUES_TYPE.fillEva">
                  填空题智能批改
                </div>
                <template v-for="( subItem, subIndex ) in  spItem.data || fillList " :key="subItem.id">
                  <!-- mixinMode：过滤掉混合模式的父级题目 -->
                  <!-- 拆分线 -->
                  <div class="split-line" @click="mergeSubjectiveQuestions(subItem.id)" title="点击合并"
                    v-if="subItem.mixinMode && subItem.isSplited && subIndex > 0">
                  </div>

                  <template v-if="!subItem.mixinMode">
                    <div class="split-line" @click="mergeSubjectiveQuestions(subItem.id)" title="点击合并"
                      v-if="!subItem.parentId && subItem.isSplited && subIndex > 0">
                    </div>
                    <template v-for="( line, lineIndex ) in  subItem.lineList" :key="line.id">
                      <!-- 换行线：不可见 -->
                      <div class="split-line split-line--linefeed" v-if="line.isLineFeed"></div>

                      <!-- 填空题控件 -->
                      <div class="ques-item-wrap ques-item-wrap--fill split-tag" :class="`tag-${subIndex}-${lineIndex}`"
                        :data-index="subIndex">
                        <div class="ques-item" :class="`fill-item-${index}`"
                          :style="{ lineHeight: item.fillDistance + 'mm' }" :qid="subItem.id">
                          <div class="fill-item">
                            <div class="ques-sort middle-helper" :class="{ hidden: lineIndex !== 0 }"
                              contenteditable="true">
                              <span>
                                {{ (!subItem.parentId ? subItem.quesNos : (subItem.quesName || subItem.quesNos)) || '&nbsp;' }}
                              </span>
                            </div>
                            <div class="score-opts-list"
                              v-if="isGridPlaceShow(subItem.gridPlace || item.gridPlace, 1) && Number(subItem.score) > 0">
                              <mark-score :typeId="item.typeId" :judgeType="subItem.judgeType || item.judgeType"
                                :mark-type="subItem.markType || item.markType" :score="Number(subItem.score)"
                                :step="subItem.step || item.step" v-if="lineIndex === 0" />
                            </div>
                          </div>

                          <fill-line @keyup="handleKeyUpImage" @click="handleImageSelect" @updateContent="syncLineList"
                            @changeLineNum="handleChangeLineNum" @changeLineWidth="syncLineList"
                            @setLineFeed="syncLineList" @pasteImage="handleHttpUpload" :ques="subItem" :index="lineIndex"
                            :line="line" :isFillEva="item.typeId == QUES_TYPE.fillEva" :lineHeight="item.fillDistance" />

                          <div class="score-opts-list"
                            v-if="isGridPlaceShow(subItem.gridPlace || item.gridPlace, 2) && Number(subItem.score) > 0">
                            <mark-score :typeId="item.typeId" :mark-type="subItem.markType || item.markType"
                              :score="subItem.score" :step="subItem.step || item.step"
                              v-if="lineIndex === subItem.lineList.length - 1" />
                          </div>
                        </div>
                      </div>
                    </template>

                  </template>
                </template>
              </div>
            </div>
          </div>

          <!-- 分页底部空白区域 -->
          <div class="page-splitor" :data-id="spItem.id" :style="{ height: spItem.height }" v-else>
          </div>
        </template>
      </div>

      <!-- 图片展示区 -->
      <div class="imglist-content ques-box" contenteditable="true" ref="imglistContentRef" @keyup="handleKeyUpImage"
        @click="handleImageSelect" v-html="item.editContent">
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  onMounted,
  ref,
  nextTick,
  computed,
  onBeforeUnmount,
  watch,
} from 'vue';
import Paper from '@/views/paper';
import {
  DECIMAL_TYPE,
  GRIDPLACE_TYPE,
  GRID_TYPE,
  JUDGE_TYPE,
  MARK_TYPE,
  QUES_TYPE,
  IPAGELAYOUT,
  LINE_WIDTH,
  ICorrectType,
  ICARDMODEL,
} from '@/typings/card';
import { deepClone, generateUUID, sleep } from '@/utils/util';
import bus from '@/utils/bus';
import { SplitNodes } from '@/typings/card';
import { getNameElement, pxConversionMm } from '@/utils/dom';
import { pageHeight, footerKeepHeight, getHeaderInfoHeight } from '../../utils/config';
import MarkScore from '../MarkScore.vue';
import FillLine from './FillLine.vue';
import ObjectHeightTool from './ObjectHeightTool.vue';
import BigquesName from './BigquesName.vue';
import paper from '@/views/paper';
import { UploadRequestOptions } from 'element-plus';
import { ajaxUpload } from 'element-plus/es/components/upload/src/ajax';
import { PaperConstant } from '@/views/paper.constant';

export default defineComponent({
  props: {
    qId: {
      type: String,
      default: '',
    },
    // 题干信息
    item: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 题目下标
    index: {
      type: Number,
      default: 0,
    },
    // 大题目下标
    bigIndex: {
      type: Number,
      default: 0,
    },
  },
  emits: ['updateList'],
  components: {
    MarkScore,
    FillLine,
    ObjectHeightTool,
    BigquesName,
  },
  setup(props: any, ctx: any) {
    const state = reactive({
      uid: generateUUID(),
      // uid: props.qId,
      QUES_TYPE: QUES_TYPE,
      GRIDPLACE_TYPE: GRIDPLACE_TYPE,
      JUDGE_TYPE: JUDGE_TYPE,
      MARK_TYPE: MARK_TYPE,
      GRID_TYPE: GRID_TYPE,
      DECIMAL_TYPE: DECIMAL_TYPE,
      ICorrectType: ICorrectType,
      LINE_WIDTH: LINE_WIDTH,
      Paper: Paper,
      isOver: false,
      isFocus: false,
      isEdit: true,
      wrapHeight: 0,
      isObj: false,
      az: '0ABCDEFGHIJKLMNOPQRSTUVWXYZ',
      height: '' as any,
      // 是否混合题的小问
      isMixinSmallQues: !!props.item.parentId,
      isOnlyCard: Paper.cardType != ICARDMODEL.QUESCARD,
      // 是否手阅模式
      isHandCorrent: Paper.correctType == ICorrectType.HAND,
      // 是否A33栏
      isA33Layout: false,
      // 客观题是否合并
      isObjectiveMerged: Paper.mergeQues,
      // 填空题列表
      fillList: props.item.mixinMode ? props.item.data : props.item.data[0].data || props.item.data,
      // 拆分节点，type:'ques'|'blank'
      splitNodes: [{ id: generateUUID(), type: 'ques', height: 'auto' }] as SplitNodes[],
      mixinMode: props.item.mixinMode,
      // 线条需要强制渲染
      needForceRender: false,
      // 文字最大行高
      maxWordLineHeight: 7,

      // 强制刷新key
      refreshKey: 0,
    });

    watch(
      () => props.item.data,
      () => {
        updateFillList();
      }
    );

    const updateFillList = async () => {
      state.fillList = props.item.mixinMode
        ? props.item.data
        : props.item.data[0].data || props.item.data;
      if (state.splitNodes.length === 1) {
        state.splitNodes[0].data = state.fillList;
      }

      let firstNode = state.fillList[0];
      if (!firstNode.lineList || !firstNode.lineList.length) renderFill();
      // console.log('state.fillList', state.fillList);
    };

    const quesCardRef = ref<HTMLElement | any>(null);
    const objectHeightToolRef = ref<HTMLElement | any>(null);
    const qWrapRef = ref<HTMLElement | any>(null);
    const imglistContentRef = ref<HTMLElement | any>(null);

    const questionCard = computed(() => {
      return props.item;
    });

    // 是否显示打分
    const isGridPlaceShow = (
      gType: GRIDPLACE_TYPE,
      gridPlace: GRIDPLACE_TYPE = props.item.gridPlace
    ) => {
      return (
        gType === gridPlace &&
        Paper.correctType == ICorrectType.HAND &&
        props.item.typeId != QUES_TYPE.fillEva
      );
    };

    // 同步设置数据到源数据
    const syncSettingToOrigin = (qId: string, key: string, value: any, force: boolean = false) => {
      if (!force && Paper.cardType != ICARDMODEL.ONLYCARD) return;

      let qIndex = Paper.findBigIndexByQuesId(props.item.parentId || props.item.id);
      if (qIndex === -1) return;

      let bigQues = Paper.getQuesInfos()[qIndex];
      let qItem = bigQues.data.find(item => qId === item.id);
      // console.debug("syncSettingToOrigin", { key, ques: Paper.getQuesInfos()[qIndex], qId, item: props.item })
      if (!qItem && bigQues.typeId == QUES_TYPE.mixin) {
        let fillQues = bigQues.data.find(subItem => props.item.id === subItem.id);
        if (!fillQues) return;

        qItem = fillQues.data.find(item => qId === item.id)
      }
      if (qItem) qItem[key] = value;
    };

    // 是否显示大标题
    const showBigTitle = computed(() => {
      return props.item.hasTitle && props.item.showName == '1' && !props.item.parentId;
    });

    /**
     * @description: 设置卡片合适高度
     * @param {*} force 强制更新
     * @return {*}
     */
    const setFitHeight = async (force: boolean = false) => {
      await mergeAllPageSplitedCards(props.qId);

      let sNode = state.splitNodes[0];
      let $dragDomBox: any = document.querySelectorAll(`[data-id="${sNode.id}"] > .ques-box`)[0];
      let fitHeight = pxConversionMm($dragDomBox.offsetHeight);
      let nodeHeight = Number(sNode.height.replace('mm', ''));
      let newHeight = (!force && nodeHeight > fitHeight) ? nodeHeight : fitHeight;

      // 大于0的分段计算最小高度
      sNode.height = newHeight + 'mm';
    };

    /* 重新计算和渲染题目数据 */
    const reCaculQuestionPos = async () => {
      state.isObjectiveMerged = Paper.mergeQues;
      state.isA33Layout = checkIsA33Layout();

      await nextTick();
      renderFill();
    };

    /* 处理更新题目事件 */
    const handleUpdateAllQustions = async () => {
      await reCaculQuestionPos();
      await nextTick();
      reCalcuSplitHeight();
    };

    /* 处理切换页面布局 */
    const handleChangeLayout = async () => {
      state.isA33Layout = checkIsA33Layout();
      state.needForceRender = true;
      renderFill();
      await nextTick();

      setFitHeight();
      reCaculQuestionPos();
    };

    /* 处理切换批阅类型 */
    const handleSwitchCorrect = async () => {
      state.isHandCorrent = Paper.correctType == ICorrectType.HAND;

      await setFitHeight();
      await nextTick();
      setQuesCardHeight();
    };

    /* 设置题目卡片的总高度 */
    const setQuesCardHeight = () => {
      if (!quesCardRef.value) return;

      let offsetHeight = quesCardRef.value.offsetHeight;
      let splitorDoms = quesCardRef.value.querySelectorAll('.page-splitor');
      let splitorDomsHeight = 0;
      if (splitorDoms.length) {
        Array.from(splitorDoms).forEach(($dom: HTMLElement) => {
          splitorDomsHeight += $dom.offsetHeight;
        });
      }
      let tHeight = pxConversionMm(offsetHeight - splitorDomsHeight);

      state.height = tHeight;
      props.item.height = tHeight + 'mm';
      syncSettingToOrigin(props.item.id, 'height', props.item.height);
      return tHeight;
    };

    /* 获取跨页拆分的高度和 */
    const getSplitSumQuesH = (tHeight?: number) => {
      tHeight = tHeight || setQuesCardHeight();
      let $dom = quesCardRef.value;
      // 减去分数和标题栏高度
      let $title = getNameElement($dom);
      if ($title) {
        tHeight -= pxConversionMm($title.offsetHeight);
      }

      return tHeight;
    };

    /**
     * @description: 检查分段高度是否合适
     * @param {*} canOverPage 支持计算超过分页高度
     * @return {*}
     */
    const checkSplitNodeHeight = (canOverPage: boolean = false) => {
      // 检测是否需要重新刷新高度
      let needRefresh = false;
      state.splitNodes.forEach((sNode, index) => {
        if ((state.splitNodes.length > 1 && index === 0) || sNode.type === 'blank') return;

        let $dragDomBox: any = document.querySelectorAll(`[data-id="${sNode.id}"] > .ques-box`)[0];
        let oldHeight = Number(sNode.height.replace('mm', ''));
        if (!$dragDomBox) return;

        let newHeight = pxConversionMm($dragDomBox.offsetHeight);
        needRefresh = newHeight > oldHeight;
        if (!needRefresh) return;

        newHeight = newHeight + 4;
        if (!canOverPage) {
          let pageContentHeight = pageHeight - footerKeepHeight * 2;
          newHeight = newHeight > pageContentHeight ? pageContentHeight : newHeight;
        }
        sNode.height = newHeight + 'mm';
        state.refreshKey++;
      });
    };

    /* 处理拆分卡片的消息 - 跨页拆分 */
    const handlePageSplitCard = async ({
      id,
      splitNodes,
    }: {
      id: string;
      splitNodes: SplitNodes[];
    }) => {
      if (props.qId !== id) return;

      state.splitNodes = splitNodes.map((sNode, index) => {
        if (sNode.type !== 'ques') return sNode;

        let data;
        if (index > 0) {
          let lsIndex = Number(splitNodes[index - 2].tagIndex);
          if (index === splitNodes.length - 1) {
            if (lsIndex !== -1) data = state.fillList.slice(lsIndex);
          } else {
            let nextIndex = Number(sNode.tagIndex);
            data = state.fillList.slice(lsIndex, nextIndex);
          }
        } else {
          data = state.fillList.slice(0, Number(sNode.tagIndex));
        }

        return {
          ...sNode,
          data,
        };
      });
      state.refreshKey++;
      await nextTick();
      renderFill();
      checkSplitNodeHeight();
    };

    /* 合并所有拆分的卡片 - 跨页合并 */
    const mergeAllPageSplitedCards = async (id: string) => {
      if ((id !== null && props.qId !== id) || state.splitNodes.length === 1) return;

      let firstNode = deepClone(state.splitNodes[0]);
      firstNode.height = getSplitSumQuesH() + 'mm';
      // console.log('sNode.height2', firstNode.height);

      firstNode.data = props.item.data;
      state.splitNodes = [firstNode];
      state.refreshKey++;

      await nextTick();
      setQuesCardHeight();
    };

    /* 处理线条数量改变 */
    const handleChangeLineNum = async ({ ques, type, index }) => {
      if (type == 'plus') {
        let lastLine = ques.lineList[index];
        ques.lineList.splice(index + 1, 0, paper.createLineModel(lastLine.width, index));
        ques.lineList[index + 1].editContent = lastLine.editContent;
        ques.lineList.forEach((item, subIndex) => {
          item.index = subIndex;
        });
        ques.lineNum++;
      } else {
        ques.lineList.splice(index, 1);
        ques.lineNum--;
      }

      syncLineList(ques);
    };

    /* 处理线条长度改变 */
    const syncLineList = async (ques: any) => {
      syncSettingToOrigin(ques.id, 'lineList', ques.lineList, true);
      syncSettingToOrigin(ques.id, 'lineNum', ques.lineList.length, true);
      Paper.notifyUpdateData('card');

      renderFill();
      setFitHeight();
      setQuesCardHeight();
      await nextTick();
      bus.emit('renderAllCard');
    };

    /* 重新计算阶段高度 */
    const reCalcuSplitHeight = async () => {
      state.refreshKey++;
      await nextTick();
      checkSplitNodeHeight(true);
    };

    /* 处理触底事件 */
    const handleReachBottom = async ({ id, pageSize }: { id: string; pageSize: number }) => {
      if (props.qId !== id) return;
      await nextTick();

      objectHeightToolRef.value.setHeightFull();
      // console.log('reachBottom', id);
    };

    /**
     * @description: 处理卡片渲染完成
     * @return {*}
     */
    const handleCardRendered = (id: string) => {
      if (id !== null && props.qId !== id) return;

      let isA33Layout = checkIsA33Layout();
      if (isA33Layout === state.isA33Layout) return;

      state.isA33Layout = checkIsA33Layout();
      // state.needForceRender = true;
      renderFill();
    };

    /* 检查数据完整性 */
    const checkDataComplete = () => {
      if (!props.item.editContent) props.item.editContent = '';
      if (!props.item.fillDistance) {
        props.item.fillDistance = props.item.fillLineHeight ? (props.item.fillLineHeight + 9) : 10;
        syncSettingToOrigin(props.item.id, 'fillDistance', props.item.fillDistance, true);
      }
    };

    onBeforeUnmount(() => {
      bus.off('reachBottom', handleReachBottom);
      bus.off('cardRendered', handleCardRendered);
      bus.off('splitPageCard', handlePageSplitCard);
      bus.off('mergePageSplitedCard', mergeAllPageSplitedCards);
      bus.off('reCalcuSplitHeight', reCalcuSplitHeight);
      bus.off('switchCorrect', handleSwitchCorrect);

      Paper.off('changeLayout', handleChangeLayout);
      Paper.off('changeCardType', reCaculQuestionPos);
      Paper.off('updateAllQustions', handleUpdateAllQustions);
      Paper.off('changeNumberLayout', reCaculQuestionPos);
    });

    onMounted(async () => {
      bus.on('reachBottom', handleReachBottom);
      bus.on('cardRendered', handleCardRendered);
      bus.on('splitPageCard', handlePageSplitCard);
      bus.on('mergePageSplitedCard', mergeAllPageSplitedCards);
      bus.on('reCalcuSplitHeight', reCalcuSplitHeight);
      bus.on('switchCorrect', handleSwitchCorrect);

      Paper.on('changeLayout', handleChangeLayout);
      Paper.on('changeCardType', reCaculQuestionPos);
      Paper.on('updateAllQustions', handleUpdateAllQustions);
      Paper.on('changeNumberLayout', reCaculQuestionPos);

      checkDataComplete();
      state.isA33Layout = checkIsA33Layout();
      let quesHeight = props.item.height;
      if (quesHeight) {
        state.height = quesHeight.replace('mm', '');
        state.splitNodes[0].height = getSplitSumQuesH(state.height) + 'mm';
      }
      state.refreshKey++;

      renderFill();

      await setFitHeight();
      await nextTick();
      setQuesCardHeight();
    });

    /**
     * @name 检查是否A33布局
     */
    const checkIsA33Layout = () => {
      let pageLayout = Number(Paper.pageLayout);
      if ([IPAGELAYOUT.A4, IPAGELAYOUT.A3, IPAGELAYOUT.A33].includes(pageLayout))
        return [IPAGELAYOUT.A33].includes(pageLayout);

      let isA33Card = [IPAGELAYOUT.A33].includes(pageLayout);
      let $cardDom: any = document.getElementById(props.qId);
      if (!$cardDom) return isA33Card;

      if ($cardDom.style.width) {
        isA33Card = $cardDom.style.width === PaperConstant.WIDTH_A33;
      } else if ($cardDom.previousElementSibling.style.width) {
        isA33Card = $cardDom.previousElementSibling.style.width === PaperConstant.WIDTH_A33;
      }

      return isA33Card;
    };

    const editQues = () => {
      // 合并模式，dom和源数据无法对应，需要单独查询
      let qIndex = Paper.findBigIndexByQuesId(props.item.parentId || props.item.id);
      let qItem = Paper.getQuesInfos()[qIndex];

      bus.emit('editQues', { item: deepClone(qItem), index: qIndex });
    };

    /* 拆分题目 */
    const splitSubjectiveQuestions = async () => {
      let ques = props.item;
      let qId = ques.parentId || ques.id;

      if (!props.item.mixinMode) qId = ques.id;

      Paper.splitSubjectiveQuestions(qId);
      Paper.notifyUpdateData('right');

      updateFillList();
      bus.emit('renderAllCard');
    };

    /* 合并主观题 */
    const mergeSubjectiveQuestions = async (subId: string) => {
      let ques = props.item;
      let qId = ques.parentId || ques.id;
      if (!props.item.mixinMode) qId = ques.id;

      let subIndex = ques.data.findIndex(subItem => subItem.id === subId);
      Paper.mergeSubjectiveQuestions(qId, subIndex);
      Paper.notifyUpdateData('right');
      state.refreshKey++;
      bus.emit('renderAllCard');
      // setTimeout(function () {
      //   updateFillList();
      // }, 10);
    };

    /**
     * @name 渲染填空题
     */
    const renderFill = () => {
      let pageWidth = state.isA33Layout ? 130 : 190;
      let modeChanged = state.mixinMode != props.item.mixinMode;

      props.item.data.forEach((ques: any) => {
        // 混合题型中，父层仅为标注层，不参与内容
        if (ques.mixinMode) return;

        // 未初始化的填空题，计算线条参数
        if (ques.rendered && !modeChanged && !state.needForceRender) return;

        state.mixinMode = props.item.mixinMode;
        ques = paper.updateFillLineList(ques, pageWidth);
        ques.rendered = true;
        syncSettingToOrigin(ques.id, 'rendered', true);
        syncSettingToOrigin(ques.id, 'lineList', ques.lineList);
      });
      state.needForceRender = false;
    };

    /**
     * @description: 处理自定义上传
     * @param {*} options
     * @return {*}
     */
    const handleHttpUpload = (options: UploadRequestOptions) => {
      // 上传图片服务器地址
      const uploadAction = `https://service.iclass30.com/testbank/testBank/uploadEditorImgNew?htmlurl=${location.href}&responseType=json`;
      ajaxUpload({
        ...options,
        action: uploadAction,
        filename: 'upload',
        file: options.file,
        onSuccess(res) {
          let img = document.createElement('img');
          img.src = res.url;
          imglistContentRef.value.appendChild(img);

          props.item.editContent = imglistContentRef.value.innerHTML;
          syncSettingToOrigin(props.item.id, 'editContent', imglistContentRef.value.innerHTML);
        },
      });
    };

    // 当前选中的图片
    let selectedImg = null;
    /**
     * @description: 处理图片的按钮事件,剪切、复制、删除
     * @return {*}
     */
    const handleKeyUpImage = async (event: any) => {
      if (!selectedImg) return;

      let isCopyAction =
        event.ctrlKey && (event.key.toLowerCase() === 'x' || event.key.toLowerCase() === 'c');
      if (isCopyAction) {
        const response = await fetch(selectedImg.src);
        let blob = await response.blob();
        blob = new Blob([blob], { type: 'image/png' });
        const data = [new ClipboardItem({ [blob.type]: blob })];
        await navigator.clipboard.write(data);
      }

      if (event.key === 'Delete' || (event.ctrlKey && event.key.toLowerCase() === 'x')) {
        selectedImg.remove();
        selectedImg = null;

        props.item.editContent = imglistContentRef.value.innerHTML;
        syncSettingToOrigin(props.item.id, 'editContent', imglistContentRef.value.innerHTML);
      }
    };

    /**
     * @description: 图片被选中
     * @param {*} event
     * @return {*}
     */
    const handleImageSelect = (event: any) => {
      selectedImg = event.target.tagName === 'IMG' ? event.target : null;
    };

    /**
     * @description: 调整填空题行高倍数
     * @return {*}
     */
    const handleLineHeightChange = async () => {
      let qIndex = Paper.findBigIndexByQuesId(props.item.parentId || props.item.id);
      let qItem = Paper.getQuesInfos()[qIndex];
      if (qItem) qItem.fillDistance = props.item.fillDistance;

      syncSettingToOrigin(props.item.id, 'fillDistance', props.item.fillDistance);
      setFitHeight(true);
      setQuesCardHeight();
      await nextTick();
      bus.emit('renderAllCard');
    };

    return {
      ...toRefs(state),
      qWrapRef,
      quesCardRef,
      questionCard,
      showBigTitle,
      objectHeightToolRef,
      imglistContentRef,
      editQues,
      syncLineList,
      isGridPlaceShow,
      setQuesCardHeight,
      handlePageSplitCard,
      handleChangeLineNum,
      handleUpdateAllQustions,
      mergeSubjectiveQuestions,
      splitSubjectiveQuestions,
      mergeAllPageSplitedCards,
      handleHttpUpload,
      handleKeyUpImage,
      handleImageSelect,
      handleLineHeightChange,
    };
  },
});
</script>

<style lang="scss" scoped>
.q-opt {
  &:hover {

    .edit-button-tools,
    .object-height-tools {
      display: block;
    }
  }

  .fill-item {
    position: relative;
  }

  .ques-sort {
    width: 10mm;
    cursor: text;

    &:hover,
    &:focus {
      background-color: #eee;
    }
  }
}

.ques-item-wrap--fill {
  margin: 0;
  vertical-align: top;
}

.imglist-content {
  position: absolute !important;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  pointer-events: none;
  overflow: hidden;
  padding: 0 !important;

  ::v-deep>img {
    position: relative;
    z-index: 1;
    pointer-events: auto;
    max-width: 100%;
    max-height: 100%;
  }
}
</style>
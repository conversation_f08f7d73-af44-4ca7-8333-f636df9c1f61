﻿<template>
  <mk-dialog :isShowDialog="true" :isCustomFooter="true" :appendTobody="true" :title="'退出登录'"
    :customClass="'mk-logout-dialog'" @click-cancel="closeDialog" :width="'32%'">
    <template #customSlot>
      <div class="mk-logout-dialog-content">
        <div class="login-out-img">
        </div>
        <div class="login-out-text">确定要退出当前账号吗?</div>
      </div>
    </template>

    <template #customFooterSlot>
      <el-row type="flex" align="middle" justify="center" class="dialog-footer">
        <el-button class="no-btn" @click="closeDialog">取消</el-button>
        <el-button class="ok-btn" @click="logout">确定</el-button>
      </el-row>
    </template>
  </mk-dialog>
</template>

<script lang="ts">
import { defineComponent } from "vue";
import MkDialog from "../mk-dialog";

export default defineComponent({
  name: "mk-logout",

  emits: ["on-cancle", "on-logout"],

  components: {
    [MkDialog.name]: MkDialog,
  },

  setup(props, ctx) {
    /**
     * @name: 关闭弹窗
     */
    const closeDialog = () => {
      ctx.emit("on-cancle");
    };
    /**
     * @name: 退出登录
     */
    const logout = () => {
      ctx.emit("on-logout");
      closeDialog()
    };

    return {
      closeDialog,
      logout,
      isShow: true,
    };
  },
});
</script>

<style lang="scss" scoped>
.mk-logout-dialog {
  z-index: 9999;

  .el-dialog__header {
    display: none !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 50px;

    .dialog-header {
      width: 100%;
    }

    .el-dialog__title {
      padding: 0;
    }

    .el-dialog__headerbtn {
      position: static;

      .el-dialog__close {
        font-size: 20px;
      }
    }
  }

  .dialog-footer {
    padding: 20px;

    .el-button {
      width: 134px !important;
      height: 60px !important;
      border: 1px solid #E5E5E5 !important;
      border-radius: 8px !important;
      font-size: 20px !important;

      &.no-btn {
        margin-right: 20px !important;
        @include theme_color();
      }

      &.ok-btn {
        margin-left: 47px !important;
        @include theme_background-color();
        @include theme_border-color();
        color: #fff;
      }
    }
  }
}

.mk-logout-dialog-content {
  padding-top: 32px;
  text-align: center;

  .login-out-img {
    width: 181px;
    height: 121px;
    margin: auto;
    background: url('https://fs.iclass30.com/aliba/region/logo/mk-login-out.png') 100% 100% no-repeat;
  }

  .login-out-text {
    margin-top: 22px;
    font-size: 24px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #2A3034;
    line-height: 36px;
  }
}
</style>

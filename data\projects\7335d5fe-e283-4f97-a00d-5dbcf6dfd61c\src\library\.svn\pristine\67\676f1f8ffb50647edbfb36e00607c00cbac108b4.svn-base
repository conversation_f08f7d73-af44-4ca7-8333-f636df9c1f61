<template>
  <!--通知弹框-->
  <mk-dialog
    :isShowDialog="true"
    :title="'活动分享'"
    :isCustomFooter="true"
    @click-cancel="closeDialog"
    :dialogWidth="'36%'"
  >
    <template v-slot:customSlot>
      <div class="share-box">
        <!--tab页切换-->
        <div class="type-box" v-if="isShowQRCode">
          <span
            @click="handleClick(TAB_TYPE.link)"
            class="type-item"
            :class="activeName == TAB_TYPE.link ? 'is-active' : ''"
          >
            链接分享
          </span>
          <span
            @click="handleClick(TAB_TYPE.qcshare)"
            class="type-item"
            :class="activeName == TAB_TYPE.qcshare ? 'is-active' : ''"
          >
            二维码分享
          </span>
        </div>
        <!--链接分享内容-->
        <div
          v-if="activeName == TAB_TYPE.link"
          class="share-content flex-center"
        >
          <div class="link-box">
            <span :title="activityInfo" class="team-title"
              >{{ activityInfo }}：</span
            >
            <span @click="addressJump" class="copy-link">{{ url }}</span>
            <span style="color: #999">点击链接进入直接查看活动</span>
          </div>
        </div>
        <!--二维码分享内容-->
        <div
          v-if="activeName == TAB_TYPE.qcshare"
          class="share-content flex-center"
        >
          <div class="qr-code" id="qc-img">
            <img :src="qcImg" class="code-img" />
            <div :title="activityInfo" class="team-title">
              {{ activityInfo }}
            </div>
            <div class="img-look">
              <img
                :src="
                  require('@/library/ui/mk-share-dialog/assets/scanning.png')
                "
                alt=""
              />
              扫码查看
            </div>
          </div>
        </div>
      </div>
    </template>
    <template v-slot:customFooterSlot>
      <el-button
        v-if="activeName == TAB_TYPE.link"
        type="primary"
        class="copy-btn"
        :loading="isShowLinkLoading"
        @click="copyLink"
        >复制链接</el-button
      >
      <!-- v-show="!isShare" -->
      <el-button
        v-else
        type="primary"
        class="copy-btn"
        :loading="isShowQCLoading"
        @click="copyQrCode"
      >
        复制二维码
      </el-button>
      <div class="address-tip-box">
        <textarea id="address" readonly>  {{ url }}</textarea>
      </div>
    </template>
  </mk-dialog>
</template>
<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  computed,
  onMounted,
  PropType,
} from 'vue';
import { ElMessage } from 'element-plus';
import { copyText } from '@/library/src/utils/globalFunction';
import html2canvas from 'html2canvas';
import qrcode from 'qrcode';
declare let ssoClient: any;
declare module 'qrcode';
import {isSY} from '@/library/src/utils/valiteSite'

/**活动接口数据类 */
export interface IActivity {
  /** 活动名称 */
  title: string;
  /** 学段学科 */
  subjectName: string;
  /** 所属分类 */
  typeName: string;
  /** 教师名称 */
  teaName: string;
}
/**tab类型**/
export enum TAB_TYPE {
  /** 链接分享 */
  link = 'link',
  /** 二维码分享 */
  qcshare = 'qcshare'
}
export default defineComponent({
  name: 'mk-share-dialog',

  emits: ['close-dialog'],

  props: {
    //活动数据
    activityData: {
      type: Object as PropType<IActivity>,
      default: {}
    },
    //链接地址
    linkUrl: {
      type: String,
      default: ''
    },
    //是否展示二维码
    isShowQRCode: {
      type: Boolean,
      default: true
    },
    //活动前缀名称
    prefixName: {
      type: String,
      default: '教研活动分享'
    },
    isShare: {
      type: Boolean,
      default: false
    },
  },
  setup(props, ctx) {
    const state = reactive({
      //tab绑定值
      activeName: TAB_TYPE.link as keyof typeof TAB_TYPE,
      //活动信息
      activityInfo: '',
      //二维码图片
      qcImg: '',
      //地址
      url: '',
      //是否显示链接加载状态
      isShowLinkLoading: false,
      //是否显示二维码加载状态
      isShowQCLoading: false
    });

    /**
     * @name:活动信息
     */
    const activityInfo = computed(() => {
      //集体备课 听评课：活动名称+年级学科+xx老师创建
      //其他活动：活动名称+所属分类+学段学科+xx老师创建
      const info = `${props.prefixName}《${props.activityData.title}》${
        props.activityData.typeName ? '-' : ''
      }${props.activityData.typeName}-${props.activityData.subjectName ? props.activityData.subjectName : ''}-${
        props.activityData.teaName ? props.activityData.teaName : ''
      }老师创建`;
      return info;
    });

    /**
     * @name: 点击链接地址
     */
    const addressJump = () => {
      if (ssoClient) {
        const url: any = state.url;
        window.open(url, '_blank');
      } else {
        ElMessage.warning('请先登录');
        ssoClient.login();
      }
    };
    /**
     * @name:切换tab页
     */
    const handleClick = (name: keyof typeof TAB_TYPE) => {
      state.activeName = name;
      state.url = props.linkUrl;
      if (state.activeName == TAB_TYPE.qcshare && state.url) {
        getQRcode();
      }
    };
    /**
     * @name:获取二维码
     */
    const getQRcode = () => {

      const thirdlogin = 'https://dingtalk.iclass30.com/third_login.html?corpId=$CORPID$&isRegion=1&appType=0&returnUrl=' + encodeURIComponent(state.url)
      qrcode.toDataURL(isSY()?thirdlogin:state.url, (err: any, url: any) => {
        if (err) throw err;
        state.qcImg = url;
      });
    };
    /**
     * @name 复制链接
     */
    const copyLink = () => {
      state.isShowLinkLoading = true;
      const text = `${activityInfo.value}：${state.url}点击链接直接查看活动`;
      if (copyText(text)) {
        ElMessage({ message: '复制成功!', type: 'success' });
        closeDialog();
        state.isShowLinkLoading = false;
      } else {
        ElMessage({ message: '复制失败!', type: 'error' });
        state.isShowLinkLoading = true;
      }
    };
    /**
     * @name:复制二维码的事件
     */
    const copyQrCode = () => {
      state.isShowQCLoading = true;
      const htmlDom: any = document.getElementById('qc-img');
      setTimeout(() => {
        //html2canvas 的作用就是根据 DOM 生成对应的图片
        html2canvas(htmlDom as HTMLElement, {
          allowTaint: false,
          useCORS: true
        }).then((canvas: any) => {
          // 将canvas转为blob
          canvas.toBlob(async (blob: any) => {
            try {
              const ClipboardItem: any = (window as any).ClipboardItem;
              if( !ClipboardItem ){
                ElMessage.warning('您的浏览器暂不支持此功能,请更新版本或右击二维码复制');
                state.isShowQCLoading = false;
                return
              }
              const data = [new ClipboardItem({ [blob.type]: blob })];
              await (navigator.clipboard as any).write(data); //注:此方法只能在localhost跟https协议下可用,http协议下不存在此方法
              ElMessage.success('复制成功');
              closeDialog();
              state.isShowQCLoading = false;
            } catch(e) {
              ElMessage.warning('请在https协议下操作');
              state.isShowQCLoading = false;
            }
          });
        });
      }, 300); //这里加上 300ms 的延迟是为了让 DOM 元素完全渲染完成后再进行图片的生成
    };
    /**
     * @name 关闭弹框
     */
    const closeDialog = () => {
      ctx.emit('close-dialog');
    };

    onMounted(() => {
      state.url = props.linkUrl;
      handleClick(state.activeName);
    });

    return {
      ...toRefs(state),
      copyLink,
      closeDialog,
      addressJump,
      handleClick,
      activityInfo,
      TAB_TYPE,
      copyQrCode
    };
  }
});
</script>
<style lang="scss" scoped>
.share-box {
  .type-box {
    border-bottom: 1px solid #e2e9ed;
    text-align: center;
    .type-item {
      padding: 10px 8px;
      margin: 0 60px;
      display: inline-block;
      font-size: 16px;
      color: #4e5668;
      cursor: pointer;
      &:hover {
        @include theme_color();
      }
      &.is-active {
        border-radius: 2px;
        @include theme_color();
        @include theme_border-bottom(4px);
      }
    }
  }
  .share-content {
    margin-top: 30px;
    .link-box {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .copy-link {
        font-size: 16px;
        line-height: 26px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        @include theme_color();
        width: 100%;
        margin-bottom: 10px;
      }
    }
    .qr-code {
      width: 450px;
      height: 208px;
      text-align: center;
      background: url('~@/library/ui/mk-share-dialog/assets/background_img.png');
      position: relative;
      .code-img {
        width: 157px;
        height: 157px;
        border-radius: 8px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        left: 16px;
      }
      .team-title {
        position: absolute;
        width: 220px;
        text-align: left;
        top: 30px;
        right: 20px;
      }
      .img-look {
        font-size: 14px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #869dcc;
        position: absolute;
        bottom: 46px;
        left: 208px;
        display: flex;
        align-items: center;
        img {
          margin-right: 3px;
          width: 21px;
          height: 16px;
        }
      }
    }
    .team-title {
      font-size: 16px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      color: #333333;
      line-height: 26px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
    }
  }
}

.address-tip-box {
  opacity: 0;
  height: 0;
}

.copy-btn {
  color: #ffffff;
  @include theme_background-color();
  @include theme_border-color();
}
</style>

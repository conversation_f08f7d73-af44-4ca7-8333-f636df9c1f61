<!--@Descripttion: 标签共用组件  -->
<template>
  <div :class="['res-label-box', { jlwx: isJLWX() }]">
    <div class="res-label-ul-box">
      <span>{{ labelTitle }}</span>

      <ul
          class="res-label-ul"
          :class="isShowMoreLabel ? 'activeLabel' : ''"
          ref="ulRef"
      >
        <template v-if="list.length">
          <template v-for="item in list" >
            <li
                v-if="item.isShow"
                :class="[
                'res-label-li',
                  currentValue === item.code ? 'active' : ''
                ]"
                @click="changeType(item)"
            >
              {{ item.name }}
            </li>
          </template>
        </template>
        <template v-else>
          <li class="defalut-text-box">
            暂无该筛选项下对应的{{ labelTitle.replace('：', '') }}，请联系管理员添加
          </li>
        </template>
      </ul>
    </div>

    <div
        class="is-show-box"
        v-if="isMoreLine"
    >
      <span @click="showMoreLabel()" v-if="isShowMoreLabel">展开<i class="el-icon-arrow-down"></i></span>
      <span @click="showMoreLabel()" v-else>收起<i class="el-icon-arrow-up"></i></span>
    </div>
  </div>
</template>

<script>
import {defineComponent, toRefs, reactive, watch, onMounted,ref, nextTick} from "vue";
import { isJLWX } from "@/library/src/utils/valiteSite";

export default defineComponent({
  name: "label-list",

  emits: ["change-type", "change-expend"],

  props: {
    // 标签标题
    labelTitle: {
      type: String,
      default: ""
    },
    // 标签列表
    labelList: {
      type: Array,
      require: true
    },
    // 当前值
    value: {
      type: String,
      default: ""
    }
  },

  setup(props, ctx) {

    const ulRef = ref(null)
    const state = reactive({
      // 是否多行
      isMoreLine: false,
      // 展开与收起
      isShowMoreLabel: false,
      // 当前点击的标签
      currentValue: props.value,
      // 标签列表
      list: [],
    });

    /**
     * @name:展示更多
     */
    function showMoreLabel() {
      state.isShowMoreLabel = !state.isShowMoreLabel;
      ctx.emit("change-expend", !state.isShowMoreLabel);
    }
    /**
     * @name:切换标签
     */
    function changeType(item) {
      state.currentValue = item.code;
      ctx.emit("change-type", item);
    }
    function setMoreLabel(){
      if( !ulRef.value ){
        return
      }
      state.isMoreLine = ulRef.value.offsetHeight > 40
      state.isShowMoreLabel = true
    }
    const setField = ()=>{
      const result =  props.labelList.map((item)=>{
        if( typeof item.isShow === 'undefined' ){
          item.isShow = true
        }
        return item
      })
      state.list = result
    }
    const init = ()=>{
      state.isMoreLine =false
      state.isShowMoreLabel= false
      setField()
      nextTick(()=>{
        setMoreLabel()
      })
    }

    /**
     * @name:监听当前的值
     */
    watch(
        () => props.value,
        () => {
          state.currentValue = props.value;
        }
    );
    /**
     * @name:监听当前列表
     */
    watch(
        () => props.labelList,
        () => {
          init()
        }
    );


    onMounted(() => {
      init()
    });

    return {
      ...toRefs(state),
      ulRef,
      showMoreLabel,
      changeType,
      isJLWX
    };
  }
});
</script>

<style lang="scss" scoped>
.res-label-box {
  background: #ffffff;
  border-radius: 4px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  .res-label-ul-box {
    display: flex;
    justify-content: flex-start;
    justify-items: center;
    width: 100%;
    span {
      color: #333;
      padding-top: 7px;
      min-width: 45px;
    }
    .res-label-ul {
      max-width: 91%;
      margin-bottom: 15px;
      margin-top: 0;

      .res-label-li {
        display: inline-block;
        cursor: pointer;
        padding: 7px 15px;
        margin: 0 0 5px 10px;
        &:hover,
        &.active {
          border-radius: 25px;
          @include theme_hover_background-color();
          @include theme_color();
        }
      }

      .defalut-text-box {
        margin-top: 2px;
        padding: 7px 14px;
        color: #bbb;
      }
    }
  }
  .activeLabel {
    height: 32px;
    overflow: hidden;
  }
  .is-show-box {
    padding-top: 8px;
    width: 60px;
    span {
      cursor: pointer;
      &:hover {
        @include theme_color();
      }
    }
  }
  &.jlwx {
    color: #525252;
    .res-label-ul-box {
      span {
        font-family: PingFangSC-Medium;
        font-size: 18px;
        margin-right: 20px;
        padding-top: 10px;
      }
      .res-label-ul {
        font-size: 16px;
        font-family: PingFangSC-Medium;
        max-width: 91%;
        margin-bottom: 10px;
        .res-label-li {
          display: inline-block;
          cursor: pointer;
          padding: 11px 17px;
          margin: 0 0 10px 11px;
          &:hover,
          &.active {
            border-radius: 29px;
            @include theme_hover_background-color();
            @include theme_color();
          }
        }

        .defalut-text-box {
          margin-top: 2px;
          padding: 7px 14px;
          color: #bbb;
        }
      }
    }
    .activeLabel {
      height: 40px;
    }
    .is-show-box {
      padding-top: 11px;
    }
  }
}
</style>

const fs = require('fs-extra');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class Server {
  constructor() {
    this.configDir = path.join(process.cwd(), 'data', 'configs');
    this.serversFile = path.join(this.configDir, 'servers.json');
    fs.ensureDirSync(this.configDir);
    this.initServersFile();
  }

  // 初始化服务器配置文件
  async initServersFile() {
    try {
      if (!(await fs.pathExists(this.serversFile))) {
        await fs.writeJson(this.serversFile, { servers: [] }, { spaces: 2 });
      }
    } catch (error) {
      throw new Error(`初始化服务器配置文件失败: ${error.message}`);
    }
  }

  // 获取所有服务器
  async getAll() {
    try {
      const data = await fs.readJson(this.serversFile);
      return data.servers;
    } catch (error) {
      throw new Error(`获取服务器列表失败: ${error.message}`);
    }
  }

  // 根据ID获取服务器
  async getById(id) {
    try {
      const data = await fs.readJson(this.serversFile);
      return data.servers.find(server => server.id === id) || null;
    } catch (error) {
      throw new Error(`获取服务器详情失败: ${error.message}`);
    }
  }

  // 创建新服务器
  async create(serverData) {
    try {
      const data = await fs.readJson(this.serversFile);
      const id = uuidv4();
      
      const server = {
        id,
        name: serverData.name,
        host: serverData.host,
        port: serverData.port || 22,
        username: serverData.username,
        password: serverData.password, // 生产环境中应使用安全存储或SSH密钥
        description: serverData.description || '',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      data.servers.push(server);
      await fs.writeJson(this.serversFile, data, { spaces: 2 });
      
      // 返回不含敏感数据的服务器信息
      const { password, ...safeServer } = server;
      return safeServer;
    } catch (error) {
      throw new Error(`创建服务器失败: ${error.message}`);
    }
  }

  // 更新服务器
  async update(id, serverData) {
    try {
      const data = await fs.readJson(this.serversFile);
      const serverIndex = data.servers.findIndex(server => server.id === id);

      if (serverIndex === -1) {
        throw new Error('服务器不存在');
      }

      // 如果密码为空或未定义，则不更新密码字段
      const updateData = { ...serverData };
      if (!updateData.password) {
        delete updateData.password;
      }

      const updatedServer = {
        ...data.servers[serverIndex],
        ...updateData,
        updatedAt: new Date().toISOString()
      };

      data.servers[serverIndex] = updatedServer;
      await fs.writeJson(this.serversFile, data, { spaces: 2 });

      // 返回不含敏感数据的服务器信息
      const { password, ...safeServer } = updatedServer;
      return safeServer;
    } catch (error) {
      throw new Error(`更新服务器失败: ${error.message}`);
    }
  }

  // 删除服务器
  async delete(id) {
    try {
      const data = await fs.readJson(this.serversFile);
      const serverIndex = data.servers.findIndex(server => server.id === id);
      
      if (serverIndex === -1) {
        return false;
      }
      
      data.servers.splice(serverIndex, 1);
      await fs.writeJson(this.serversFile, data, { spaces: 2 });
      return true;
    } catch (error) {
      throw new Error(`删除服务器失败: ${error.message}`);
    }
  }

  // 获取项目关联的服务器
  async getServersByProjectId(projectId) {
    try {
      const ProjectServer = require('./ProjectServer');
      const relationships = await ProjectServer.getByProjectId(projectId);
      
      if (relationships.length === 0) {
        return [];
      }
      
      const serverIds = relationships.map(rel => rel.serverId);
      const allServers = await this.getAll();
      
      // 获取服务器列表，并为每个服务器添加部署路径
      return allServers
        .filter(server => serverIds.includes(server.id))
        .map(server => {
          const relation = relationships.find(rel => rel.serverId === server.id);
          return {
            ...server,
            deployPath: relation.deployPath
          };
        });
    } catch (error) {
      throw new Error(`获取项目服务器失败: ${error.message}`);
    }
  }
}

module.exports = new Server(); 
<template>
  <div :class="['no-data',{background}]" :style="{paddingTop:paddingTop}">
    <img :src="noDataBg" alt="" class="no_pic" v-if="type=='noData'">
    <img :src="addFsUrl('aliba/region/default/err.png')" alt="" class="error_pic" v-else>
    <span class="txt">{{ type == 'noData' ? noDataTipTex : errorTipTex }}</span>
    <slot name="append"></slot>
  </div>
</template>

<script lang="ts">
import {addFsUrl} from "@/library/src/utils/globalFunction";
import {defineComponent} from "vue";

export default defineComponent({
  name: 'mk-no-data',

  props: {
    //缺省图的类型：noData：暂无数据 error：数据获取失败
    type: {
      type: String,
      default: 'noData'
    },
    //暂无数据提示语
    noDataTipTex: {
      type: String,
      default: '暂无数据'
    },
    //数据获取失败提示语
    errorTipTex: {
      type: String,
      default: '数据获取失败，请联系技术人员'
    },
    // 距离顶部距离
    paddingTop: {
      type: String,
      default: '20px'
    },
    // 自定义背景图
    noDataBg: {
      type: String,
      default: addFsUrl('aliba/region/default/empty.png')
    },
    // 是否添加背景色
    background: {
      type: Boolean,
      default: true
    }
  },

  setup(){
    return {addFsUrl}
  }
})
</script>

<style lang="scss" scoped>
.no-data {
  text-align: center;
  padding-bottom: 20px;

  &.background {
    background-color: #fff;
    border-radius: 10px;
  }

  .no_pic {
    display: block;
    margin: auto;
    max-width: 100%;
    height: 100%;
  }

  .error_pic {
    display: block;
    margin: auto;
    width: 346px;
    height: 234px;
  }

  .txt {
    text-align: center;
    margin: 20px auto 0;
    display: inherit;
    color: #999;
    font-size: 16px;
  }
}
</style>

<template>
  <div :class="['previewSlider', customClass]" v-if="isShowPreviewSlider">
    <i class="el-icon-error close-icon" @click="closePreview()" />
    <!--  资源包预览轮播组件:S  -->
    <div v-if="isResourcePackage" class="left-package-slide">
      <div
        v-for="item in resInfoList"
        @click="changePackageRes(item)"
        :key="item.id"
        class="item ellipsis"
        :title="item.title"
      >
        {{ item.title }}
      </div>
    </div>
    <!--  预览轮播组件:S  -->
    <template v-if="previewModeType == 'previewMode'">
      <transition
        name="fade"
        enter-active-class="animated fadeInDown"
        leave-active-class="animated fadeOutUp"
      >
        <div class="swiper-container preview-swiper gallery-top">
          <div class="swiper-wrapper">
            <div
              class="swiper-slide"
              v-for="(item, index) in galleryTopImgs"
              :key="item.id"
              :data-scale="(item.scale ? item.scale : 1) * 100 + '%'"
            >
              <!--  平移层  -->
              <div
                class="swiper-tanslate-box"
                :style="
                  '-webkit-transform:translate3d(' +
                  (item.tranX ? item.tranX : 0) +
                  'px, ' +
                  (item.tranY ? item.tranY : 0) +
                  'px,0);transform:translate3d(' +
                  (item.tranX ? item.tranX : 0) +
                  'px, ' +
                  (item.tranY ? item.tranY : 0) +
                  'px,0);'
                "
              >
                <!--  旋转缩放层  -->
                <div
                  class="swiper-rorate-box"
                  :style="
                    '-webkit-transform:rotate3d(0, 0, 1, ' +
                    (item.rotate ? item.rotate : 0) +
                    'deg) scale3d(' +
                    (item.scale ? item.scale : 1) +
                    ',' +
                    (item.scale ? item.scale : 1) +
                    ',1);transform:rotate3d(0, 0, 1, ' +
                    (item.rotate ? item.rotate : 0) +
                    'deg) scale3d(' +
                    (item.scale ? item.scale : 1) +
                    ',' +
                    (item.scale ? item.scale : 1) +
                    ',1);'
                  "
                >
                  <img
                    :src="addFsUrl(item.src)"
                    class="preview-lazy"
                    :class="item.stop ? item.stop : ''"
                    :swipe-options="{ threshold: '5', velocity: '4' }"
                    @mousewheel.prevent="mousewheel($event, index)"
                    @mousedown.prevent="imgPanMove($event, index)"
                  />
                  <div class="swiper-lazy-preloader"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </transition>
      <transition
        name="fade"
        enter-active-class="animated fadeIn"
        leave-active-class="animated fadeOut"
      >
        <div class="swiper-imageTool">
          <div class="icon-imageTools icon-pre"></div>
          <div
            class="icon-imageTools icon-recover"
            @click="previewTo('recover')"
          ></div>
          <div
            class="icon-imageTools icon-zoomin"
            @click="previewTo('zoomin')"
          ></div>
          <div
            class="icon-imageTools icon-zoomout"
            @click="previewTo('zoomout')"
          ></div>
          <div class="preview-pagination"></div>
          <div
            class="icon-imageTools icon-rotateL"
            @click="previewTo('rotateL')"
          ></div>
          <div
            class="icon-imageTools icon-rotateR"
            @click="previewTo('rotateR')"
          ></div>
          <div class="icon-imageTools icon-close" @click="closePreview()"></div>

          <template v-if="isShowDetail">
            <div
              :class="['icon-imageTools like', { active: isLike }]"
              @click="onLike"
              v-show="userId"
            >
              <i
                class="icon iconfont icondianzan_kuai"
                :title="isLike ? '取消点赞' : '点赞'"
              ></i>
            </div>
            <div class="icon-imageTools detail" @click="onDetail">详情</div>
          </template>
          <div class="icon-imageTools icon-next"></div>
        </div>
      </transition>
      <transition
        name="fade"
        enter-active-class="animated fadeInUp"
        leave-active-class="animated fadeOutDown"
      >
        <div class="swiper-container preview-swiper gallery-thumbs">
          <div class="swiper-wrapper">
            <div
              class="swiper-slide"
              v-for="item in galleryThumbsImgs"
              :key="item.id"
            >
              <div
                :data-background="addFsUrl(item.src)"
                class="thumb-box preview-lazy"
              >
                <div class="swiper-lazy-preloader"></div>
              </div>
            </div>
          </div>
        </div>
      </transition>
    </template>
    <!--  预览轮播组件:E  -->
    <div
      class="preview-swiper-cover"
      v-if="previewModeType != 'previewAudioMode'"
    ></div>
    <!--  视频弹窗组件：S  -->
    <div class="video-modal-box" v-if="previewModeType == 'previewVideoMode'">
      <video id="player" autoplay="autoplay" controls controlsList="nodownload" :disablePictureInPicture="true">
        <source :src="videoSrc" type="video/mp4" />
        您的浏览器不支持 HTML5 video 标签。
      </video>
      <ul
        v-if="isShowplayBackBtn"
        class="video-playback-rate"
        @mouseleave="togglePlayBackMenu(false)"
      >
        <template v-if="isShowPlaybackRateMenu">
          <li
            v-for="i in 4"
            :key="i"
            :span="6"
            :class="{ active: videoPlaybackRate == 0.5 * i }"
            @click="switchVideoPlayBackRate(0.5 * i)"
          >
            {{ 0.5 * i }}×
          </li>
        </template>
        <li class="active" @click="togglePlayBackMenu(true)">
          {{ videoPlaybackRate }}×
        </li>
      </ul>
      <div class="video-close-btn" @click="closePreview()"></div>
      <template v-if="isShowDetail">
        <div
          :class="['video-close-btn like', { active: isLike }]"
          @click="onLike"
          v-show="userId"
        >
          <i
            class="icon iconfont icondianzan_kuai"
            :title="isLike ? '取消点赞' : '点赞'"
          ></i>
        </div>
        <div class="video-close-btn detail" @click="onDetail">详情</div>
      </template>
    </div>
    <!--  视频弹窗组件：E  -->
    <!--  音频弹窗组件：S  -->
    <div
      class="img-ml-box"
      data-type="question"
      v-if="previewModeType == 'previewAudioMode'"
    >
      <!--遮罩层背景-->
      <div class="img-ml-bg"></div>
      <audio
        id="audio-player"
        class="audio"
        controls="true"
        autoplay="true"
        style="display: none"
      ></audio>
      <div id="audio-player-container" class="audio-player-container">
        <div class="play-btn play"></div>
        <div class="audio-progress">
          <div class="audio-progress-bar" style="width: 0"></div>
        </div>
        <!--关闭按钮-->
        <a
          href="javascript:void(0)"
          class="img-ml-close"
          @click="closePreview()"
        ></a>

        <template v-if="isShowDetail">
          <a
            href="javascript:void(0)"
            :class="['img-ml-close like', { active: isLike }]"
            @click="onLike"
            v-show="userId"
          >
            <i
              class="icon iconfont icondianzan_kuai"
              :title="isLike ? '取消点赞' : '点赞'"
            ></i>
          </a>
          <a
            href="javascript:void(0)"
            class="img-ml-close detail"
            @click="onDetail"
            >详情</a
          >
        </template>
      </div>
    </div>
    <!--  音频弹窗组件：E  -->
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  reactive,
  toRefs,
  nextTick,
  watch,
  computed
} from 'vue';
import Swiper, { Controller, Pagination, Navigation, Lazy } from 'swiper';
Swiper.use([Controller, Pagination, Navigation, Lazy]);
import $ from 'jquery';
import { addFsUrl } from '@/library/src/utils/globalFunction';
import { useStore } from 'vuex';
import { IGlobalState } from '@/library/src/store/index';

export default defineComponent({
  name: 'mk-preview-slider',

  props: [
    'isShowPreviewSlider',
    'previewModeType',
    'videoSrc',
    'resInfoList',
    'isResourcePackage',
    'isShowDetail',
    'customClass'
  ],

  emits: ['close-preview', 'change-package-res', 'on-detail', 'on-like'],

  setup(props, context) {
    const store = useStore<IGlobalState>();
    const state = reactive({
      galleryTop: null as any,
      galleryThumbs: null as any,
      galleryTopImgs: [] as any[],
      galleryThumbsImgs: [] as any[],
      //视频倍速列表
      playbackRateList: [0.5, 1, 1.25, 1.5, 2],
      //视频倍速
      videoPlaybackRate: 1,
      isShowPlaybackRateMenu: false,
      isShowplayBackBtn: false,
      userId: computed(() => store.state.user.userId),
      // 是否点赞
      isLike: false
    });

    watch(
      () => props.isShowPreviewSlider,
      () => {
        state.isLike = false
        if (props.isShowPreviewSlider) {
          if (props.previewModeType === 'previewVideoMode') {
            nextTick(() => {
              state.isShowplayBackBtn = true;
            });
          }
        }
      }
    );

    /**
     * @name: 倍速菜单显示影藏
     * @param isShow 是否展示
     */
    function togglePlayBackMenu(isShow: boolean) {
      state.isShowPlaybackRateMenu = isShow;
    }

    /**
     * @name: 视频倍速切换
     * @param playbackRate 倍速
     */
    function switchVideoPlayBackRate(playbackRate: number) {
      let videoPlayer: any = document.getElementById('player');
      if (videoPlayer) {
        state.videoPlaybackRate = playbackRate;
        videoPlayer.playbackRate = playbackRate;
      }
    }

    /**
     * @name 初始化视频
     */
    function initVideo() {
      let videoPlaybackRateSelect: any = document.getElementById(
        'videoPlaybackRateSelect'
      );
      let video: any = document.getElementById('player');
      videoPlaybackRateSelect.addEventListener('change', function (data: any) {
        video.playbackRate = data.currentTarget.value;
        state.videoPlaybackRate = data.currentTarget.value;
      });
    }

    /**
     * 恢复图片位置信息到初始值
     */
    function imgPosiInit(index: number) {
      state.galleryTopImgs[index].stop = '';
      state.galleryTopImgs[index].tranX = 0;
      state.galleryTopImgs[index].tranY = 0;
    }
    /**
     * 预览按钮操作
     */
    function previewTo(type: string, index: number | string) {
      const tempIndex =
        index == null || index == 'undefined'
          ? state.galleryTop.activeIndex
          : index;
      switch (type) {
        case 'zoomin':
        case 'zoomout': {
          const scaleOrigin = !state.galleryTopImgs[tempIndex].scale
              ? 1
              : state.galleryTopImgs[tempIndex].scale,
            scaleNow =
              type === 'zoomin' ? scaleOrigin * 1.5 : scaleOrigin / 1.5;
          state.galleryTopImgs[tempIndex].scale =
            scaleNow < 0.3 ? 0.3 : scaleNow > 5 ? 5 : scaleNow;
          if (scaleNow === 1) {
            // 当图片处于正常比例，放开swiper拖动峡效果
            imgPosiInit(tempIndex);
          } else {
            state.galleryTopImgs[tempIndex].stop = 'stop-swiping';
          }
          break;
        }
        case 'rotateL':
        case 'rotateR': {
          const rotateOrigin = !state.galleryTopImgs[tempIndex].rotate
              ? 0
              : state.galleryTopImgs[tempIndex].rotate,
            rotateNow =
              type === 'rotateL' ? rotateOrigin - 90 : rotateOrigin + 90;
          state.galleryTopImgs[tempIndex].rotate = rotateNow;
          break;
        }
        case 'recover':
          // 恢复原始比例大小
          state.galleryTopImgs[tempIndex].scale = 1;
          imgPosiInit(tempIndex);
          break;
      }
    }
    // 轮播初始化
    function swiperInit(url: string, count: number) {
      const galleryImgs = [];
      for (let i = 0; i < count; i++) {
        galleryImgs.push({
          id: i + 1,
          src: url + '/0.0.' + (i + 1) + '.png'
        });
      }
      state.galleryTopImgs = galleryImgs;
      state.galleryThumbsImgs = galleryImgs;
      nextTick(function () {
        // 生成预览轮播
        state.galleryTop = new Swiper('.gallery-top', {
          lazy: {
            elementClass: 'preview-lazy',
            loadPrevNext: true,
            loadPrevNextAmount: 3
          },
          noSwiping: true,
          noSwipingClass: 'stop-swiping',
          longSwipesMs: 1000,
          longSwipes: false,
          spaceBetween: 10,
          pagination: {
            el: '.preview-pagination',
            type: 'fraction'
          },
          navigation: {
            nextEl: '.swiper-imageTool .icon-next',
            prevEl: '.swiper-imageTool .icon-pre'
          },
          on: {
            slideNextTransitionStart: function () {
              if (state.galleryTopImgs.length === 1) return;
              previewTo('recover', state.galleryTop.activeIndex - 1);
            },
            slidePrevTransitionStart: function () {
              if (state.galleryTopImgs.length === 1) return;
              previewTo('recover', state.galleryTop.activeIndex + 1);
            }
          }
        });
        state.galleryThumbs = new Swiper('.gallery-thumbs', {
          lazy: {
            elementClass: 'preview-lazy',
            loadPrevNext: true,
            loadPrevNextAmount: 3
          },
          freeMode: true,
          freeModeSticky: true,
          spaceBetween: 10,
          centeredSlides: true,
          slidesPerView: 9,
          touchRatio: 0.2,
          slideToClickedSlide: true
        });
        state.galleryTop.controller.control = state.galleryThumbs;
        state.galleryThumbs.controller.control = state.galleryTop;
      });
    }
    /**
     * 传图片集合
     */
    function swiperInitByImgArr(imgArr: any, slideIndex = 0) {
      const galleryImgs = [];
      for (let i = 0; i < imgArr.length; i++) {
        galleryImgs.push({
          id: i + 1,
          src: imgArr[i].url
        });
      }
      state.galleryTopImgs = galleryImgs;
      state.galleryThumbsImgs = galleryImgs;
      nextTick(function () {
        // 生成预览轮播
        state.galleryTop = new Swiper('.gallery-top', {
          lazy: {
            elementClass: 'preview-lazy',
            loadPrevNext: true,
            loadPrevNextAmount: 3
          },
          noSwiping: true,
          noSwipingClass: 'stop-swiping',
          longSwipesMs: 1000,
          longSwipes: false,
          spaceBetween: 10,
          pagination: {
            el: '.preview-pagination',
            type: 'fraction'
          },
          navigation: {
            nextEl: '.swiper-imageTool .icon-next',
            prevEl: '.swiper-imageTool .icon-pre'
          },
          on: {
            slideNextTransitionStart: function () {
              if (state.galleryTopImgs.length === 1) return;
              previewTo('recover', state.galleryTop.activeIndex - 1);
            },
            slidePrevTransitionStart: function () {
              if (state.galleryTopImgs.length === 1) return;
              previewTo('recover', state.galleryTop.activeIndex + 1);
            }
          }
        });
        state.galleryThumbs = new Swiper('.gallery-thumbs', {
          lazy: {
            elementClass: 'preview-lazy',
            loadPrevNext: true,
            loadPrevNextAmount: 3
          },
          freeMode: true,
          freeModeSticky: true,
          spaceBetween: 10,
          centeredSlides: true,
          slidesPerView: 9,
          touchRatio: 0.2,
          slideToClickedSlide: true
        });

        state.galleryTop.controller.control = state.galleryThumbs;
        state.galleryThumbs.controller.control = state.galleryTop;
        state.galleryTop.slideTo(slideIndex);
      });
    }
    /**
     * 当图片处于锁定状态，响应左滑动事件
     */
    function slideNext() {
      const index = state.galleryTop.activeIndex;
      const swingStop = !state.galleryTopImgs[index].stop
        ? ''
        : state.galleryTopImgs[index].stop;
      if (!swingStop) return;
      state.galleryTop.slideNext();
    }
    /**
     * 当图片处于锁定状态，响应右滑动事件
     */
    function slidePrev() {
      const index = state.galleryTop.activeIndex;
      const swingStop = !state.galleryTopImgs[index].stop
        ? ''
        : state.galleryTopImgs[index].stop;
      if (!swingStop) return;
      state.galleryTop.slidePrev();
    }
    /**
     * 指定音频播放
     */
    function setAudioPlay(fileUrl: any) {
      $('.img-btn-box .img-delete').show();
      $('.img-ml-box .audio').attr('src', fileUrl);
      $('#audio-player-container').show();
      $('.img-ml-box .canvas').hide();
      $('.img-ml-box').data('type', 'question').show();
    }
    /*
     * 初始化音频播放
     */
    function initAudio(fileUrl: any) {
      //获取并绑定音频播放器
      var $audioPlayerContainer = $('#audio-player-container'),
        $audioPlayer = $('#audio-player')[0],
        $audioPlayBtn = $('#audio-player-container .play-btn');
      console.log('@@@@', $('#audio-player'));
      $audioPlayer.ontimeupdate = function (val: any) {
        var c, d, curM, curS;
        d = val.currentTarget.duration;
        c = val.currentTarget.currentTime;
        curM = Math.floor(c / 60);
        curS = Math.round(c - curM * 60);
        curM = curM < 10 ? '0' + curM : curM;
        curS = curS < 10 ? '0' + curS : curS;
        $audioPlayerContainer
          .find('.audio-progress-bar')
          .css('width', (c / d) * 100 + '%');
        if ((c / d) * 100 == 100) {
          setTimeout(function () {
            $audioPlayer.pause();
            $audioPlayBtn.removeClass('play').addClass('pause');
            $audioPlayerContainer.find('.audio-progress-bar').width(0);
          }, 1000);
        }
      };
      $audioPlayerContainer.on('click', '.play-btn.play', function () {
        $audioPlayer.pause();
        $audioPlayBtn.removeClass('play').addClass('pause');
        $audioPlayerContainer.find('.audio-progress-bar').css('width', '0px');
      });
      $audioPlayerContainer.on('click', '.play-btn.pause', function () {
        $audioPlayer.play();
        $audioPlayBtn.removeClass('pause').addClass('play');
      });
      setAudioPlay(fileUrl);
    }
    /**
     * 绑定音频播放点击事件
     */
    function bindEvent() {
      //图片预览遮————关闭
      $('.img-ml-box').delegate('.img-ml-close', 'click', function () {
        $('.img-ml-box').hide();
        $('.img-ml-box .audio')[0].pause();
        $('.img-ml-box .audio')[0].currentTime = 0;
        $('#audio-player-container .play-btn')
          .removeClass('pause')
          .addClass('play');
        $('#audio-player-container').hide();
        closePreview();
      });
    }
    /**
     * 初始化音频预览事件
     */
    function initAudioPreview(fileUrl: any) {
      initAudio(fileUrl);
      bindEvent();
    }
    /**
     * 关闭预览
     */
    function closePreview() {
      context.emit('close-preview');
    }
    function onLike() {
      state.isLike = !state.isLike;
      context.emit('on-like', state.isLike);
    }
    function onDetail() {
      context.emit('on-detail');
      context.emit('close-preview');
    }

    function mousewheel(e: any, index: any) {
      const scaleOrigin = !state.galleryTopImgs[index].scale
          ? 1
          : state.galleryTopImgs[index].scale,
        scaleNow = e.deltaY <= 0 ? scaleOrigin * 1.5 : scaleOrigin / 1.5;
      state.galleryTopImgs[index].scale =
        scaleNow < 0.3 ? 0.3 : scaleNow > 5 ? 5 : scaleNow;
      if (scaleNow === 1) {
        // 当图片处于正常比例，放开swiper拖动峡效果
        imgPosiInit(index);
      } else {
        state.galleryTopImgs[index].stop = 'stop-swiping';
      }
    }
    /**
     * @name  图片拖动 - 开始
     */
    function imgPanMove(e: any, index: any) {
      let swingStop = !state.galleryTopImgs[index].stop
        ? ''
        : state.galleryTopImgs[index].stop;
      if (!swingStop) return;
      document.onmousemove = (ev) => {
        // 用鼠标的位置减去鼠标相对元素的位置，得到元素的位置
        state.galleryTopImgs[index].tranX = ev.clientX - e.clientX;
        state.galleryTopImgs[index].tranY = ev.clientY - e.clientY;
      };
      document.onmouseup = () => {
        document.onmousemove = null;
      };
    }
    /**
     * @name 切换资源包中的资源
     */
    const changePackageRes = (item: any) => {
      context.emit('change-package-res', item);
    };

    return {
      ...toRefs(state),
      addFsUrl,
      swiperInit,
      swiperInitByImgArr,
      previewTo,
      slideNext,
      slidePrev,
      closePreview,
      setAudioPlay,
      initAudio,
      bindEvent,
      initAudioPreview,
      togglePlayBackMenu,
      switchVideoPlayBackRate,
      mousewheel,
      imgPanMove,
      changePackageRes,
      onDetail,
      onLike
    };
  }
});
</script>

<style lang="scss" scoped>
@import './style/previewSlider';
@import './style/swiper.min.css';

.left-package-slide {
  position: fixed;
  top: 0;
  left: 0;
  width: 10%;
  height: 100%;
  z-index: 9000;
  background: rgba(0, 0, 0, 0.5);
  overflow: hidden auto;

  .item {
    height: 80px;
    line-height: 80px;
    text-align: center;
    color: #fff;
    border-bottom: 1px solid #ccc;
  }
}

.swiper-imageTool .icon-imageTools {
  background: url('./assets/icon-imageTools.png') no-repeat;

  &.detail {
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
  }
  &.like {
    background: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    &.active {
      @include theme_color();
    }
  }
}

.video-close-btn {
  background: url('./assets/icon-video-close.png') no-repeat;
  &.detail {
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 96px;
    border-radius: 0 10px 10px 0;
    background: linear-gradient(to right, #ddd, #fff 20%);
  }
  &.like {
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 166px;
    border-radius: 0 10px 10px 0;
    background: linear-gradient(to right, #ddd, #fff 20%);
    &.active {
      @include theme_color();
    }
  }
}

.video-playback-rate {
  position: absolute;
  right: 150px;
  z-index: 26;
  display: none;
  margin: 0;
  width: 48px;
  height: 48px;
  bottom: 46px;
  padding: 6px;

  li {
    color: rgba(255, 255, 255, 0.5);
    text-align: center;
    font-size: 14px;
    line-height: 20px;
    list-style-type: none;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:hover,
    &.active {
      color: #fff;
    }
  }
}

.video-playback-rate:hover {
  li {
    background: rgba(32, 33, 36, 0.71);
    border-radius: 50%;
  }
}

/*图片预览遮罩层*/
.img-ml-box {
  position: fixed;
  top: 90px;
  height: 100%;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  z-index: 50;
  display: none;
}

/*遮罩层背景*/
.img-ml-box .img-ml-bg {
  width: 100%;
  height: 100%;
  position: fixed;
  left: 0px;
  top: 0px;
  right: 0px;
  bottom: 0px;
  background: #000;
  opacity: 0.7;
  z-index: 55;
}

/*关闭按钮*/
.img-ml-box .img-ml-close {
  position: absolute;
  right: -45px;
  top: 32px;
  width: 25px;
  height: 25px;
  z-index: 999;
  opacity: 0.6;
  background: url('./assets/close.png') center center no-repeat;
  &.detail {
    right: -110px;
    background: none;
    width: 50px;
    top: 34px;
    color: #fff;
  }
  &.like {
    right: -155px;
    background: none;
    width: 50px;
    top: 32px;
    color: #fff;
    &.active {
      @include theme_color();
    }
  }
}

.img-ml-box .img-ml-close:hover {
  opacity: 1;
}

.audio-player-container {
  position: absolute;
  z-index: 70;
  top: 44%;
  left: 50%;
  width: 70%;
  max-width: 1200px;
  padding: 33px 80px 33px 156px;
  margin: 0 auto;
  display: block;
  background-color: #fff;
  border-radius: 5px;
  -webkit-transform: translate3d(-50%, -50%, 0);
  -moz-transform: translate3d(-50%, -50%, 0);
  -ms-transform: translate3d(-50%, -50%, 0);
  -o-transform: translate3d(-50%, -50%, 0);
  transform: translate3d(-50%, -50%, 0);
  display: none;
}

.audio-player-container .audio-progress {
  width: 100%;
  height: 24px;
  background: rgba(239, 241, 250, 1);
  border: 1px solid rgba(232, 234, 244, 1);
  border-radius: 12px;
  overflow: hidden;
}

.audio-player-container .audio-progress .audio-progress-bar {
  height: 100%;
  background: rgba(62, 115, 246, 1);
  border-radius: 12px;
}

.audio-player-container .play-btn {
  position: absolute;
  top: 14px;
  left: 71px;
  width: 63px;
  height: 63px;
  background: url('./assets/icon-audioplayer.png') no-repeat;
  cursor: pointer;
}

.audio-player-container .play-btn:active {
  opacity: 0.6;
}

.audio-player-container .play-btn.play {
  background-position: 1px 1px;
}

.audio-player-container .play-btn.pause {
  background-position: 1px -64px;
}

video::-webkit-media-controls-volume-control-container {
  margin-right: 60px;
}

.close-icon {
  position: fixed;
  top: 10px;
  right: 60px;
  font-size: 36px;
  color: #fff;
  cursor: pointer;
  z-index: 222;
}
</style>
